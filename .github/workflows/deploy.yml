name: Deploy to Azure VM

on:
  push:
    branches: [ fresh-start ]
  pull_request:
    branches: [ fresh-start ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd src/next && npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build:all

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/fresh-start'
    
    steps:
    - name: Deploy to Azure VM
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.AZURE_VM_HOST }}
        username: ${{ secrets.AZURE_VM_USER }}
        key: ${{ secrets.AZURE_VM_SSH_KEY }}
        script: |
          cd /var/www/kapi
          ./deploy.sh
