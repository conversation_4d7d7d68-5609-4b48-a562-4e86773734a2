# KAPI MVP: The "Holy Shit" Todo List

## The Opening Experience (Week 1)
- [ ] **Project Health Dashboard** - Single view that shows when opening existing project
  - [ ] Security confidence score with visual meter (green/yellow/red)
  - [ ] "No silent killers detected" or "3 potential issues found" banner
  - [ ] Technical debt heatmap - color-coded file tree showing complexity
  - [ ] Last 24h changes summary with who changed what

## The Backwards Build Magic (Week 1-2)
- [ ] **Auto-generate documentation from existing code**
  - [ ] Extract business logic into plain English explanations
  - [ ] Create visual flow diagram of main user journeys
  - [ ] Generate "What this project does" executive summary
  
- [ ] **Test Coverage Visualization**
  - [ ] Show which features have tests vs which don't
  - [ ] "Suggested tests" based on uncovered critical paths
  - [ ] One-click test generation for selected components

- [ ] **Sync Status Panel**
  - [ ] "Docs ↔ Code Alignment: 73%" metric
  - [ ] Highlight where code has drifted from documented behavior
  - [ ] "Fix drift" button that updates either code or docs

## The Context Restoration (Week 2)
- [ ] **"Welcome Back" Screen**
  - [ ] "You were working on: [component name]" with visual preview
  - [ ] "Changes since you left: 3 files modified by TeamMember1"
  - [ ] One-click restore to exact cursor position and file state
  
- [ ] **Smart History Timeline**
  - [ ] Visual git history focused on business changes, not commits
  - [ ] "Feature X was completed 3 days ago" instead of "Merge PR #234"

## The Confidence Builders (Week 2-3)
- [ ] **AI vs Human Code Indicators**
  - [ ] Subtle sidebar showing % of AI-generated vs human-reviewed code
  - [ ] "Verified by human" checkmarks on critical functions
  - [ ] Security audit trail for AI-generated segments

- [ ] **One-Click Verification Suite**
  - [ ] "Verify Security" button - runs top 5 security checks
  - [ ] "Check Best Practices" - compares against Modern AI Pro patterns
  - [ ] Results in plain English: "Your auth flow matches industry standards ✓"

## The Delight Moments (Week 3)
- [ ] **Project Personality**
  - [ ] Auto-generated project "mood" based on code health
  - [ ] Celebratory animation when technical debt decreases
  - [ ] "Your project is 23% healthier than last month" notifications

- [ ] **Quick Wins Panel**
  - [ ] "Fix these 3 things to improve project health by 40%"
  - [ ] Time estimates for each fix
  - [ ] One-click automated fixes for simple issues

## Launch Checklist (Final Week)
- [ ] **Polish the "First 30 Seconds"**
  - [ ] Loading animation that shows "Analyzing project health..."
  - [ ] Smooth transitions between views
  - [ ] Clear, non-technical language throughout

- [ ] **The Hook**
  - [ ] "See your project like never before" splash screen
  - [ ] Before/after preview: messy code → clean documentation
  - [ ] Share button: "Show your team this view"

---

**THE NORTH STAR**: When a burned-out developer opens their 6-month-old project, they should feel *relief* not dread. Every feature should answer: "Does this reduce anxiety or increase it?"

**SHIP DATE**: 4 weeks from start
**MUST HAVE**: Health dashboard, backwards documentation, sync checking
**CUT IF NEEDED**: Delight moments, some polish
**ABSOLUTELY NO**: Voice, sketching, mobile apps, complex animations
