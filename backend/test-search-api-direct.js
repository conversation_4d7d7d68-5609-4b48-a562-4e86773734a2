const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testSearchAPIDirect() {
  console.log('🔍 Testing Search API with Direct ChromaDB Documents...\n');

  try {
    // Test 1: Check backend connection
    console.log('1. Checking backend connection...');
    const healthResponse = await axios.get(`${BASE_URL}/api/search/health`);
    console.log('✅ Backend is running');
    console.log(`   Health status: ${JSON.stringify(healthResponse.data)}`);
    console.log('');

    // Test 2: Add test documents directly to ChromaDB
    console.log('2. Adding test documents directly to ChromaDB...');
    const { chromaDBService } = require('./dist/src/services/chroma-db.service.js');

    await chromaDBService.initialize();

    // Clean up any existing test documents
    try {
      await chromaDBService.deleteDocuments(['test_search_1', 'test_search_2', 'test_search_3']);
    } catch (error) {
      // Ignore if documents don't exist
    }

    const testDocs = [
      {
        id: 'test_search_1',
        content: 'User authentication and login functionality with password validation and security features',
        metadata: {
          sourceFile: 'auth.ts',
          filePath: '/test/auth.ts',
          type: 'function',
          language: 'typescript'
        }
      },
      {
        id: 'test_search_2',
        content: 'Database connection error handling and retry logic for PostgreSQL connections',
        metadata: {
          sourceFile: 'database.ts',
          filePath: '/test/database.ts',
          type: 'function',
          language: 'typescript'
        }
      },
      {
        id: 'test_search_3',
        content: 'React component state management using hooks and context API',
        metadata: {
          sourceFile: 'component.tsx',
          filePath: '/test/component.tsx',
          type: 'component',
          language: 'typescript'
        }
      }
    ];

    for (const doc of testDocs) {
      await chromaDBService.addDocument(doc.id, doc.content, doc.metadata);
      console.log(`   ✅ Added: ${doc.id}`);
    }
    console.log('');

    // Test 3: Test semantic search API
    console.log('3. Testing semantic search API...');

    const queries = [
      { query: 'user login authentication', expected: 'test_search_1' },
      { query: 'database error connection', expected: 'test_search_2' },
      { query: 'react component state', expected: 'test_search_3' }
    ];

    for (const { query, expected } of queries) {
      console.log(`\n   Query: "${query}"`);

      try {
        const searchResponse = await axios.post(`${BASE_URL}/api/search/semantic`, {
          query: query,
          limit: 5
        });

        const results = searchResponse.data.results;
        console.log(`   ✅ Found ${results.length} results`);

        if (results.length > 0) {
          results.forEach((result, index) => {
            console.log(`     ${index + 1}. ${result.documentId || result.id}`);
            console.log(`        File: ${result.file}`);
            console.log(`        Score: ${result.score.toFixed(4)}`);
            console.log(`        Preview: ${result.preview.substring(0, 60)}...`);
          });

          // Check if expected document is in top results
          const topResult = results[0];
          if (topResult.documentId === expected || topResult.id === expected) {
            console.log(`   🎯 Expected document "${expected}" found as top result!`);
          } else {
            console.log(`   ⚠️  Expected document "${expected}" not found as top result`);
          }
        } else {
          console.log('   ❌ No results found');
        }
      } catch (error) {
        console.error(`   ❌ Search failed: ${error.message}`);
        if (error.response) {
          console.error(`   Response: ${JSON.stringify(error.response.data)}`);
        }
      }
    }
    console.log('');

    // Test 4: Test with filters
    console.log('4. Testing filtered search...');
    try {
      const filteredResponse = await axios.post(`${BASE_URL}/api/search/semantic`, {
        query: 'authentication',
        limit: 5,
        filters: {
          language: 'typescript'
        }
      });

      console.log(`   ✅ Filtered search successful`);
      console.log(`   Found ${filteredResponse.data.results.length} TypeScript results`);
    } catch (error) {
      console.error(`   ❌ Filtered search failed: ${error.message}`);
    }
    console.log('');

    // Test 5: Test documentation search endpoint
    console.log('5. Testing documentation search endpoint...');
    try {
      const docSearchResponse = await axios.post(`${BASE_URL}/api/documentation/search`, {
        query: 'authentication',
        limit: 5
      });

      console.log(`   ✅ Documentation search successful`);
      console.log(`   Found ${docSearchResponse.data.results.length} documentation results`);

      if (docSearchResponse.data.results.length > 0) {
        docSearchResponse.data.results.forEach((result, index) => {
          console.log(`     ${index + 1}. ${result.id}`);
          console.log(`        Similarity: ${result.similarity.toFixed(4)}`);
        });
      }
    } catch (error) {
      console.error(`   ❌ Documentation search failed: ${error.message}`);
    }
    console.log('');

    // Cleanup
    console.log('6. Cleaning up test documents...');
    await chromaDBService.deleteDocuments(['test_search_1', 'test_search_2', 'test_search_3']);
    console.log('✅ Cleanup completed');
    console.log('');

    console.log('🎉 Search API test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testSearchAPIDirect()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testSearchAPIDirect };
