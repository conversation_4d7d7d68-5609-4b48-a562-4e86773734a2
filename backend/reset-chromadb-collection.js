#!/usr/bin/env node

/**
 * Reset ChromaDB Collection
 * 
 * This script resets the ChromaDB collection with proper embedding function
 */

const { ChromaClient } = require('chromadb');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function resetChromaDBCollection() {
  console.log('🔄 Resetting ChromaDB Collection...\n');

  try {
    // Initialize ChromaDB client
    const client = new ChromaClient({
      host: process.env.CHROMA_HOST || 'localhost',
      port: parseInt(process.env.CHROMA_PORT || '8000', 10)
    });

    console.log('✅ ChromaDB client initialized');

    const collectionName = process.env.CHROMA_COLLECTION || 'documentation_embeddings';
    console.log(`📁 Working with collection: ${collectionName}`);

    // Try to delete existing collection
    try {
      await client.deleteCollection({ name: collectionName });
      console.log('🗑️  Deleted existing collection');
    } catch (error) {
      console.log('ℹ️  No existing collection to delete');
    }

    // Create new collection with default embedding function
    console.log('📁 Creating new collection with embedding function...');
    const collection = await client.createCollection({
      name: collectionName
      // ChromaDB will use the default embedding function
    });

    console.log('✅ New collection created successfully');

    // Test the collection with a simple document
    console.log('\n🧪 Testing collection with sample document...');
    await collection.add({
      ids: ['test_doc'],
      documents: ['This is a test document to verify the collection works correctly.'],
      metadatas: [{ type: 'test', timestamp: new Date().toISOString() }]
    });

    console.log('✅ Sample document added successfully');

    // Test query
    const results = await collection.query({
      queryTexts: ['test document'],
      nResults: 1
    });

    console.log('✅ Query test successful');
    console.log('📊 Results:', {
      ids: results.ids[0],
      documents: results.documents[0]
    });

    // Clean up test document
    await collection.delete({ ids: ['test_doc'] });
    console.log('🧹 Test document cleaned up');

    console.log('\n🎉 ChromaDB collection reset complete!');
    console.log('💡 The collection is now ready for semantic search operations.');
    
    return true;

  } catch (error) {
    console.error('❌ Failed to reset ChromaDB collection:', error.message);
    return false;
  }
}

// Run the reset
if (require.main === module) {
  resetChromaDBCollection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { resetChromaDBCollection };
