import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

/**
 * <PERSON><PERSON><PERSON> to apply performance optimization indexes to the database
 * Run with: npx ts-node scripts/apply-performance-indexes.ts
 */

const prisma = new PrismaClient();

async function applyPerformanceIndexes() {
  console.log('🚀 Applying performance indexes to database...\n');

  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../prisma/migrations/performance_indexes.sql');
    const sql = fs.readFileSync(sqlPath, 'utf-8');

    // Split by semicolon and filter out empty statements
    const statements = sql
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0);

    let successCount = 0;
    let skipCount = 0;

    // Execute each statement
    for (const statement of statements) {
      try {
        console.log(`Executing: ${statement.substring(0, 60)}...`);
        await prisma.$executeRawUnsafe(statement);
        successCount++;
        console.log('✅ Success\n');
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          console.log('⏭️  Index already exists, skipping...\n');
          skipCount++;
        } else {
          console.error('❌ Error:', error.message, '\n');
        }
      }
    }

    console.log('📊 Summary:');
    console.log(`✅ Successfully created: ${successCount} indexes`);
    console.log(`⏭️  Already existed: ${skipCount} indexes`);
    console.log(`📈 Total indexes: ${statements.length}`);

    // Analyze query performance improvement
    console.log('\n🔍 Analyzing table statistics...');
    
    const tables = [
      'conversations',
      'messages',
      'model_usage',
      'user_sessions',
      'projects'
    ];

    for (const table of tables) {
      try {
        const result = await prisma.$queryRawUnsafe<any[]>(
          `SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            n_live_tup as row_count
          FROM pg_stat_user_tables 
          WHERE tablename = $1`,
          table
        );

        if (result.length > 0) {
          const stats = result[0];
          console.log(`\n📊 ${table}:`);
          console.log(`   Size: ${stats.size}`);
          console.log(`   Rows: ${stats.row_count?.toLocaleString() || 0}`);
        }
      } catch (error) {
        // Ignore errors for missing tables
      }
    }

    console.log('\n✨ Performance optimization complete!');
    console.log('💡 Remember to run ANALYZE on your tables for best performance:');
    console.log('   ANALYZE conversations, messages, model_usage, projects;');

  } catch (error) {
    console.error('❌ Failed to apply indexes:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
applyPerformanceIndexes().catch(console.error);
