import { Project, SyntaxKind, FunctionDeclaration, ClassDeclaration } from 'ts-morph';
import * as fs from 'fs';

interface CodeUnit {
    name: string;
    type: 'function' | 'class';
    content: string;
}

function parseFile(filePath: string): CodeUnit[] {
    const project = new Project();
    const sourceFile = project.addSourceFileAtPath(filePath);
    const units: CodeUnit[] = [];

    // Get top-level functions
    sourceFile.getFunctions().forEach(func => {
        units.push({
            name: func.getName() || '[anonymous]',
            type: 'function',
            content: func.getFullText()
        });
    });

    // Get top-level classes
    sourceFile.getClasses().forEach(cls => {
        units.push({
            name: cls.getName() || '[anonymous]',
            type: 'class',
            content: cls.getFullText()
        });
    });

    return units;
}

const filePath = process.argv[2];
if (!filePath) {
    console.error('Usage: ts-node parse_js_ts.ts <file_path>');
    process.exit(1);
}

if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    process.exit(1);
}

const units = parseFile(filePath);
console.log(JSON.stringify(units, null, 2));
