#!/bin/bash

# Simple test runner for KAPI
# Usage: ./scripts/run-tests.sh [unit|integration|all]

set -e

export NODE_ENV=test

case ${1:-all} in
  "unit")
    echo "Running unit tests..."
    npx jest --testPathIgnorePatterns='integration'
    ;;
  
  "integration")
    echo "Running integration tests..."
    npx jest tests/integration
    ;;
  
  "all")
    echo "Running all tests..."
    npx jest
    ;;
  
  *)
    echo "Usage: $0 [unit|integration|all]"
    exit 1
    ;;
esac
