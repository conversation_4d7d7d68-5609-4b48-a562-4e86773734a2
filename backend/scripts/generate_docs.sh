#!/usr/bin/env bash
# This script orchestrates the AI-powered documentation generation for staged files.

set -e # Exit immediately if a command exits with a non-zero status.
set -x # Enable shell debugging

echo "--- Ka<PERSON> AI: Running Automated Documentation Generator ---"

# Define paths
BASE_DIR="/home/<USER>/Desktop/kapi-main/kapi/backend"
VENV_DIR="$BASE_DIR/venv"
PYTHON_EXEC="$VENV_DIR/bin/python"
PIP_EXEC="$VENV_DIR/bin/pip"
REQ_FILE="$BASE_DIR/requirements.txt"
SCRIPT_FILE="$BASE_DIR/scripts/generate_docs.py"
ERROR_LOG="$BASE_DIR/docs/generated/error.log"

# Ensure the generated docs directory exists
mkdir -p "$BASE_DIR/docs/generated/files"

# Activate virtual environment and install dependencies
$PIP_EXEC install -r "$REQ_FILE"

# Execute the Python script.
# stdout will contain the list of files to stage.
# stderr will be redirected to a log file for debugging.
output=$($PYTHON_EXEC "$SCRIPT_FILE" 2> "$ERROR_LOG")

# Check if the script produced any file paths to stage.
if [ -n "$output" ]; then
    echo "--- Staging updated documentation files ---"
    # Use xargs to safely handle file paths.
    echo "$output" | xargs git add
    echo "--- Documentation staged successfully. ---"
else
    echo "--- No documentation files needed to be updated. ---"
fi

# Check if there were any errors logged
if [ -s "$ERROR_LOG" ]; then
    echo "--- Errors were logged during documentation generation. See $ERROR_LOG ---"
fi

echo "--- Documentation generation script finished. ---"
