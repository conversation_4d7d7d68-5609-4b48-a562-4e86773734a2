#!/usr/bin/env npx tsx

/**
 * Simple script to create an admin user in the database
 * This system uses Clerk for authentication, so this creates a user record
 * that can be used with the admin login system.
 * Usage: npm run create-admin-user-simple
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    console.log('🔐 Creating KAPI Admin User...\n');
    
    const email = '<EMAIL>';
    const username = 'balaji';
    const clerkId = `admin_${Date.now()}`;
    
    // Check if user already exists
    const existingUser = await prisma.users.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });
    
    if (existingUser) {
      console.log(`✅ Admin user already exists!`);
      console.log(`📧 Email: ${existingUser.email}`);
      console.log(`👤 Username: ${existingUser.username}`);
      console.log(`🆔 User ID: ${existingUser.id}`);
      console.log(`🔑 Role: ${existingUser.role}`);
      console.log(`\n🌐 You can login at: http://localhost:3000/admin/login`);
      console.log(`📋 Use the development credentials: <EMAIL> / admin123`);
      return;
    }
    
    // Create admin user
    const adminUser = await prisma.users.create({
      data: {
        email,
        username,
        first_name: 'Balaji',
        last_name: 'Viswanathan',
        clerk_id: clerkId,
        role: 'ADMIN',
        is_active: true,
        email_verified: true,
        onboarding_completed: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    });
    
    console.log(`✅ Admin user created successfully!`);
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`👤 Username: ${adminUser.username}`);
    console.log(`🆔 User ID: ${adminUser.id}`);
    console.log(`🔑 Role: ${adminUser.role}`);
    console.log(`🔗 Clerk ID: ${adminUser.clerk_id}`);
    console.log(`\n🌐 Login Options:`);
    console.log(`1. Development login: http://localhost:3000/admin/login`);
    console.log(`   📋 Credentials: <EMAIL> / admin123`);
    console.log(`\n2. Database user created for: ${adminUser.email}`);
    console.log(`   💡 This user can be integrated with Clerk authentication`);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        console.error('💡 This email or username is already taken.');
      } else {
        console.error(`💡 Details: ${error.message}`);
      }
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    process.exit(0);
  }
}

// Run the script
createAdminUser();