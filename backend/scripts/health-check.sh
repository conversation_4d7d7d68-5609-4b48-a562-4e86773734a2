#!/bin/bash

# System health monitoring script
set -e

echo "🏥 KAPI System Health Check"
echo "=========================="

# Check disk space
echo "💾 Disk Space:"
df -h / | tail -n 1

# Check memory usage
echo -e "\n🧠 Memory Usage:"
free -h

# Check CPU load
echo -e "\n⚡ CPU Load:"
uptime

# Check PM2 processes
echo -e "\n🔄 PM2 Processes:"
pm2 list

# Check application health
echo -e "\n🌐 Application Health:"
curl -s http://localhost:3000/health || echo "❌ Application health check failed"

# Check PostgreSQL
echo -e "\n🗄️ Database Status:"
sudo systemctl is-active postgresql || echo "❌ PostgreSQL not running"

# Check Nginx
echo -e "\n🌍 Nginx Status:"
sudo systemctl is-active nginx || echo "❌ Nginx not running"

# Check SSL certificates (expires in 30 days or less)
echo -e "\n🔒 SSL Certificate Status:"
for domain in kapihq.com modernaipro.com; do
    if openssl s_client -connect $domain:443 -servername $domain </dev/null 2>/dev/null | openssl x509 -noout -checkend 2592000; then
        echo "✅ $domain SSL certificate is valid"
    else
        echo "❌ $domain SSL certificate expires within 30 days"
    fi
done

echo -e "\n✅ Health check completed!"