#!/usr/bin/env bash
# Post-commit hook to automatically index generated documentation
# This script runs after a successful commit to index any new documentation

set -e # Exit immediately if a command exits with a non-zero status.

echo "--- Post-commit: Indexing Documentation ---"

# Get the current commit hash
COMMIT_HASH=$(git rev-parse HEAD)
PROJECT_PATH=$(git rev-parse --show-toplevel)

echo "Commit: $COMMIT_HASH"
echo "Project: $PROJECT_PATH"

# Check if documentation was generated in this commit
DOC_FILES_CHANGED=$(git diff-tree --no-commit-id --name-only -r $COMMIT_HASH | grep -E "docs/generated/.*\.doc\.json$" || true)

if [ -n "$DOC_FILES_CHANGED" ]; then
    echo "Documentation files were updated in this commit:"
    echo "$DOC_FILES_CHANGED"
    
    # Call the backend API to index the documentation for this commit
    # Note: This assumes the backend is running on localhost:3000
    BACKEND_URL="http://localhost:3000"
    
    echo "Indexing documentation for commit $COMMIT_HASH..."
    
    # Use curl to call the commit indexing endpoint
    RESPONSE=$(curl -s -X POST "$BACKEND_URL/documentation/commit/$COMMIT_HASH" \
        -H "Content-Type: application/json" \
        -d "{\"projectPath\": \"$PROJECT_PATH\"}" \
        -w "%{http_code}" || echo "000")
    
    HTTP_CODE="${RESPONSE: -3}"
    RESPONSE_BODY="${RESPONSE%???}"
    
    if [ "$HTTP_CODE" = "202" ]; then
        echo "✅ Documentation indexing started successfully"
        echo "Response: $RESPONSE_BODY"
    else
        echo "⚠️  Documentation indexing request failed (HTTP $HTTP_CODE)"
        echo "Response: $RESPONSE_BODY"
        echo "Note: This is not critical - documentation can be indexed manually later"
    fi
else
    echo "No documentation files were updated in this commit - skipping indexing"
fi

echo "--- Post-commit indexing completed ---"
