-- Add this migration to your Prisma schema or run as a SQL migration
-- This creates the document_embeddings table with pgvector support

-- Enable the pgvector extension (run as superuser if needed)
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the document_embeddings table
CREATE TABLE IF NOT EXISTS document_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL,
  file_path TEXT NOT NULL,
  document_type VARCHAR(50) NOT NULL CHECK (document_type IN ('auto-doc', 'interview', 'readme', 'comment')),
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI text-embedding-3-small dimension
  metadata JSONB DEFAULT '{}',
  commit_hash VARCHAR(40),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Add foreign key to projects table (adjust table name if different)
  CONSTRAINT fk_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- <PERSON>reate indexes for efficient searching
CREATE INDEX idx_embedding_search ON document_embeddings 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Create index for project_id lookups
CREATE INDEX idx_document_project ON document_embeddings(project_id);

-- Create index for file_path lookups
CREATE INDEX idx_document_file_path ON document_embeddings(file_path);

-- Create index for document_type filtering
CREATE INDEX idx_document_type ON document_embeddings(document_type);

-- Create composite index for common queries
CREATE INDEX idx_document_project_type ON document_embeddings(project_id, document_type);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_embeddings_updated_at BEFORE UPDATE
    ON document_embeddings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
