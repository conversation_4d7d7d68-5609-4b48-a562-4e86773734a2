#!/bin/bash

# Database migration script from dev to production
set -e

DEV_DB_HOST="localhost"
DEV_DB_USER="postgres" 
DEV_DB_NAME="kapi"
PROD_DB_HOST="localhost"
PROD_DB_USER="postgres"
PROD_DB_NAME="kapi"

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DUMP_FILE="/tmp/kapi_migration_$TIMESTAMP.sql"

echo "🔄 Starting database migration..."

# Step 1: Create dump from development
echo "📤 Exporting from development database..."
pg_dump -h $DEV_DB_HOST -U $DEV_DB_USER -d $DEV_DB_NAME --clean --if-exists --no-owner --no-privileges > $DUMP_FILE

# Step 2: Stop application
echo "⏸️ Stopping application..."
pm2 stop kapi-backend || true

# Step 3: Backup production database
echo "💾 Backing up production database..."
./scripts/backup-db.sh

# Step 4: Restore to production
echo "📥 Importing to production database..."
psql -h $PROD_DB_HOST -U $PROD_DB_USER -d $PROD_DB_NAME < $DUMP_FILE

# Step 5: Run migrations
echo "🔧 Running database migrations..."
npm run migration:run

# Step 6: Start application
echo "▶️ Starting application..."
pm2 start kapi-backend

# Cleanup
rm $DUMP_FILE

echo "✅ Database migration completed successfully!"