#!/bin/bash

# Database backup script for production
set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/postgresql"
DB_NAME="kapi"
DB_USER="postgres"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

echo "📦 Creating database backup..."

# Create backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME --clean --if-exists > $BACKUP_DIR/kapi_backup_$TIMESTAMP.sql

echo "✅ Backup created: $BACKUP_DIR/kapi_backup_$TIMESTAMP.sql"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "kapi_backup_*.sql" -mtime +7 -delete

echo "🧹 Cleaned up old backups"