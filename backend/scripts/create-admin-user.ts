#!/usr/bin/env npx tsx

/**
 * <PERSON><PERSON><PERSON> to create an admin user in the database
 * Note: This system uses Clerk for authentication, so we create a local admin
 * for development purposes that can be used with the built-in admin login.
 * Usage: npm run create-admin-user
 */

import { PrismaClient } from '@prisma/client';
import readline from 'readline';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

function askPassword(question: string): Promise<string> {
  return new Promise((resolve) => {
    process.stdout.write(question);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    
    process.stdin.on('data', (char) => {
      char = char.toString();
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004': // Ctrl+D
          process.stdin.setRawMode(false);
          process.stdin.pause();
          console.log(); // New line
          resolve(password);
          break;
        case '\u0003': // Ctrl+C
          process.exit();
          break;
        case '\u007f': // Backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function createAdminUser() {
  try {
    console.log('🔐 KAPI Admin User Creation Tool\n');
    
    // Get user details
    const email = await askQuestion('Enter admin email: ');
    const username = await askQuestion('Enter admin username: ');
    const password = await askPassword('Enter admin password (hidden): ');
    const confirmPassword = await askPassword('Confirm admin password (hidden): ');
    
    // Validate input
    if (!email || !username || !password) {
      console.error('❌ All fields are required!');
      process.exit(1);
    }
    
    if (password !== confirmPassword) {
      console.error('❌ Passwords do not match!');
      process.exit(1);
    }
    
    if (password.length < 8) {
      console.error('❌ Password must be at least 8 characters long!');
      process.exit(1);
    }
    
    // Check if user already exists
    const existingUser = await prisma.users.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });
    
    if (existingUser) {
      console.error(`❌ User already exists with email: ${existingUser.email}`);
      process.exit(1);
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create admin user
    const adminUser = await prisma.users.create({
      data: {
        email,
        username,
        clerk_id: `admin_${Date.now()}`, // Unique Clerk ID for admin
        role: 'ADMIN',
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    });
    
    console.log(`✅ Admin user created successfully!`);
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`👤 Username: ${adminUser.username}`);
    console.log(`🆔 User ID: ${adminUser.id}`);
    console.log(`🔑 Role: ${adminUser.role}`);
    console.log(`\n🌐 You can now login at: http://localhost:3000/admin/login`);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        console.error('💡 This email or username is already taken.');
      } else {
        console.error(`💡 Details: ${error.message}`);
      }
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    rl.close();
    process.exit(0);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  rl.close();
  process.exit(0);
});

// Run the script
createAdminUser();