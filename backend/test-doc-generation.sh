#!/usr/bin/env bash
# Test documentation generation script independently

echo "🧪 Testing Documentation Generation Script..."

# Create a test file
TEST_FILE="test-doc-generation-sample.js"
cat > $TEST_FILE << 'EOF'
/**
 * Sample function for testing documentation generation
 * @param {string} name - The name to greet
 * @param {number} age - The person's age
 * @returns {string} A greeting message
 */
function greet<PERSON>erson(name, age) {
    return `Hello ${name}, you are ${age} years old!`;
}

/**
 * Calculate the area of a rectangle
 * @param {number} width - Rectangle width
 * @param {number} height - Rectangle height
 * @returns {number} The calculated area
 */
function calculateArea(width, height) {
    return width * height;
}

module.exports = { greetPerson, calculateArea };
EOF

echo "✅ Created test file: $TEST_FILE"

# Test the documentation generation script directly
echo "🔄 Running documentation generation on test file..."
cd backend/scripts

# Run the Python script directly on our test file
poetry run python3 generate_docs.py --file "../../$TEST_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Documentation generation completed successfully"
    
    # Check if documentation was generated
    DOC_FILE="../../docs/generated/${TEST_FILE}.doc.json"
    if [ -f "$DOC_FILE" ]; then
        echo "✅ Documentation file created: $DOC_FILE"
        echo "📄 Documentation preview:"
        head -20 "$DOC_FILE" | sed 's/^/   /'
    else
        echo "⚠️  Documentation file not found at expected location"
        echo "   Looking for files in docs/generated/..."
        find ../../docs -name "*.doc.json" -newer "../../$TEST_FILE" 2>/dev/null | head -5 | sed 's/^/   /'
    fi
else
    echo "❌ Documentation generation failed"
fi

cd - > /dev/null

# Cleanup
rm -f $TEST_FILE
echo "🧹 Cleaned up test file"
