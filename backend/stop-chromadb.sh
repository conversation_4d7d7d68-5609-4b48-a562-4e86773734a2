#!/bin/bash

# ChromaDB Stop Script for KAPI Backend
# This script stops the ChromaDB server gracefully

set -e

echo "🛑 Stopping ChromaDB Server..."

# Function to check if ChromaDB is running
check_chromadb() {
    curl -s -f "http://localhost:8000/api/v2/version" > /dev/null 2>&1
}

# Check if PID file exists
if [ -f chromadb.pid ]; then
    PID=$(cat chromadb.pid)
    echo "📝 Found ChromaDB PID: $PID"
    
    # Check if process is actually running
    if kill -0 $PID 2>/dev/null; then
        echo "🔄 Stopping ChromaDB process..."
        kill $PID
        
        # Wait for process to stop
        local count=0
        while [ $count -lt 10 ]; do
            if ! kill -0 $PID 2>/dev/null; then
                echo "✅ ChromaDB stopped successfully"
                break
            fi
            echo "   Waiting for process to stop... ($((count + 1))/10)"
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if kill -0 $PID 2>/dev/null; then
            echo "⚠️  Force killing ChromaDB process..."
            kill -9 $PID 2>/dev/null || true
        fi
    else
        echo "⚠️  Process $PID is not running"
    fi
    
    # Remove PID file
    rm -f chromadb.pid
    echo "🗑️  Removed PID file"
else
    echo "📝 No PID file found"
fi

# Try to find and kill any remaining ChromaDB processes
CHROMA_PIDS=$(pgrep -f "chroma run" 2>/dev/null || true)
if [ ! -z "$CHROMA_PIDS" ]; then
    echo "🔍 Found additional ChromaDB processes: $CHROMA_PIDS"
    echo "$CHROMA_PIDS" | xargs kill 2>/dev/null || true
    sleep 2
    
    # Force kill if still running
    REMAINING_PIDS=$(pgrep -f "chroma run" 2>/dev/null || true)
    if [ ! -z "$REMAINING_PIDS" ]; then
        echo "⚠️  Force killing remaining processes: $REMAINING_PIDS"
        echo "$REMAINING_PIDS" | xargs kill -9 2>/dev/null || true
    fi
fi

# Verify ChromaDB is stopped
if check_chromadb; then
    echo "⚠️  ChromaDB server is still responding"
    echo "💡 You may need to manually stop it or check for other instances"
else
    echo "✅ ChromaDB server stopped successfully"
fi

# Clean up log file if desired
if [ "$1" = "--clean" ]; then
    if [ -f chromadb.log ]; then
        rm -f chromadb.log
        echo "🗑️  Removed log file"
    fi
fi

echo "🎉 ChromaDB shutdown complete"
