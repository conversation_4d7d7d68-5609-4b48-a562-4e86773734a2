/**
 * This file is a comprehensive test for the automated documentation generation feature
 * for TypeScript. It includes a variety of common TypeScript constructs.
 */

// 1. Interfaces and Types
export interface UserProfile {
  id: string;
  username: string;
  email?: string;
}

export type UserStatus = 'active' | 'inactive' | 'pending';

// 2. A simple function with type annotations
export function formatUsername(user: UserProfile): string {
  return `${user.username} (ID: ${user.id})`;
}

// 3. A class with properties, methods, and access modifiers
export class DataManager<T> {
  private data: T[] = [];

  constructor(initialData: T[] = []) {
    this.data = initialData;
  }

  public addItem(item: T): void {
    this.data.push(item);
  }

  public getItem(index: number): T | undefined {
    return this.data[index];
  }

  get count(): number {
    return this.data.length;
  }
}

// 4. An asynchronous function using Promises
export async function fetchUserData(userId: string): Promise<UserProfile> {
  console.log(`Fetching data for user ${userId}...`);
  // Simulate an API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ id: userId, username: 'testuser' });
    }, 500);
  });
}

// 5. A function with optional parameters and default values
export function createGreeting(name: string, greeting: string = 'Hello'): string {
  return `${greeting}, ${name}!`;
}
