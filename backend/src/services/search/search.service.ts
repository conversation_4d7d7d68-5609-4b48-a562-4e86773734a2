/**
 * Search Service
 *
 * This module provides a service for searching across different content types in the application.
 */
import { BaseService } from '../base.service';
import { logger } from '../../common/logger';

interface SearchBlogPostsParams {
  query?: string;
  tagIds?: number[];
  tagSlugs?: string[];
  authorId?: number;
  publishedOnly?: boolean;
  fromDate?: Date;
  toDate?: Date;
  skip?: number;
  limit?: number;
}

interface SearchAllParams {
  query: string;
  contentTypes?: string[];
  skip?: number;
  limit?: number;
}

interface SearchBlogResult {
  results: any[];
  total: number;
  skip: number;
  limit: number;
  query?: string;
}

interface SearchAllResult {
  results: Record<string, any[]>;
  total: number;
  skip: number;
  limit: number;
  query: string;
  contentTypes: string[];
}

export class SearchService extends BaseService {
  /**
   * Search blog posts with various filters.
   *
   * @param params - Search parameters
   * @returns Dictionary with search results and metadata
   */
  async searchBlogPosts({
    query,
    tagIds,
    tagSlugs,
    authorId,
    publishedOnly = true,
    fromDate,
    toDate,
    skip = 0,
    limit = 10,
  }: SearchBlogPostsParams): Promise<SearchBlogResult> {
    try {
      // Base query conditions
      const where: any = {};

      // Apply text search if provided
      if (query) {
        where.title = {
          contains: query,
          mode: 'insensitive',
        };
      }

      // Filter by author
      if (authorId) {
        where.authorId = authorId;
      }

      // Filter by published status
      if (publishedOnly) {
        where.publishedAt = { not: null };
      }

      // Filter by date range
      if (fromDate) {
        where.createdAt = { ...(where.createdAt || {}), gte: fromDate };
      }

      if (toDate) {
        where.createdAt = { ...(where.createdAt || {}), lte: toDate };
      }

      // Handle tag filtering
      if (tagIds && tagIds.length > 0) {
        where.tags = {
          some: {
            id: { in: tagIds },
          },
        };
      }

      if (tagSlugs && tagSlugs.length > 0) {
        where.tags = {
          some: {
            slug: { in: tagSlugs },
          },
        };
      }

      // Get total count first
      const totalCount = await this.prisma.blog_posts.count({ where });

      // Then get blog posts with pagination
      const blogPosts = await this.prisma.blog_posts.findMany({
        where,
        orderBy: {
          created_at: 'desc',
        },
        include: {
          blog_post_tags: true,
          users: true,
        },
        skip,
        take: limit,
      });

      // Format the results
      const results = blogPosts.map((post: any) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        featuredImagePath: post.featured_image_path,
        authorId: post.user_id,
        authorName: post.users
          ? `${post.users.first_name} ${post.users.last_name}`.trim() || post.users.username
          : 'Unknown',
        publishedAt: post.published_at,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
        estimatedReadingTime: post.estimated_reading_time,
        tags: post.blog_post_tags.map((tag: any) => ({
          id: tag.tag_id,
          name: tag.tag?.name || '',
          slug: tag.tag?.slug || '',
        })),
      }));

      return {
        results,
        total: totalCount,
        skip,
        limit,
        query,
      };
    } catch (error) {
      logger.error(
        `Error searching blog posts: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get recent blog posts from the specified number of days.
   *
   * @param days - Number of days to look back
   * @param limit - Maximum number of posts to return
   * @returns List of recent blog posts
   */
  async getRecentBlogPosts(days: number = 7, limit: number = 5): Promise<any[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const posts = await this.prisma.blog_posts.findMany({
        where: {
          published_at: {
            not: null,
            gte: cutoffDate,
          },
        },
        orderBy: {
          published_at: 'desc',
        },
        include: {
          blog_post_tags: true,
          users: true,
        },
        take: limit,
      });

      return posts.map((post: any) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        featuredImagePath: post.featured_image_path,
        authorId: post.user_id,
        authorName: post.users
          ? `${post.users.first_name} ${post.users.last_name}`.trim() || post.users.username
          : 'Unknown',
        publishedAt: post.published_at,
        createdAt: post.created_at,
        estimatedReadingTime: post.estimated_reading_time,
        tags: post.blog_post_tags.map((tag: any) => ({
          id: tag.tag_id,
          name: tag.tag?.name || '',
          slug: tag.tag?.slug || '',
        })),
      }));
    } catch (error) {
      logger.error(
        `Error getting recent blog posts: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Search across all supported content types.
   *
   * @param params - Search parameters
   * @returns Dictionary with search results and metadata
   */
  async searchAll({
    query,
    contentTypes = ['blog'],
    skip = 0,
    limit = 10,
  }: SearchAllParams): Promise<SearchAllResult> {
    try {
      const results: Record<string, any[]> = {};
      let totalResults = 0;

      // Search blog posts if requested
      if (contentTypes.includes('blog')) {
        const blogResults = await this.searchBlogPosts({
          query,
          skip,
          limit,
        });

        results['blog'] = blogResults.results;
        totalResults += blogResults.total;
      }

      // In the future, add other content types here
      // For example:
      // if (contentTypes.includes('project')) {
      //   const projectResults = await this.searchProjects(...);
      //   results['project'] = projectResults.results;
      //   totalResults += projectResults.total;
      // }

      return {
        results,
        total: totalResults,
        skip,
        limit,
        query,
        contentTypes,
      };
    } catch (error) {
      logger.error(
        `Error searching all content: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
