# Documentation for `/home/<USER>/Desktop/kapi-main/kapi/backend/src/services/document-processing.service.ts`

**Commit:** `1b54a030ed1b088c138034b5554ddddd69e29d2b` | **Last Updated:** `2025-07-10T15:36:17+05:30`

---

## `DocumentProcessingService` (Class)

**Purpose:** This class processes various types of documentation and textual data from a project, formats them, and generates vector embeddings for storage and retrieval.

### Detailed Explanation

The `DocumentProcessingService` acts as a central hub for preparing diverse textual content for a vector database, likely for AI-powered search or knowledge retrieval. It handles four main types of data:

1.  **Automated Documentation (`.doc.json` files):** It scans a specified project directory for these structured JSON files, parses their content (e.g., purpose, unit, inputs), and formats them into a comprehensive text string.
2.  **README Files:** It identifies and processes README files within the project, using their raw content.
3.  **Interview Responses:** It takes structured interview data, including questions, answers, and code context, and formats them into a coherent document.
4.  **Code Comments:** It extracts both block and line comments from provided code files, filtering out very short ones.

For all these data types, the service prepares the content and then utilizes an injected `VectorService` to update or generate their numerical embeddings, which are essential for semantic search. It also includes a private utility (`chunkText`) to break down long texts into smaller, manageable chunks suitable for embedding models, ensuring context is maintained through overlapping segments. The actual embedding generation for these chunks is delegated to an `azureService`. The class is robust, incorporating error handling and logging throughout its operations.

### Visual Representation

```mermaid
```mermaid
classDiagram
    class DocumentProcessingService {
        -CHUNK_SIZE: number
        -CHUNK_OVERLAP: number
        -vectorService: VectorService
        +constructor(vectorService: VectorService)
        +processAutomatedDocs(projectPath: string, projectId: string): Promise<void>
        +processInterviewResponses(responses: InterviewResponse[], projectId: string): Promise<void>
        +processCodeComments(files: CodeFile[], projectId: string): Promise<void>
        +generateEmbeddings(texts: string[]): Promise<number[][]>
        -chunkText(text: string): string[]
        -formatAutomatedDoc(docData: any): string
        -formatInterviewResponse(response: InterviewResponse): string
        -extractComments(file: CodeFile): Array<Comment>
        -findDocumentationFiles(projectPath: string): Promise<string[]>
        -findReadmeFiles(projectPath: string): Promise<string[]>
    }

    class VectorService {
        +updateDocumentEmbeddings(projectId: string, documents: Document[]): Promise<void>
    }

    class AzureService {
        +generateEmbeddings(texts: string[]): Promise<{embeddings: number[][]}>
    }

    class InterviewResponse {
        +question: string
        +answer: string
        +timestamp: string
        +context: object
    }

    class CodeFile {
        +path: string
        +content: string
        +language: string
    }

    class Document {
        +filePath: string
        +content: string
        +type: 'auto-doc' | 'interview' | 'readme' | 'comment'
        +metadata?: Record<string, any>
    }

    class Comment {
        +content: string
        +type: 'block' | 'line' | 'docstring'
        +startLine: number
        +endLine: number
    }

    class BadRequestError {
        // ...
    }

    DocumentProcessingService --> VectorService : uses
    DocumentProcessingService --> AzureService : uses
    DocumentProcessingService ..> InterviewResponse : processes
    DocumentProcessingService ..> CodeFile : processes
    DocumentProcessingService ..> Document : creates
    DocumentProcessingService ..> Comment : extracts
    DocumentProcessingService ..> BadRequestError : throws
```
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `projectPath` | `string` | The root path of the project to scan for documentation files. |
| `projectId` | `string` | The ID of the project to associate the embeddings with. |
| `responses` | `InterviewResponse[]` | An array of structured interview responses to process. |
| `files` | `CodeFile[]` | An array of code file objects containing content and metadata for comment extraction. |
| `texts` | `string[]` | An array of text strings for which to generate embeddings. |

### Outputs

- **Returns:** `Promise<void> | Promise<number[][]>` - Public methods `processAutomatedDocs`, `processInterviewResponses`, and `processCodeComments` return a Promise that resolves to `void` upon successful completion. The `generateEmbeddings` method returns a Promise that resolves to a 2D array of numbers, representing the generated embeddings.
- **Throws:** `BadRequestError`

### Dependencies

- **VectorService** (internal)
- **TYPES** (internal)
- **logger** (internal)
- **BadRequestError** (internal)
- **InterviewResponse** (internal)
- **CodeFile** (internal)
- **fs** (external)
- **path** (external)
- **@injectable** (external)
- **@inject** (external)
- **azureService** (external)

---

