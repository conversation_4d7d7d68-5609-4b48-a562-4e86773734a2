# Unified Conversation Service

This module implements a unified conversation service that consolidates the functionality of the conversation service and conversation task service using a strategy pattern to handle different task types.

## Architecture

The architecture follows the Strategy Pattern, which allows the conversation service to handle different types of tasks (chat, code generation, SVG mockups, etc.) with specialized behavior while maintaining a consistent interface.

### Components

1. **TaskStrategy Interface**: Defines the contract for task-specific strategies.
2. **BaseTaskStrategy**: Provides default implementations for the TaskStrategy interface.
3. **Task-Specific Strategies**: Implement specialized behavior for different task types.
4. **TaskStrategyRegistry**: Manages the registration and retrieval of task strategies.
5. **UnifiedConversationService**: Uses the appropriate strategy based on the task type.

## Task Strategies

The following task strategies are currently implemented:

- **ChatTaskStrategy**: For general chat conversations.
- **CodeGenerationTaskStrategy**: For generating code based on requirements.
- **CodePlanningTaskStrategy**: For planning code architecture and implementation.
- **SlideGenerationTaskStrategy**: For generating presentation slides.
- **SvgMockupTaskStrategy**: For generating SVG mockups.
- **TestCasesTaskStrategy**: For generating test cases for code.

## Usage

### Basic Usage

```typescript
import { conversationService } from '../services';

// Add a user message and get a response using the appropriate task strategy
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'chat', // or 'code_generation', 'svg_mockup', 'test_cases'
    modelId: 'claude-3-5-opus-20240307', // optional, will use default for task type if not provided
    maxTokens: 1000, // optional, will use default for task type if not provided
    temperature: 0.7, // optional, will use default for task type if not provided
  },
);
```

### Creating a New Conversation with Initial Message

```typescript
const response = await conversationService.createConversationWithMessage(userId, initialMessage, {
  taskType: 'code_generation',
  modelId: 'claude-3-5-opus-20240307',
  maxTokens: 2000,
  temperature: 0.3,
});
```

### Task-Specific Options

Each task type can accept additional options specific to that task:

#### Code Generation

```typescript
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'code_generation',
    language: 'typescript',
    context: 'This is for a React component',
  },
);
```

#### SVG Mockup

```typescript
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'svg_mockup',
    style: 'modern',
    width: 800,
    height: 600,
    components: ['header', 'sidebar', 'content area'],
  },
);
```

#### Test Cases

```typescript
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'test_cases',
    code: '// Code to test',
    framework: 'Jest',
    coverageLevel: 'high',
  },
);
```

#### Code Planning

```typescript
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'code_planning',
    modelId: 'claude-3-5-opus-20240307',
  },
);
```

#### Slide Generation

```typescript
const response = await conversationService.addUserMessageAndGetResponse(
  conversationId,
  userMessage,
  {
    taskType: 'slides',
    topic: 'Artificial Intelligence',
    numSlides: 10,
    format: 'markdown',
  },
);
```

## Adding New Task Strategies

To add a new task strategy:

1. Create a new strategy class that extends `BaseTaskStrategy`.
2. Implement the required methods.
3. Register the strategy in the `TaskStrategyRegistry`.

Example:

```typescript
// Create a new strategy
export class SlideGenerationTaskStrategy extends BaseTaskStrategy {
  getTaskType(): string {
    return 'slide_generation';
  }

  getDefaultModel(): string {
    return 'claude-3-5-opus-20240307';
  }

  formatUserMessage(context: TaskContext): string {
    // Format the user message for slide generation
    // ...
  }

  getSystemPrompt(context: TaskContext): string | null {
    return 'You are an expert presentation designer...';
  }
}

// Register the strategy
taskStrategyRegistry.registerStrategy(new SlideGenerationTaskStrategy());
```

## Benefits

- **Reduced Code Duplication**: Common functionality is consolidated in the unified service.
- **Consistent Interface**: All task types use the same interface, making the API more predictable.
- **Extensibility**: New task types can be added without modifying the core service.
- **Maintainability**: Task-specific logic is isolated in separate strategy classes.
- **Testability**: Each strategy can be tested independently.
