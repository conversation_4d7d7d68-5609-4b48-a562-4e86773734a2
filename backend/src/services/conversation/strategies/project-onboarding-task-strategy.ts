/**
 * Project Onboarding Task Strategy
 *
 * Specialized strategy for project discovery conversations that focus on understanding
 * WHAT the user wants to build - their project goals, tech preferences, and requirements.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext, TaskOptions } from '../task-strategy.interface';
import { ChatResponse } from '../../../common/types/conversation.types';
import { logger } from '../../../common/logger';
import { promises as fs } from 'fs';
import path from 'path';
import yaml from 'js-yaml';

export class ProjectOnboardingTaskStrategy extends BaseTaskStrategy {
  getTaskType(): string {
    return 'project_onboarding';
  }

  getDefaultModel(): string {
    // Use slightly more capable model for technical project planning
    return 'claude-3.5-haiku';
  }

  getDefaultMaxTokens(): number {
    // Higher token limit for technical explanations and project details
    return 1200;
  }

  getDefaultTemperature(): number {
    // Moderate temperature for balance between creativity and precision
    return 0.6;
  }

  getSystemPrompt(context: TaskContext): string | null {
    return context.options.systemPrompt || null;
  }

  /**
   * Load the system prompt fresh from prompts.yaml file (no caching)
   */
  private async loadSystemPromptFromConfig(): Promise<string> {
    try {
      const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
      const configFile = await fs.readFile(configPath, 'utf8');
      const config = yaml.load(configFile) as any;
      const systemPrompt = config?.conversation_tasks?.project_onboarding?.system;
      
      if (systemPrompt) {
        logger.info('🎯 [PROJECT-ONBOARDING-STRATEGY] Loaded fresh project_onboarding system prompt from prompts.yaml');
        return systemPrompt;
      }
    } catch (error) {
      logger.warn('Error loading project_onboarding system prompt from prompts.yaml:', error);
    }
    
    logger.warn('🎯 [PROJECT-ONBOARDING-STRATEGY] No system prompt found for project_onboarding task, using default fallback');
    return `You are KAPI's intelligent project discovery assistant. Your name is Kapi, and you help users clarify what they want to build and how KAPI can help them achieve it.

YOUR MISSION:
Guide users through discovering their project goals and translating those into actionable plans using KAPI's capabilities.

CRITICAL INTERVIEW RULES:
- Keep responses to 1-2 sentences maximum
- Ask ONE specific question at a time
- Focus on project goals, not personal details
- Help them articulate vague ideas into concrete plans

PROJECT DISCOVERY PRIORITIES:
1. **Intent**: Learn/Build/Improve (their stated goal)
2. **Specific Project**: What exactly they want to create
3. **Technology Preferences**: Stack, frameworks, tools
4. **Scope & Timeline**: How big, how fast
5. **Experience Context**: Technical background for this project
6. **Success Criteria**: How they'll know they succeeded

ADAPTIVE QUESTIONING:
- For "Learn" mode: What specific skills or technologies?
- For "Build" mode: What type of application or system?
- For "Improve" mode: What exists now and what needs enhancement?

TONE: Excited about their ideas, technically curious, solution-focused.`;
  }

  formatUserMessage(context: TaskContext): string {
    const { userMessage, options } = context;
    
    if (!userMessage) {
      return '';
    }

    // For project onboarding, we focus on technical context and project requirements
    return userMessage;
  }

  processResponse(response: ChatResponse, context: TaskContext): ChatResponse {
    logger.debug(`Processing project onboarding response for conversation ${context.conversationId}`);
    return response;
  }

  protected async formatPrompt(context: TaskContext): Promise<string> {
    const systemPrompt = await this.loadSystemPromptFromConfig();
    const userMessage = this.formatUserMessage(context);

    // Build conversation history with focus on project evolution
    let conversationHistory = '';
    if (context.messages && context.messages.length > 0) {
      const recentMessages = context.messages.slice(-10); // Keep more messages for project context
      conversationHistory = recentMessages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n');
    }

    const fullPrompt = [
      systemPrompt,
      conversationHistory ? `\nConversation History:\n${conversationHistory}` : '',
      `\nUser: ${userMessage}`,
      '\nAssistant:'
    ].filter(Boolean).join('\n');

    return fullPrompt;
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const formattedMessage = this.formatUserMessage(context);

      const response = await aiService.generateText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      const message = {
        role: 'assistant' as const,
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      logger.error('Error in project onboarding task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const formattedMessage = this.formatUserMessage(context);

      const stream = await aiService.streamText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      logger.error('Error in project onboarding task strategy streamExecute:', error);
      throw error;
    }
  }
}