/**
 * User Onboarding Task Strategy
 *
 * Specialized strategy for user discovery conversations that focus on understanding
 * WHO the person is - their name, role, experience, and personal goals.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext, TaskOptions } from '../task-strategy.interface';
import { ChatResponse } from '../../../common/types/conversation.types';
import { logger } from '../../../common/logger';
import { promises as fs } from 'fs';
import path from 'path';
import yaml from 'js-yaml';

export class UserOnboardingTaskStrategy extends BaseTaskStrategy {
  getTaskType(): string {
    return 'user_onboarding';
  }

  getDefaultModel(): string {
    // Use GPT-4.1 for better conversational abilities and user understanding
    return 'gpt-4.1';
  }

  getDefaultMaxTokens(): number {
    // Lower token limit for concise personal questions
    return 800;
  }

  getDefaultTemperature(): number {
    // Higher temperature for more natural, conversational responses
    return 0.8;
  }

  getSystemPrompt(context: TaskContext): string | null {
    return context.options.systemPrompt || null;
  }

  /**
   * Load the system prompt fresh from prompts.yaml file (no caching)
   */
  private async loadSystemPromptFromConfig(): Promise<string> {
    try {
      const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
      const configFile = await fs.readFile(configPath, 'utf8');
      const config = yaml.load(configFile) as any;
      const systemPrompt = config?.conversation_tasks?.user_onboarding?.system;
      
      if (systemPrompt) {
        logger.info('🎯 [USER-ONBOARDING-STRATEGY] Loaded fresh user_onboarding system prompt from prompts.yaml');
        return systemPrompt;
      }
    } catch (error) {
      logger.warn('Error loading user_onboarding system prompt from prompts.yaml:', error);
    }
    
    logger.warn('🎯 [USER-ONBOARDING-STRATEGY] No system prompt found for user_onboarding task, using default fallback');
    return `You are KAPI's intelligent user discovery assistant. Your name is Kapi, and you're the first touchpoint for understanding WHO this person is and how KAPI can serve them.

YOUR MISSION:
Discover the person behind the screen through natural conversation. Learn their name, role, experience level, goals, and work style to build their personalized KAPI profile.

CRITICAL INTERVIEW RULES:
- Keep responses to 1-2 sentences maximum
- Ask ONE specific question at a time
- ALWAYS start by asking for their name if you don't know it
- Listen for implicit signals (confidence level, terminology used, enthusiasm)
- Adapt follow-up questions based on their responses

USER DISCOVERY PRIORITIES (in order):
1. **Name**: "What should I call you?" or "What's your name?"
2. **Role**: Developer, PM, Designer, Student, etc.
3. **Experience Level**: Beginner, intermediate, advanced
4. **Current Goals**: What they want to achieve
5. **Work Style**: How they prefer to learn/work
6. **Background**: Previous experience, interests

TONE: Warm, genuinely curious, personal, like meeting a new colleague.`;
  }

  formatUserMessage(context: TaskContext): string {
    const { userMessage, options } = context;
    
    if (!userMessage) {
      return '';
    }

    // For user onboarding, we maintain personal context about their identity
    return userMessage;
  }

  processResponse(response: ChatResponse, context: TaskContext): ChatResponse {
    logger.debug(`Processing user onboarding response for conversation ${context.conversationId}`);
    return response;
  }

  protected async formatPrompt(context: TaskContext): Promise<string> {
    const systemPrompt = await this.loadSystemPromptFromConfig();
    const userMessage = this.formatUserMessage(context);

    // Build conversation history for personal context
    let conversationHistory = '';
    if (context.messages && context.messages.length > 0) {
      const recentMessages = context.messages.slice(-8); // Keep fewer messages for focused personal discovery
      conversationHistory = recentMessages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n');
    }

    const fullPrompt = [
      systemPrompt,
      conversationHistory ? `\nConversation History:\n${conversationHistory}` : '',
      `\nUser: ${userMessage}`,
      '\nAssistant:'
    ].filter(Boolean).join('\n');

    return fullPrompt;
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      
      // Build conversation history if available
      let conversationHistory = '';
      if (context.messages && context.messages.length > 0) {
        const recentMessages = context.messages.slice(-8);
        conversationHistory = recentMessages
          .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
          .join('\n');
      }
      
      // Include conversation history in the prompt
      const userMessage = this.formatUserMessage(context);
      const fullPrompt = conversationHistory ? 
        `Conversation History:\n${conversationHistory}\n\nCurrent message: ${userMessage}` : 
        userMessage;

      const response = await aiService.generateText({
        prompt: fullPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      const message = {
        role: 'assistant' as const,
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      logger.error('Error in user onboarding task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      
      // Build conversation history if available
      let conversationHistory = '';
      if (context.messages && context.messages.length > 0) {
        const recentMessages = context.messages.slice(-8);
        conversationHistory = recentMessages
          .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
          .join('\n');
      }
      
      // Include conversation history in the prompt
      const userMessage = this.formatUserMessage(context);
      const fullPrompt = conversationHistory ? 
        `Conversation History:\n${conversationHistory}\n\nCurrent message: ${userMessage}` : 
        userMessage;

      const stream = await aiService.streamText({
        prompt: fullPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      logger.error('Error in user onboarding task strategy streamExecute:', error);
      throw error;
    }
  }
}