/**
 * Chat Task Strategy
 *
 * Strategy for general chat conversations.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';
import { ChatResponse, MessageResponse } from '../../../common/types/conversation.types';
import aiService from '../../ai';
import { logger } from '../../../common/logger';

export class ChatTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'chat';
  }

  /**
   * Get the system prompt for chat tasks
   */
  getSystemPrompt(context: TaskContext): string | null {
    return (
      context.options.systemPrompt ||
      "You are a helpful AI assistant. Provide clear, concise, and accurate responses to the user's questions."
    );
  }

  /**
   * Format the user message for chat tasks
   * If there are messages in the context, format them as a conversation history
   */
  formatUserMessage(context: TaskContext): string {
    // If there are no messages in the context, just return the user message
    if (!context.messages || context.messages.length === 0) {
      return context.userMessage || '';
    }

    // Format the conversation history
    let formattedMessage = 'Previous conversation history:\n\n';

    // Add previous messages
    context.messages.forEach((message) => {
      const role = message.role === 'user' ? 'User' : 'Assistant';
      formattedMessage += `${role}: ${message.content}\n\n`;
    });

    // Add the current user message
    formattedMessage += `Current user message: ${context.userMessage || ''}`;

    return formattedMessage;
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const startTime = Date.now();
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);

      // Get response from AI service with token usage
      const response = await aiService.generateText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      const message: MessageResponse = {
        role: 'assistant',
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      logger.error('Error in chat task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);

      // Stream response from AI service
      const stream = await aiService.streamText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        // Extract content from chunk based on its structure
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        // If this is the final chunk with usage info, capture it
        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        // Capture the model used if available
        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      // Send final chunk with usage info
      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      logger.error('Error in chat task strategy streamExecute:', error);
      throw error;
    }
  }
}
