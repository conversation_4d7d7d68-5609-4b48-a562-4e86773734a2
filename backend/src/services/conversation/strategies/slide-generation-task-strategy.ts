/**
 * Slide Generation Task Strategy
 *
 * Strategy for generating presentation slides using reveal.js template.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';
import path from 'path';
import fs from 'fs';

export class SlideGenerationTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'slides';
  }

  /**
   * Get the default model for slide generation
   */
  getDefaultModel(): string {
    return 'claude-3.7-sonnet'; // Use code_gen_big model
  }

  /**
   * Get the default max tokens for slide generation
   */
  getDefaultMaxTokens(): number {
    return 8192; // <PERSON>'s actual max output tokens (API limit)
  }

  /**
   * Get the default temperature for slide generation
   */
  getDefaultTemperature(): number {
    return 0.7;
  }

  /**
   * Format the user message for slide generation
   */
  formatUserMessage(context: TaskContext): string {
    const { userMessage } = context;
    const topic = context.options.topic || '';
    const numSlides = context.options.numSlides || 5;
    const format = context.options.format || 'markdown';

    let enhancedPrompt = `Generate ${numSlides} presentation slides in ${format} format on the topic: ${topic}`;

    if (userMessage) {
      enhancedPrompt += `\n\nAdditional requirements: ${userMessage}`;
    }

    return enhancedPrompt;
  }

  /**
   * Get the system prompt for slide generation with template
   */
  getSystemPrompt(context: TaskContext): string | null {
    try {
      // Load the reveal.js template
      const templatePath = path.join(process.cwd(), 'templates', 'slides', 'reveal-template.html');
      console.log('🔧 [SLIDE-STRATEGY] Loading template from:', templatePath);
      
      const template = fs.readFileSync(templatePath, 'utf8');
      console.log('🔧 [SLIDE-STRATEGY] Template loaded, length:', template.length);
      console.log('🔧 [SLIDE-STRATEGY] Template preview:', template.substring(0, 200) + '...');
      
      // Load system prompt from config
      const { config } = require('../../../../config/config');
      const systemPrompt = config.prompts?.conversation_tasks?.slides?.system || 
        'You are an expert reveal.js presentation designer.';
      
      console.log('🔧 [SLIDE-STRATEGY] System prompt loaded:', systemPrompt.substring(0, 100) + '...');
      
      // Combine system prompt with template
      const fullSystemPrompt = `${systemPrompt}

TEMPLATE TO USE (replace all {{PLACEHOLDER}} variables with appropriate content):

${template}`;

      console.log('🔧 [SLIDE-STRATEGY] Full system prompt length:', fullSystemPrompt.length);
      
      return fullSystemPrompt;
      
    } catch (error) {
      console.error('🔧 [SLIDE-STRATEGY] Error loading slide template:', error);
      return (
        context.options.systemPrompt ||
        'You are an expert web developer specializing in reveal.js presentations. Create professional business slide decks with vibrant colors, bold fonts, and complete reveal.js integration. Always include CDN links for reveal.js framework. Output only complete HTML code with ALL slides filled out.'
      );
    }
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<any> {
    try {
      const aiService = (await import('../../ai')).default;
      const { logger } = await import('../../../common/logger');
      
      const startTime = Date.now();
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      
      console.log('🔧 [SLIDE-STRATEGY] Slide generation strategy started');
      console.log('🔧 [SLIDE-STRATEGY] Context options:', JSON.stringify(context.options, null, 2));
      console.log('🔧 [SLIDE-STRATEGY] Model ID:', modelId);
      console.log('🔧 [SLIDE-STRATEGY] Max tokens:', maxTokens);
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);

      // Direct HTML generation approach (no template needed)

      // Simple, focused prompt since template is in system prompt
      const enhancedPrompt = `Create a professional slide presentation for: "${context.userMessage}"

Please generate content for a comprehensive business presentation covering all aspects from problem identification to solution implementation.`;

      // Get response from AI service
      console.log('🔧 [SLIDE-STRATEGY] Calling AI service with prompt length:', enhancedPrompt.length);
      console.log('🔧 [SLIDE-STRATEGY] System prompt length:', systemPrompt?.length || 0);
      console.log('🔧 [SLIDE-STRATEGY] System prompt preview:', systemPrompt?.substring(0, 300) + '...');
      
      const response = await aiService.generateText({
        prompt: enhancedPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      console.log('🔧 [SLIDE-STRATEGY] AI response received');
      console.log('🔧 [SLIDE-STRATEGY] Response length:', response.content.length, 'characters');
      console.log('🔧 [SLIDE-STRATEGY] Token usage:', {
        promptTokens: response.usage?.promptTokens || 'unknown',
        completionTokens: response.usage?.completionTokens || 'unknown',
        totalTokens: response.usage?.totalTokens || 'unknown'
      });
      console.log('🔧 [SLIDE-STRATEGY] Model used:', response.model);
      console.log('🔧 [SLIDE-STRATEGY] First 100 chars:', response.content.substring(0, 100));
      console.log('🔧 [SLIDE-STRATEGY] Last 100 chars:', response.content.substring(response.content.length - 100));

      // Post-process and clean up the HTML response
      const finalHtml = cleanupHtmlResponse(response.content);
      console.log('🔧 [SLIDE-STRATEGY] HTML cleaned up, final length:', finalHtml.length);
      console.log('🔧 [SLIDE-STRATEGY] Final HTML starts with:', finalHtml.substring(0, 50));

      const message: any = {
        role: 'assistant',
        content: finalHtml,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      const { logger } = await import('../../../common/logger');
      logger.error('Error in slide generation task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);
      
      console.log('🔧 [SLIDE-STRATEGY-STREAM] Slide generation streaming started');
      console.log('🔧 [SLIDE-STRATEGY-STREAM] Model ID:', modelId);
      console.log('🔧 [SLIDE-STRATEGY-STREAM] Max tokens:', maxTokens);
      console.log('🔧 [SLIDE-STRATEGY-STREAM] User message:', context.userMessage);
      console.log('🔧 [SLIDE-STRATEGY-STREAM] System prompt length:', systemPrompt?.length || 0);
      console.log('🔧 [SLIDE-STRATEGY-STREAM] System prompt preview:', systemPrompt?.substring(0, 300) + '...');

      // Use the same simple prompt for streaming (template is in system prompt)
      const enhancedPrompt = `Create a professional slide presentation for: "${context.userMessage}"

Please generate content for a comprehensive business presentation covering all aspects from problem identification to solution implementation.`;

      console.log('🔧 [SLIDE-STRATEGY-STREAM] Calling AI service with prompt length:', enhancedPrompt.length);

      // Stream response from AI service
      const stream = await aiService.streamText({
        prompt: enhancedPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      // Apply cleanup to the final content
      const cleanedContent = cleanupHtmlResponse(fullContent);
      
      yield {
        content: cleanedContent,
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      const { logger } = await import('../../../common/logger');
      logger.error('Error in slide generation task strategy streamExecute:', error);
      throw error;
    }
  }
}

// HTML cleanup function to remove markdown formatting and extract pure HTML
function cleanupHtmlResponse(content: string): string {
  console.log('🔧 [CLEANUP] Starting HTML cleanup...');
  
  // Remove markdown code blocks and any surrounding text
  let cleaned = content;
  
  // Remove ```html and ``` markers
  cleaned = cleaned.replace(/```html\s*/gi, '');
  cleaned = cleaned.replace(/```\s*$/gm, '');
  cleaned = cleaned.replace(/```/g, '');
  
  // Find the start of HTML content
  const htmlStartPattern = /<!DOCTYPE\s+html|<html[^>]*>/i;
  const htmlStartMatch = cleaned.search(htmlStartPattern);
  
  if (htmlStartMatch !== -1) {
    cleaned = cleaned.substring(htmlStartMatch);
    console.log('🔧 [CLEANUP] Found HTML start at position:', htmlStartMatch);
  }
  
  // Find the end of HTML content
  const htmlEndPattern = /<\/html>\s*$/i;
  const htmlEndMatch = cleaned.search(htmlEndPattern);
  
  if (htmlEndMatch !== -1) {
    const endPosition = htmlEndMatch + cleaned.match(htmlEndPattern)![0].length;
    cleaned = cleaned.substring(0, endPosition);
    console.log('🔧 [CLEANUP] Found HTML end at position:', htmlEndMatch);
  }
  
  // Remove any remaining markdown artifacts
  cleaned = cleaned.replace(/^\s*[\`\*\#\-\+]\s*/gm, '');
  
  // Clean up whitespace
  cleaned = cleaned.trim();
  
  // Ensure it starts with DOCTYPE if it doesn't already
  if (!cleaned.toLowerCase().startsWith('<!doctype')) {
    if (cleaned.toLowerCase().startsWith('<html')) {
      cleaned = '<!DOCTYPE html>\n' + cleaned;
    }
  }
  
  console.log('🔧 [CLEANUP] HTML cleanup completed, length:', cleaned.length);
  console.log('🔧 [CLEANUP] Starts with:', cleaned.substring(0, 50));
  console.log('🔧 [CLEANUP] Ends with:', cleaned.substring(cleaned.length - 50));
  
  return cleaned;
}
