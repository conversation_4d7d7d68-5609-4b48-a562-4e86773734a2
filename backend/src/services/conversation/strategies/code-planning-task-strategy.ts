/**
 * Code Planning Task Strategy
 *
 * Strategy for code architecture and planning tasks.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';

export class CodePlanningTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'code_planning';
  }

  /**
   * Get the default model for code planning
   */
  getDefaultModel(): string {
    return 'claude-3-5-opus-20240307';
  }

  /**
   * Get the default max tokens for code planning
   */
  getDefaultMaxTokens(): number {
    return 2000;
  }

  /**
   * Get the default temperature for code planning
   */
  getDefaultTemperature(): number {
    return 0.7;
  }

  /**
   * Format the user message for code planning
   */
  formatUserMessage(context: TaskContext): string {
    return context.userMessage || '';
  }

  /**
   * Get the system prompt for code planning
   */
  getSystemPrompt(context: TaskContext): string | null {
    return (
      context.options.systemPrompt ||
      'You are an expert software architect and planner. Your task is to help plan code implementation, design systems, and provide detailed technical guidance.'
    );
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<any> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }
}
