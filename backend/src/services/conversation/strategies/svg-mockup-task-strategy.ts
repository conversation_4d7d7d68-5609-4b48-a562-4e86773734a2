/**
 * SVG Mockup Task Strategy
 *
 * Strategy for generating SVG mockups.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';

export class SvgMockupTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'svg_mockup';
  }

  /**
   * Get the default model for SVG mockups
   */
  getDefaultModel(): string {
    return 'pro'; // Using Nova's specialized model
  }

  /**
   * Get the default max tokens for SVG mockups
   */
  getDefaultMaxTokens(): number {
    return 4000; // SVG can be token-heavy
  }

  /**
   * Format the user message for SVG mockup generation
   */
  formatUserMessage(context: TaskContext): string {
    const { userMessage } = context;
    const style = context.options.style || 'modern';
    const components = context.options.components || [];
    const width = context.options.width || 800;
    const height = context.options.height || 600;
    const description = context.options.description || '';

    let enhancedPrompt = `Create an SVG mockup with the following specifications:\n\n`;
    enhancedPrompt += `Main request: ${userMessage || 'Create a UI mockup'}\n`;

    if (description) {
      enhancedPrompt += `Description: ${description}\n`;
    }

    enhancedPrompt += `Style: ${style}\n`;
    enhancedPrompt += `Dimensions: ${width}x${height}px\n`;

    if (components.length > 0) {
      enhancedPrompt += `\nInclude these components:\n`;
      components.forEach((component: string) => {
        enhancedPrompt += `- ${component}\n`;
      });
    }

    enhancedPrompt += `\nRespond with valid, well-formatted SVG code that can be directly used in a web application.`;

    return enhancedPrompt;
  }

  /**
   * Get the system prompt for SVG mockup generation
   */
  getSystemPrompt(context: TaskContext): string | null {
    return (
      context.options.systemPrompt ||
      "You are an expert UI/UX designer specializing in creating SVG mockups. Create clean, valid SVG code based on the user's requirements."
    );
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<any> {
    try {
      const aiService = (await import('../../ai')).default;
      const { logger } = await import('../../../common/logger');
      
      const startTime = Date.now();
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);

      // Enhanced prompt for SVG mockup generation
      const enhancedPrompt = `${formattedMessage}

Create a lo-fi wireframe SVG mockup. Requirements:
1. Use simple geometric shapes (rectangles, circles, lines)
2. Grayscale color palette (#f5f5f5, #e0e0e0, #bdbdbd, #757575)
3. Clear visual hierarchy with proper spacing
4. Include placeholder text areas and content blocks
5. Show navigation elements, buttons, and key interactive areas
6. Make it look like a professional wireframe
7. Ensure all SVG elements are properly structured and valid
8. Use appropriate fonts (Arial, system fonts)

Return ONLY the SVG code with proper XML declaration, no markdown formatting or explanations.`;

      // Get response from AI service
      const response = await aiService.generateText({
        prompt: enhancedPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      const message: any = {
        role: 'assistant',
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      const { logger } = await import('../../../common/logger');
      logger.error('Error in SVG mockup task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = this.getSystemPrompt(context) || undefined;
      const formattedMessage = this.formatUserMessage(context);

      // Enhanced prompt for streaming
      const enhancedPrompt = `${formattedMessage}

Create a lo-fi wireframe SVG mockup. Return ONLY the SVG code with proper structure.`;

      // Stream response from AI service
      const stream = await aiService.streamText({
        prompt: enhancedPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      const { logger } = await import('../../../common/logger');
      logger.error('Error in SVG mockup task strategy streamExecute:', error);
      throw error;
    }
  }
}
