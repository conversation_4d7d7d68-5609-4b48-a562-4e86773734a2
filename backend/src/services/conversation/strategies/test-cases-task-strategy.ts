/**
 * Test Cases Task Strategy
 *
 * Strategy for generating test cases.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';

export class TestCasesTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'test_cases';
  }

  /**
   * Get the default model for test case generation
   */
  getDefaultModel(): string {
    return 'claude-3-5-opus-20240307';
  }

  /**
   * Get the default max tokens for test case generation
   */
  getDefaultMaxTokens(): number {
    return 3000;
  }

  /**
   * Get the default temperature for test case generation
   */
  getDefaultTemperature(): number {
    return 0.3; // Lower temperature for more deterministic test generation
  }

  /**
   * Format the user message for test case generation
   */
  formatUserMessage(context: TaskContext): string {
    const { userMessage } = context;
    const code = context.options.code || '';
    const framework = context.options.framework || 'Jest';
    const coverageLevel = context.options.coverageLevel || 'high';

    let enhancedPrompt = `Generate ${framework} test cases for the following code with ${coverageLevel} coverage level:\n\n`;

    if (code) {
      enhancedPrompt += `\`\`\`\n${code}\n\`\`\`\n\n`;
    }

    enhancedPrompt += `Additional requirements: ${userMessage || 'Create comprehensive tests'}\n\n`;
    enhancedPrompt += `Please include:\n`;
    enhancedPrompt += `- Comprehensive test cases covering different scenarios\n`;
    enhancedPrompt += `- Edge cases and error handling tests\n`;
    enhancedPrompt += `- Clear test descriptions\n`;
    enhancedPrompt += `- Proper setup and teardown if needed\n`;

    return enhancedPrompt;
  }

  /**
   * Get the system prompt for test case generation
   */
  getSystemPrompt(context: TaskContext): string | null {
    const framework = context.options.framework || 'Jest';
    const coverageLevel = context.options.coverageLevel || 'high';

    return (
      context.options.systemPrompt ||
      `You are an expert software tester specializing in ${framework}. Your task is to create comprehensive test cases with ${coverageLevel} coverage.`
    );
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<any> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }
}
