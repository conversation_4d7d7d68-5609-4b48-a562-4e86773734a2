/**
 * Code Generation Task Strategy
 *
 * Strategy for code generation tasks.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext } from '../task-strategy.interface';

export class CodeGenerationTaskStrategy extends BaseTaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string {
    return 'code_generation';
  }

  /**
   * Get the default model for code generation
   */
  getDefaultModel(): string {
    return 'claude-3-5-opus-20240307';
  }

  /**
   * Get the default max tokens for code generation
   */
  getDefaultMaxTokens(): number {
    return 2000;
  }

  /**
   * Get the default temperature for code generation
   */
  getDefaultTemperature(): number {
    return 0.3; // Lower temperature for more deterministic code generation
  }

  /**
   * Format the user message for code generation
   */
  formatUserMessage(context: TaskContext): string {
    const { userMessage } = context;
    const language = context.options.language || '';
    const codeContext = context.options.context || '';

    let enhancedPrompt = `Generate ${language} code for: ${userMessage || ''}`;
    if (codeContext) {
      enhancedPrompt += `\n\nAdditional context: ${codeContext}`;
    }

    return enhancedPrompt;
  }

  /**
   * Get the system prompt for code generation
   */
  getSystemPrompt(context: TaskContext): string | null {
    return (
      context.options.systemPrompt ||
      'You are an expert software developer. Your task is to generate high-quality, well-documented code based on requirements.'
    );
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<any> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    // Implementation would go here
    throw new Error('Method not implemented.');
  }
}
