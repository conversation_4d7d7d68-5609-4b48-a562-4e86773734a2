/**
 * Onboarding Task Strategy
 *
 * Specialized strategy for user onboarding conversations that guides new users
 * through discovery and setup in a personalized, conversational way.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext, TaskOptions } from '../task-strategy.interface';
import { ChatResponse } from '../../../common/types/conversation.types';
import { logger } from '../../../common/logger';
import { promises as fs } from 'fs';
import path from 'path';
import yaml from 'js-yaml';

export class OnboardingTaskStrategy extends BaseTaskStrategy {
  getTaskType(): string {
    return 'onboarding';
  }

  getDefaultModel(): string {
    // Use GPT-4.1 as the default model for onboarding conversations
    return 'gpt-4.1';
  }

  getDefaultMaxTokens(): number {
    // Moderate token limit for concise responses
    return 1000;
  }

  getDefaultTemperature(): number {
    // Slightly higher temperature for more conversational responses
    return 0.7;
  }

  getSystemPrompt(context: TaskContext): string | null {
    // Return null to use the async method instead
    return context.options.systemPrompt || null;
  }

  /**
   * Load the system prompt fresh from prompts.yaml file (no caching)
   */
  private async loadSystemPromptFromConfig(): Promise<string> {
    try {
      const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
      const configFile = await fs.readFile(configPath, 'utf8');
      const config = yaml.load(configFile) as any;
      const systemPrompt = config?.conversation_tasks?.onboarding?.system;
      
      if (systemPrompt) {
        logger.info('🎯 [ONBOARDING-STRATEGY] Loaded fresh onboarding system prompt from prompts.yaml');
        logger.info('🎯 [ONBOARDING-STRATEGY] System prompt preview:', systemPrompt.substring(0, 200) + '...');
        return systemPrompt;
      }
    } catch (error) {
      logger.warn('Error loading system prompt from prompts.yaml:', error);
    }
    
    logger.warn('🎯 [ONBOARDING-STRATEGY] No system prompt found for onboarding task, using default fallback');
    return `You are a personalized onboarding assistant for KAPI IDE.
Your task is to guide new users through discovery and setup in a conversational, encouraging way.

APPROACH:
- Be warm, supportive, and adapt to their experience level
- Ask thoughtful follow-up questions to build a comprehensive user profile
- Focus on understanding their goals, experience, and preferred learning style
- Keep responses concise but helpful (2-3 sentences max)
- Use their responses to personalize the experience

GOAL:
Build a profile including: motivation, experience level, languages, interests, and learning preferences.
Guide them through stages: Welcome → Experience → Goals → Preferences.

TONE: Conversational, encouraging, and personalized.`;
  }

  formatUserMessage(context: TaskContext): string {
    const { userMessage, options } = context;
    
    if (!userMessage) {
      return '';
    }

    // For onboarding, we want to maintain context about the user's journey
    // The message might include profile context from the frontend
    return userMessage;
  }

  processResponse(response: ChatResponse, context: TaskContext): ChatResponse {
    // Process the response for onboarding-specific needs
    logger.debug(`Processing onboarding response for conversation ${context.conversationId}`);

    // Keep the response as-is for onboarding
    // The frontend will handle profile analysis and context building
    return response;
  }

  protected async formatPrompt(context: TaskContext): Promise<string> {
    const systemPrompt = await this.loadSystemPromptFromConfig();
    const userMessage = this.formatUserMessage(context);

    // Build conversation history if available
    let conversationHistory = '';
    if (context.messages && context.messages.length > 0) {
      // Include recent conversation context (last 10 messages)
      const recentMessages = context.messages.slice(-10);
      conversationHistory = recentMessages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n');
    }

    // Combine system prompt, conversation history, and current message
    const fullPrompt = [
      systemPrompt,
      conversationHistory ? `\nConversation History:\n${conversationHistory}` : '',
      `\nUser: ${userMessage}`,
      '\nAssistant:'
    ].filter(Boolean).join('\n');

    return fullPrompt;
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const aiService = (await import('../../ai')).default;
      const startTime = Date.now();
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const formattedMessage = this.formatUserMessage(context);

      // Get response from AI service
      const response = await aiService.generateText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      const message = {
        role: 'assistant' as const,
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      logger.error('Error in onboarding task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const formattedMessage = this.formatUserMessage(context);

      // Stream response from AI service
      const stream = await aiService.streamText({
        prompt: formattedMessage,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt,
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      logger.error('Error in onboarding task strategy streamExecute:', error);
      throw error;
    }
  }
}