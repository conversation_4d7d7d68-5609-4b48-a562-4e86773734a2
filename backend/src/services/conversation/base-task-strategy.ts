/**
 * Base Task Strategy
 *
 * This class provides a base implementation of the TaskStrategy interface
 * with sensible defaults that can be overridden by specific task strategies.
 */

import { ChatResponse } from '../../common/types/conversation.types';
import { TaskStrategy, TaskContext, TaskOptions } from './task-strategy.interface';

export abstract class BaseTaskStrategy implements TaskStrategy {
  /**
   * Get the task type identifier
   */
  abstract getTaskType(): string;

  /**
   * Get the default model for this task type
   */
  getDefaultModel(): string {
    return 'gpt-4.1'; // Default model for all strategies
  }

  /**
   * Get the default max tokens for this task type
   */
  getDefaultMaxTokens(): number {
    return 1000;
  }

  /**
   * Get the default temperature for this task type
   */
  getDefaultTemperature(): number {
    return 0.7;
  }

  /**
   * Format the user message for this task type
   * By default, just returns the original message
   */
  formatUserMessage(context: TaskContext): string {
    return context.userMessage || '';
  }

  /**
   * Get the system prompt for this task type
   * By default, returns null (no system prompt)
   */
  getSystemPrompt(context: TaskContext): string | null {
    return context.options.systemPrompt || null;
  }

  /**
   * Process the response from the AI service
   * By default, just returns the original response
   */
  processResponse(response: ChatResponse, context: TaskContext): ChatResponse {
    return response;
  }

  /**
   * Execute the task strategy and get a response
   * This is an abstract method that must be implemented by subclasses
   */
  abstract execute(context: TaskContext): Promise<ChatResponse>;

  /**
   * Stream the response from the task strategy
   * This is an abstract method that must be implemented by subclasses
   */
  abstract streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown>;
}
