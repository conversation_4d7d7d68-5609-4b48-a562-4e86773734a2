/**
 * Task Strategy Interface
 *
 * This interface defines the contract for task-specific strategies used by the conversation service.
 * Each strategy handles the specific prompt formatting and model selection for a particular task type.
 */

import { ChatResponse } from '../../common/types/conversation.types';
import { messages } from '../../generated/prisma';

export interface TaskOptions {
  modelId?: string;
  maxTokens?: number;
  temperature?: number;
  userId?: number;
  systemPrompt?: string;
  taskType?: string;
  // Multimodal-specific options
  multimodalOptions?: {
    images?: Array<{
      url: string;
      description?: string;
    }>;
    instructions?: string;
    outputFormat?: 'description' | 'code' | 'both';
    designStyle?: string;
    targetPlatform?: 'web' | 'mobile' | 'desktop';
    systemPrompt?: string;
  };
  [key: string]: any; // Additional task-specific options
}

export interface TaskContext {
  conversationId: number;
  userMessage?: string;
  messages?: messages[];
  options: TaskOptions;
}

export interface TaskStrategy {
  /**
   * Get the task type identifier
   */
  getTaskType(): string;

  /**
   * Get the default model for this task type
   */
  getDefaultModel(): string;

  /**
   * Get the default max tokens for this task type
   */
  getDefaultMaxTokens(): number;

  /**
   * Get the default temperature for this task type
   */
  getDefaultTemperature(): number;

  /**
   * Format the user message for this task type
   */
  formatUserMessage(context: TaskContext): string;

  /**
   * Get the system prompt for this task type
   */
  getSystemPrompt(context: TaskContext): string | null;

  /**
   * Process the response from the AI service
   */
  processResponse(response: ChatResponse, context: TaskContext): ChatResponse;

  /**
   * Execute the task strategy and get a response
   */
  execute(context: TaskContext): Promise<ChatResponse>;

  /**
   * Stream the response from the task strategy
   */
  streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown>;
}
