/**
 * Conversation Service Module
 *
 * This module exports all components of the unified conversation service.
 */

// Export interfaces
export * from './task-strategy.interface';

// Export base classes
export * from './base-task-strategy';

// Export strategies
export * from './strategies/chat-task-strategy';
export * from './strategies/code-generation-task-strategy';
export * from './strategies/code-planning-task-strategy';
export * from './strategies/slide-generation-task-strategy';
export * from './strategies/svg-mockup-task-strategy';
export * from './strategies/test-cases-task-strategy';

// Export registry
export * from './task-strategy-registry';

// Export the unified conversation service
export * from './unified-conversation.service';
