/**
 * Project service for handling project-related operations.
 *
 * This module provides services for project management, focusing on the core
 * functionality while keeping the implementation simple at this early stage.
 */
import { logger } from '../common/logger';
import {
  ProjectCreate,
  ProjectUpdate,
  ObjectiveCreate,
  TechStackCreate,
  SlideCreate,
  TestCreate,
} from '../common/types/project.types';
import { projectRepository } from '../db/repositories/project.repository';
import {
  projects as Project,
  ProjectMotivationType,
  project_objectives as Objective,
  project_tech_stacks as TechStack,
  project_git_repos as GitRepo,
  project_slides as Slide,
  project_tests as Test,
  project_quality_metrics as QualityMetric,
} from '../generated/prisma';

export class ProjectService {
  /**
   * Get a project by its ID.
   *
   * @param projectId - The project's ID.
   * @returns The project if found, null otherwise.
   */
  async getById(projectId: number): Promise<Project | null> {
    return projectRepository.findById(projectId);
  }

  /**
   * Get all projects for a user.
   *
   * @param userId - The user's ID.
   * @param activeOnly - Only include active projects if true.
   * @param sortByUpdated - Sort by last updated if true.
   * @returns List of matching projects.
   */
  async getUserProjects(
    userId: number,
    activeOnly: boolean = true,
    sortByUpdated: boolean = true,
  ): Promise<Project[]> {
    const query: Record<string, any> = { userId };

    if (activeOnly) {
      query.is_active = true;
    }

    const orderBy = sortByUpdated ? { updated_at: 'desc' as const } : undefined;

    return projectRepository.findMany({
      where: query,
      orderBy,
    });
  }

  /**
   * Create a new project.
   *
   * @param projectData - Project data.
   * @returns The created project if successful, null otherwise.
   */
  async createProject(projectData: ProjectCreate & { userId: number }): Promise<Project | null> {
    try {
      // Set defaults if not provided
      const now = new Date();

      return await projectRepository.create({
        ...projectData,
        created_at: now,
        updated_at: now,
      });
    } catch (error) {
      logger.error(`Error creating project: ${error}`);
      return null;
    }
  }

  /**
   * Update a project.
   *
   * @param projectId - The project's ID.
   * @param projectData - Updated project data.
   * @returns The updated project if successful, null otherwise.
   */
  async updateProject(projectId: number, projectData: ProjectUpdate): Promise<Project | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Always update the updated_at timestamp
      return await projectRepository.update(projectId, {
        ...projectData,
        updated_at: new Date(),
      });
    } catch (error) {
      logger.error(`Error updating project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Delete a project.
   *
   * @param projectId - The project's ID.
   * @returns True if successful, false otherwise.
   */
  async deleteProject(projectId: number): Promise<boolean> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return false;
      }

      await projectRepository.delete(projectId);
      return true;
    } catch (error) {
      logger.error(`Error deleting project ${projectId}: ${error}`);
      return false;
    }
  }

  /**
   * Archive a project (mark as inactive).
   *
   * @param projectId - The project's ID.
   * @returns The archived project if successful, null otherwise.
   */
  async archiveProject(projectId: number): Promise<Project | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      return await projectRepository.update(projectId, {
        is_active: false,
        updated_at: new Date(),
      });
    } catch (error) {
      logger.error(`Error archiving project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Unarchive a project (mark as active).
   *
   * @param projectId - The project's ID.
   * @returns The unarchived project if successful, null otherwise.
   */
  async unarchiveProject(projectId: number): Promise<Project | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      return await projectRepository.update(projectId, {
        is_active: true,
        updated_at: new Date(),
      });
    } catch (error) {
      logger.error(`Error unarchiving project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Set the motivation type for a project.
   *
   * This affects which features are enabled for the project.
   *
   * @param projectId - The project's ID.
   * @param motivation - The motivation type.
   * @returns The updated project if successful, null otherwise.
   */
  async setProjectMotivation(
    projectId: number,
    motivation: ProjectMotivationType,
  ): Promise<Project | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      return await projectRepository.update(projectId, {
        project_motivation: motivation,
        updated_at: new Date(),
      });
    } catch (error) {
      logger.error(`Error setting project motivation ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Check if a feature is enabled for a project.
   *
   * @param projectId - The project's ID.
   * @param featureName - The name of the feature to check.
   * @returns True if the feature is enabled, false otherwise.
   */
  async checkFeatureEnabled(projectId: number, featureName: string): Promise<boolean> {
    try {
      return projectRepository.isFeatureEnabled(projectId, featureName);
    } catch (error) {
      logger.error(`Error checking feature ${featureName} for project ${projectId}: ${error}`);
      return false;
    }
  }

  // Project components - Objectives

  /**
   * Add an objective to a project.
   *
   * @param projectId - The project's ID.
   * @param objectiveData - Objective data.
   * @returns The created objective if successful, null otherwise.
   */
  async addObjective(projectId: number, objectiveData: ObjectiveCreate): Promise<Objective | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Create the objective
      const objective = await projectRepository.getComponentModel('project_objectives').create({
        data: {
          ...objectiveData,
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return objective;
    } catch (error) {
      logger.error(`Error adding objective to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all objectives for a project.
   *
   * @param projectId - The project's ID.
   * @returns List of objectives.
   */
  async getProjectObjectives(projectId: number): Promise<Objective[]> {
    return projectRepository.getComponentModel('project_objectives').findMany({
      where: { project_id: projectId },
    });
  }

  // Project components - Tech Stack

  /**
   * Add a tech stack item to a project.
   *
   * @param projectId - The project's ID.
   * @param techStackData - Tech stack data.
   * @returns The created tech stack item if successful, null otherwise.
   */
  async addTechStack(projectId: number, techStackData: TechStackCreate): Promise<TechStack | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Create the tech stack
      const techStack = await projectRepository.getComponentModel('project_tech_stacks').create({
        data: {
          ...techStackData,
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return techStack;
    } catch (error) {
      logger.error(`Error adding tech stack to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all tech stack items for a project.
   *
   * @param projectId - The project's ID.
   * @returns List of tech stack items.
   */
  async getProjectTechStack(projectId: number): Promise<TechStack[]> {
    return projectRepository.getComponentModel('project_tech_stacks').findMany({
      where: { project_id: projectId },
    });
  }

  // Project components - Git Repositories

  /**
   * Add a git repository to a project.
   *
   * @param projectId - The project's ID.
   * @param gitRepoData - Git repository data.
   * @returns The created git repository if successful, null otherwise.
   */
  async addGitRepo(
    projectId: number,
    gitRepoData: { url: string; branch?: string; accessToken?: string },
  ): Promise<GitRepo | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Create the git repo
      const gitRepo = await projectRepository.getComponentModel('project_git_repos').create({
        data: {
          ...gitRepoData,
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return gitRepo;
    } catch (error) {
      logger.error(`Error adding git repo to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all git repositories for a project.
   *
   * @param projectId - The project's ID.
   * @returns List of git repositories.
   */
  async getProjectGitRepos(projectId: number): Promise<GitRepo[]> {
    return projectRepository.getComponentModel('project_git_repos').findMany({
      where: { project_id: projectId },
    });
  }

  // Project components - Slides

  /**
   * Add a slide to a project.
   *
   * @param projectId - The project's ID.
   * @param slideData - Slide data.
   * @returns The created slide if successful, null otherwise.
   */
  async addSlide(projectId: number, slideData: SlideCreate): Promise<Slide | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Create the slide
      const slide = await projectRepository.getComponentModel('project_slides').create({
        data: {
          ...slideData,
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return slide;
    } catch (error) {
      logger.error(`Error adding slide to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all slides for a project.
   *
   * @param projectId - The project's ID.
   * @returns List of slides, ordered by their order field.
   */
  async getProjectSlides(projectId: number): Promise<Slide[]> {
    return projectRepository.getComponentModel('project_slides').findMany({
      where: { project_id: projectId },
      orderBy: { order: 'asc' },
    });
  }

  // Project components - Tests

  /**
   * Add a test to a project.
   *
   * @param projectId - The project's ID.
   * @param testData - Test data.
   * @returns The created test if successful, null otherwise.
   */
  async addTest(projectId: number, testData: TestCreate): Promise<Test | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Create the test
      const test = await projectRepository.getComponentModel('project_tests').create({
        data: {
          ...testData,
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return test;
    } catch (error) {
      logger.error(`Error adding test to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all tests for a project, optionally filtered by type.
   *
   * @param projectId - The project's ID.
   * @param testType - Filter by test type.
   * @returns List of tests.
   */
  async getProjectTests(projectId: number, testType?: string): Promise<Test[]> {
    const where: Record<string, any> = { project_id: projectId };

    if (testType) {
      where.type = testType;
    }

    return projectRepository.getComponentModel('project_tests').findMany({ where });
  }

  // Project components - Quality Metrics

  /**
   * Add a quality metric to a project.
   *
   * @param projectId - The project's ID.
   * @param metricData - Quality metric data.
   * @returns The created quality metric if successful, null otherwise.
   */
  async addQualityMetric(
    projectId: number,
    metricData: {
      name: string;
      value?: number;
      target?: number;
      description?: string;
      unit?: string;
      category?: string;
    },
  ): Promise<QualityMetric | null> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return null;
      }

      // Ensure required fields are present
      if (!metricData.name) {
        logger.warn('Quality metric name is required');
        return null;
      }

      // Create the quality metric
      const metric = await projectRepository.getComponentModel('project_quality_metrics').create({
        data: {
          name: metricData.name,
          description: metricData.description || '',
          value: metricData.value || 0,
          target: metricData.target || 0,
          unit: metricData.unit || '',
          category: metricData.category || 'general',
          project_id: projectId,
        },
      });

      // Update project timestamp
      await projectRepository.update(projectId, { updated_at: new Date() });

      return metric;
    } catch (error) {
      logger.error(`Error adding quality metric to project ${projectId}: ${error}`);
      return null;
    }
  }

  /**
   * Get all quality metrics for a project.
   *
   * @param projectId - The project's ID.
   * @returns List of quality metrics.
   */
  async getProjectQualityMetrics(projectId: number): Promise<QualityMetric[]> {
    return projectRepository.getComponentModel('project_quality_metrics').findMany({
      where: { project_id: projectId },
    });
  }

  // Project Summary and Dashboard

  /**
   * Get a summary of a project, including component counts.
   *
   * @param projectId - The project's ID.
   * @returns Project summary data.
   */
  async getProjectSummary(projectId: number): Promise<Record<string, any>> {
    try {
      const project = await this.getById(projectId);
      if (!project) {
        logger.warn(`Project with ID ${projectId} not found`);
        return {};
      }

      // Count components
      const [objectivesCount, techStackCount, slidesCount, testsCount] = await Promise.all([
        projectRepository
          .getComponentModel('project_objectives')
          .count({ where: { project_id: projectId } }),
        projectRepository
          .getComponentModel('project_tech_stacks')
          .count({ where: { project_id: projectId } }),
        projectRepository
          .getComponentModel('project_slides')
          .count({ where: { project_id: projectId } }),
        projectRepository
          .getComponentModel('project_tests')
          .count({ where: { project_id: projectId } }),
      ]);

      // Get feature state
      const featureState = await projectRepository.getFeatureState(projectId);

      // Build summary
      return {
        id: project.id,
        name: project.name,
        description: project.description,
        projectType: project.project_type,
        projectMotivation: project.project_motivation,
        language: project.language,
        framework: project.framework,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        isActive: project.is_active,
        components: {
          objectives: objectivesCount,
          techStack: techStackCount,
          slides: slidesCount,
          tests: testsCount,
        },
        featureState,
      };
    } catch (error) {
      logger.error(`Error getting project summary for ${projectId}: ${error}`);
      return {};
    }
  }

  // Helper methods
}

// Export a singleton instance
export const projectService = new ProjectService();
export default projectService;
