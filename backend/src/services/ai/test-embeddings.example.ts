import azureService from './azure.service';

/**
 * Example usage of the Azure OpenAI embedding functionality
 * This demonstrates how to use the new embedding methods in the Azure service
 */

async function testEmbeddings() {
  try {
    console.log('Testing Azure OpenAI Embeddings...\n');

    // Example 1: Generate embedding for a single text
    console.log('1. Single text embedding:');
    const singleResult = await azureService.generateEmbedding(
      'KAPI IDE is an advanced AI-native development environment'
    );
    
    console.log(`- Embedding dimension: ${singleResult.embedding.length}`);
    console.log(`- First 5 values: [${singleResult.embedding.slice(0, 5).join(', ')}...]`);
    console.log(`- Tokens used: ${singleResult.usage.totalTokens}`);
    console.log(`- Cost: $${singleResult.usage.cost.toFixed(6)}`);
    console.log(`- Duration: ${singleResult.usage.durationMs}ms\n`);

    // Example 2: Generate embeddings for multiple texts (batch processing)
    console.log('2. Batch text embeddings:');
    const texts = [
      'First phrase about semantic search',
      'Second phrase about vector databases',
      'Third phrase about RAG implementation'
    ];
    
    const batchResult = await azureService.generateEmbeddings(texts);
    
    console.log(`- Number of embeddings: ${batchResult.embeddings.length}`);
    batchResult.embeddings.forEach((embedding, index) => {
      console.log(`- Text ${index + 1}: dimension=${embedding.length}, first value=${embedding[0].toFixed(6)}`);
    });
    console.log(`- Total tokens: ${batchResult.usage.totalTokens}`);
    console.log(`- Total cost: $${batchResult.usage.cost.toFixed(6)}`);
    console.log(`- Duration: ${batchResult.usage.durationMs}ms\n`);

    // Example 3: Using custom deployment/model (if different from default)
    console.log('3. Custom deployment example:');
    const customResult = await azureService.generateEmbedding(
      'Testing custom deployment configuration',
      {
        deployment: 'text-embedding-3-small', // Could be different deployment
        model: 'text-embedding-3-small'       // Model name for the API
      }
    );
    
    console.log(`- Model used: ${customResult.model}`);
    console.log(`- Embedding dimension: ${customResult.embedding.length}`);
    console.log(`- Cost: $${customResult.usage.cost.toFixed(6)}\n`);

    // Example 4: Integration with RAG pipeline
    console.log('4. RAG integration example:');
    
    // Simulate document chunks
    const documentChunks = [
      'KAPI IDE enables developers to search for the "what" and "why" behind their code',
      'The automated documentation system generates comprehensive docs from code',
      'Vector embeddings enable natural language search across the codebase'
    ];
    
    // Generate embeddings for all chunks
    const docEmbeddings = await azureService.generateEmbeddings(documentChunks);
    
    // Generate query embedding
    const queryEmbedding = await azureService.generateEmbedding('How does KAPI help with code search?');
    
    // Calculate cosine similarity (simplified example)
    const similarities = docEmbeddings.embeddings.map((docEmbed, index) => {
      const dotProduct = docEmbed.reduce((sum, val, i) => sum + val * queryEmbedding.embedding[i], 0);
      const docMagnitude = Math.sqrt(docEmbed.reduce((sum, val) => sum + val * val, 0));
      const queryMagnitude = Math.sqrt(queryEmbedding.embedding.reduce((sum, val) => sum + val * val, 0));
      const similarity = dotProduct / (docMagnitude * queryMagnitude);
      
      return {
        index,
        text: documentChunks[index],
        similarity
      };
    });
    
    // Sort by similarity
    similarities.sort((a, b) => b.similarity - a.similarity);
    
    console.log('Most similar documents:');
    similarities.forEach((result, rank) => {
      console.log(`${rank + 1}. Similarity: ${result.similarity.toFixed(4)} - "${result.text.substring(0, 50)}..."`);
    });
    
    console.log(`\nTotal cost for RAG example: $${(docEmbeddings.usage.cost + queryEmbedding.usage.cost).toFixed(6)}`);

  } catch (error) {
    console.error('Error testing embeddings:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEmbeddings().then(() => {
    console.log('\nEmbedding tests completed!');
  }).catch(console.error);
}

export { testEmbeddings };
