import {
  BedrockRuntimeClient,
  InvokeModelCommand,
  InvokeModelWithResponseStreamCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { config } from '../../common/config';
import { BadRequestError } from '../../common/errors/http.error';
import { createNovaStreamTransformer } from './nova-stream-handler';
import { logger } from '../../common/logger';
import { config as appConfig } from '../../../config/config';

/**
 * Service for interacting with Amazon Nova models via AWS Bedrock
 */
class NovaService {
  private client: BedrockRuntimeClient;

  /**
   * Model mapping from friendly names to Nova model IDs
   * These must match exactly with the Python implementation
   */
  private modelMap: Record<string, string> = {
    micro: 'us.amazon.nova-micro-v1:0',
    lite: 'us.amazon.nova-lite-v1:0',
    pro: 'us.amazon.nova-pro-v1:0',
    premier: 'us.amazon.nova-premier-v1:0', // When available
  };

  constructor() {
    // Initialize the Bedrock Runtime client with appropriate configuration
    this.client = new BedrockRuntimeClient({
      region: config.aws.region || 'us-east-1',
      credentials: {
        accessKeyId: config.aws.accessKeyId || process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: config.aws.secretAccessKey || process.env.AWS_SECRET_ACCESS_KEY || '',
      },
      // Set a longer timeout for larger requests
      requestHandler: {
        timeoutInMs: 300000, // 5 minutes timeout for Nova models
      },
    });
  }

  /**
   * Invoke Nova model to generate text
   */
  async generateText({
    prompt,
    modelType = 'pro',
    maxTokens = 8000,
    temperature = 0.7,
    topP = 0.9,
    systemPrompt = 'You are a helpful, harmless, and honest AI assistant.',
  }: {
    prompt: string;
    modelType?: string;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    systemPrompt?: string;
  }): Promise<any> {
    try {
      const startTime = Date.now();
      // Get the correct model ID from the mapping
      const modelId = this.modelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(this.modelMap).join(', ')}`,
        );
      }

      // Format the request payload for Nova models
      const requestBody = {
        schemaVersion: 'messages-v1',
        messages: [
          {
            role: 'user',
            content: [{ text: prompt }],
          },
        ],
        system: [
          {
            text: systemPrompt,
          },
        ],
        inferenceConfig: {
          maxTokens: maxTokens,
          temperature: temperature,
          topP: topP,
        },
      };

      // Create the command to invoke the model
      const command = new InvokeModelCommand({
        modelId: modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      // Invoke the model and get the response
      const response = await this.client.send(command);

      // Parse the response
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      // Extract token usage and calculate cost
      let promptTokens = 0;
      let completionTokens = 0;
      let totalTokens = 0;
      let cost = 0;
      const duration_ms = Date.now() - startTime;

      if (responseBody.usage) {
        promptTokens = responseBody.usage.inputTokens || 0;
        completionTokens = responseBody.usage.outputTokens || 0;
        totalTokens = promptTokens + completionTokens;
        cost = this.calculateCost(modelType, promptTokens, completionTokens);
      } else {
        // Estimate token counts if not provided
        promptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);
        completionTokens = Math.ceil((responseBody.output.message.content[0].text?.length || 0) / 4);
        totalTokens = promptTokens + completionTokens;
        cost = this.calculateCost(modelType, promptTokens, completionTokens);
        logger.warn(`No token usage from Nova, using estimated values`);
      }

      logger.info(
        `Nova non-streaming complete. Model: ${modelType}, Tokens: ${promptTokens}/${completionTokens}/${totalTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
      );

      // Return structured response
      return {
        content: responseBody.output.message.content[0].text,
        usage: {
          promptTokens,
          completionTokens,
          totalTokens,
          cost,
          durationMs: duration_ms,
        },
        model: `nova-${modelType}`,
      };
    } catch (error: any) {
      logger.error('Error invoking Nova model:', error);
      throw new BadRequestError(`Failed to generate text with Nova: ${error.message}`);
    }
  }

  /**
   * Stream text from Nova model using response streaming
   *
   * @param options - Options for the streaming request
   * @param transformOutput - Whether to transform the output to match OpenAI format (default: false)
   * @returns An async iterable stream of response chunks
   */
  async streamText(
    {
      prompt,
      modelType = 'pro',
      maxTokens = 8000,
      temperature = 0.7,
      topP = 0.9,
      systemPrompt = 'You are a helpful, harmless, and honest AI assistant.',
    }: {
      prompt: string;
      modelType?: string;
      maxTokens?: number;
      temperature?: number;
      topP?: number;
      systemPrompt?: string;
    },
    transformOutput: boolean = false,
  ): Promise<AsyncIterable<any>> {
    try {
      // Get the correct model ID from the mapping
      const modelId = this.modelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(this.modelMap).join(', ')}`,
        );
      }

      // Format the request payload for Nova models - same format as non-streaming
      const requestBody = {
        schemaVersion: 'messages-v1',
        messages: [
          {
            role: 'user',
            content: [{ text: prompt }],
          },
        ],
        system: [
          {
            text: systemPrompt,
          },
        ],
        inferenceConfig: {
          maxTokens: maxTokens,
          temperature: temperature,
          topP: topP,
        },
      };

      // Create the command to invoke the model with streaming
      const command = new InvokeModelWithResponseStreamCommand({
        modelId: modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      // Invoke the model and get the streaming response
      const response = await this.client.send(command);

      // Check if we have a valid response
      if (!response.body) {
        throw new Error('No response stream received from Nova model');
      }

      // If transformation is requested, transform the stream to match OpenAI format
      if (transformOutput) {
        const transformer = createNovaStreamTransformer(modelType);
        return transformer(response.body);
      }

      // Otherwise return the raw stream
      return response.body;
    } catch (error: any) {
      logger.error('Error streaming from Nova model:', error);
      throw new BadRequestError(`Failed to stream text from Nova: ${error.message}`);
    }
  }

  /**
   * Test all available Nova models with a simple prompt
   */
  async testAllModels(
    prompt: string = 'Explain how large language models work in simple terms.',
  ): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    // Test each model type
    for (const modelType of Object.keys(this.modelMap)) {
      try {
        logger.info(
          `\n--- Nova ${modelType.charAt(0).toUpperCase() + modelType.slice(1)} Response ---`,
        );
        const response = await this.generateText({
          prompt,
          modelType,
          maxTokens: 500,
        });

        results[modelType] = response;
        logger.info(response);
      } catch (error: any) {
        logger.error(`Error with Nova ${modelType}:`, error.message);
        results[modelType] = `Error: ${error.message}`;
      }
    }

    return results;
  }

  /**
   * Test streaming with a Nova model
   */
  async testStreaming(
    prompt: string = 'Explain how large language models work in simple terms.',
    modelType: string = 'pro',
  ): Promise<string> {
    try {
      logger.info(`\n--- Testing Nova ${modelType} Streaming ---`);
      logger.info(`Prompt: ${prompt}`);

      // Get the stream
      const stream = await this.streamText({
        prompt,
        modelType,
        maxTokens: 500,
      });

      let fullResponse = '';

      // Process the stream - Nova uses a different streaming format
      for await (const chunk of stream) {
        if (chunk.chunk?.bytes) {
          try {
            const textResponse = new TextDecoder().decode(chunk.chunk.bytes);
            const jsonResponse = JSON.parse(textResponse);

            // Nova streaming format has contentBlockDelta with text chunks
            if (jsonResponse.contentBlockDelta?.delta?.text) {
              const text = jsonResponse.contentBlockDelta.delta.text;
              process.stdout.write(text);
              fullResponse += text;
            }
          } catch (e) {
            logger.error('Error processing chunk:', e);
          }
        }
      }

      logger.info('\n\n--- Streaming Complete ---');
      return fullResponse;
    } catch (error: any) {
      logger.error(`Error testing Nova streaming:`, error.message);
      // Return an empty string instead of throwing to make the function more robust
      return `Error: ${error.message}`;
    }
  }

  /**
   * Calculate cost for Nova models based on pricing from models.tsv
   */
  private calculateCost(modelType: string, promptTokens: number, completionTokens: number): number {
    try {
      // Map Nova model types to model IDs used in config
      const modelIdMap: Record<string, string> = {
        micro: 'nova-micro',
        lite: 'nova-lite',
        pro: 'nova-pro',
        premier: 'nova-premier',
      };

      const configModelId = modelIdMap[modelType.toLowerCase()] || modelType;

      // Get model configuration from config
      const modelConfig = appConfig.getModelById(configModelId);

      // Default costs if model not found in config
      let inputCostPer1k = 0;
      let outputCostPer1k = 0;

      if (modelConfig) {
        // Get pricing from model config
        inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
        outputCostPer1k = parseFloat(modelConfig.output_price) || 0;

        logger.debug(
          `Found pricing for ${configModelId}: input=${inputCostPer1k}, output=${outputCostPer1k} per 1k tokens`,
        );
      } else {
        logger.warn(
          `No pricing information found for model: ${configModelId} in models.tsv config`,
        );
      }

      // Calculate costs
      const inputCost = (promptTokens / 1000) * inputCostPer1k;
      const outputCost = (completionTokens / 1000) * outputCostPer1k;
      const totalCost = inputCost + outputCost;

      logger.debug(
        `Cost calculation for ${modelType}: input=${inputCost.toFixed(6)}, output=${outputCost.toFixed(6)}, total=${totalCost.toFixed(6)}`,
      );

      // Return total cost
      return totalCost;
    } catch (error) {
      logger.error(`Error calculating cost for model ${modelType}:`, error);
      // Return 0 if unable to calculate cost - pricing should be in models.tsv
      return 0;
    }
  }
}

export default new NovaService();
