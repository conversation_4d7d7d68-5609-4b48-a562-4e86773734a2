import {
  BedrockRuntimeClient,
  InvokeModelCommand,
  InvokeModelWithResponseStreamCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { config } from '../../../config/config';
import { BadRequestError } from '../../common/errors/http.error';
import { logger } from '../../common/logger';

/**
 * Interface for Claude streaming response chunks
 */
interface ClaudeStreamChunk {
  type?: string;
  index?: number;
  delta?: {
    text?: string;
    stop_reason?: string;
    stop_sequence?: string;
    type?: string;
  };
  content_block?: {
    type?: string;
    text?: string;
  };
  message?: {
    id?: string;
    type?: string;
    role?: string;
    content?: Array<{
      type: string;
      text: string;
    }>;
    usage?: {
      input_tokens: number;
      output_tokens: number;
    };
    stop_reason?: string;
    stop_sequence?: string;
  };
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
}

/**
 * Handler for Claude streaming responses
 */
class ClaudeStreamHandler {
  private fullText: string = '';
  private inputTokens: number = 0;
  private outputTokens: number = 0;
  private isDone: boolean = false;
  private stopReason: string = '';

  /**
   * Process a chunk from the Claude streaming response
   */
  processChunk(chunk: ClaudeStreamChunk): { text?: string; done: boolean } {
    // Log the chunk type for debugging
    logger.debug('Processing Claude chunk:', JSON.stringify(chunk).substring(0, 200));

    // Handle content delta (actual text content)
    if (chunk.delta?.text) {
      const text = chunk.delta.text;
      this.fullText += text;
      return { text, done: false };
    }

    // Handle stop reason
    if (chunk.delta?.stop_reason) {
      this.stopReason = chunk.delta.stop_reason;
      this.isDone = true;
      return { done: true };
    }

    // Handle usage information
    if (chunk.usage) {
      this.inputTokens = chunk.usage.input_tokens;
      this.outputTokens = chunk.usage.output_tokens;
      logger.debug(
        `Claude usage info received: ${this.inputTokens} input, ${this.outputTokens} output tokens`,
      );
      return { done: this.isDone };
    }

    // Handle message content (for non-delta format)
    if (chunk.message?.content && Array.isArray(chunk.message.content)) {
      for (const content of chunk.message.content) {
        if (content.type === 'text' && content.text) {
          this.fullText += content.text;
          return { text: content.text, done: false };
        }
      }
    }

    // Handle message usage (for non-delta format)
    if (chunk.message?.usage) {
      this.inputTokens = chunk.message.usage.input_tokens;
      this.outputTokens = chunk.message.usage.output_tokens;
      logger.debug(
        `Claude message usage info received: ${this.inputTokens} input, ${this.outputTokens} output tokens`,
      );
      return { done: this.isDone };
    }

    // Handle content block format
    if (chunk.type === 'content_block_delta' && chunk.delta?.text) {
      const text = chunk.delta.text;
      this.fullText += text;
      return { text, done: false };
    }

    // Handle message stop
    if (chunk.type === 'message_stop') {
      this.isDone = true;
      this.stopReason = 'stop';
      return { done: true };
    }

    // For other chunk types, just return current state
    return { done: this.isDone };
  }

  /**
   * Get the full text accumulated so far
   */
  getFullText(): string {
    return this.fullText;
  }

  /**
   * Get token usage information
   */
  getTokenUsage(): { inputTokens: number; outputTokens: number } {
    // If we don't have token counts, estimate them
    if (this.inputTokens === 0 && this.outputTokens === 0) {
      // Rough estimate: 1 token is approximately 4 characters for English text
      const estimatedOutputTokens = Math.ceil(this.fullText.length / 4);
      logger.warn('No token usage information from Claude, using estimated values');
      return {
        inputTokens: Math.ceil(estimatedOutputTokens * 0.5), // Rough estimate for input tokens
        outputTokens: estimatedOutputTokens,
      };
    }

    return {
      inputTokens: this.inputTokens,
      outputTokens: this.outputTokens,
    };
  }

  /**
   * Check if the stream is complete
   */
  isComplete(): boolean {
    return this.isDone;
  }

  /**
   * Get the stop reason if available
   */
  getStopReason(): string {
    return this.stopReason;
  }

  /**
   * Reset the handler state
   */
  reset(): void {
    this.fullText = '';
    this.inputTokens = 0;
    this.outputTokens = 0;
    this.isDone = false;
    this.stopReason = '';
  }
}

/**
 * Create a transform function for Claude streaming responses
 * This converts Claude's streaming format to a more standard format
 * that matches other LLM providers
 */
function createClaudeStreamTransformer(modelType: string, originalModelId?: string) {
  const handler = new ClaudeStreamHandler();
  const startTime = Date.now();
  // Use the original model ID if provided, otherwise use the model type
  const modelId = originalModelId || `claude-${modelType.replace('.', '-')}`;

  return async function* transform(stream: AsyncIterable<any>): AsyncGenerator<any> {
    try {
      for await (const chunk of stream) {
        if (chunk.chunk?.bytes) {
          try {
            const textResponse = new TextDecoder().decode(chunk.chunk.bytes);
            logger.debug(`Claude stream chunk received: ${textResponse.substring(0, 100)}...`);

            const jsonResponse = JSON.parse(textResponse);

            // Log the chunk type for debugging
            if (jsonResponse.type) {
              logger.debug(`Claude chunk type: ${jsonResponse.type}`);
            }

            const result = handler.processChunk(jsonResponse);

            if (result.text) {
              // Yield in a format similar to OpenAI's streaming format
              yield {
                choices: [
                  {
                    delta: {
                      content: result.text,
                    },
                    index: 0,
                    finish_reason: null,
                  },
                ],
                model: modelId,
              };
            }

            if (result.done) {
              // Get token usage
              const usage = handler.getTokenUsage();
              const duration_ms = Date.now() - startTime;

              // Log token usage
              logger.info(`Claude model ${modelId} token usage:`, {
                inputTokens: usage.inputTokens,
                outputTokens: usage.outputTokens,
                totalTokens: usage.inputTokens + usage.outputTokens,
                duration_ms: duration_ms,
              });

              // Calculate cost based on model and token usage
              // Map the model ID to the format used in models.tsv
              let configModelId: string;

              if (modelId.includes('claude-3-7-sonnet') || modelId.includes('claude-3.7-sonnet')) {
                configModelId = 'claude-3.7-sonnet';
              } else if (
                modelId.includes('claude-3-5-sonnet') ||
                modelId.includes('claude-3.5-sonnet')
              ) {
                configModelId = 'claude-3.5-sonnet';
              } else if (
                modelId.includes('claude-3-5-haiku') ||
                modelId.includes('claude-3.5-haiku')
              ) {
                configModelId = 'claude-3.5-haiku';
              } else {
                configModelId = modelId;
              }

              logger.info(`Using config model ID ${configModelId} for cost calculation`);

              // Calculate cost
              let cost = 0;

              try {
                // Get model configuration from config
                const modelConfig = config.getModelById(configModelId);

                if (modelConfig) {
                  const inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
                  const outputCostPer1k = parseFloat(modelConfig.output_price) || 0;

                  cost =
                    (usage.inputTokens / 1000) * inputCostPer1k +
                    (usage.outputTokens / 1000) * outputCostPer1k;

                  logger.info(`Claude model ${modelId} cost calculation:`, {
                    inputCost: (usage.inputTokens / 1000) * inputCostPer1k,
                    outputCost: (usage.outputTokens / 1000) * outputCostPer1k,
                    totalCost: cost,
                    duration_ms: duration_ms,
                  });
                } else {
                  logger.warn(
                    `Model config not found for ${configModelId} in models.tsv config`,
                  );
                }
              } catch (error) {
                logger.error(`Error calculating cost for ${configModelId}:`, error);
                // Return 0 if unable to calculate cost - pricing should be in models.tsv
                cost = 0;
              }

              logger.info(
                `Claude stream complete. Model: ${modelId}, Tokens: ${usage.inputTokens}/${usage.outputTokens}/${usage.inputTokens + usage.outputTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
              );

              // Final chunk with completion info
              yield {
                choices: [
                  {
                    delta: { content: '' },
                    index: 0,
                    finish_reason: handler.getStopReason() || 'stop',
                  },
                ],
                usage: {
                  prompt_tokens: usage.inputTokens,
                  completion_tokens: usage.outputTokens,
                  total_tokens: usage.inputTokens + usage.outputTokens,
                  cost: cost,
                  duration_ms: duration_ms,
                },
                model: modelId,
                cost: cost,
              };
            }
          } catch (e) {
            logger.error('Error processing Claude stream chunk:', e);
            // Yield an error message that can be handled by the client
            yield {
              error: true,
              message: `Error processing Claude stream: ${e instanceof Error ? e.message : String(e)}`,
            };
          }
        }
      }
    } catch (error) {
      logger.error('Fatal error in Claude stream transformer:', error);
      // Yield a final error message
      yield {
        error: true,
        message: `Fatal error in Claude stream: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  };
}

class ClaudeService {
  private client: BedrockRuntimeClient;

  constructor() {
    // Initialize the Bedrock Runtime client with appropriate configuration
    this.client = new BedrockRuntimeClient({
      region: config.aws.region,
      // Use environment variables for credentials if not explicitly provided in config
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
      // Set a longer timeout for larger requests
      requestHandler: {
        timeoutInMs: 300000, // 5 minutes timeout
      },
    });
  }

  /**
   * Model mapping from friendly names to AWS Bedrock model IDs
   * Using the US region-specific model IDs that work with on-demand throughput
   */
  private modelMap: Record<string, string> = {
    '3.5-sonnet': 'us.anthropic.claude-3-5-sonnet-20241022-v2:0',
    '3.5-haiku': 'us.anthropic.claude-3-5-haiku-20241022-v1:0',
    '3.7-sonnet': 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
  };

  /**
   * Invoke Claude model via AWS Bedrock
   */
  async invokeClaudeModel({
    prompt,
    modelType = '3.7-sonnet',
    maxTokens = 8192,
    temperature = 0.7,
    systemPrompt,
  }: {
    prompt: string;
    modelType?: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  }): Promise<any> {
    try {
      const startTime = Date.now();
      // Get the correct model ID from the mapping
      const modelId = this.modelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(this.modelMap).join(', ')}`,
        );
      }

      // Format the request payload for Claude through Bedrock
      const requestBody: any = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: maxTokens,
        temperature: temperature,
        messages: [
          {
            role: 'user',
            content: [{ type: 'text', text: prompt }],
          },
        ],
      };

      // Add system parameter if provided (as a top-level parameter, not as a message)
      if (systemPrompt) {
        // Claude expects system to be an array of objects with type and text properties
        requestBody.system = [
          {
            type: 'text',
            text: systemPrompt,
          },
        ];
      }

      // Create the command to invoke the model
      const command = new InvokeModelCommand({
        modelId: modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      // Invoke the model and get the response
      const response = await this.client.send(command);

      // Parse the response
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      // Extract token usage if available
      let promptTokens = 0;
      let completionTokens = 0;
      let totalTokens = 0;

      if (responseBody.usage) {
        promptTokens = responseBody.usage.input_tokens || 0;
        completionTokens = responseBody.usage.output_tokens || 0;
        totalTokens = promptTokens + completionTokens;
      } else {
        // Estimate token counts if not provided
        promptTokens = Math.ceil((prompt.length + (systemPrompt?.length || 0)) / 4);
        completionTokens = Math.ceil((responseBody.content[0].text?.length || 0) / 4);
        totalTokens = promptTokens + completionTokens;
        logger.warn('No token usage in Claude response, using estimated values');
      }

      // Calculate cost
      const cost = this.calculateCost(modelType, promptTokens, completionTokens);

      // Calculate duration
      const duration_ms = Date.now() - startTime;

      logger.info(
        `Claude non-streaming complete. Model: ${modelType}, Tokens: ${promptTokens}/${completionTokens}/${totalTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
      );

      // Return structured response with usage data
      return {
        content: responseBody.content[0].text,
        usage: {
          promptTokens,
          completionTokens,
          totalTokens,
          cost,
          durationMs: duration_ms,
        },
        model: `claude-${modelType}`,
      };
    } catch (error: any) {
      logger.error('Error invoking Claude model:', error);
      throw new BadRequestError(`Failed to generate text with Claude: ${error.message}`);
    }
  }

  /**
   * Stream text from Claude model using response streaming
   */
  async streamText({
    prompt,
    modelType = '3.7-sonnet',
    maxTokens = 8192,
    temperature = 0.7,
    systemPrompt,
  }: {
    prompt: string;
    modelType?: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  }): Promise<AsyncIterable<any>> {
    try {
      // Get the correct model ID from the mapping
      const modelId = this.modelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(this.modelMap).join(', ')}`,
        );
      }

      // Prepare messages array - only user and assistant messages
      const messages = [
        {
          role: 'user',
          content: [{ type: 'text', text: prompt }],
        },
      ];

      // Format the request payload for Claude through Bedrock
      const requestBody: any = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: maxTokens,
        temperature: temperature,
        messages: messages,
      };

      // Add system parameter if provided (as a top-level parameter, not as a message)
      if (systemPrompt) {
        // Claude expects system to be an array of objects with type and text properties
        requestBody.system = [
          {
            type: 'text',
            text: systemPrompt,
          },
        ];
      }

      // Log the request payload for debugging
      logger.info(`Claude streaming request payload for model ${modelId}:`, {
        modelId,
        hasSystemPrompt: !!systemPrompt,
        systemPromptFormat: systemPrompt ? JSON.stringify(requestBody.system) : 'none',
        maxTokens,
        temperature,
        promptLength: prompt.length,
      });

      // Create the command to invoke the model with streaming
      const command = new InvokeModelWithResponseStreamCommand({
        modelId: modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      // Invoke the model and get the streaming response
      logger.info(`Sending streaming request to Claude model ${modelId}`);
      const response = await this.client.send(command);
      logger.info(`Received streaming response from Claude model ${modelId}`);

      // Check if we have a valid response
      if (!response.body) {
        logger.error(`No response stream received from Claude model ${modelId}`);
        throw new Error('No response stream received from Claude model');
      }

      // Log successful response
      logger.info(`Successfully received stream from Claude model ${modelId}, transforming stream`);

      try {
        // Transform the stream to match OpenAI format
        // Pass both the model type and the original model ID for proper identification
        const transformer = createClaudeStreamTransformer(modelType, modelId);
        return transformer(response.body);
      } catch (error) {
        logger.error(`Error transforming Claude stream for model ${modelId}:`, error);
        throw error;
      }
    } catch (error: any) {
      logger.error('Error streaming from Claude model:', error);
      throw new BadRequestError(`Failed to stream text from Claude: ${error.message}`);
    }
  }

  /**
   * Generate text using Claude models (maintaining backward compatibility with previous implementation)
   */
  async generateText({
    prompt,
    model = 'claude-3-7-sonnet-20250219',
    maxTokens = 8192,
    temperature = 0.7,
    systemPrompt,
  }: {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  }): Promise<any> {
    // Map model names from the original format to the new format
    let modelType = '3.7-sonnet'; // default

    // Log the model name for debugging
    logger.info(`Mapping Claude model name to model type: ${model}`);

    if (model.includes('3-5-sonnet') || model.includes('3.5-sonnet')) {
      modelType = '3.5-sonnet';
      logger.info(`Mapped to model type: 3.5-sonnet`);
    } else if (model.includes('3-5-haiku') || model.includes('3.5-haiku')) {
      modelType = '3.5-haiku';
      logger.info(`Mapped to model type: 3.5-haiku`);
    } else if (model.includes('3-7-sonnet') || model.includes('3.7-sonnet')) {
      modelType = '3.7-sonnet';
      logger.info(`Mapped to model type: 3.7-sonnet`);
    } else {
      logger.info(`Using default model type: 3.7-sonnet for model: ${model}`);
    }

    // Invoke Claude model with the mapped model type
    return this.invokeClaudeModel({
      prompt,
      modelType,
      maxTokens,
      temperature,
      systemPrompt,
    });
  }

  /**
   * Calculate cost for Claude models based on pricing from models.tsv
   */
  private calculateCost(modelType: string, promptTokens: number, completionTokens: number): number {
    try {
      // Map model types to model IDs used in config
      const modelIdMap: Record<string, string> = {
        '3.5-sonnet': 'claude-3.5-sonnet',
        '3.5-haiku': 'claude-3.5-haiku',
        '3.7-sonnet': 'claude-3.7-sonnet',
      };

      const configModelId = modelIdMap[modelType.toLowerCase()] || modelType;

      // Get model configuration from config
      const modelConfig = config.getModelById(configModelId);

      // Default costs if model not found in config
      let inputCostPer1k = 0;
      let outputCostPer1k = 0;

      if (modelConfig) {
        // Get pricing from model config
        inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
        outputCostPer1k = parseFloat(modelConfig.output_price) || 0;

        logger.debug(
          `Found pricing for ${configModelId}: input=${inputCostPer1k}, output=${outputCostPer1k} per 1k tokens`,
        );
      } else {
        logger.warn(
          `No pricing information found for model: ${configModelId} in models.tsv config`,
        );
      }

      // Calculate costs
      const inputCost = (promptTokens / 1000) * inputCostPer1k;
      const outputCost = (completionTokens / 1000) * outputCostPer1k;
      const totalCost = inputCost + outputCost;

      logger.debug(
        `Cost calculation for ${modelType}: input=${inputCost.toFixed(6)}, output=${outputCost.toFixed(6)}, total=${totalCost.toFixed(6)}`,
      );

      // Return total cost
      return totalCost;
    } catch (error) {
      logger.error(`Error calculating cost for model ${modelType}:`, error);
      // Return 0 if unable to calculate cost - pricing should be in models.tsv
      return 0;
    }
  }
}

const claudeService = new ClaudeService();
export default claudeService;
