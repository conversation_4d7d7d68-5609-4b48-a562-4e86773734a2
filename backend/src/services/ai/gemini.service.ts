import OpenAI from 'openai';
import { BadRequestError } from '../../common/errors/http.error';
import axios from 'axios';
import { config } from '../../../config/config';
import { logger } from '../../common/logger';

/**
 * Reasoning effort levels for Gemini models that support thinking
 */
enum ReasoningEffort {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  NONE = 'none',
}

class GeminiService {
  private client: OpenAI;

  /**
   * Model mapping from friendly names to Gemini model IDs
   * Based on the latest Gemini models as of May 2025
   */
  private modelMap: Record<string, string> = {
    // Gemini 2.5 models - full model names
    'gemini-2.5-pro-preview-05-06': 'gemini-2.5-pro-preview-05-06',
    'gemini-2.5-flash-preview-04-17': 'gemini-2.5-flash-preview-04-17',

    // Gemini 2.5 models - short names
    '2.5-pro': 'gemini-2.5-pro-preview-05-06',
    '2.5-flash': 'gemini-2.5-flash-preview-04-17',
    '2.5-pro-preview': 'gemini-2.5-pro-preview-05-06',
    '2.5-flash-preview': 'gemini-2.5-flash-preview-04-17',

    // Model IDs from config.yaml
    'gemini-2.5-pro': 'gemini-2.5-flash-preview-04-17',
    'gemini-2.5-flash': 'gemini-2.5-flash-preview-04-17',

    // Gemini 2.0 models
    '2.0-flash': 'gemini-2.0-flash',
    '2.0-flash-lite': 'gemini-2.0-flash-lite',
    '2.0-flash-preview-image-generation': 'gemini-2.0-flash-preview-image-generation',
    '2.0-flash-live': 'gemini-2.0-flash-live-001',

    // Embedding models
    'text-embedding': 'text-embedding-004',
    'gemini-embedding': 'gemini-embedding-exp-03-07',
  };

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      logger.warn('GEMINI_API_KEY not found in environment');
    }

    // Initialize OpenAI client with Gemini base URL
    this.client = new OpenAI({
      apiKey: process.env.GEMINI_API_KEY,
      baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai/',
      timeout: 120000, // 2 minutes timeout for longer-running models like Gemini 2.5
      maxRetries: 3, // Retry failed requests up to 3 times
    });
  }

  /**
   * Generate text using Gemini models with OpenAI compatibility
   */
  async generateText({
    prompt,
    model = 'gemini-2.0-flash',
    maxTokens = 16000,
    temperature = 0.7,
    reasoningEffort = ReasoningEffort.NONE,
    systemPrompt,
  }: {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    reasoningEffort?: ReasoningEffort;
    systemPrompt?: string;
  }): Promise<any> {
    try {
      const startTime = Date.now();
      // Get the correct model ID from the mapping or use the provided model directly
      const modelId = this.modelMap[model] || model;

      // Create messages array
      const messages = [];

      // Add system prompt if provided
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }

      // Add user prompt
      messages.push({ role: 'user', content: prompt });

      // Format messages
      const formattedMessages = messages.map((msg) => {
        if (msg.role === 'system') {
          return { role: 'system', content: msg.content };
        } else if (msg.role === 'user') {
          return { role: 'user', content: msg.content };
        } else {
          return { role: 'assistant', content: msg.content };
        }
      });

      // Check if this is a Gemini 2.5 model
      const isGemini25 = modelId.includes('gemini-2.5');

      // For Gemini 2.5 models, always use reasoning_effort
      const effectiveReasoningEffort = isGemini25
        ? reasoningEffort !== ReasoningEffort.NONE
          ? reasoningEffort
          : ReasoningEffort.LOW
        : reasoningEffort;

      logger.info(
        `Creating chat completion with model ${modelId}, reasoning effort: ${effectiveReasoningEffort}`,
      );

      try {
        // Create the chat completion request
        const response = await this.client.chat.completions.create({
          model: modelId,
          // @ts-ignore - We've properly formatted the messages above
          messages: formattedMessages,
          max_tokens: isGemini25 ? undefined : maxTokens, // Don't specify max_tokens for Gemini 2.5
          temperature: temperature,
          ...(effectiveReasoningEffort !== ReasoningEffort.NONE && {
            reasoning_effort: effectiveReasoningEffort,
          }),
        });

        logger.debug('Gemini response:', JSON.stringify(response, null, 2));

        if (!response.choices || response.choices.length === 0) {
          logger.error('No choices in Gemini response');
          throw new Error('No response generated');
        }

        if (!response.choices[0].message) {
          logger.error('No message in first choice of Gemini response');
          throw new Error('No message in response');
        }

        if (!response.choices[0].message.content) {
          logger.error('No content in message of Gemini response');
          throw new Error('Empty response content');
        }

        // Extract token usage and calculate cost
        let promptTokens = 0;
        let completionTokens = 0;
        let totalTokens = 0;
        let cost = 0;
        const duration_ms = Date.now() - startTime;

        if (response.usage) {
          promptTokens = response.usage.prompt_tokens || 0;
          completionTokens = response.usage.completion_tokens || 0;
          totalTokens = promptTokens + completionTokens;
          cost = this.calculateCost(modelId, promptTokens, completionTokens);
        } else {
          // Estimate token counts if not provided
          promptTokens = Math.ceil((prompt.length + (systemPrompt?.length || 0)) / 4);
          completionTokens = Math.ceil((response.choices[0].message.content?.length || 0) / 4);
          totalTokens = promptTokens + completionTokens;
          cost = this.calculateCost(modelId, promptTokens, completionTokens);
          logger.warn(`No token usage from Gemini, using estimated values`);
        }

        logger.info(
          `Gemini non-streaming complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
        );

        // Return structured response
        return {
          content: response.choices[0].message.content || '',
          usage: {
            promptTokens,
            completionTokens,
            totalTokens,
            cost,
            durationMs: duration_ms,
          },
          model: modelId,
        };
      } catch (error: any) {
        logger.error(`Error with OpenAI compatibility layer: ${error.message}`);
        
        // For Gemini 2.5 models or any model that fails, try the raw API as fallback
        if (isGemini25 || error.message.includes('2.5')) {
          logger.info(`Falling back to raw API for model: ${modelId}`);
          try {
            const rawResponse = await this.generateTextRaw({
              prompt,
              model: modelId,
              maxTokens: isGemini25 ? undefined : maxTokens,
              temperature: temperature,
              reasoningEffort: effectiveReasoningEffort,
              systemPrompt: systemPrompt,
            });
            
            // generateTextRaw returns just the text, so wrap it in our standard format
            const duration_ms = Date.now() - startTime;
            const estimatedPromptTokens = Math.ceil((prompt.length + (systemPrompt?.length || 0)) / 4);
            const estimatedCompletionTokens = Math.ceil(rawResponse.length / 4);
            const totalTokens = estimatedPromptTokens + estimatedCompletionTokens;
            const cost = this.calculateCost(modelId, estimatedPromptTokens, estimatedCompletionTokens);
            
            return {
              content: rawResponse,
              usage: {
                promptTokens: estimatedPromptTokens,
                completionTokens: estimatedCompletionTokens,
                totalTokens,
                cost,
                durationMs: duration_ms,
              },
              model: modelId,
            };
          } catch (fallbackError: any) {
            logger.error(`Raw API fallback also failed: ${fallbackError.message}`);
            throw new BadRequestError(`Failed to generate text with Gemini: ${error.message}. Fallback also failed: ${fallbackError.message}`);
          }
        }
        
        throw new BadRequestError(`Failed to generate text with Gemini: ${error.message}`);
      }
    } catch (error: any) {
      logger.error('Gemini API error:', error);
      throw new BadRequestError(`Failed to generate text with Gemini: ${error.message}`);
    }
  }

  /**
   * Generate embeddings using Gemini embedding models
   */
  async generateEmbedding({
    input,
    model = 'text-embedding',
  }: {
    input: string;
    model?: string;
  }): Promise<number[]> {
    try {
      // Get the correct model ID from the mapping or use the provided model directly
      const modelId = this.modelMap[model] || model;

      const response = await this.client.embeddings.create({
        model: modelId,
        input: input,
      });

      return response.data[0].embedding;
    } catch (error: any) {
      logger.error('Gemini embedding error:', error);
      throw new BadRequestError(`Failed to generate embedding with Gemini: ${error.message}`);
    }
  }

  /**
   * Generate chat completion with function calling
   */
  async chatWithFunctionCalling({
    messages,
    model = 'gemini-2.0-flash',
    tools,
    maxTokens = 16000,
    temperature = 0.7,
    reasoningEffort = ReasoningEffort.NONE,
  }: {
    messages: Array<{ role: string; content: string; name?: string }>;
    model?: string;
    tools: any[];
    maxTokens?: number;
    temperature?: number;
    reasoningEffort?: ReasoningEffort;
  }): Promise<any> {
    try {
      // Get the correct model ID from the mapping or use the provided model directly
      const modelId = this.modelMap[model] || model;

      // Convert messages to the format expected by OpenAI
      const formattedMessages = messages.map((msg) => {
        // Handle different message types
        if (msg.role === 'system') {
          return { role: 'system', content: msg.content };
        } else if (msg.role === 'user') {
          return { role: 'user', content: msg.content };
        } else if (msg.role === 'assistant') {
          return { role: 'assistant', content: msg.content };
        } else if (msg.role === 'function') {
          if (!msg.name) {
            throw new BadRequestError('Function messages must include a name');
          }
          return { role: 'function', content: msg.content, name: msg.name };
        } else {
          throw new BadRequestError(`Unsupported message role: ${msg.role}`);
        }
      });

      // Check if this is a Gemini 2.5 model
      const isGemini25 = modelId.includes('gemini-2.5');

      // For Gemini 2.5 models, always use reasoning_effort
      const effectiveReasoningEffort = isGemini25
        ? reasoningEffort !== ReasoningEffort.NONE
          ? reasoningEffort
          : ReasoningEffort.LOW
        : reasoningEffort;

      logger.info(
        `Creating function calling with model ${modelId}, reasoning effort: ${effectiveReasoningEffort}`,
      );

      // Record start time for duration calculation
      const startTime = Date.now();

      const response = await this.client.chat.completions.create({
        model: modelId,
        // @ts-ignore - We've properly formatted the messages above
        messages: formattedMessages,
        tools: tools,
        tool_choice: 'auto',
        max_tokens: maxTokens,
        temperature: temperature,
        ...(effectiveReasoningEffort !== ReasoningEffort.NONE && {
          reasoning_effort: effectiveReasoningEffort,
        }),
      });

      // Calculate duration in milliseconds
      const duration_ms = Date.now() - startTime;

      // Extract token usage information if available
      if (response.usage) {
        const promptTokens = response.usage.prompt_tokens || 0;
        const completionTokens = response.usage.completion_tokens || 0;
        // totalTokens is calculated as sum of prompt and completion tokens

        // Calculate cost based on token usage
        const cost = this.calculateCost(modelId, promptTokens, completionTokens);

        logger.info(
          `Gemini function calling complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
        );
      } else {
        // If no token usage information is available, log a warning
        logger.warn(
          `No token usage information available from Gemini API for function calling with model: ${modelId}`,
        );

        // Estimate token counts based on input and output length
        const inputText = messages.map((msg) => msg.content).join(' ');
        const outputText = JSON.stringify(response.choices[0].message);
        const estimatedPromptTokens = Math.ceil(inputText.length / 4);
        const estimatedCompletionTokens = Math.ceil(outputText.length / 4);
        // Total tokens would be the sum of prompt and completion tokens

        // Calculate cost based on estimated token usage
        const cost = this.calculateCost(modelId, estimatedPromptTokens, estimatedCompletionTokens);

        logger.warn(
          `Using estimated token counts for function calling: ${estimatedPromptTokens}/${estimatedCompletionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
        );
      }

      return response.choices[0].message;
    } catch (error: any) {
      logger.error('Gemini function calling error:', error);
      throw new BadRequestError(`Failed to execute function calling with Gemini: ${error.message}`);
    }
  }

  /**
   * Stream chat completions from Gemini models
   */
  async streamChatCompletions({
    messages,
    model = 'gemini-2.0-flash',
    maxTokens = 16000,
    temperature = 0.7,
    reasoningEffort = ReasoningEffort.NONE,
  }: {
    messages: Array<{ role: string; content: string; name?: string }>;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    reasoningEffort?: ReasoningEffort;
  }): Promise<any> {
    try {
      // Get the correct model ID from the mapping or use the provided model directly
      const modelId = this.modelMap[model] || model;

      // Convert messages to the format expected by OpenAI
      const formattedMessages = messages.map((msg) => {
        // Handle different message types
        if (msg.role === 'system') {
          return { role: 'system', content: msg.content };
        } else if (msg.role === 'user') {
          return { role: 'user', content: msg.content };
        } else if (msg.role === 'assistant') {
          return { role: 'assistant', content: msg.content };
        } else if (msg.role === 'function') {
          if (!msg.name) {
            throw new BadRequestError('Function messages must include a name');
          }
          return { role: 'function', content: msg.content, name: msg.name };
        } else {
          throw new BadRequestError(`Unsupported message role: ${msg.role}`);
        }
      });

      // Check if this is a Gemini 2.5 model
      const isGemini25 = modelId.includes('gemini-2.5');

      // For Gemini 2.5 models, always use reasoning_effort
      const effectiveReasoningEffort = isGemini25
        ? reasoningEffort !== ReasoningEffort.NONE
          ? reasoningEffort
          : ReasoningEffort.LOW
        : reasoningEffort;

      logger.info(
        `Creating streaming chat completion with model ${modelId}, reasoning effort: ${effectiveReasoningEffort}`,
      );

      // Calculate approximate token counts for input
      // This is a rough estimate - 1 token is approximately 4 characters for English text
      const inputText = messages.map((msg) => msg.content).join(' ');
      const estimatedPromptTokens = Math.ceil(inputText.length / 4);

      // Record start time for duration calculation
      const startTime = Date.now();

      // Make a non-streaming request first to get accurate token counts
      let actualPromptTokens = 0;
      let actualCompletionTokens = 0;
      let hasActualTokenCounts = false;

      try {
        // Create a non-streaming request with the same parameters to get token usage
        const nonStreamingResponse = await this.client.chat.completions.create({
          model: modelId,
          // @ts-ignore - We've properly formatted the messages above
          messages: formattedMessages,
          max_tokens: Math.min(100, maxTokens), // Use a reasonable max_tokens for token count estimation
          temperature: temperature,
          ...(effectiveReasoningEffort !== ReasoningEffort.NONE && {
            reasoning_effort: effectiveReasoningEffort,
          }),
        });

        // Extract token usage information if available
        if (nonStreamingResponse.usage) {
          actualPromptTokens = nonStreamingResponse.usage.prompt_tokens || 0;
          // Don't use completion tokens from this response since it's truncated
          hasActualTokenCounts = true;
          logger.info(`Got actual prompt token count from Gemini API: ${actualPromptTokens}`);
        }
      } catch (error: any) {
        logger.warn(
          `Failed to get token usage from non-streaming request: ${error?.message || 'Unknown error'}`,
        );
      }

      // Create the stream
      const stream = await this.client.chat.completions.create({
        model: modelId,
        // @ts-ignore - We've properly formatted the messages above
        messages: formattedMessages,
        max_tokens: maxTokens,
        temperature: temperature,
        stream: true,
        ...(effectiveReasoningEffort !== ReasoningEffort.NONE && {
          reasoning_effort: effectiveReasoningEffort,
        }),
      });

      // Create a wrapper around the stream to add token usage information
      let fullContent = '';
      let isFirstChunk = true;

      // Store a reference to the calculateCost method
      const calculateCostFn = (modelId: string, promptTokens: number, completionTokens: number) =>
        this.calculateCost(modelId, promptTokens, completionTokens);

      // Map the model ID to the format used in models.tsv
      let configModelId = modelId;
      if (modelId.includes('gemini-2.5-pro')) {
        configModelId = 'gemini-2.5-pro';
      } else if (modelId.includes('gemini-2.5-flash')) {
        configModelId = 'gemini-2.5-flash';
      } else if (modelId.includes('gemini-2.0-pro')) {
        configModelId = 'gemini-2.0-pro';
      } else if (modelId.includes('gemini-2.0-flash')) {
        configModelId = 'gemini-2.0-flash';
      }

      logger.info(`Using config model ID ${configModelId} for cost calculation`);

      // Create a wrapped stream that adds token usage information
      const wrappedStream = {
        [Symbol.asyncIterator]() {
          const iterator = stream[Symbol.asyncIterator]();

          return {
            next: async () => {
              try {
                const result = await iterator.next();

                if (result.done) {
                  // When the stream is done, add a final chunk with token usage information
                  let promptTokens = actualPromptTokens;
                  let completionTokens = actualCompletionTokens;

                  // Check if the final chunk has usage information
                  if (result.value && result.value.usage) {
                    // Extract token usage from the final chunk if available
                    promptTokens = result.value.usage.prompt_tokens || promptTokens;
                    completionTokens = result.value.usage.completion_tokens || completionTokens;
                    logger.info(
                      `Got token usage from final Gemini streaming chunk: prompt=${promptTokens}, completion=${completionTokens}`,
                    );
                  } else if (hasActualTokenCounts) {
                    // We already have token counts from previous chunks
                    logger.info(
                      `Using token counts from previous chunks: prompt=${promptTokens}, completion=${completionTokens}`,
                    );
                  } else {
                    // Try to make a non-streaming request to get accurate token counts
                    try {
                      logger.info(
                        `No token usage from streaming, making non-streaming request to get accurate counts`,
                      );
                      // We can't use this.client here because we're in a different context
                      // Instead, make a direct API call to get token counts
                      try {
                        logger.info(
                          `Making direct API call to get token counts for model ${modelId}`,
                        );

                        // Create the request payload for the Gemini API
                        const payload: any = {
                          contents: formattedMessages.map((msg) => ({
                            role: msg.role,
                            parts: [{ text: msg.content }],
                          })),
                          generationConfig: {
                            maxOutputTokens: maxTokens,
                            temperature: temperature,
                          },
                        };

                        // Add reasoning effort for Gemini 2.5 models
                        if (effectiveReasoningEffort !== ReasoningEffort.NONE) {
                          payload.generationConfig.reasoningEffort = effectiveReasoningEffort;
                        }

                        // Make the API request
                        const response = await axios.post(
                          `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent`,
                          payload,
                          {
                            headers: {
                              'Content-Type': 'application/json',
                              'x-goog-api-key': process.env.GEMINI_API_KEY,
                            },
                          },
                        );

                        // Extract token usage information if available
                        if (response.data.usageMetadata) {
                          promptTokens =
                            response.data.usageMetadata.promptTokenCount || promptTokens;
                          completionTokens =
                            response.data.usageMetadata.candidatesTokenCount || completionTokens;
                          logger.info(
                            `Got token usage from direct API call: prompt=${promptTokens}, completion=${completionTokens}`,
                          );
                        }
                      } catch (apiError) {
                        logger.warn(`Error making direct API call: ${apiError}`);
                      }

                      // Fall back to estimation if no token information is available
                      if (promptTokens === 0) {
                        promptTokens = estimatedPromptTokens;
                        logger.warn(
                          `No prompt token count available, using estimated value: ${promptTokens}`,
                        );
                      }

                      if (completionTokens === 0) {
                        // Use a more sophisticated token estimation method
                        // Count spaces, punctuation, and special characters
                        const wordCount = fullContent.split(/\s+/).length;
                        const specialCharCount = (fullContent.match(/[^\w\s]/g) || []).length;

                        // Adjust the token count based on word boundaries and special characters
                        // Each word is approximately 1.3 tokens, and each special character is about 0.5 tokens
                        completionTokens = Math.ceil(wordCount * 1.3 + specialCharCount * 0.5);
                        logger.warn(
                          `No completion token count available, using estimated value: ${completionTokens}`,
                        );
                      }
                    } catch (error) {
                      // Fall back to estimation if the non-streaming request fails
                      if (promptTokens === 0) {
                        promptTokens = estimatedPromptTokens;
                        logger.warn(
                          `Error getting prompt token count, using estimated value: ${promptTokens}`,
                        );
                      }

                      if (completionTokens === 0) {
                        // Use a more sophisticated token estimation method
                        // Count spaces, punctuation, and special characters
                        const wordCount = fullContent.split(/\s+/).length;
                        const specialCharCount = (fullContent.match(/[^\w\s]/g) || []).length;

                        // Adjust the token count based on word boundaries and special characters
                        // Each word is approximately 1.3 tokens, and each special character is about 0.5 tokens
                        completionTokens = Math.ceil(wordCount * 1.3 + specialCharCount * 0.5);
                        logger.warn(
                          `Error getting completion token count, using estimated value: ${completionTokens}`,
                        );
                      }

                      logger.warn(
                        `Error getting token counts from non-streaming request: ${error}`,
                      );
                    }
                  }

                  logger.info(
                    `Final token counts: ${promptTokens} prompt, ${completionTokens} completion`,
                  );

                  // Calculate cost based on token usage
                  const cost = calculateCostFn(configModelId, promptTokens, completionTokens);

                  // Calculate duration in milliseconds
                  const duration_ms = Date.now() - startTime;

                  logger.info(
                    `Gemini stream complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
                  );

                  return {
                    done: true,
                    value: {
                      choices: [],
                      usage: {
                        prompt_tokens: promptTokens,
                        completion_tokens: completionTokens,
                        total_tokens: promptTokens + completionTokens,
                        cost: cost,
                        duration_ms: duration_ms,
                      },
                      model: modelId,
                      cost: cost,
                    },
                  };
                }

                // Process the chunk
                const chunk = result.value;

                // If this is the first chunk, add model information
                if (isFirstChunk) {
                  isFirstChunk = false;
                  chunk.model = modelId;
                }

                // Extract content from the chunk to build the full response
                if (
                  chunk.choices &&
                  chunk.choices[0] &&
                  chunk.choices[0].delta &&
                  chunk.choices[0].delta.content
                ) {
                  fullContent += chunk.choices[0].delta.content;
                }

                // Extract token usage information if available
                if (chunk.usage) {
                  actualPromptTokens = chunk.usage.prompt_tokens || 0;
                  actualCompletionTokens = chunk.usage.completion_tokens || 0;
                  hasActualTokenCounts = true;
                  logger.debug(
                    `Received token usage from Gemini: ${actualPromptTokens} prompt, ${actualCompletionTokens} completion`,
                  );
                }

                return result;
              } catch (error) {
                logger.error('Error processing Gemini stream chunk:', error);
                throw error;
              }
            },
          };
        },
      };

      return wrappedStream;
    } catch (error: any) {
      logger.error('Gemini streaming error:', error);
      throw new BadRequestError(`Failed to stream chat completions with Gemini: ${error.message}`);
    }
  }

  /**
   * Calculate cost for models based on pricing from models.tsv
   */
  private calculateCost(modelId: string, promptTokens: number, completionTokens: number): number {
    try {
      // Map the model ID to the format used in models.tsv
      let configModelId = modelId;

      // Normalize model ID for config lookup
      if (modelId.includes('gemini-2.5-pro')) {
        configModelId = 'gemini-2.5-pro';
      } else if (modelId.includes('gemini-2.5-flash')) {
        configModelId = 'gemini-2.5-flash';
      } else if (modelId.includes('gemini-2.0-pro')) {
        configModelId = 'gemini-2.0-pro';
      } else if (modelId.includes('gemini-2.0-flash')) {
        configModelId = 'gemini-2.0-flash';
      }

      logger.debug(`Looking up pricing for model ID: ${configModelId} (original: ${modelId})`);

      // Get model configuration from config
      const modelConfig = config.getModelById(configModelId);

      // Default costs if model not found in config
      let inputCostPer1k = 0;
      let outputCostPer1k = 0;

      if (modelConfig) {
        // Get pricing from model config
        inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
        outputCostPer1k = parseFloat(modelConfig.output_price) || 0;

        logger.debug(
          `Found pricing for ${configModelId}: input=${inputCostPer1k}, output=${outputCostPer1k} per 1k tokens`,
        );
      } else {
        // Fallback to looking up by partial model name
        const models = config.models || [];
        const matchingModel = models.find(
          (model) =>
            configModelId.includes(model.model_id) ||
            (model.actual_model_id && configModelId.includes(model.actual_model_id)),
        );

        if (matchingModel) {
          inputCostPer1k = parseFloat(matchingModel.input_price) || 0;
          outputCostPer1k = parseFloat(matchingModel.output_price) || 0;
          logger.debug(
            `Found matching model ${matchingModel.model_id}: input=${inputCostPer1k}, output=${outputCostPer1k} per 1k tokens`,
          );
        } else {
          logger.warn(
            `No pricing information found for model: ${configModelId} in models.tsv config`,
          );
        }
      }

      // Calculate costs
      const inputCost = (promptTokens / 1000) * inputCostPer1k;
      const outputCost = (completionTokens / 1000) * outputCostPer1k;
      const totalCost = inputCost + outputCost;

      logger.debug(
        `Cost calculation for ${configModelId}: input=${inputCost.toFixed(6)}, output=${outputCost.toFixed(6)}, total=${totalCost.toFixed(6)}`,
      );

      // Return total cost
      return totalCost;
    } catch (error) {
      logger.error(`Error calculating cost for model ${modelId}:`, error);
      // Return 0 if unable to calculate cost - pricing should be in models.tsv
      return 0;
    }
  }

  /**
   * Generate text using the raw Gemini API (not the OpenAI compatibility layer)
   * This is a fallback for when the OpenAI compatibility layer doesn't work
   */
  async generateTextRaw({
    prompt,
    model = 'gemini-2.0-flash',
    maxTokens = 16000,
    temperature = 0.7,
    reasoningEffort = ReasoningEffort.NONE,
    systemPrompt,
  }: {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    reasoningEffort?: ReasoningEffort;
    systemPrompt?: string;
  }): Promise<string> {
    try {
      // Get the correct model ID from the mapping or use the provided model directly
      const modelId = this.modelMap[model] || model;

      // Check if this is a Gemini 2.5 model
      const isGemini25 = modelId.includes('gemini-2.5');

      // For Gemini 2.5 models, always use reasoning_effort
      const effectiveReasoningEffort = isGemini25
        ? reasoningEffort !== ReasoningEffort.NONE
          ? reasoningEffort
          : ReasoningEffort.LOW
        : reasoningEffort;

      logger.info(
        `Creating raw API request for model ${modelId}, reasoning effort: ${effectiveReasoningEffort}`,
      );

      // Record start time for duration calculation
      const startTime = Date.now();

      // Create the request payload
      const payload: any = {
        contents: [],
        generationConfig: {
          maxOutputTokens: maxTokens,
          temperature: temperature,
        },
      };

      // Add system prompt if provided
      if (systemPrompt) {
        payload.contents.push({
          role: 'user',
          parts: [{ text: systemPrompt }],
        });
        payload.contents.push({
          role: 'model',
          parts: [{ text: 'I understand and will follow these instructions.' }],
        });
      }

      // Add user prompt
      payload.contents.push({
        role: 'user',
        parts: [{ text: prompt }],
      });

      // Add reasoning effort for Gemini 2.5 models
      if (effectiveReasoningEffort !== ReasoningEffort.NONE) {
        payload.generationConfig.reasoningEffort = effectiveReasoningEffort;
      }

      // Make the API request
      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-goog-api-key': process.env.GEMINI_API_KEY,
          },
        },
      );

      logger.debug('Raw Gemini API response:', JSON.stringify(response.data, null, 2));

      // Extract token usage information if available
      if (response.data.usageMetadata) {
        const promptTokens = response.data.usageMetadata.promptTokenCount || 0;
        const completionTokens = response.data.usageMetadata.candidatesTokenCount || 0;
        // totalTokens is calculated as sum of prompt and completion tokens

        // Calculate cost based on token usage
        const cost = this.calculateCost(modelId, promptTokens, completionTokens);

        // Calculate duration in milliseconds
        const duration_ms = Date.now() - startTime;

        logger.info(
          `Raw Gemini API complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
        );
      } else {
        const duration_ms = Date.now() - startTime;
        logger.warn(
          `No token usage information available from raw Gemini API for model: ${modelId}, Duration: ${duration_ms}ms`,
        );
      }

      // Extract the response text
      if (response.data.candidates && response.data.candidates.length > 0) {
        const candidate = response.data.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          return candidate.content.parts[0].text || '';
        }
      }

      return 'No response content from Gemini API';
    } catch (error: any) {
      logger.error('Raw Gemini API error:', error);
      if (error.response) {
        logger.error('Error response data:', error.response.data);
      }
      throw new BadRequestError(`Failed to generate text with raw Gemini API: ${error.message}`);
    }
  }
}

export { ReasoningEffort };
export default new GeminiService();
