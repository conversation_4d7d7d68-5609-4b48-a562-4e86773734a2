import { logger } from '../../common/logger';
import { config as appConfig } from '../../../config/config';

/**
 * Helper class for handling Nova streaming responses
 *
 * Nova uses a different streaming format than other models, so we need
 * special handling to process the chunks correctly.
 */

/**
 * Types for Nova streaming response chunks
 */
export interface NovaStreamChunk {
  // Message start/stop events
  messageStart?: { role: string };
  messageStop?: { stopReason: string };

  // Content block events
  contentBlockDelta?: {
    delta: { text: string };
    contentBlockIndex: number;
  };
  contentBlockStop?: { contentBlockIndex: number };

  // Metadata at the end of the stream
  metadata?: {
    usage: {
      inputTokens: number;
      outputTokens: number;
      cacheReadInputTokenCount: number;
      cacheWriteInputTokenCount: number;
    };
    metrics: Record<string, any>;
    trace: Record<string, any>;
  };
}

/**
 * Handler for Nova streaming responses
 */
export class NovaStreamHandler {
  private fullText: string = '';
  private role: string = '';
  private inputTokens: number = 0;
  private outputTokens: number = 0;
  private isDone: boolean = false;
  private stopReason: string = '';

  /**
   * Process a chunk from the Nova streaming response
   */
  processChunk(chunk: NovaStreamChunk): { text?: string; done: boolean } {
    // Handle message start
    if (chunk.messageStart) {
      this.role = chunk.messageStart.role;
      return { done: false };
    }

    // Handle content block delta (actual text content)
    if (chunk.contentBlockDelta?.delta?.text) {
      const text = chunk.contentBlockDelta.delta.text;
      this.fullText += text;
      return { text, done: false };
    }

    // Handle message stop
    if (chunk.messageStop) {
      this.stopReason = chunk.messageStop.stopReason;
      this.isDone = true;
      return { done: true };
    }

    // Handle metadata (usually the last chunk)
    if (chunk.metadata?.usage) {
      this.inputTokens = chunk.metadata.usage.inputTokens;
      this.outputTokens = chunk.metadata.usage.outputTokens;
      return { done: this.isDone };
    }

    // For other chunk types (like contentBlockStop), just return current state
    return { done: this.isDone };
  }

  /**
   * Get the full text accumulated so far
   */
  getFullText(): string {
    return this.fullText;
  }

  /**
   * Get the role of the message (usually "assistant")
   */
  getRole(): string {
    return this.role;
  }

  /**
   * Get token usage information
   */
  getTokenUsage(): { inputTokens: number; outputTokens: number } {
    return {
      inputTokens: this.inputTokens,
      outputTokens: this.outputTokens,
    };
  }

  /**
   * Check if the stream is complete
   */
  isComplete(): boolean {
    return this.isDone;
  }

  /**
   * Get the stop reason if available
   */
  getStopReason(): string {
    return this.stopReason;
  }

  /**
   * Reset the handler state
   */
  reset(): void {
    this.fullText = '';
    this.role = '';
    this.inputTokens = 0;
    this.outputTokens = 0;
    this.isDone = false;
    this.stopReason = '';
  }
}

/**
 * Create a transform function for Nova streaming responses
 * This can be used to convert Nova's streaming format to a more standard format
 * that matches other LLM providers
 */
export function createNovaStreamTransformer(modelType: string = 'pro') {
  const handler = new NovaStreamHandler();
  const startTime = Date.now();

  return async function* transform(stream: AsyncIterable<any>): AsyncGenerator<any> {
    for await (const chunk of stream) {
      if (chunk.chunk?.bytes) {
        try {
          const textResponse = new TextDecoder().decode(chunk.chunk.bytes);
          const jsonResponse = JSON.parse(textResponse);

          const result = handler.processChunk(jsonResponse);

          if (result.text) {
            // Yield in a format similar to OpenAI's streaming format
            yield {
              choices: [
                {
                  delta: {
                    content: result.text,
                  },
                  index: 0,
                  finish_reason: null,
                },
              ],
            };
          }

          if (result.done) {
            const tokenUsage = handler.getTokenUsage();
            const promptTokens = tokenUsage.inputTokens;
            const completionTokens = tokenUsage.outputTokens;
            const totalTokens = promptTokens + completionTokens;
            const duration_ms = Date.now() - startTime;

            // Calculate cost
            const cost = calculateNovaCost(modelType, promptTokens, completionTokens);

            logger.info(
              `Nova stream complete. Model: ${modelType}, Tokens: ${promptTokens}/${completionTokens}/${totalTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
            );

            // Final chunk with completion info
            yield {
              choices: [
                {
                  delta: { content: '' },
                  index: 0,
                  finish_reason: handler.getStopReason() || 'stop',
                },
              ],
              usage: {
                prompt_tokens: promptTokens,
                completion_tokens: completionTokens,
                total_tokens: totalTokens,
                cost: cost,
                duration_ms: duration_ms,
              },
              model: `nova-${modelType}`,
              cost: cost,
            };
          }
        } catch (e) {
          logger.error('Error processing Nova stream chunk:', e);
        }
      }
    }
  };
}

/**
 * Calculate cost for Nova models (helper function for stream handler)
 */
function calculateNovaCost(modelType: string, promptTokens: number, completionTokens: number): number {
  try {
    // Map Nova model types to model IDs used in config
    const modelIdMap: Record<string, string> = {
      micro: 'nova-micro',
      lite: 'nova-lite',
      pro: 'nova-pro',
      premier: 'nova-premier',
    };

    const configModelId = modelIdMap[modelType.toLowerCase()] || modelType;

    // Get model configuration from config
    const modelConfig = appConfig.getModelById(configModelId);

    // Default costs if model not found in config
    let inputCostPer1k = 0;
    let outputCostPer1k = 0;

    if (modelConfig) {
      // Get pricing from model config
      inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
      outputCostPer1k = parseFloat(modelConfig.output_price) || 0;
    } else {
      logger.warn(
        `No pricing information found for model: ${configModelId} in models.tsv config`,
      );
    }

    // Calculate costs
    const inputCost = (promptTokens / 1000) * inputCostPer1k;
    const outputCost = (completionTokens / 1000) * outputCostPer1k;
    const totalCost = inputCost + outputCost;

    return totalCost;
  } catch (error) {
    logger.error(`Error calculating cost for model ${modelType}:`, error);
    // Return 0 if unable to calculate cost - pricing should be in models.tsv
    return 0;
  }
}
