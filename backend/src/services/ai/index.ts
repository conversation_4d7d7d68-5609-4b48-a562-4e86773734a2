import claudeService from './claude.service';
import geminiService from './gemini.service';
import novaService from './nova.service';
import azureService from './azure.service';
import { BadRequestError } from '../../common/errors/http.error';
import { config } from '../../../config/config';
import { logger } from '../../common/logger';

export type AIProvider = 'claude' | 'gemini' | 'nova' | 'azure';

export interface GenerateTextOptions {
  prompt: string;
  provider?: AIProvider;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
  topP?: number;
  headers?: Record<string, string>;
}

export interface StreamTextOptions extends GenerateTextOptions {
  // Additional streaming-specific options can be added here
  headers?: Record<string, string>;
}

export interface GenerateTextResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    cost: number;
    durationMs: number;
  };
  model: string;
}

/**
 * Helper function to calculate cost based on token usage and model pricing from models.tsv
 */
export function calculateCost(modelId: string, promptTokens: number, completionTokens: number): number {
  // Get model configuration from config
  const modelConfig = config.getModelById(modelId);

  // Default costs if model not found in config
  let inputCostPer1k = 0;
  let outputCostPer1k = 0;

  if (modelConfig) {
    // Get pricing from model config
    inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
    outputCostPer1k = parseFloat(modelConfig.output_price) || 0;
  } else {
    // Fallback to looking up by partial model name
    const models = config.models || [];
    const matchingModel = models.find(
      (model) =>
        modelId.includes(model.model_id) ||
        (model.actual_model_id && modelId.includes(model.actual_model_id)),
    );

    if (matchingModel) {
      inputCostPer1k = parseFloat(matchingModel.input_price) || 0;
      outputCostPer1k = parseFloat(matchingModel.output_price) || 0;
    } else {
      logger.warn(`No pricing information found for model: ${modelId}`);
    }
  }

  // Calculate costs
  const inputCost = (promptTokens / 1000) * inputCostPer1k;
  const outputCost = (completionTokens / 1000) * outputCostPer1k;

  // Return total cost
  return inputCost + outputCost;
}

class AIService {
  /**
   * Generate text using specified AI provider or default
   */
  async generateText({
    prompt,
    provider = 'claude', // Default provider
    model,
    maxTokens = 8192,
    temperature = 0.7,
    systemPrompt,
    topP = 0.9,
  }: GenerateTextOptions): Promise<GenerateTextResponse> {
    // Determine provider from model if not explicitly specified
    let effectiveProvider = provider;

    // If model is specified, try to infer provider from model
    if (model) {
      // Log the model name for debugging
      logger.info(`Determining provider for model in generateText: ${model}`);

      // Force provider based on model name - order matters!
      if (model.includes('gpt') || model.includes('o3') || model.includes('o4')) {
        // Azure models take precedence
        effectiveProvider = 'azure';
        logger.info(`Setting provider to Azure for model: ${model}`);
      } else if (model.includes('claude') || model.includes('anthropic')) {
        effectiveProvider = 'claude';
      } else if (model.includes('gemini')) {
        effectiveProvider = 'gemini';
      } else if (model.includes('nova')) {
        effectiveProvider = 'nova';
      }
    }

    logger.info(
      `Generating text with provider: ${effectiveProvider}, model: ${model || 'default'}`,
    );

    switch (effectiveProvider) {
      case 'claude':
        return claudeService.generateText({
          prompt,
          model: model || 'claude-3-7-sonnet-20250219',
          maxTokens,
          temperature,
        });

      case 'gemini':
        return geminiService.generateText({
          prompt,
          model: model || 'gemini-2.0-flash',
          maxTokens,
          temperature,
          systemPrompt,
        });

      case 'nova':
        // Handle Nova model names - strip 'nova-' prefix if present
        let novaModelType = model || 'pro';
        if (novaModelType.startsWith('nova-')) {
          novaModelType = novaModelType.replace('nova-', '');
        }

        return novaService.generateText({
          prompt,
          modelType: novaModelType,
          maxTokens,
          temperature,
          topP,
          systemPrompt: systemPrompt || 'You are a helpful, harmless, and honest AI assistant.',
        });

      case 'azure':
        // Extract the model type from the full model name if needed
        let azureModelType = model || 'gpt-4.1';
        if (azureModelType.startsWith('azure-')) {
          azureModelType = azureModelType.replace('azure-', '');
        }

        return azureService.invokeModel(prompt, {
          modelType: azureModelType,
          maxTokens,
          temperature,
          systemPrompt,
        });

      default:
        throw new BadRequestError(`Unknown AI provider: ${provider}`);
    }
  }

  /**
   * Stream text using specified AI provider
   * Supports Claude, Gemini, Nova, and Azure providers
   */
  async streamText({
    prompt,
    provider = 'claude', // Default streaming provider (changed from gemini)
    model,
    maxTokens = 8192,
    temperature = 0.7,
    systemPrompt,
    topP = 0.9,
    headers,
  }: StreamTextOptions): Promise<AsyncIterable<any>> {
    // Determine provider from model if not explicitly specified
    let effectiveProvider = provider;

    // If model is specified but provider is not, try to infer provider from model
    if (model) {
      // Log the model name for debugging
      logger.info(`Determining provider for model: ${model}`);

      // Force provider based on model name - order matters!
      if (model.includes('gpt') || model.includes('o3') || model.includes('o4')) {
        // Azure models take precedence
        effectiveProvider = 'azure';
        logger.info(`Setting provider to Azure for model: ${model}`);
      } else if (model.includes('claude') || model.includes('anthropic')) {
        effectiveProvider = 'claude';
      } else if (model.includes('gemini')) {
        effectiveProvider = 'gemini';
      } else if (model.includes('nova')) {
        effectiveProvider = 'nova';
      }
    }

    logger.info(`🔧 [AI-SERVICE] Streaming with provider: ${effectiveProvider}, model: ${model || 'default'}`);
    logger.info(`🔧 [AI-SERVICE] Original provider: ${provider}, effectiveProvider: ${effectiveProvider}`);

    switch (effectiveProvider) {
      case 'gemini':
        // Format messages for Gemini
        const messages = [];
        if (systemPrompt) {
          messages.push({ role: 'system', content: systemPrompt });
        }
        messages.push({ role: 'user', content: prompt });

        return geminiService.streamChatCompletions({
          messages,
          model: model || 'gemini-2.0-flash',
          maxTokens,
          temperature,
        });

      case 'nova':
        // Handle Nova model names - strip 'nova-' prefix if present
        let novaModelType = model || 'pro';
        if (novaModelType.startsWith('nova-')) {
          novaModelType = novaModelType.replace('nova-', '');
        }

        return novaService.streamText(
          {
            prompt,
            modelType: novaModelType,
            maxTokens,
            temperature,
            topP,
            systemPrompt: systemPrompt || 'You are a helpful, harmless, and honest AI assistant.',
          },
          true,
        ); // Transform to OpenAI-compatible format

      case 'claude':
        // Map model names from the original format to the new format
        let modelType = '3.7-sonnet'; // default

        if (model) {
          // Log the model name for debugging
          logger.info(`Determining Claude model type for model: ${model}`);

          if (model.includes('3-5-sonnet') || model.includes('3.5-sonnet')) {
            modelType = '3.5-sonnet';
            logger.info(`Selected Claude model type: 3.5-sonnet`);
          } else if (model.includes('3-5-haiku') || model.includes('3.5-haiku')) {
            modelType = '3.5-haiku';
            logger.info(`Selected Claude model type: 3.5-haiku`);
          } else if (model.includes('3-7-sonnet') || model.includes('3.7-sonnet')) {
            modelType = '3.7-sonnet';
            logger.info(`Selected Claude model type: 3.7-sonnet`);
          } else {
            logger.info(`Using default Claude model type: 3.7-sonnet for model: ${model}`);
          }
        }

        // Use the new streaming implementation
        return claudeService.streamText({
          prompt,
          modelType,
          maxTokens,
          temperature,
          systemPrompt,
        });

      case 'azure':
        // Extract the model type from the full model name if needed
        let azureModelType = model || 'gpt-4.1';
        if (azureModelType.startsWith('azure-')) {
          azureModelType = azureModelType.replace('azure-', '');
        }

        // Use the new streaming implementation
        logger.info(`Using Azure streaming implementation for model: ${azureModelType}`);
        return azureService.streamText(prompt, {
          modelType: azureModelType,
          maxTokens,
          temperature,
          systemPrompt,
        });

      default:
        throw new BadRequestError(`Unknown AI provider for streaming: ${effectiveProvider}`);
    }
  }
}

export default new AIService();
