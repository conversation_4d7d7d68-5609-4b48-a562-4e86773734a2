# Azure OpenAI Service Integration

This file implements the Azure OpenAI service integration for KAPI. It interfaces with Azure OpenAI's API using the latest TypeScript SDK.

## Key Features

- Supports both API key and Azure Identity authentication
- Loads model configurations from TSV file
- Maps model types (o3, o4-mini, gpt-4.1, etc.) to actual deployment IDs
- Respects model-specific features (like temperature support)
- Comprehensive error handling

## Usage

```typescript
import azureService from './services/ai/azure.service';

// Simple usage
const response = await azureService.invokeModel('Explain how LLMs work in simple terms');

// Advanced usage with options
const response = await azureService.invokeModel('Explain quantum computing', {
  modelType: 'gpt-4.1',
  maxTokens: 2000,
  temperature: 0.5,
  systemPrompt: 'You are a quantum physics expert.',
});
```

## Configuration

Requires the following environment variables:

- AZURE_ENDPOINT - Azure OpenAI service endpoint
- AZURE_API_KEY - API key (optional if using Azure Identity)

And a `config/models.tsv` file with model configurations.
