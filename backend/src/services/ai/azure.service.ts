import { AzureOpenAI } from 'openai';
import { config } from '../../../config/config';
import { BadRequestError } from '../../common/errors/http.error';
import { logger } from '../../common/logger';

/**
 * Service for interacting with Azure OpenAI models
 */
class AzureOpenAIService {
  private client!: AzureOpenAI;
  private modelConfigs: Map<string, any> = new Map();
  private embeddingDeployment: string = 'text-embedding-3-small'; // Default embedding model deployment name

  constructor() {
    // Load model configurations directly from config
    this.loadModelConfigs();

    // Get Azure OpenAI settings from environment
    const azureEndpoint = process.env.AZURE_ENDPOINT;
    const azureApiKey = process.env.AZURE_API_KEY;

    // Create Azure OpenAI client with API key
    if (azureApiKey && azureEndpoint) {
      this.client = new AzureOpenAI({
        apiVersion: '2024-12-01-preview',
        endpoint: azureEndpoint,
        apiKey: azureApiKey,
      });
    } else {
      logger.warn('Azure OpenAI endpoint not found in environment');
    }
  }

  /**
   * Load model configurations from config
   */
  private loadModelConfigs(): void {
    try {
      // Use the models from config
      const models = config.models || [];

      for (const model of models) {
        this.modelConfigs.set(model.model_id, model);
      }

      logger.info('Azure model configurations loaded successfully');
    } catch (error) {
      logger.error('Error loading Azure model configs:', error);
    }
  }

  /**
   * Invoke Azure OpenAI model with multimodal support
   */
  async invokeModel(
    prompt: string,
    options: {
      modelType?: string;
      maxTokens?: number;
      temperature?: number;
      systemPrompt?: string;
      images?: Array<{
        type: 'image_url';
        image_url: {
          url: string;
          detail?: 'low' | 'high' | 'auto';
        };
      }>;
    } = {},
  ): Promise<any> {
    try {
      const {
        modelType = 'o3',
        maxTokens = 4000,
        temperature = 0.7,
        systemPrompt = 'You are a helpful assistant.',
        images,
      } = options;

      // Map model type to model_id
      const azureModelMap: Record<string, string> = {
        'gpt-4.1': 'gpt-4.1',
        'gpt-4.1-mini': 'gpt-4.1-mini',
        'gpt-4.1-nano': 'gpt-4.1-nano',
        o3: 'o3',
        'o4-mini': 'o4-mini',
      };

      // Get the correct model_id
      const modelId = azureModelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(azureModelMap).join(', ')}`,
        );
      }

      // Check if model exists in config
      const modelConfig = this.modelConfigs.get(modelId) || config.getModelById(modelId);
      if (!modelConfig) {
        throw new BadRequestError(`Model ID '${modelId}' not found in configuration.`);
      }

      // Get model deployment name
      const deployment = modelConfig.actual_model_id;
      const supportsTemperature = modelConfig.supports_temperature?.toLowerCase() === 'true';

      // Create user message content
      let userContent: any = prompt;
      
      // If images are provided, create multimodal content
      if (images && images.length > 0) {
        userContent = [
          {
            type: 'text',
            text: prompt,
          },
          ...images,
        ];
      }

      // Create chat completion parameters
      const completionParams: any = {
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: userContent,
          },
        ],
        model: deployment,
      };

      // Add temperature parameter only if supported by the model
      if (supportsTemperature) {
        completionParams.temperature = temperature;
      }

      // Add max tokens parameter - use max_completion_tokens instead of max_tokens
      if (maxTokens) {
        completionParams.max_completion_tokens = maxTokens;
      }

      // Record start time
      const startTime = Date.now();

      // Create chat completion
      const response = await this.client.chat.completions.create(completionParams);

      // Extract token usage and calculate cost
      let promptTokens = 0;
      let completionTokens = 0;
      let totalTokens = 0;
      let cost = 0;
      const duration_ms = Date.now() - startTime;

      if (response.usage) {
        promptTokens = response.usage.prompt_tokens || 0;
        completionTokens = response.usage.completion_tokens || 0;
        totalTokens = response.usage.total_tokens || 0;
        cost = AzureOpenAIService.calculateCost(modelId, promptTokens, completionTokens);
        logger.debug(
          `Token usage from Azure non-streaming response: ${JSON.stringify(response.usage)}`,
        );
      } else {
        // Estimate token counts if not provided
        promptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);
        completionTokens = Math.ceil((response.choices[0].message.content?.length || 0) / 4);
        totalTokens = promptTokens + completionTokens;
        cost = AzureOpenAIService.calculateCost(modelId, promptTokens, completionTokens);
        logger.warn(`No token usage from Azure, using estimated values`);
      }

      logger.info(
        `Azure non-streaming complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}/${totalTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
      );

      // Return structured response
      return {
        content: response.choices[0].message.content || '',
        usage: {
          promptTokens,
          completionTokens,
          totalTokens,
          cost,
          durationMs: duration_ms,
        },
        model: modelId,
      };
    } catch (error) {
      logger.error('Error invoking Azure OpenAI model:', error);
      throw new BadRequestError(
        `Failed to generate text with Azure OpenAI: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Stream text from Azure OpenAI models
   * This method converts a non-streaming response to a streaming format
   * for compatibility with other model providers
   */
  async streamText(
    prompt: string,
    options: {
      modelType?: string;
      maxTokens?: number;
      temperature?: number;
      systemPrompt?: string;
    } = {},
  ): Promise<any> {
    try {
      const startTime = Date.now();

      // Get the model configuration
      const {
        modelType = 'o3',
        maxTokens = 4000,
        temperature = 0.7,
        systemPrompt = 'You are a helpful assistant.',
      } = options;

      // Map model type to model_id
      const azureModelMap: Record<string, string> = {
        'gpt-4.1': 'gpt-4.1',
        'gpt-4.1-mini': 'gpt-4.1-mini',
        'gpt-4.1-nano': 'gpt-4.1-nano',
        o3: 'o3',
        'o4-mini': 'o4-mini',
      };

      // Get the correct model_id
      const modelId = azureModelMap[modelType.toLowerCase()];
      if (!modelId) {
        throw new BadRequestError(
          `Invalid model type: ${modelType}. Must be one of: ${Object.keys(azureModelMap).join(', ')}`,
        );
      }

      // Check if model exists in config
      const modelConfig = this.modelConfigs.get(modelId) || config.getModelById(modelId);
      if (!modelConfig) {
        throw new BadRequestError(`Model ID '${modelId}' not found in configuration.`);
      }

      // Get model deployment name
      const deployment = modelConfig.actual_model_id;
      const supportsTemperature = modelConfig.supports_temperature?.toLowerCase() === 'true';

      logger.info(`Streaming Azure model ${modelId} (deployment: ${deployment})`);

      // Try to use streaming if available
      try {
        // Create streaming chat completion parameters
        const streamingParams: any = {
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          model: deployment,
          stream: true,
        };

        // Add temperature parameter only if supported by the model
        if (supportsTemperature) {
          streamingParams.temperature = temperature;
        }

        // Add max tokens parameter - use max_completion_tokens instead of max_tokens
        if (maxTokens) {
          streamingParams.max_completion_tokens = maxTokens;
        }

        // Create streaming chat completion
        const stream = await this.client.chat.completions.create(streamingParams);

        // Calculate approximate token counts for input
        // This is a rough estimate - 1 token is approximately 4 characters for English text
        const estimatedPromptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);

        // Create a wrapper around the stream to add token usage information
        let fullContent = '';
        let isFirstChunk = true;

        // Create a wrapped stream that adds token usage information
        // We need to use any here because the stream type is complex
        const wrappedStream: any = {
          [Symbol.asyncIterator]() {
            // Cast the stream to any to access the asyncIterator
            const iterator = (stream as any)[Symbol.asyncIterator]();

            return {
              next: async (): Promise<{ done: boolean; value: any }> => {
                try {
                  const result = await iterator.next();

                  if (result.done) {
                    // When the stream is done, add a final chunk with token usage information
                    let promptTokens = estimatedPromptTokens;
                    let completionTokens = Math.ceil(fullContent.length / 4);
                    let totalTokens = promptTokens + completionTokens;

                    // Try to get accurate token counts from the final chunk
                    if (result.value && result.value.usage) {
                      // Extract token usage from the final chunk if available
                      promptTokens = result.value.usage.prompt_tokens || promptTokens;
                      completionTokens = result.value.usage.completion_tokens || completionTokens;
                      totalTokens =
                        result.value.usage.total_tokens || promptTokens + completionTokens;
                      logger.info(
                        `Got actual token usage from Azure streaming API: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
                      );
                    } else {
                      // If no token usage information is available, make a separate API call to get accurate counts
                      try {
                        // Create a separate API call to get token counts
                        logger.info(
                          `No token usage from Azure streaming API, making separate API call to get accurate counts`,
                        );

                        // Create a copy of the streaming parameters without the stream flag
                        const nonStreamingParams = {
                          messages: streamingParams.messages,
                          model: streamingParams.model,
                          max_tokens: streamingParams.max_tokens,
                          temperature: streamingParams.temperature,
                          stream: false,
                        };

                        // Make a direct API call to Azure OpenAI
                        const response = await fetch(
                          `${process.env.AZURE_ENDPOINT}/openai/deployments/${modelId}/chat/completions?api-version=2024-06-01`,
                          {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                              'api-key': process.env.AZURE_API_KEY || '',
                            },
                            body: JSON.stringify(nonStreamingParams),
                          },
                        );

                        if (response.ok) {
                          const data = await response.json() as any;
                          if (data.usage) {
                            promptTokens = data.usage.prompt_tokens;
                            completionTokens = data.usage.completion_tokens;
                            totalTokens = data.usage.total_tokens;
                            logger.info(
                              `Got actual token usage from Azure API: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
                            );
                          } else {
                            logger.warn(
                              `No token usage in Azure API response, using estimated values: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
                            );
                          }
                        } else {
                          logger.warn(
                            `Error response from Azure API: ${response.status} ${response.statusText}, using estimated values`,
                          );
                        }
                      } catch (error) {
                        logger.warn(
                          `Error getting token counts from separate API call: ${error}, using estimated values`,
                        );
                      }
                    }

                    // Calculate cost based on token usage
                    // Use the static method
                    const cost = AzureOpenAIService.calculateCost(
                      modelId,
                      promptTokens,
                      completionTokens,
                    );

                    // Calculate duration in milliseconds
                    const duration_ms = Date.now() - startTime;

                    logger.info(
                      `Azure stream complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
                    );

                    return {
                      done: true,
                      value: {
                        choices: [],
                        usage: {
                          prompt_tokens: promptTokens,
                          completion_tokens: completionTokens,
                          total_tokens: totalTokens,
                          cost: cost,
                          duration_ms: duration_ms,
                        },
                        model: modelId,
                        cost: cost,
                      },
                    };
                  }

                  // Process the chunk
                  const chunk = result.value;

                  // If this is the first chunk, add model information
                  if (isFirstChunk) {
                    isFirstChunk = false;
                    chunk.model = modelId;
                  }

                  // Extract content from the chunk to build the full response
                  if (
                    chunk.choices &&
                    chunk.choices[0] &&
                    chunk.choices[0].delta &&
                    chunk.choices[0].delta.content
                  ) {
                    fullContent += chunk.choices[0].delta.content;
                  }

                  // Extract token usage information if available
                  if (chunk.usage) {
                    logger.debug(`Received token usage from Azure: ${JSON.stringify(chunk.usage)}`);
                  }

                  return result;
                } catch (error) {
                  logger.error('Error processing Azure stream chunk:', error);
                  throw error;
                }
              },
            };
          },
        };

        return wrappedStream;
      } catch (streamError) {
        // If streaming fails, fall back to non-streaming and simulate a stream
        logger.warn(
          `Streaming not supported for Azure model ${modelId}, falling back to non-streaming: ${streamError}`,
        );

        // Create non-streaming chat completion parameters
        const completionParams: any = {
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          model: deployment,
        };

        // Add temperature parameter only if supported by the model
        if (supportsTemperature) {
          completionParams.temperature = temperature;
        }

        // Add max tokens parameter - use max_completion_tokens instead of max_tokens
        if (maxTokens) {
          completionParams.max_completion_tokens = maxTokens;
        }

        // Create chat completion to get the full response and token usage
        const completionResponse = await this.client.chat.completions.create(completionParams);

        if (!completionResponse || !completionResponse.choices || !completionResponse.choices[0]) {
          throw new Error('No response from Azure OpenAI model');
        }

        const response = completionResponse.choices[0].message.content || '';

        // Try to get actual token usage from the API response
        let promptTokens = 0;
        let completionTokens = 0;
        let totalTokens = 0;

        if (completionResponse.usage) {
          promptTokens = completionResponse.usage.prompt_tokens || 0;
          completionTokens = completionResponse.usage.completion_tokens || 0;
          totalTokens = completionResponse.usage.total_tokens || 0;

          logger.info(
            `Got actual token usage from Azure API: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
          );
        } else {
          // Try to make a separate API call to get accurate token counts
          try {
            logger.info(
              `No token usage in response, making separate API call to get accurate counts`,
            );

            // Create a copy of the completion parameters
            const nonStreamingParams = {
              messages: completionParams.messages,
              model: completionParams.model,
              max_tokens: completionParams.max_tokens,
              temperature: completionParams.temperature,
              stream: false,
            };

            // Make a direct API call to Azure OpenAI
            const apiResponse = await fetch(
              `${process.env.AZURE_ENDPOINT}/openai/deployments/${modelId}/chat/completions?api-version=2024-06-01`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'api-key': process.env.AZURE_API_KEY || '',
                },
                body: JSON.stringify(nonStreamingParams),
              },
            );

            if (apiResponse.ok) {
              const data = await apiResponse.json() as any;
              if (data.usage) {
                promptTokens = data.usage.prompt_tokens;
                completionTokens = data.usage.completion_tokens;
                totalTokens = data.usage.total_tokens;
                logger.info(
                  `Got actual token usage from Azure API: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
                );
              } else {
                // Fall back to estimation if still no usage information is available
                promptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);
                completionTokens = Math.ceil(response.length / 4);
                totalTokens = promptTokens + completionTokens;
                logger.warn(
                  `No token usage from Azure API, using estimated values: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
                );
              }
            } else {
              // Fall back to estimation if the API call fails
              promptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);
              completionTokens = Math.ceil(response.length / 4);
              totalTokens = promptTokens + completionTokens;
              logger.warn(
                `Error response from Azure API: ${apiResponse.status} ${apiResponse.statusText}, using estimated values`,
              );
            }
          } catch (error) {
            // Fall back to estimation if the separate request fails
            promptTokens = Math.ceil((prompt.length + systemPrompt.length) / 4);
            completionTokens = Math.ceil(response.length / 4);
            totalTokens = promptTokens + completionTokens;
            logger.warn(
              `Error getting token counts from separate request: ${error}, using estimated values: prompt=${promptTokens}, completion=${completionTokens}, total=${totalTokens}`,
            );
          }
        }

        // Calculate cost based on token usage
        const cost = AzureOpenAIService.calculateCost(modelId, promptTokens, completionTokens);

        // Calculate duration in milliseconds
        const duration_ms = Date.now() - startTime;

        logger.info(
          `Azure non-streaming complete. Model: ${modelId}, Tokens: ${promptTokens}/${completionTokens}, Cost: ${cost}, Duration: ${duration_ms}ms`,
        );

        // Create a simulated stream from the response
        // Break the response into chunks of approximately 20 characters
        const chunks: string[] = [];
        const chunkSize = 20;
        for (let i = 0; i < response.length; i += chunkSize) {
          chunks.push(response.substring(i, i + chunkSize));
        }

        // Create an async generator that yields chunks
        const simulatedStream: any = {
          [Symbol.asyncIterator]() {
            let index = 0;

            return {
              next: async (): Promise<{ done: boolean; value: any }> => {
                // Add a small delay to simulate streaming
                await new Promise((resolve) => setTimeout(resolve, 10));

                if (index < chunks.length) {
                  const chunk = chunks[index++];
                  return {
                    done: false,
                    value: {
                      choices: [
                        {
                          delta: {
                            content: chunk,
                          },
                          index: 0,
                          finish_reason: null,
                        },
                      ],
                      model: modelId,
                    },
                  };
                } else {
                  // Final chunk with usage information
                  return {
                    done: true,
                    value: {
                      choices: [],
                      usage: {
                        prompt_tokens: promptTokens,
                        completion_tokens: completionTokens,
                        total_tokens: totalTokens,
                        cost: cost,
                        duration_ms: duration_ms,
                      },
                      model: modelId,
                      cost: cost,
                    },
                  };
                }
              },
            };
          },
        };

        return simulatedStream;
      }
    } catch (error) {
      logger.error('Error streaming Azure OpenAI model:', error);
      throw new BadRequestError(
        `Failed to stream text with Azure OpenAI: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Generate embeddings for text using Azure OpenAI
   * @param texts Array of strings to embed
   * @param options Optional parameters for embedding generation
   * @returns Array of embedding vectors
   */
  async generateEmbeddings(
    texts: string | string[],
    options: {
      model?: string;
      deployment?: string;
    } = {},
  ): Promise<{
    embeddings: number[][];
    usage: {
      promptTokens: number;
      totalTokens: number;
      cost: number;
      durationMs: number;
    };
    model: string;
  }> {
    try {
      // Ensure texts is an array
      const inputTexts = Array.isArray(texts) ? texts : [texts];
      
      // Validate input
      if (inputTexts.length === 0) {
        throw new BadRequestError('No texts provided for embedding generation');
      }

      // Use provided deployment or fallback to default
      const deployment = options.deployment || this.embeddingDeployment;
      const modelName = options.model || 'text-embedding-3-small';

      logger.info(`Generating embeddings for ${inputTexts.length} texts using deployment: ${deployment}`);

      // Record start time
      const startTime = Date.now();

      // Create embeddings request
      const response = await this.client.embeddings.create({
        input: inputTexts,
        model: modelName, // This is the model name, not deployment name
      });

      // Extract embeddings
      const embeddings: number[][] = response.data.map(item => item.embedding);

      // Calculate token usage and cost
      let promptTokens = 0;
      let totalTokens = 0;
      let cost = 0;
      const durationMs = Date.now() - startTime;

      if (response.usage) {
        promptTokens = response.usage.prompt_tokens || 0;
        totalTokens = response.usage.total_tokens || 0;
        
        // Calculate cost for embeddings (using text-embedding-3-small pricing)
        // Azure pricing for text-embedding-3-small: $0.00002 per 1K tokens
        const costPer1kTokens = 0.00002;
        cost = (totalTokens / 1000) * costPer1kTokens;
        
        logger.debug(`Embedding token usage: ${JSON.stringify(response.usage)}`);
      } else {
        // Estimate tokens if not provided (rough estimate: 1 token per 4 characters)
        const totalChars = inputTexts.reduce((sum, text) => sum + text.length, 0);
        promptTokens = Math.ceil(totalChars / 4);
        totalTokens = promptTokens;
        cost = (totalTokens / 1000) * 0.00002;
        
        logger.warn('No token usage from Azure embeddings, using estimated values');
      }

      logger.info(
        `Azure embeddings complete. Model: ${modelName}, Texts: ${inputTexts.length}, ` +
        `Tokens: ${totalTokens}, Cost: ${cost.toFixed(6)}, Duration: ${durationMs}ms`
      );

      return {
        embeddings,
        usage: {
          promptTokens,
          totalTokens,
          cost,
          durationMs,
        },
        model: modelName,
      };
    } catch (error) {
      logger.error('Error generating embeddings with Azure OpenAI:', error);
      throw new BadRequestError(
        `Failed to generate embeddings with Azure OpenAI: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Generate embeddings for a single text (convenience method)
   * @param text String to embed
   * @param options Optional parameters for embedding generation
   * @returns Single embedding vector
   */
  async generateEmbedding(
    text: string,
    options: {
      model?: string;
      deployment?: string;
    } = {},
  ): Promise<{
    embedding: number[];
    usage: {
      promptTokens: number;
      totalTokens: number;
      cost: number;
      durationMs: number;
    };
    model: string;
  }> {
    const result = await this.generateEmbeddings(text, options);
    return {
      embedding: result.embeddings[0],
      usage: result.usage,
      model: result.model,
    };
  }

  /**
   * Process mockup images and return updated mockups
   * Specialized method for UI/UX mockup enhancement and iteration
   */
  async processMockupImages(
    inputImages: Array<{
      url: string;
      description?: string;
    }>,
    instructions: string,
    options: {
      modelType?: string;
      maxTokens?: number;
      temperature?: number;
      outputFormat?: 'description' | 'code' | 'both';
      designStyle?: string;
      targetPlatform?: 'web' | 'mobile' | 'desktop';
    } = {},
  ): Promise<{
    analysis: string;
    recommendations: string[];
    updatedMockup?: {
      description: string;
      components: Array<{
        type: string;
        properties: Record<string, any>;
        position: { x: number; y: number; width: number; height: number };
      }>;
      codeSnippet?: string;
      improvedSvg?: string;
    };
    usage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
      cost: number;
      durationMs: number;
    };
    model: string;
  }> {
    try {
      const {
        modelType = 'gpt-4.1',
        maxTokens = 8000,
        temperature = 0.7,
        outputFormat = 'both',
        designStyle = 'modern',
        targetPlatform = 'web',
      } = options;

      // Convert images to the format expected by the model
      const imageInputs = inputImages.map((img) => ({
        type: 'image_url' as const,
        image_url: {
          url: img.url,
          detail: 'high' as const,
        },
      }));

      // Create comprehensive system prompt for mockup processing
      const systemPrompt = `You are an expert UI/UX designer and developer specializing in mockup analysis and enhancement. 

Your task is to analyze provided mockup images and provide:
1. Detailed analysis of the current design
2. Specific improvement recommendations
3. Updated mockup description with precise component specifications
4. ${outputFormat === 'code' || outputFormat === 'both' ? 'Implementation code when requested' : ''}

Design Context:
- Style: ${designStyle}
- Platform: ${targetPlatform}
- Output Format: ${outputFormat}

For each mockup, analyze:
- Layout and composition
- Color scheme and typography
- User experience flow
- Accessibility considerations
- Component structure and hierarchy
- Interactive elements and states

Provide responses in structured JSON format with clear, actionable recommendations.`;

      // Create detailed prompt for mockup analysis
      const analysisPrompt = `Please analyze the provided mockup image(s) and respond with the following improvements:

${instructions}

Additional context for each image:
${inputImages.map((img, index) => `Image ${index + 1}: ${img.description || 'No description provided'}`).join('\n')}

Please respond in the following JSON structure:
{
  "analysis": "Detailed analysis of the current mockup design, layout, and user experience",
  "recommendations": [
    "Specific improvement recommendation 1",
    "Specific improvement recommendation 2",
    "..."
  ],
  "updatedMockup": {
    "description": "Detailed description of the improved mockup",
    "components": [
      {
        "type": "component_type (e.g., button, input, card, header)",
        "properties": {
          "text": "component text",
          "style": "styling properties",
          "variant": "component variant",
          "state": "component state"
        },
        "position": {
          "x": 0,
          "y": 0,
          "width": 100,
          "height": 50
        }
      }
    ]${outputFormat === 'code' || outputFormat === 'both' ? ',\n    "codeSnippet": "Implementation code (HTML, CSS, React, etc.)"' : ''},
    "improvedSvg": "Complete SVG markup of the improved mockup design with proper dimensions, colors, and layout. Include all UI elements with realistic styling."
  }
}

Focus on practical, implementable improvements that enhance usability, accessibility, and visual appeal.`;

      logger.info(`[Mockup Processing] Analyzing ${inputImages.length} mockup image(s) with model ${modelType}`);

      // Process the images using the multimodal model
      const result = await this.invokeModel(analysisPrompt, {
        modelType,
        maxTokens,
        temperature,
        systemPrompt,
        images: imageInputs,
      });

      // Try to parse the response as JSON
      // Log the full raw response for debugging
      logger.info(`[Mockup Processing] RAW AI RESPONSE START:`);
      logger.info(result.content);
      logger.info(`[Mockup Processing] RAW AI RESPONSE END`);

      let parsedResponse: any;
      try {
        // Clean the response content to extract JSON
        const cleanContent = result.content.replace(/```json\n?|\n?```/g, '').trim();
        parsedResponse = JSON.parse(cleanContent);
        
        // Log the parsed response structure
        logger.info(`[Mockup Processing] PARSED JSON RESPONSE:`);
        logger.info(JSON.stringify(parsedResponse, null, 2));
        
        // Specifically log the SVG if present
        if (parsedResponse.updatedMockup?.improvedSvg) {
          logger.info(`[Mockup Processing] SVG GENERATED (${parsedResponse.updatedMockup.improvedSvg.length} chars):`);
          logger.info(parsedResponse.updatedMockup.improvedSvg);
        } else {
          logger.warn(`[Mockup Processing] No SVG found in response`);
        }
        
      } catch (parseError) {
        logger.warn('[Mockup Processing] Failed to parse JSON response, using text analysis');
        logger.error(`[Mockup Processing] Parse error: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        
        // Fallback: create structured response from text
        parsedResponse = {
          analysis: result.content,
          recommendations: [
            "Review the AI analysis above for detailed recommendations",
            "Consider implementing suggested improvements incrementally",
            "Test changes with target users for validation"
          ],
          updatedMockup: {
            description: "AI analysis provided in text format. Please review the full response for detailed mockup improvements.",
            components: [],
          }
        };
      }

      // Ensure required fields exist
      const response = {
        analysis: parsedResponse.analysis || result.content,
        recommendations: parsedResponse.recommendations || [],
        updatedMockup: {
          description: parsedResponse.updatedMockup?.description || "Analysis provided in text format",
          components: parsedResponse.updatedMockup?.components || [],
          codeSnippet: parsedResponse.updatedMockup?.codeSnippet || undefined,
          improvedSvg: parsedResponse.updatedMockup?.improvedSvg || undefined,
        },
        usage: result.usage,
        model: result.model,
      };

      logger.info(
        `[Mockup Processing] Successfully processed mockups. ` +
        `Recommendations: ${response.recommendations.length}, ` +
        `Components: ${response.updatedMockup?.components?.length || 0}, ` +
        `Cost: $${result.usage.cost.toFixed(6)}`
      );

      return response;
    } catch (error) {
      logger.error('[Mockup Processing] Error processing mockup images:', error);
      throw new BadRequestError(
        `Failed to process mockup images: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Analyze single image with custom prompt
   * General purpose image analysis method
   */
  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options: {
      modelType?: string;
      maxTokens?: number;
      temperature?: number;
      systemPrompt?: string;
      detail?: 'low' | 'high' | 'auto';
    } = {},
  ): Promise<{
    content: string;
    usage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
      cost: number;
      durationMs: number;
    };
    model: string;
  }> {
    try {
      const {
        modelType = 'gpt-4.1',
        maxTokens = 4000,
        temperature = 0.7,
        systemPrompt = 'You are a helpful assistant that can analyze images in detail.',
        detail = 'high',
      } = options;

      logger.info(`[Image Analysis] Analyzing image with model ${modelType}`);

      // Process the image using the multimodal model
      const result = await this.invokeModel(prompt, {
        modelType,
        maxTokens,
        temperature,
        systemPrompt,
        images: [{
          type: 'image_url',
          image_url: {
            url: imageUrl,
            detail,
          },
        }],
      });

      logger.info(
        `[Image Analysis] Successfully analyzed image. ` +
        `Cost: $${result.usage.cost.toFixed(6)}, Duration: ${result.usage.durationMs}ms`
      );

      return result;
    } catch (error) {
      logger.error('[Image Analysis] Error analyzing image:', error);
      throw new BadRequestError(
        `Failed to analyze image: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Calculate cost for Azure models based on pricing from models.tsv
   * Static method to avoid 'this' context issues in async iterators
   */
  private static calculateCost(
    modelId: string,
    promptTokens: number,
    completionTokens: number,
  ): number {
    try {
      // Get model configuration from config directly
      const modelConfig = config.getModelById(modelId);

      // Default costs if model not found in config
      let inputCostPer1k = 0;
      let outputCostPer1k = 0;

      if (modelConfig) {
        // Get pricing from model config
        inputCostPer1k = parseFloat(modelConfig.input_price) || 0;
        outputCostPer1k = parseFloat(modelConfig.output_price) || 0;

        logger.debug(
          `Found pricing for ${modelId}: input=${inputCostPer1k}, output=${outputCostPer1k} per 1k tokens`,
        );
      } else {
        logger.warn(
          `No pricing information found for model: ${modelId} in models.tsv config`,
        );
      }

      // Calculate costs
      const inputCost = (promptTokens / 1000) * inputCostPer1k;
      const outputCost = (completionTokens / 1000) * outputCostPer1k;
      const totalCost = inputCost + outputCost;

      logger.debug(
        `Cost calculation for ${modelId}: input=${inputCost.toFixed(6)}, output=${outputCost.toFixed(6)}, total=${totalCost.toFixed(6)}`,
      );

      // Return total cost
      return totalCost;
    } catch (error) {
      logger.error(`Error calculating cost for model ${modelId}:`, error);
      // Return 0 if unable to calculate cost - pricing should be in models.tsv
      return 0;
    }
  }
}

export default new AzureOpenAIService();
