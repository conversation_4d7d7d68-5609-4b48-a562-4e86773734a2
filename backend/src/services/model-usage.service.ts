/**
 * Service for tracking and managing LLM model usage
 */
import { logger } from '../common/logger';
import { prisma } from '../db/client';
import { model_usage as ModelUsage, Provider, TaskType, PrismaClient } from '../generated/prisma';

class ModelUsageService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * Log LLM usage for tracking and billing purposes
   */
  async logLlmUsage(data: {
    userId: number;
    modelRequested: string;
    modelUsed: string;
    taskType: string;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    estimatedCost?: number;
    processingTime?: number;
    status: string;
    errorMessage?: string;
    provider?: Provider;
    metadata?: any;
  }): Promise<ModelUsage> {
    try {
      // Calculate total tokens if not provided but individual counts are
      const totalTokens =
        data.totalTokens ||
        (data.promptTokens && data.completionTokens
          ? data.promptTokens + data.completionTokens
          : 0);

      // Determine provider from model name if not specified
      const provider = data.provider || this.determineProviderFromModel(data.modelUsed);

      // Create the model usage record
      return await this.prisma.model_usage.create({
        data: {
          user_id: data.userId,
          model_name: data.modelUsed,
          provider: provider,
          prompt_tokens: data.promptTokens || 0,
          completion_tokens: data.completionTokens || 0,
          total_tokens: totalTokens,
          estimated_cost:
            data.estimatedCost ||
            this.calculateEstimatedCost(
              data.modelUsed,
              data.promptTokens || 0,
              data.completionTokens || 0,
            ),
          response_time: data.processingTime ? Math.round(data.processingTime) : null,
          taskType: this.convertTaskType(data.taskType),
          success: data.status === 'success',
          error_message: data.errorMessage,
          timestamp: new Date(),
          metadata: data.metadata || {},
        },
      });
    } catch (error) {
      logger.error('Error logging LLM usage', error);
      throw error;
    }
  }

  /**
   * Get model usage statistics for a user within a date range
   */
  async getUserUsageStatistics(
    userId: number,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalTokens: number;
    totalCost: number;
    successCount: number;
    failureCount: number;
    modelBreakdown: Record<
      string,
      {
        tokens: number;
        cost: number;
        count: number;
      }
    >;
    taskTypeBreakdown: Record<
      string,
      {
        tokens: number;
        cost: number;
        count: number;
      }
    >;
  }> {
    try {
      // Set default dates if not provided
      const now = new Date();
      const start = startDate || new Date(now.getFullYear(), now.getMonth(), 1); // Start of current month
      const end = endDate || now;

      // Get all user's usage records in the date range
      const usageRecords = await this.prisma.model_usage.findMany({
        where: {
          user_id: userId,
          timestamp: {
            gte: start,
            lte: end,
          },
        },
      });

      // Initialize result object
      const result = {
        totalTokens: 0,
        totalCost: 0,
        successCount: 0,
        failureCount: 0,
        modelBreakdown: {} as Record<string, { tokens: number; cost: number; count: number }>,
        taskTypeBreakdown: {} as Record<string, { tokens: number; cost: number; count: number }>,
      };

      // Process each usage record
      usageRecords.forEach((record) => {
        // Update totals
        result.totalTokens += record.total_tokens;
        result.totalCost += record.estimated_cost;

        if (record.success) {
          result.successCount++;
        } else {
          result.failureCount++;
        }

        // Update model breakdown
        if (!result.modelBreakdown[record.model_name]) {
          result.modelBreakdown[record.model_name] = { tokens: 0, cost: 0, count: 0 };
        }
        result.modelBreakdown[record.model_name].tokens += record.total_tokens;
        result.modelBreakdown[record.model_name].cost += record.estimated_cost;
        result.modelBreakdown[record.model_name].count++;

        // Update task type breakdown
        const taskType = record.taskType || 'unknown';
        if (!result.taskTypeBreakdown[taskType]) {
          result.taskTypeBreakdown[taskType] = { tokens: 0, cost: 0, count: 0 };
        }
        result.taskTypeBreakdown[taskType].tokens += record.total_tokens;
        result.taskTypeBreakdown[taskType].cost += record.estimated_cost;
        result.taskTypeBreakdown[taskType].count++;
      });

      return result;
    } catch (error) {
      logger.error('Error getting user usage statistics', error);
      throw error;
    }
  }

  /**
   * Update user's daily token quota and usage
   */
  async updateUserDailyTokenUsage(userId: number, tokensUsed: number): Promise<void> {
    try {
      // Get current user data
      const user = await this.prisma.users.findUnique({
        where: { id: userId },
        select: {
          daily_llm_token_usage: true,
          daily_llm_token_quota: true,
          quota_reset_date: true,
        },
      });

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      // Check if we need to reset quota (new day)
      const today = new Date();
      const resetDate = new Date(user.quota_reset_date);

      // If it's a new day, reset usage
      if (
        today.getDate() !== resetDate.getDate() ||
        today.getMonth() !== resetDate.getMonth() ||
        today.getFullYear() !== resetDate.getFullYear()
      ) {
        await this.prisma.users.update({
          where: { id: userId },
          data: {
            daily_llm_token_usage: tokensUsed,
            quota_reset_date: today,
          },
        });
      } else {
        // Otherwise update the current usage
        await this.prisma.users.update({
          where: { id: userId },
          data: {
            daily_llm_token_usage: { increment: tokensUsed },
          },
        });
      }
    } catch (error) {
      logger.error('Error updating daily token usage', error);
      throw error;
    }
  }

  /**
   * Check if a user has exceeded their daily token quota
   */
  async hasUserExceededDailyQuota(userId: number): Promise<boolean> {
    try {
      const user = await this.prisma.users.findUnique({
        where: { id: userId },
        select: {
          daily_llm_token_usage: true,
          daily_llm_token_quota: true,
          quota_reset_date: true,
          role: true,
        },
      });

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      // Check if we need to reset quota (new day)
      const today = new Date();
      const resetDate = new Date(user.quota_reset_date);

      // If it's a new day, reset usage
      if (
        today.getDate() !== resetDate.getDate() ||
        today.getMonth() !== resetDate.getMonth() ||
        today.getFullYear() !== resetDate.getFullYear()
      ) {
        await this.prisma.users.update({
          where: { id: userId },
          data: {
            daily_llm_token_usage: 0,
            quota_reset_date: today,
          },
        });

        return false; // After reset, user hasn't exceeded quota
      }

      // Admin users bypass quota checks
      if (user.role === 'ADMIN') {
        return false;
      }

      // Check if usage exceeds quota
      return user.daily_llm_token_usage >= user.daily_llm_token_quota;
    } catch (error) {
      logger.error('Error checking daily token quota', error);
      throw error;
    }
  }

  /**
   * Helper function to determine provider from model name
   */
  private determineProviderFromModel(modelName: string): Provider {
    const lowerCaseModel = modelName.toLowerCase();

    if (lowerCaseModel.includes('claude')) {
      return Provider.bedrock;
    } else if (lowerCaseModel.includes('gpt') || lowerCaseModel.includes('open')) {
      return Provider.azure;
    } else if (lowerCaseModel.includes('gemini') || lowerCaseModel.includes('palm')) {
      return Provider.google;
    } else {
      return Provider.other;
    }
  }

  /**
   * Helper function to convert task type string to enum
   */
  private convertTaskType(taskType: string): TaskType | undefined {
    switch (taskType.toLowerCase()) {
      case 'chat':
        return TaskType.chat;
      case 'code_review':
        return TaskType.code_review;
      case 'code_gen_big':
        return TaskType.code_gen_big;
      case 'code_gen_agentic':
        return TaskType.code_gen_agentic;
      case 'svg_mockup':
        return TaskType.svg_mockup;
      case 'slides':
        return TaskType.slides;
      case 'test_cases':
        return TaskType.test_cases;
      case 'general':
        return TaskType.general;
      default:
        return undefined;
    }
  }

  /**
   * Helper function to calculate estimated cost based on model and tokens
   * Using approximate market rates
   */
  private calculateEstimatedCost(
    model: string,
    promptTokens: number,
    completionTokens: number,
  ): number {
    const lowerCaseModel = model.toLowerCase();
    let promptRate = 0;
    let completionRate = 0;

    // Claude models
    if (lowerCaseModel.includes('claude-3-opus') || lowerCaseModel.includes('claude-3.5-opus')) {
      promptRate = 15.0 / 1000000; // $15 per million tokens
      completionRate = 75.0 / 1000000; // $75 per million tokens
    } else if (
      lowerCaseModel.includes('claude-3-sonnet') ||
      lowerCaseModel.includes('claude-3.7-sonnet')
    ) {
      promptRate = 3.0 / 1000000; // $3 per million tokens
      completionRate = 15.0 / 1000000; // $15 per million tokens
    } else if (lowerCaseModel.includes('claude-3-haiku')) {
      promptRate = 0.25 / 1000000; // $0.25 per million tokens
      completionRate = 1.25 / 1000000; // $1.25 per million tokens
    }
    // GPT models
    else if (lowerCaseModel.includes('gpt-4-turbo')) {
      promptRate = 10.0 / 1000000; // $10 per million tokens
      completionRate = 30.0 / 1000000; // $30 per million tokens
    } else if (lowerCaseModel.includes('gpt-4')) {
      promptRate = 30.0 / 1000000; // $30 per million tokens
      completionRate = 60.0 / 1000000; // $60 per million tokens
    } else if (lowerCaseModel.includes('gpt-3.5')) {
      promptRate = 0.5 / 1000000; // $0.5 per million tokens
      completionRate = 1.5 / 1000000; // $1.5 per million tokens
    }
    // Gemini models
    else if (lowerCaseModel.includes('gemini-pro')) {
      promptRate = 0.5 / 1000000; // $0.5 per million tokens
      completionRate = 1.5 / 1000000; // $1.5 per million tokens
    } else if (lowerCaseModel.includes('gemini-ultra')) {
      promptRate = 10.0 / 1000000; // $10 per million tokens
      completionRate = 30.0 / 1000000; // $30 per million tokens
    }
    // Default to a low rate for unknown models
    else {
      promptRate = 1.0 / 1000000; // $1 per million tokens
      completionRate = 2.0 / 1000000; // $2 per million tokens
    }

    return promptTokens * promptRate + completionTokens * completionRate;
  }
}

// Export a singleton instance
export const modelUsageService = new ModelUsageService();
export default modelUsageService;
