/**
 * DTOs for social-related operations
 */

export interface CreateChannelDto {
  name: string;
  description?: string;
  isPrivate?: boolean;
  memberIds?: number[];
}

export interface UpdateChannelDto {
  name?: string;
  description?: string;
  isPrivate?: boolean;
}

export interface ChannelMemberDto {
  userId: number;
}

export interface ChannelFilterDto {
  skip?: number;
  take?: number;
}

export interface CreateMessageDto {
  content: string;
  parentId?: number;
  attachments?: string[];
}

export interface UpdateMessageDto {
  content?: string;
  attachments?: string[];
}

export interface MessageReactionDto {
  emoji: string;
}

export interface MessageFilterDto {
  skip?: number;
  take?: number;
  parentOnly?: boolean;
}

export interface NotificationFilterDto {
  skip?: number;
  take?: number;
  unreadOnly?: boolean;
}
