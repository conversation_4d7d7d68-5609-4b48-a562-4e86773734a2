/**
 * Application types and interfaces for dependency injection
 */

export const TYPES = {
  // Services
  DatabaseService: Symbol.for('DatabaseService'),
  PrismaService: Symbol.for('PrismaService'),
  PaymentService: Symbol.for('PaymentService'),
  SubscriptionService: Symbol.for('SubscriptionService'),
  EnhancedQAService: Symbol.for('EnhancedQAService'),

  // New split social services
  ChannelService: Symbol.for('ChannelService'),
  MessageService: Symbol.for('MessageService'),
  NotificationService: Symbol.for('NotificationService'),

  // Legacy service - keep temporarily during migration
  EnhancedSocialService: Symbol.for('EnhancedSocialService'),

  // Repositories
  BaseRepository: Symbol.for('BaseRepository'),
  UserRepository: Symbol.for('UserRepository'),
  ProjectRepository: Symbol.for('ProjectRepository'),
  ConversationRepository: Symbol.for('ConversationRepository'),

  // New AI Repositories
  AIAgentRepository: Symbol.for('AIAgentRepository'),
  AIPromptTemplateRepository: Symbol.for('AIPromptTemplateRepository'),

  // New Workshop Repositories
  WorkshopRepository: Symbol.for('WorkshopRepository'),
  ModuleRepository: Symbol.for('ModuleRepository'),

  // New Gamification Repositories
  EloHistoryRepository: Symbol.for('EloHistoryRepository'),
  KarmaRepository: Symbol.for('KarmaRepository'),

  // New Template Repositories
  TemplateRepository: Symbol.for('TemplateRepository'),
  TemplateFileRepository: Symbol.for('TemplateFileRepository'),
  TemplateVariableRepository: Symbol.for('TemplateVariableRepository'),
  TemplateCollectionRepository: Symbol.for('TemplateCollectionRepository'),
  ProjectTemplateRepository: Symbol.for('ProjectTemplateRepository'),

  // Payment Repositories
  PaymentRepository: Symbol.for('PaymentRepository'),
  SubscriptionRepository: Symbol.for('SubscriptionRepository'),

  // QA Repositories
  QuestionRepository: Symbol.for('QuestionRepository'),
  AnswerRepository: Symbol.for('AnswerRepository'),
  TagRepository: Symbol.for('TagRepository'),

  // Social Repositories
  ChannelRepository: Symbol.for('ChannelRepository'),
  SocialMessageRepository: Symbol.for('SocialMessageRepository'),
  NotificationRepository: Symbol.for('NotificationRepository'),

  // Controllers
  AIAgentController: Symbol.for('AIAgentController'),
  WorkshopController: Symbol.for('WorkshopController'),
  TemplateController: Symbol.for('TemplateController'),
  PaymentController: Symbol.for('PaymentController'),
  QAController: Symbol.for('QAController'),
  SocialController: Symbol.for('SocialController'),
};
