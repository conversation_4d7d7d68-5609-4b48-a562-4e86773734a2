/**
 * Channel Service
 *
 * This service handles channel-related features like channel creation, updates,
 * membership management, and listing.
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { ChannelRepository } from '../../../db/repositories/social';
import { UserRepository } from '../../../db/repositories';
import { logger } from '../../../common/logger';
import { ChannelResponse, ChannelList } from '../../../common/types/social';

// Define repository return types
interface ChannelWithRelations {
  id: number;
  name: string;
  description: string | null;
  is_private: boolean;
  creator_id: number;
  created_at: Date;
  updated_at: Date;
  project_id: number | null;
  // These properties are included via Prisma's include option
  creator?: {
    id: number;
    username: string;
    email: string;
    avatar_url: string | null;
  };
  members?: Array<{
    user_id: number;
    channel_id: number;
    joined_at: Date;
    user: {
      id: number;
      username: string;
      email: string;
      avatar_url: string | null;
    };
  }>;
  _count?: {
    messages?: number;
    members?: number;
  };
}

/**
 * Channel Service with repository pattern
 */
@injectable()
export class ChannelService {
  constructor(
    @inject(TYPES.ChannelRepository) private channelRepository: ChannelRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
  ) {}

  /**
   * Get channels for a user
   */
  async getUserChannels(
    userId: number,
    options: {
      skip?: number;
      take?: number;
    } = {},
  ): Promise<ChannelList> {
    try {
      const channels = await this.channelRepository.findByUserId(userId, options);
      const channelsWithRelations = channels as ChannelWithRelations[];

      return {
        channels: channelsWithRelations.map((channel: ChannelWithRelations) => ({
          id: channel.id,
          name: channel.name,
          description: channel.description || undefined,
          isPrivate: channel.is_private,
          ownerId: channel.creator_id,
          createdAt: channel.created_at,
          updatedAt: channel.updated_at,
          owner: channel.creator
            ? {
                id: channel.creator.id,
                username: channel.creator.username,
                email: channel.creator.email,
                avatarUrl: channel.creator.avatar_url || undefined,
              }
            : {
                id: channel.creator_id,
                username: 'Unknown',
                email: '',
                avatarUrl: undefined,
              },
          members: channel.members?.map((member: any) => ({
            userId: member.user_id,
            channelId: member.channel_id,
            joinedAt: member.joined_at,
            user: {
              id: member.user.id,
              username: member.user.username,
              email: member.user.email,
              avatarUrl: member.user.avatar_url || undefined,
            },
          })),
          messageCount: channel._count?.messages,
        })),
        total: channelsWithRelations.length,
      };
    } catch (error: unknown) {
      logger.error(`Error getting channels for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get public channels
   */
  async getPublicChannels(
    options: {
      skip?: number;
      take?: number;
    } = {},
  ): Promise<ChannelList> {
    try {
      const publicChannels = await this.channelRepository.findPublicChannels(options);
      const channelsWithRelations = publicChannels as ChannelWithRelations[];

      return {
        channels: channelsWithRelations.map((channel: ChannelWithRelations) => ({
          id: channel.id,
          name: channel.name,
          description: channel.description || undefined,
          isPrivate: channel.is_private,
          ownerId: channel.creator_id,
          createdAt: channel.created_at,
          updatedAt: channel.updated_at,
          owner: channel.creator
            ? {
                id: channel.creator.id,
                username: channel.creator.username,
                email: channel.creator.email,
                avatarUrl: channel.creator.avatar_url || undefined,
              }
            : {
                id: channel.creator_id,
                username: 'Unknown',
                email: '',
                avatarUrl: undefined,
              },
          memberCount: channel._count?.members,
          messageCount: channel._count?.messages,
        })),
        total: channelsWithRelations.length,
      };
    } catch (error: unknown) {
      logger.error('Error getting public channels:', error);
      throw error;
    }
  }

  /**
   * Get a channel by ID
   */
  async getChannelById(channelId: number): Promise<ChannelResponse> {
    try {
      const channel = await this.channelRepository.findById(channelId);
      if (!channel) {
        throw new Error(`Channel with ID ${channelId} not found`);
      }

      const channelWithRelations = channel as ChannelWithRelations;

      return {
        id: channelWithRelations.id,
        name: channelWithRelations.name,
        description: channelWithRelations.description || undefined,
        isPrivate: channelWithRelations.is_private,
        ownerId: channelWithRelations.creator_id,
        createdAt: channelWithRelations.created_at,
        updatedAt: channelWithRelations.updated_at,
        owner: channelWithRelations.creator
          ? {
              id: channelWithRelations.creator.id,
              username: channelWithRelations.creator.username,
              email: channelWithRelations.creator.email,
              avatarUrl: channelWithRelations.creator.avatar_url || undefined,
            }
          : {
              id: channelWithRelations.creator_id,
              username: 'Unknown',
              email: '',
              avatarUrl: undefined,
            },
        members: channelWithRelations.members?.map((member) => ({
          userId: member.user_id,
          channelId: member.channel_id,
          joinedAt: member.joined_at,
          user: {
            id: member.user.id,
            username: member.user.username,
            email: member.user.email,
            avatarUrl: member.user.avatar_url || undefined,
          },
        })),
      };
    } catch (error: unknown) {
      logger.error(`Error getting channel ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new channel
   */
  async createChannel(data: {
    name: string;
    description?: string;
    ownerId: number;
    isPrivate?: boolean;
    memberIds?: number[];
  }): Promise<ChannelResponse | { statusCode: number; detail: string }> {
    try {
      const channel = await this.channelRepository.create({
        name: data.name,
        description: data.description,
        ownerId: data.ownerId,
        isPrivate: data.isPrivate,
        memberIds: data.memberIds,
      });

      return {
        id: channel.id,
        name: channel.name,
        description: channel.description || undefined,
        isPrivate: channel.is_private,
        ownerId: channel.creator_id,
        createdAt: channel.created_at,
        updatedAt: channel.updated_at,
        owner: {
          id: data.ownerId,
          username: 'Unknown',
          email: '',
          avatarUrl: undefined,
        },
        members: [],
      };
    } catch (error) {
      logger.error('Error creating channel:', error);
      return {
        statusCode: 500,
        detail: `Error creating channel: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Update a channel
   */
  async updateChannel(
    id: number,
    userId: number,
    data: {
      name?: string;
      description?: string;
      isPrivate?: boolean;
    },
  ): Promise<ChannelResponse | { statusCode: number; detail: string }> {
    try {
      // Check if channel exists
      const channel = await this.channelRepository.findById(id);
      if (!channel) {
        return {
          statusCode: 404,
          detail: 'Channel not found',
        };
      }

      // Check if user is authorized to update this channel
      const isOwner = await this.channelRepository.isOwner(id, userId);
      if (!isOwner) {
        return {
          statusCode: 403,
          detail: 'Not authorized to update this channel',
        };
      }

      const updatedChannel = await this.channelRepository.update(id, {
        name: data.name,
        description: data.description,
        isPrivate: data.isPrivate,
      });

      return {
        id: updatedChannel.id,
        name: updatedChannel.name,
        description: updatedChannel.description || undefined,
        isPrivate: updatedChannel.is_private,
        ownerId: updatedChannel.creator_id,
        createdAt: updatedChannel.created_at,
        updatedAt: updatedChannel.updated_at,
        owner: {
          id: updatedChannel.creator_id,
          username: 'Unknown',
          email: '',
          avatarUrl: undefined,
        },
        members: [],
      };
    } catch (error) {
      logger.error(`Error updating channel ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error updating channel: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Delete a channel
   */
  async deleteChannel(
    id: number,
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      // Check if channel exists
      const channel = await this.channelRepository.findById(id);
      if (!channel) {
        return {
          statusCode: 404,
          detail: 'Channel not found',
        };
      }

      // Check if user is authorized to delete this channel
      const isOwner = channel.creator_id === userId;
      if (!isOwner) {
        return {
          statusCode: 403,
          detail: 'Not authorized to delete this channel',
        };
      }

      await this.channelRepository.delete(id);
      return true;
    } catch (error) {
      logger.error(`Error deleting channel ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error deleting channel: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Add a member to a channel
   */
  async addChannelMember(
    channelId: number,
    userId: number,
    memberId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      // Check if channel exists
      const channel = await this.channelRepository.findById(channelId);
      if (!channel) {
        return {
          statusCode: 404,
          detail: 'Channel not found',
        };
      }

      // Check if user is authorized to add members
      const isOwner = channel.creator_id === userId;
      const isMember = await this.channelRepository.isMember(channelId, userId);
      if (!isOwner && !isMember) {
        return {
          statusCode: 403,
          detail: 'Not authorized to add members to this channel',
        };
      }

      // Check if the user to be added exists
      const memberUser = await this.userRepository.findById(memberId);
      if (!memberUser) {
        return {
          statusCode: 404,
          detail: 'User to be added not found',
        };
      }

      await this.channelRepository.addMember(channelId, memberId);
      return true;
    } catch (error) {
      logger.error(`Error adding member ${memberId} to channel ${channelId}:`, error);
      return {
        statusCode: 500,
        detail: `Error adding member to channel: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Remove a member from a channel
   */
  async removeChannelMember(
    channelId: number,
    userId: number,
    memberId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      // Check if channel exists
      const channel = await this.channelRepository.findById(channelId);
      if (!channel) {
        return {
          statusCode: 404,
          detail: 'Channel not found',
        };
      }

      // Check if user is authorized to remove members
      const isOwner = channel.creator_id === userId;
      if (!isOwner && userId !== memberId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to remove members from this channel',
        };
      }

      // Cannot remove the owner
      if (memberId === channel.creator_id) {
        return {
          statusCode: 400,
          detail: 'Cannot remove the channel owner',
        };
      }

      await this.channelRepository.removeMember(channelId, memberId);
      return true;
    } catch (error) {
      logger.error(`Error removing member ${memberId} from channel ${channelId}:`, error);
      return {
        statusCode: 500,
        detail: `Error removing member from channel: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}
