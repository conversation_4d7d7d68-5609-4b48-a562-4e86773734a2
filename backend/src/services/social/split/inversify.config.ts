/**
 * Inversify dependency injection container configuration
 */
import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from './types';

// Database services
import { DatabaseService } from '../../../db/database.service';
import { PrismaClient } from '../../../generated/prisma';

// Repositories
import {
  UserRepository,
  ProjectRepository,
  ConversationRepository,
  AIAgentRepository,
  AIPromptTemplateRepository,
  WorkshopRepository,
  ModuleRepository,
  EloHistoryRepository,
  KarmaRepository,
  TemplateRepository,
  TemplateFileRepository,
  TemplateVariableRepository,
  TemplateCollectionRepository,
  ProjectTemplateRepository,
  PaymentRepository,
  SubscriptionRepository,
  QuestionRepository,
  AnswerRepository,
  TagRepository,
  ChannelRepository,
  SocialMessageRepository,
  NotificationRepository,
} from '../../../db/repositories';

// Controllers
import { AIAgentController } from '../../../api/ai/controllers/ai-agent.controller';
import { WorkshopController } from '../../../api/workshop/controllers/workshop.controller';
import { TemplateController } from '../../../api/template/template.controller';
import { PaymentController } from '../../../api/payment/controllers';
import { QAController } from '../../../api/qa/controllers';
import { SocialController } from '../../../api/social/controllers';

// Services
import { PaymentService, SubscriptionService } from '../../payment';
import { EnhancedQAService } from '../../qa';
// New split social services import
import { ChannelService } from './channel.service';
import { MessageService } from './message.service';
import { NotificationService } from './notification.service';

// Create and configure container
const container = new Container();

// Register database services
container.bind<DatabaseService>(TYPES.DatabaseService).to(DatabaseService).inSingletonScope();
container.bind<PrismaClient>(TYPES.PrismaService).toConstantValue(new PrismaClient());

// Register repositories
container.bind<UserRepository>(TYPES.UserRepository).to(UserRepository);
container.bind<ProjectRepository>(TYPES.ProjectRepository).to(ProjectRepository);
container.bind<ConversationRepository>(TYPES.ConversationRepository).to(ConversationRepository);

// Register AI repositories
container.bind<AIAgentRepository>(TYPES.AIAgentRepository).to(AIAgentRepository);
container
  .bind<AIPromptTemplateRepository>(TYPES.AIPromptTemplateRepository)
  .to(AIPromptTemplateRepository);

// Register Workshop repositories
container.bind<WorkshopRepository>(TYPES.WorkshopRepository).to(WorkshopRepository);
container.bind<ModuleRepository>(TYPES.ModuleRepository).to(ModuleRepository);

// Register Gamification repositories
container.bind<EloHistoryRepository>(TYPES.EloHistoryRepository).to(EloHistoryRepository);
container.bind<KarmaRepository>(TYPES.KarmaRepository).to(KarmaRepository);

// Register Template repositories
container.bind<TemplateRepository>(TYPES.TemplateRepository).to(TemplateRepository);
container.bind<TemplateFileRepository>(TYPES.TemplateFileRepository).to(TemplateFileRepository);
container
  .bind<TemplateVariableRepository>(TYPES.TemplateVariableRepository)
  .to(TemplateVariableRepository);
container
  .bind<TemplateCollectionRepository>(TYPES.TemplateCollectionRepository)
  .to(TemplateCollectionRepository);
container
  .bind<ProjectTemplateRepository>(TYPES.ProjectTemplateRepository)
  .to(ProjectTemplateRepository);

// Register Payment repositories
container.bind<PaymentRepository>(TYPES.PaymentRepository).to(PaymentRepository);
container.bind<SubscriptionRepository>(TYPES.SubscriptionRepository).to(SubscriptionRepository);

// Register QA repositories
container.bind<QuestionRepository>(TYPES.QuestionRepository).to(QuestionRepository);
container.bind<AnswerRepository>(TYPES.AnswerRepository).to(AnswerRepository);
container.bind<TagRepository>(TYPES.TagRepository).to(TagRepository);

// Register Social repositories
container.bind<ChannelRepository>(TYPES.ChannelRepository).to(ChannelRepository);
container.bind<SocialMessageRepository>(TYPES.SocialMessageRepository).to(SocialMessageRepository);
container.bind<NotificationRepository>(TYPES.NotificationRepository).to(NotificationRepository);

// Register controllers
container.bind<AIAgentController>(TYPES.AIAgentController).to(AIAgentController);
container.bind<WorkshopController>(TYPES.WorkshopController).to(WorkshopController);
container.bind<TemplateController>(TYPES.TemplateController).to(TemplateController);
container.bind<PaymentController>(TYPES.PaymentController).to(PaymentController);
container.bind<QAController>(TYPES.QAController).to(QAController);
container.bind<SocialController>(TYPES.SocialController).to(SocialController);

// Register services
container.bind<PaymentService>(TYPES.PaymentService).to(PaymentService);
container.bind<SubscriptionService>(TYPES.SubscriptionService).to(SubscriptionService);
container.bind<EnhancedQAService>(TYPES.EnhancedQAService).to(EnhancedQAService);

// Register the split social services
container.bind<ChannelService>(TYPES.ChannelService).to(ChannelService).inSingletonScope();
container.bind<MessageService>(TYPES.MessageService).to(MessageService).inSingletonScope();
container
  .bind<NotificationService>(TYPES.NotificationService)
  .to(NotificationService)
  .inSingletonScope();

export { container };
