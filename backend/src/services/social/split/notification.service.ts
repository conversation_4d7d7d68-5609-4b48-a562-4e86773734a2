/**
 * Notification Service
 *
 * This service handles notification-related features like getting notifications,
 * marking them as read, and more.
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { NotificationRepository } from '../../../db/repositories/social';
import { UserRepository } from '../../../db/repositories';
import { logger } from '../../../common/logger';
import {
  NotificationList,
  SocialUserBase,
} from '../../../common/types/social';

interface NotificationWithRelations {
  id: number;
  user_id: number;
  type: string;
  message: string | null;
  title: string;
  link: string | null;
  related_entity_id: number | null;
  related_entity_type: string | null;
  actor_id: number | null;
  created_at: Date;
  expires_at: Date | null;
  is_read: boolean;
  read_at: Date | null;
  user_: {
    id: number;
    username: string;
    email: string;
    avatar_url: string | null;
  } | null;
}

/**
 * Notification Service using repositories
 */
@injectable()
export class NotificationService {
  constructor(
    @inject(TYPES.NotificationRepository) private notificationRepository: NotificationRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
  ) {}

  /**
   * Get notifications for a user
   */
  async getUserNotifications(
    userId: number,
    options: {
      skip?: number;
      take?: number;
      unreadOnly?: boolean;
    } = {},
  ): Promise<NotificationList | { statusCode: number; detail: string }> {
    try {
      const notifications = (await this.notificationRepository.findByUserId(
        userId,
        options,
      )) as unknown as NotificationWithRelations[];
      const total = notifications.length;
      const unreadCount = await this.notificationRepository.countUnread(userId);

      return {
        notifications: await Promise.all(
          notifications.map(async (notification: NotificationWithRelations) => {
            let actor: SocialUserBase | undefined;

            if (notification.actor_id) {
              const actorUser = await this.userRepository.findById(notification.actor_id);
              if (actorUser) {
                actor = {
                  id: actorUser.id,
                  username: actorUser.username || 'Unknown',
                  email: actorUser.email,
                  avatarUrl: actorUser.profile_image_url || undefined,
                };
              }
            }

            return {
              id: notification.id,
              userId: notification.user_id,
              type: notification.type,
              content: notification.message || '',
              message: notification.message || '',
              relatedEntityId: notification.related_entity_id || undefined,
              relatedEntityType: notification.related_entity_type || undefined,
              actor: actor,
              timestamp: notification.created_at,
              isRead: notification.is_read,
              readAt: notification.read_at || undefined,
            };
          }),
        ),
        total,
        unreadCount,
      };
    } catch (error) {
      logger.error(`Error getting notifications for user ${userId}:`, error);
      return {
        statusCode: 500,
        detail: `Error getting notifications: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(
    id: number,
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) {
        return {
          statusCode: 404,
          detail: 'Notification not found',
        };
      }

      // Check if the notification belongs to the user
      if (notification.user_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to update this notification',
        };
      }

      await this.notificationRepository.markAsRead(id);
      return true;
    } catch (error) {
      logger.error(`Error marking notification ${id} as read:`, error);
      return {
        statusCode: 500,
        detail: `Error marking notification as read: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllNotificationsAsRead(
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      await this.notificationRepository.markAllAsRead(userId);
      return true;
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      return {
        statusCode: 500,
        detail: `Error marking all notifications as read: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(
    id: number,
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) {
        return {
          statusCode: 404,
          detail: 'Notification not found',
        };
      }

      // Check if the notification belongs to the user
      if (notification.user_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to delete this notification',
        };
      }

      await this.notificationRepository.delete(id);
      return true;
    } catch (error) {
      logger.error(`Error deleting notification ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error deleting notification: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Delete all notifications for a user
   */
  async deleteAllNotifications(
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      await this.notificationRepository.deleteAll(userId);
      return true;
    } catch (error) {
      logger.error(`Error deleting all notifications for user ${userId}:`, error);
      return {
        statusCode: 500,
        detail: `Error deleting all notifications: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}
