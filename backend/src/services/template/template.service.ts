/**
 * Template service for managing templates and template-related operations
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import {
  TemplateRepository,
  TemplateFileRepository,
  TemplateVariableRepository,
  TemplateCollectionRepository,
  ProjectTemplateRepository,
} from '../../db/repositories/template';
import {
  CreateTemplateDto,
  UpdateTemplateDto,
  CreateTemplateFileDto,
  UpdateTemplateFileDto,
  CreateTemplateVariableDto,
  UpdateTemplateVariableDto,
  CreateTemplateCollectionDto,
  UpdateTemplateCollectionDto,
  CreateProjectTemplateDto,
  UpdateProjectTemplateDto,
  TemplateUsageDto,
} from '../../api/template/dto';
import { logger } from '../../common/logger';
import { PrismaClient } from '../../generated/prisma';

@injectable()
export class TemplateService {
  constructor(
    @inject(TYPES.TemplateRepository) private templateRepository: TemplateRepository,
    @inject(TYPES.TemplateFileRepository) private templateFileRepository: TemplateFileRepository,
    @inject(TYPES.TemplateVariableRepository)
    private templateVariableRepository: TemplateVariableRepository,
    @inject(TYPES.TemplateCollectionRepository)
    private templateCollectionRepository: TemplateCollectionRepository,
    @inject(TYPES.ProjectTemplateRepository)
    private projectTemplateRepository: ProjectTemplateRepository,
    @inject(TYPES.PrismaService) private prisma: PrismaClient,
  ) {}

  /**
   * Create a new template
   */
  async createTemplate(data: CreateTemplateDto) {
    try {
      return await this.templateRepository.create(data);
    } catch (error) {
      logger.error('Error creating template:', error);
      throw error;
    }
  }

  /**
   * Get a template by ID
   */
  async getTemplateById(id: number) {
    try {
      return await this.templateRepository.findById(id);
    } catch (error) {
      logger.error(`Error getting template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all templates with optional filters
   */
  async getAllTemplates(filters?: { creatorId?: number; isPublic?: boolean; category?: string }) {
    try {
      return await this.templateRepository.findAll(filters);
    } catch (error) {
      logger.error('Error getting templates:', error);
      throw error;
    }
  }

  /**
   * Update a template
   */
  async updateTemplate(id: number, data: UpdateTemplateDto) {
    try {
      return await this.templateRepository.update(id, data);
    } catch (error) {
      logger.error(`Error updating template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: number) {
    try {
      return await this.templateRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a file to a template
   */
  async addTemplateFile(templateId: number, data: CreateTemplateFileDto) {
    try {
      // Check if template exists
      const template = await this.templateRepository.findById(templateId);
      if (!template) {
        throw new Error(`Template with ID ${templateId} not found`);
      }

      // Check if file with same path already exists
      const existingFile = await this.templateFileRepository.findByPath(templateId, data.path);
      if (existingFile) {
        throw new Error(`File with path ${data.path} already exists in template ${templateId}`);
      }

      return await this.templateRepository.addFile(templateId, data);
    } catch (error) {
      logger.error(`Error adding file to template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get all files for a template
   */
  async getTemplateFiles(templateId: number) {
    try {
      return await this.templateFileRepository.findByTemplateId(templateId);
    } catch (error) {
      logger.error(`Error getting files for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Update a template file
   */
  async updateTemplateFile(id: number, data: UpdateTemplateFileDto) {
    try {
      return await this.templateFileRepository.update(id, data);
    } catch (error) {
      logger.error(`Error updating template file with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template file
   */
  async deleteTemplateFile(id: number) {
    try {
      return await this.templateFileRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting template file with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a variable to a template
   */
  async addTemplateVariable(templateId: number, data: CreateTemplateVariableDto) {
    try {
      // Check if template exists
      const template = await this.templateRepository.findById(templateId);
      if (!template) {
        throw new Error(`Template with ID ${templateId} not found`);
      }

      // Check if variable with same name already exists
      const existingVariable = await this.templateVariableRepository.findByName(
        templateId,
        data.name,
      );
      if (existingVariable) {
        throw new Error(`Variable with name ${data.name} already exists in template ${templateId}`);
      }

      return await this.templateRepository.addVariable(templateId, data);
    } catch (error) {
      logger.error(`Error adding variable to template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Get all variables for a template
   */
  async getTemplateVariables(templateId: number) {
    try {
      return await this.templateVariableRepository.findByTemplateId(templateId);
    } catch (error) {
      logger.error(`Error getting variables for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Update a template variable
   */
  async updateTemplateVariable(id: number, data: UpdateTemplateVariableDto) {
    try {
      return await this.templateVariableRepository.update(id, data);
    } catch (error) {
      logger.error(`Error updating template variable with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template variable
   */
  async deleteTemplateVariable(id: number) {
    try {
      return await this.templateVariableRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting template variable with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a template collection
   */
  async createTemplateCollection(data: CreateTemplateCollectionDto) {
    try {
      return await this.templateCollectionRepository.create(data);
    } catch (error) {
      logger.error('Error creating template collection:', error);
      throw error;
    }
  }

  /**
   * Get a template collection by ID
   */
  async getTemplateCollectionById(id: number) {
    try {
      return await this.templateCollectionRepository.findById(id);
    } catch (error) {
      logger.error(`Error getting template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all template collections with optional filters
   */
  async getAllTemplateCollections(filters?: { creatorId?: number; isPublic?: boolean }) {
    try {
      return await this.templateCollectionRepository.findAll(filters);
    } catch (error) {
      logger.error('Error getting template collections:', error);
      throw error;
    }
  }

  /**
   * Update a template collection
   */
  async updateTemplateCollection(id: number, data: UpdateTemplateCollectionDto) {
    try {
      return await this.templateCollectionRepository.update(id, data);
    } catch (error) {
      logger.error(`Error updating template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template collection
   */
  async deleteTemplateCollection(id: number) {
    try {
      return await this.templateCollectionRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a template to a collection
   */
  async addTemplateToCollection(collectionId: number, templateId: number) {
    try {
      // Check if collection exists
      const collection = await this.templateCollectionRepository.findById(collectionId);
      if (!collection) {
        throw new Error(`Template collection with ID ${collectionId} not found`);
      }

      // Check if template exists
      const template = await this.templateRepository.findById(templateId);
      if (!template) {
        throw new Error(`Template with ID ${templateId} not found`);
      }

      return await this.templateCollectionRepository.addTemplate(collectionId, templateId);
    } catch (error) {
      logger.error(`Error adding template ${templateId} to collection ${collectionId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a template from a collection
   */
  async removeTemplateFromCollection(collectionId: number, templateId: number) {
    try {
      return await this.templateCollectionRepository.removeTemplate(collectionId, templateId);
    } catch (error) {
      logger.error(`Error removing template ${templateId} from collection ${collectionId}:`, error);
      throw error;
    }
  }

  /**
   * Create a project template
   */
  async createProjectTemplate(data: CreateProjectTemplateDto) {
    try {
      return await this.projectTemplateRepository.create(data);
    } catch (error) {
      logger.error('Error creating project template:', error);
      throw error;
    }
  }

  /**
   * Get a project template by ID
   */
  async getProjectTemplateById(id: number) {
    try {
      return await this.projectTemplateRepository.findById(id);
    } catch (error) {
      logger.error(`Error getting project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all project templates with optional filters
   */
  async getAllProjectTemplates(filters?: { creatorId?: number; isPublic?: boolean }) {
    try {
      return await this.projectTemplateRepository.findAll(filters);
    } catch (error) {
      logger.error('Error getting project templates:', error);
      throw error;
    }
  }

  /**
   * Update a project template
   */
  async updateProjectTemplate(id: number, data: UpdateProjectTemplateDto) {
    try {
      return await this.projectTemplateRepository.update(id, data);
    } catch (error) {
      logger.error(`Error updating project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a project template
   */
  async deleteProjectTemplate(id: number) {
    try {
      return await this.projectTemplateRepository.delete(id);
    } catch (error) {
      logger.error(`Error deleting project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Record template usage
   */
  async recordTemplateUsage(data: TemplateUsageDto) {
    try {
      // Create template usage record
      const usage = await this.prisma.template_usages.create({
        data: {
          template_id: data.templateId,
          user_id: data.userId,
          project_id: data.projectId,
          variable_values: data.variableValues || {},
        },
      });

      // Increment usage count for the template
      await this.projectTemplateRepository.incrementUsageCount(data.templateId);

      return usage;
    } catch (error) {
      logger.error(`Error recording template usage:`, error);
      throw error;
    }
  }
}
