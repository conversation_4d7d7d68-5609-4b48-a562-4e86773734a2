/**
 * Git Service
 *
 * This service provides a safe interface for git operations.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from '../../common/logger';
import { injectable } from 'inversify';

const execPromise = promisify(exec);

// Git commit interface
interface GitCommit {
  hash: string;
  author: string;
  date: string;
  message: string;
}

@injectable()
export class GitService {
  private workspacePath: string;

  constructor() {
    // Set workspace path to current directory by default
    this.workspacePath = process.cwd();
  }

  /**
   * Set the workspace path for git operations
   */
  setWorkspacePath(workspacePath: string): void {
    this.workspacePath = workspacePath;
  }

  /**
   * Get the current workspace path
   */
  getWorkspacePath(): string {
    return this.workspacePath;
  }

  /**
   * Get git information for the workspace
   */
  async getGitInfo(): Promise<{ repository: string; branch: string } | null> {
    try {
      // Check if directory is a git repository
      const { stdout: gitCheck, stderr: gitCheckErr } = await execPromise(
        'git rev-parse --is-inside-work-tree',
        {
          cwd: this.workspacePath,
        },
      );

      if (gitCheckErr || gitCheck.trim() !== 'true') {
        return null;
      }

      // Get repository URL
      const { stdout: repoUrl, stderr: repoUrlErr } = await execPromise(
        'git config --get remote.origin.url',
        { cwd: this.workspacePath },
      );

      if (repoUrlErr) {
        return null;
      }

      // Get current branch
      const { stdout: branch, stderr: branchErr } = await execPromise(
        'git rev-parse --abbrev-ref HEAD',
        { cwd: this.workspacePath },
      );

      if (branchErr) {
        return null;
      }

      return {
        repository: repoUrl.trim(),
        branch: branch.trim(),
      };
    } catch (error) {
      logger.error('Error getting git info:', error);
      return null;
    }
  }

  /**
   * Get commit history for a file or directory
   */
  async getCommitHistory(filePath: string, limit: number = 10): Promise<GitCommit[]> {
    try {
      // Format command
      const command = `git log --pretty=format:"%H|%an|%ad|%s" --date=iso -n ${limit} -- ${filePath}`;

      // Execute command
      const { stdout, stderr } = await execPromise(command, {
        cwd: this.workspacePath,
      });

      if (stderr) {
        logger.warn(`Warning getting commit history: ${stderr}`);
      }

      if (!stdout) {
        return [];
      }

      // Parse commits
      const commits = stdout.split('\n').map((line) => {
        const [hash, author, date, message] = line.split('|');
        return {
          hash,
          author,
          date,
          message,
        };
      });

      return commits;
    } catch (error) {
      logger.error(`Error getting commit history for ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get file diff between commits
   */
  async getFileDiff(
    filePath: string,
    oldCommit: string,
    newCommit: string = 'HEAD',
  ): Promise<string> {
    try {
      // Format command
      const command = `git diff ${oldCommit} ${newCommit} -- ${filePath}`;

      // Execute command
      const { stdout, stderr } = await execPromise(command, {
        cwd: this.workspacePath,
      });

      if (stderr) {
        logger.warn(`Warning getting file diff: ${stderr}`);
      }

      return stdout;
    } catch (error) {
      logger.error(`Error getting file diff for ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get file content at a specific commit
   */
  async getFileAtCommit(filePath: string, commit: string): Promise<string> {
    try {
      // Format command
      const command = `git show ${commit}:${filePath}`;

      // Execute command
      const { stdout, stderr } = await execPromise(command, {
        cwd: this.workspacePath,
      });

      if (stderr) {
        logger.warn(`Warning getting file at commit: ${stderr}`);
      }

      return stdout;
    } catch (error) {
      logger.error(`Error getting file at commit for ${filePath}:`, error);
      throw error;
    }
  }
}
