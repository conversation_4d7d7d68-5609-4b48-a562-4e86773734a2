/**
 * Agent Service
 *
 * This service implements the ReAct (Reasoning-Action-Observation) loop for agent workflows.
 * It orchestrates AI reasoning, actions, and observations to complete tasks.
 */

import { PrismaClient, AgentStatus, LastActivityType } from '@prisma/client';
import { logger } from '../../common/logger';
import { ShellService } from './shell.service';
import { FileService } from './file.service';
import { GitService } from './git.service';
import { PipelineService } from './pipeline.service';

import { WebSocket } from 'ws';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import aiService from '../ai';

// Types for agent messages
interface AgentMessage {
  id: string;
  sessionId: string;
  type: string;
  content: string;
  timestamp: Date;
  stage?: string;
}

// Types for agent actions
interface AgentAction {
  type: string;
  payload: any;
}

// Types for agent observations
interface AgentObservation {
  success: boolean;
  result: any;
  error?: string;
}

@injectable()
export class AgentService {
  // Store active WebSocket connections
  private activeConnections: Map<string, WebSocket[]> = new Map();

  // Store active sessions
  private activeSessions: Map<string, any> = new Map();

  // Store session actions and observations
  private sessionActions: Map<string, AgentAction[]> = new Map();
  private sessionObservations: Map<string, AgentObservation[]> = new Map();

  constructor(
    @inject(TYPES.PrismaService) private prisma: PrismaClient,
    @inject(TYPES.ShellService) private shellService: ShellService,
    @inject(TYPES.FileService) private fileService: FileService,
    @inject(TYPES.GitService) private gitService: GitService,
    @inject(TYPES.PipelineService) private pipelineService: PipelineService,
  ) {}

  /**
   * Start a new agent session
   */
  async startAgentSession(
    userId: number,
    taskDescription: string,
    projectId?: number,
  ): Promise<any> {
    try {
      logger.info(`Starting agent session for user ${userId} with task: ${taskDescription}`);

      // Create session in database
      const session = await this.prisma.conversations.create({
        data: {
          user_id: userId,
          title: `Agent: ${taskDescription.substring(0, 50)}${taskDescription.length > 50 ? '...' : ''}`,
          key_objective: taskDescription,
          project_id: projectId,
          agent_status: AgentStatus.initializing,
          agent_iteration_count: 0,
          agent_allowed_actions: [
            'shell_command',
            'file_read',
            'file_write',
            'file_edit',
            'git_history',
          ],
          agent_commands_executed: [],
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      logger.info(`Created agent session with ID: ${session.id}`);

      // Initialize session storage
      this.sessionActions.set(session.id.toString(), []);
      this.sessionObservations.set(session.id.toString(), []);

      // Start execution in background
      setTimeout(() => {
        this.executeReactLoop(session.id.toString()).catch((error) => {
          logger.error(`Error in executeReactLoop for session ${session.id}:`, error);
        });
      }, 100);

      return session;
    } catch (error) {
      logger.error('Error starting agent session:', error);
      throw error;
    }
  }

  /**
   * Execute the ReAct loop for an agent session
   */
  async executeReactLoop(sessionId: string): Promise<void> {
    try {
      // Get session
      const sessionIdNum = parseInt(sessionId, 10);
      const session = await this.prisma.conversations.findUnique({
        where: { id: sessionIdNum },
      });

      if (!session) {
        logger.error(`Session ${sessionId} not found`);
        return;
      }

      // Update status to running
      await this.prisma.conversations.update({
        where: { id: sessionIdNum },
        data: {
          agent_status: AgentStatus.running,
          updated_at: new Date(),
        },
      });

      // Get project context
      const projectContext = await this.getProjectContext(
        sessionId,
        session.project_id || undefined,
      );

      // Add starting message
      await this.addMessage(sessionId, 'thinking', 'Starting work on your task...', 'evidence');

      // Perform initial task analysis
      const taskAnalysis = await this.analyzeTask(
        sessionId,
        session.key_objective || '',
        projectContext,
      );

      // Add thinking message with the analysis
      await this.addMessage(
        sessionId,
        'thinking',
        taskAnalysis.reasoning || 'Analyzing the task...',
        'evidence',
      );

      // If there's a plan, add it as a message
      if (taskAnalysis.plan && Array.isArray(taskAnalysis.plan) && taskAnalysis.plan.length > 0) {
        const planText = taskAnalysis.plan.map((step: string) => `- ${step}`).join('\n');
        await this.addMessage(sessionId, 'thinking', `Here's my plan:\n${planText}`, 'evidence');
      }

      // Execute actions in a loop
      const maxIterations = 10; // Prevent infinite loops
      for (let i = 0; i < maxIterations; i++) {
        // Check if session is still running
        const currentSession = await this.prisma.conversations.findUnique({
          where: { id: parseInt(sessionId, 10) },
        });

        if (!currentSession || currentSession.agent_status !== AgentStatus.running) {
          logger.info(`Session ${sessionId} is no longer running, stopping loop`);
          break;
        }

        // Update iteration count
        await this.prisma.conversations.update({
          where: { id: parseInt(sessionId, 10) },
          data: {
            agent_iteration_count: i + 1,
            updated_at: new Date(),
          },
        });

        // Determine next action
        let nextAction: AgentAction;

        // Check if we have actions already from initial analysis
        if (i === 0 && taskAnalysis.next_action) {
          nextAction = taskAnalysis.next_action;
        } else {
          // Get next action from AI
          const nextActionResponse = await this.determineNextAction(
            sessionId,
            session.key_objective || '',
            this.sessionActions.get(sessionId) || [],
            this.sessionObservations.get(sessionId) || [],
            projectContext,
          );

          nextAction = nextActionResponse.action;

          // Add thinking message
          await this.addMessage(
            sessionId,
            'thinking',
            nextActionResponse.reasoning || 'Thinking about next step...',
            currentSession.current_stage || 'evidence',
          );
        }

        // If action is task_complete, break the loop
        if (nextAction.type === 'task_complete') {
          await this.addMessage(
            sessionId,
            'complete',
            nextAction.payload.summary || 'Task completed successfully.',
            currentSession.current_stage || 'evidence',
          );

          // Update session status
          await this.prisma.conversations.update({
            where: { id: parseInt(sessionId, 10) },
            data: {
              agent_status: AgentStatus.completed,
              agent_completion_summary:
                nextAction.payload.summary || 'Task completed successfully.',
              updated_at: new Date(),
            },
          });

          break;
        }

        // Execute the action
        const actionResult = await this.executeAction(sessionId, nextAction);

        // Store action and observation
        this.sessionActions.get(sessionId)?.push(nextAction);
        this.sessionObservations.get(sessionId)?.push(actionResult);
      }

      // If we reached max iterations, mark as failed
      const finalSession = await this.prisma.conversations.findUnique({
        where: { id: parseInt(sessionId, 10) },
      });

      if (finalSession && finalSession.agent_status === AgentStatus.running) {
        await this.prisma.conversations.update({
          where: { id: parseInt(sessionId, 10) },
          data: {
            agent_status: AgentStatus.failed,
            agent_completion_summary: 'Failed to complete task within maximum iterations.',
            updated_at: new Date(),
          },
        });

        await this.addMessage(
          sessionId,
          'error',
          'Failed to complete task within maximum iterations.',
          finalSession.current_stage || 'evidence',
        );
      }
    } catch (error) {
      logger.error(`Error in executeReactLoop for session ${sessionId}:`, error);

      // Update session status to failed
      await this.prisma.conversations.update({
        where: { id: parseInt(sessionId, 10) },
        data: {
          agent_status: AgentStatus.failed,
          agent_completion_summary: `Error: ${(error as Error).message}`,
          updated_at: new Date(),
        },
      });

      await this.addMessage(
        sessionId,
        'error',
        `An error occurred: ${(error as Error).message}`,
        'evidence',
      );
    }
  }

  /**
   * Get project context for an agent session
   */
  private async getProjectContext(sessionId: string, projectId?: number): Promise<string> {
    try {
      let context = '';

      if (projectId) {
        // Get project details
        const project = await this.prisma.projects.findUnique({
          where: { id: projectId },
          include: {
            project_slides: true,
          },
        });

        if (project) {
          context += `# Project: ${project.name}\n\n`;

          if (project.description) {
            context += `## Description\n${project.description}\n\n`;
          }

          // Add slide structure if available
          if (project.project_slides && project.project_slides.length > 0) {
            context += '## Slide Structure\n';
            project.project_slides.forEach(
              (slide: {
                id: number;
                title: string | null;
                project_id: number;
                content: string | null;
                order: number | null;
              }) => {
                context += `- ${slide.title || 'Untitled Slide'}\n`;
              },
            );
            context += '\n';
          }
        }
      }

      // Add git information if available
      try {
        const gitInfo = await this.gitService.getGitInfo();
        if (gitInfo) {
          context += '## Git Information\n';
          context += `- Repository: ${gitInfo.repository}\n`;
          context += `- Branch: ${gitInfo.branch}\n`;
          context += '\n';
        }
      } catch (error) {
        logger.warn('Error getting git information:', error);
      }

      return context;
    } catch (error) {
      logger.error(`Error getting project context for session ${sessionId}:`, error);
      return '';
    }
  }

  /**
   * Analyze a task using AI
   */
  private async analyzeTask(
    sessionId: string,
    taskDescription: string,
    projectContext: string,
  ): Promise<any> {
    try {
      const prompt = `
# Task Analysis

## Project Context
${projectContext}

## Task Description
${taskDescription}

## Instructions
Analyze the task and provide:
1. A detailed reasoning of what needs to be done
2. A step-by-step plan
3. The first action to take

Format your response as JSON with the following structure:
{
  "reasoning": "detailed reasoning about the task",
  "plan": ["step 1", "step 2", "..."],
  "next_action": {
    "type": "action_type",
    "payload": {
      // action-specific parameters
    }
  }
}

Valid action types:
- shell_command: Execute a shell command (payload: { command: "string" })
- file_read: Read a file (payload: { path: "string" })
- file_write: Write to a file (payload: { path: "string", content: "string" })
- file_edit: Edit a file (payload: { path: "string", changes: [{ type: "insert"|"delete"|"replace", line: number, content: "string" }] })
- git_history: Get git commit history (payload: { path: "string", limit: number })
- task_complete: Mark task as complete (payload: { summary: "string" })
`;

      const response = await aiService.generateText({
        prompt,
        model: 'claude-3-7-sonnet-20250219',
        maxTokens: 4000,
        temperature: 0.7,
      });

      // Parse JSON response
      try {
        // Extract JSON from the response (in case there's markdown or other text)
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }

        // If no JSON found, return a basic structure
        return {
          reasoning: response.content,
          plan: [],
          next_action: null,
        };
      } catch (error) {
        logger.error(`Error parsing task analysis response for session ${sessionId}:`, error);
        return {
          reasoning: response.content,
          plan: [],
          next_action: null,
        };
      }
    } catch (error) {
      logger.error(`Error analyzing task for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Determine the next action using AI
   */
  private async determineNextAction(
    sessionId: string,
    taskDescription: string,
    previousActions: AgentAction[],
    observations: AgentObservation[],
    projectContext: string,
  ): Promise<any> {
    try {
      // Format previous actions and observations
      let actionsObservationsText = '';
      for (let i = 0; i < previousActions.length; i++) {
        const action = previousActions[i];
        const observation = observations[i];

        actionsObservationsText += `## Action ${i + 1}\n`;
        actionsObservationsText += `Type: ${action.type}\n`;
        actionsObservationsText += `Payload: ${JSON.stringify(action.payload, null, 2)}\n\n`;

        actionsObservationsText += `## Observation ${i + 1}\n`;
        actionsObservationsText += `Success: ${observation.success}\n`;
        if (observation.error) {
          actionsObservationsText += `Error: ${observation.error}\n`;
        }
        actionsObservationsText += `Result: ${JSON.stringify(observation.result, null, 2)}\n\n`;
      }

      const prompt = `
# Next Action Determination

## Project Context
${projectContext}

## Task Description
${taskDescription}

## Previous Actions and Observations
${actionsObservationsText || 'No previous actions.'}

## Instructions
Based on the task and previous actions/observations, determine the next action to take.
Provide detailed reasoning for your decision.

Format your response as JSON with the following structure:
{
  "reasoning": "detailed reasoning about the next action",
  "action": {
    "type": "action_type",
    "payload": {
      // action-specific parameters
    }
  }
}

Valid action types:
- shell_command: Execute a shell command (payload: { command: "string" })
- file_read: Read a file (payload: { path: "string" })
- file_write: Write to a file (payload: { path: "string", content: "string" })
- file_edit: Edit a file (payload: { path: "string", changes: [{ type: "insert"|"delete"|"replace", line: number, content: "string" }] })
- git_history: Get git commit history (payload: { path: "string", limit: number })
- task_complete: Mark task as complete (payload: { summary: "string" })
`;

      const response = await aiService.generateText({
        prompt,
        model: 'claude-3-7-sonnet-20250219',
        maxTokens: 4000,
        temperature: 0.7,
      });

      // Parse JSON response
      try {
        // Extract JSON from the response (in case there's markdown or other text)
        const jsonMatch = response.content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }

        // If no JSON found, return a basic structure
        return {
          reasoning: response.content,
          action: {
            type: 'task_complete',
            payload: {
              summary: 'Could not determine next action.',
            },
          },
        };
      } catch (error) {
        logger.error(`Error parsing next action response for session ${sessionId}:`, error);
        return {
          reasoning: response.content,
          action: {
            type: 'task_complete',
            payload: {
              summary: 'Could not determine next action due to parsing error.',
            },
          },
        };
      }
    } catch (error) {
      logger.error(`Error determining next action for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Execute an agent action
   */
  private async executeAction(sessionId: string, action: AgentAction): Promise<AgentObservation> {
    try {
      logger.info(`Executing action ${action.type} for session ${sessionId}`);

      // Add action message
      await this.addMessage(
        sessionId,
        'action',
        `Executing ${action.type}: ${JSON.stringify(action.payload)}`,
        'evidence',
      );

      // Update last activity type
      await this.prisma.conversations.update({
        where: { id: parseInt(sessionId, 10) },
        data: {
          last_activity_type: this.mapActionTypeToActivityType(action.type),
          updated_at: new Date(),
        },
      });

      // Record command in agent_commands_executed
      await this.recordAgentCommand(parseInt(sessionId, 10), action);

      let result: any;
      let success = true;
      let error: string | undefined;

      // Execute based on action type
      switch (action.type) {
        case 'shell_command':
          try {
            result = await this.shellService.executeCommand(action.payload.command);
          } catch (err) {
            success = false;
            error = (err as Error).message;
            result = { stdout: '', stderr: (err as Error).message };
          }
          break;

        case 'file_read':
          try {
            const content = await this.fileService.readFile(action.payload.path);
            result = { content };
          } catch (err) {
            success = false;
            error = (err as Error).message;
            result = { content: null, error: (err as Error).message };
          }
          break;

        case 'file_write':
          try {
            await this.fileService.writeFile(action.payload.path, action.payload.content);
            result = { success: true, path: action.payload.path };
          } catch (err) {
            success = false;
            error = (err as Error).message;
            result = { success: false, error: (err as Error).message };
          }
          break;

        case 'file_edit':
          try {
            await this.fileService.editFile(action.payload.path, action.payload.changes);
            result = { success: true, path: action.payload.path };
          } catch (err) {
            success = false;
            error = (err as Error).message;
            result = { success: false, error: (err as Error).message };
          }
          break;

        case 'git_history':
          try {
            result = await this.gitService.getCommitHistory(
              action.payload.path,
              action.payload.limit,
            );
          } catch (err) {
            success = false;
            error = (err as Error).message;
            result = { commits: [], error: (err as Error).message };
          }
          break;

        default:
          success = false;
          error = `Unsupported action type: ${action.type}`;
          result = { error };
      }

      // Add result message
      await this.addMessage(
        sessionId,
        success ? 'result' : 'error',
        success ? `Result: ${JSON.stringify(result, null, 2)}` : `Error: ${error}`,
        'evidence',
      );

      return { success, result, error };
    } catch (error) {
      logger.error(`Error executing action for session ${sessionId}:`, error);

      // Add error message
      await this.addMessage(
        sessionId,
        'error',
        `Error executing action: ${(error as Error).message}`,
        'evidence',
      );

      return {
        success: false,
        result: null,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Add a message to an agent session
   */
  private async addMessage(
    sessionId: string,
    type: string,
    content: string,
    stage?: string,
  ): Promise<AgentMessage> {
    try {
      // Map message type to role
      const role = this.mapMessageTypeToRole(type);

      // Create message in database
      const message = await this.prisma.messages.create({
        data: {
          conversation_id: parseInt(sessionId, 10),
          role,
          content,
          created_at: new Date(),
          meta_data: { type, stage },
        },
      });

      // Format as AgentMessage
      const agentMessage: AgentMessage = {
        id: message.id.toString(),
        sessionId,
        type,
        content,
        timestamp: message.created_at,
        stage,
      };

      // Send to WebSocket connections if any
      this.broadcastToWebSockets(sessionId, agentMessage);

      return agentMessage;
    } catch (error) {
      logger.error(`Error adding message to session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Record an agent command
   */
  private async recordAgentCommand(sessionId: number, command: AgentAction): Promise<void> {
    try {
      const session = await this.prisma.conversations.findUnique({
        where: { id: sessionId },
        select: { agent_commands_executed: true },
      });

      const commands = (session?.agent_commands_executed as any[]) || [];
      commands.push({
        ...command,
        timestamp: new Date().toISOString(),
      });

      await this.prisma.conversations.update({
        where: { id: sessionId },
        data: { agent_commands_executed: commands },
      });
    } catch (error) {
      logger.error(`Error recording agent command for session ${sessionId}:`, error);
    }
  }

  /**
   * Map message type to role
   */
  private mapMessageTypeToRole(type: string): string {
    switch (type) {
      case 'thinking':
      case 'action':
      case 'result':
      case 'complete':
      case 'error':
        return 'assistant';
      case 'user':
        return 'user';
      default:
        return 'system';
    }
  }

  /**
   * Map action type to activity type
   */
  private mapActionTypeToActivityType(actionType: string): LastActivityType {
    switch (actionType) {
      case 'shell_command':
        return LastActivityType.command;
      case 'file_read':
      case 'file_write':
      case 'file_edit':
        return LastActivityType.file_edit;
      case 'task_complete':
        return LastActivityType.complete;
      default:
        return LastActivityType.thinking;
    }
  }

  /**
   * Broadcast a message to WebSocket connections
   */
  private broadcastToWebSockets(sessionId: string, message: any): void {
    const connections = this.activeConnections.get(sessionId);
    if (connections && connections.length > 0) {
      const messageJson = JSON.stringify(message);
      for (const ws of connections) {
        try {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(messageJson);
          }
        } catch (error) {
          logger.error(`Error sending message to WebSocket:`, error);
        }
      }
    }
  }

  /**
   * Register a WebSocket connection for a session
   */
  registerWebSocket(sessionId: string, ws: WebSocket): void {
    if (!this.activeConnections.has(sessionId)) {
      this.activeConnections.set(sessionId, []);
    }

    this.activeConnections.get(sessionId)?.push(ws);

    // Set up cleanup on close
    ws.on('close', () => {
      const connections = this.activeConnections.get(sessionId);
      if (connections) {
        const index = connections.indexOf(ws);
        if (index !== -1) {
          connections.splice(index, 1);
        }

        // Remove empty arrays
        if (connections.length === 0) {
          this.activeConnections.delete(sessionId);
        }
      }
    });
  }
}
