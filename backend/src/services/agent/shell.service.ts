/**
 * Shell Service
 *
 * This service provides a safe interface for executing shell commands.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from '../../common/logger';
import { injectable } from 'inversify';

const execPromise = promisify(exec);

@injectable()
export class ShellService {
  private workspacePath: string;
  private allowedCommands: RegExp[];
  private blockedCommands: RegExp[];

  constructor() {
    // Set workspace path to current directory by default
    this.workspacePath = process.cwd();

    // Define allowed command patterns
    this.allowedCommands = [
      /^ls(\s+-[a-zA-Z]+)*(\s+\S+)*$/, // ls with options and paths
      /^cat\s+\S+$/, // cat with a file
      /^grep(\s+-[a-zA-Z]+)*\s+\S+\s+\S+$/, // grep with options, pattern, and file
      /^find\s+\S+\s+-name\s+\S+$/, // find with path and name
      /^echo\s+.+$/, // echo with text
      /^pwd$/, // pwd
      /^mkdir(\s+-[a-zA-Z]+)*\s+\S+$/, // mkdir with options and directory
      /^touch\s+\S+$/, // touch with file
      /^git\s+(status|log|diff|show|branch|rev-parse)/, // Safe git commands
      /^npm\s+(list|ls|outdated|version)$/, // Safe npm commands
      /^node\s+--version$/, // Node version
    ];

    // Define blocked command patterns (these override allowed patterns)
    this.blockedCommands = [
      /rm(\s+-[a-zA-Z]+)*\s+/, // rm commands
      /rmdir/, // rmdir commands
      /sudo/, // sudo commands
      /\s*>\s*\S+/, // output redirection
      /\|\s*\S+/, // pipe to another command
      /;\s*\S+/, // command chaining with ;
      /&&\s*\S+/, // command chaining with &&
      /\|\|\s*\S+/, // command chaining with ||
      /`/, // backtick execution
      /\$\(/, // command substitution
      /curl/, // curl commands
      /wget/, // wget commands
      /ssh/, // ssh commands
      /scp/, // scp commands
      /telnet/, // telnet commands
      /nc/, // netcat commands
      /eval/, // eval commands
      /exec/, // exec commands
      /source/, // source commands
      /\\n/, // newlines (prevent multi-line commands)
    ];
  }

  /**
   * Set the workspace path for command execution
   */
  setWorkspacePath(workspacePath: string): void {
    this.workspacePath = workspacePath;
  }

  /**
   * Get the current workspace path
   */
  getWorkspacePath(): string {
    return this.workspacePath;
  }

  /**
   * Execute a shell command
   */
  async executeCommand(command: string): Promise<{ stdout: string; stderr: string }> {
    try {
      // Validate command
      this.validateCommand(command);

      // Execute command in workspace directory
      const options = {
        cwd: this.workspacePath,
        timeout: 30000, // 30 second timeout
        maxBuffer: 1024 * 1024, // 1MB buffer
      };

      logger.info(`Executing command: ${command}`);
      const { stdout, stderr } = await execPromise(command, options);

      return {
        stdout: stdout.trim(),
        stderr: stderr.trim(),
      };
    } catch (error) {
      logger.error(`Error executing command: ${command}`, error);
      throw error;
    }
  }

  /**
   * Validate a command against allowed and blocked patterns
   */
  private validateCommand(command: string): void {
    // Check if command matches any blocked pattern
    for (const pattern of this.blockedCommands) {
      if (pattern.test(command)) {
        throw new Error(`Command contains blocked pattern: ${pattern}`);
      }
    }

    // Check if command matches any allowed pattern
    let isAllowed = false;
    for (const pattern of this.allowedCommands) {
      if (pattern.test(command)) {
        isAllowed = true;
        break;
      }
    }

    if (!isAllowed) {
      throw new Error(`Command not allowed: ${command}`);
    }
  }

  /**
   * Get git information for the workspace
   */
  async getGitInfo(): Promise<{ repository: string; branch: string } | null> {
    try {
      // Check if directory is a git repository
      const { stdout: gitCheck, stderr: gitCheckErr } = await execPromise(
        'git rev-parse --is-inside-work-tree',
        {
          cwd: this.workspacePath,
        },
      );

      if (gitCheckErr || gitCheck.trim() !== 'true') {
        return null;
      }

      // Get repository URL
      const { stdout: repoUrl, stderr: repoUrlErr } = await execPromise(
        'git config --get remote.origin.url',
        { cwd: this.workspacePath },
      );

      if (repoUrlErr) {
        return null;
      }

      // Get current branch
      const { stdout: branch, stderr: branchErr } = await execPromise(
        'git rev-parse --abbrev-ref HEAD',
        { cwd: this.workspacePath },
      );

      if (branchErr) {
        return null;
      }

      return {
        repository: repoUrl.trim(),
        branch: branch.trim(),
      };
    } catch (error) {
      logger.error('Error getting git info:', error);
      return null;
    }
  }
}
