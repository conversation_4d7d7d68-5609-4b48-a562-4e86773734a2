/**
 * File Service
 *
 * This service provides a safe interface for file operations.
 */

import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { logger } from '../../common/logger';
import { injectable } from 'inversify';

// Promisify fs functions
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const statAsync = promisify(fs.stat);
const accessAsync = promisify(fs.access);

// File change types
type FileChangeType = 'insert' | 'delete' | 'replace';

// File change interface
interface FileChange {
  type: FileChangeType;
  line: number;
  content: string;
}

@injectable()
export class FileService {
  private workspacePath: string;
  private blockedPaths: RegExp[];
  private blockedExtensions: string[];
  private maxFileSize: number;

  constructor() {
    // Set workspace path to current directory by default
    this.workspacePath = process.cwd();

    // Define blocked path patterns
    this.blockedPaths = [
      /^\//, // Absolute paths
      /^\.\./, // Parent directory access
      /node_modules/, // node_modules directory
      /\.git/, // .git directory
      /\.env/, // .env files
      /\.npmrc/, // .npmrc files
      /\.yarnrc/, // .yarnrc files
      /package-lock\.json/, // package-lock.json
      /yarn\.lock/, // yarn.lock
      /config.*\.json/, // config files
    ];

    // Define blocked file extensions
    this.blockedExtensions = [
      '.exe',
      '.dll',
      '.so',
      '.dylib', // Binaries
      '.sh',
      '.bash',
      '.zsh', // Shell scripts
      '.env',
      '.pem',
      '.key', // Sensitive files
      '.sqlite',
      '.db', // Database files
    ];

    // Set max file size (5MB)
    this.maxFileSize = 5 * 1024 * 1024;
  }

  /**
   * Set the workspace path for file operations
   */
  setWorkspacePath(workspacePath: string): void {
    this.workspacePath = workspacePath;
  }

  /**
   * Get the current workspace path
   */
  getWorkspacePath(): string {
    return this.workspacePath;
  }

  /**
   * Read a file
   */
  async readFile(filePath: string): Promise<string> {
    try {
      // Validate and resolve path
      const resolvedPath = this.resolvePath(filePath);

      // Check if file exists
      await this.checkFileExists(resolvedPath);

      // Check file size
      await this.checkFileSize(resolvedPath);

      // Read file
      const content = await readFileAsync(resolvedPath, 'utf8');

      return content;
    } catch (error) {
      logger.error(`Error reading file: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * Write to a file
   */
  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Validate and resolve path
      const resolvedPath = this.resolvePath(filePath);

      // Create directory if it doesn't exist
      await this.ensureDirectoryExists(path.dirname(resolvedPath));

      // Write file
      await writeFileAsync(resolvedPath, content, 'utf8');

      logger.info(`File written: ${filePath}`);
    } catch (error) {
      logger.error(`Error writing file: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * Edit a file with changes
   */
  async editFile(filePath: string, changes: FileChange[]): Promise<void> {
    try {
      // Validate and resolve path
      const resolvedPath = this.resolvePath(filePath);

      // Check if file exists
      await this.checkFileExists(resolvedPath);

      // Read file
      let content = await readFileAsync(resolvedPath, 'utf8');

      // Split into lines
      const lines = content.split('\n');

      // Sort changes by line number (descending) to avoid line number shifts
      changes.sort((a, b) => b.line - a.line);

      // Apply changes
      for (const change of changes) {
        switch (change.type) {
          case 'insert':
            // Insert at line (0-indexed internally)
            lines.splice(change.line, 0, change.content);
            break;

          case 'delete':
            // Delete line
            if (change.line >= 0 && change.line < lines.length) {
              lines.splice(change.line, 1);
            }
            break;

          case 'replace':
            // Replace line
            if (change.line >= 0 && change.line < lines.length) {
              lines[change.line] = change.content;
            }
            break;
        }
      }

      // Join lines back together
      content = lines.join('\n');

      // Write file
      await writeFileAsync(resolvedPath, content, 'utf8');

      logger.info(`File edited: ${filePath}`);
    } catch (error) {
      logger.error(`Error editing file: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * Resolve a path relative to the workspace
   */
  private resolvePath(filePath: string): string {
    // Normalize path
    const normalizedPath = path.normalize(filePath);

    // Check for blocked paths
    for (const pattern of this.blockedPaths) {
      if (pattern.test(normalizedPath)) {
        throw new Error(`Access to path not allowed: ${filePath}`);
      }
    }

    // Check for blocked extensions
    const ext = path.extname(normalizedPath).toLowerCase();
    if (this.blockedExtensions.includes(ext)) {
      throw new Error(`Access to file type not allowed: ${ext}`);
    }

    // Resolve path relative to workspace
    return path.resolve(this.workspacePath, normalizedPath);
  }

  /**
   * Check if a file exists
   */
  private async checkFileExists(filePath: string): Promise<void> {
    try {
      await accessAsync(filePath, fs.constants.F_OK);
    } catch (error) {
      throw new Error(`File does not exist: ${filePath}`);
    }
  }

  /**
   * Check if a file size is within limits
   */
  private async checkFileSize(filePath: string): Promise<void> {
    try {
      const stats = await statAsync(filePath);

      if (stats.size > this.maxFileSize) {
        throw new Error(`File too large (${stats.size} bytes): ${filePath}`);
      }
    } catch (error) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        throw new Error(`File does not exist: ${filePath}`);
      }
      throw error;
    }
  }

  /**
   * Ensure a directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await accessAsync(dirPath, fs.constants.F_OK);
    } catch (error) {
      // Directory doesn't exist, create it
      await mkdirAsync(dirPath, { recursive: true });
    }
  }
}
