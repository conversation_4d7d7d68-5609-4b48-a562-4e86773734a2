/**
 * Pipeline Service
 *
 * This service manages the agent pipeline stages and workflow.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../../common/logger';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';

// Pipeline stage interface
interface PipelineStage {
  id: string;
  name: string;
  description: string;
  order: number;
}

// Default pipeline stages
const DEFAULT_PIPELINE_STAGES: PipelineStage[] = [
  {
    id: 'evidence',
    name: 'Evidence Collection',
    description: 'Gather information and evidence needed to complete the task',
    order: 1,
  },
  {
    id: 'planning',
    name: 'Planning',
    description: 'Create a detailed plan for implementing the solution',
    order: 2,
  },
  {
    id: 'implementation',
    name: 'Implementation',
    description: 'Execute the plan and implement the solution',
    order: 3,
  },
  {
    id: 'verification',
    name: 'Verification',
    description: 'Test and verify the implementation',
    order: 4,
  },
  {
    id: 'documentation',
    name: 'Documentation',
    description: 'Document the solution and any relevant information',
    order: 5,
  },
];

@injectable()
export class PipelineService {
  constructor(@inject(TYPES.PrismaService) private prisma: PrismaClient) {}

  /**
   * Get pipeline stages for a session
   */
  async getPipelineStages(sessionId: number): Promise<PipelineStage[]> {
    try {
      // Get session
      const session = await this.prisma.conversations.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }

      // Check if session has custom pipeline stages
      const customStages = session.agent_pipeline_stages as any[];

      if (customStages && Array.isArray(customStages) && customStages.length > 0) {
        return customStages;
      }

      // Return default pipeline stages
      return DEFAULT_PIPELINE_STAGES;
    } catch (error) {
      logger.error(`Error getting pipeline stages for session ${sessionId}:`, error);
      return DEFAULT_PIPELINE_STAGES;
    }
  }

  /**
   * Get current pipeline stage for a session
   */
  async getCurrentStage(sessionId: number): Promise<PipelineStage> {
    try {
      // Get session
      const session = await this.prisma.conversations.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }

      // Get current stage ID
      const currentStageId = session.current_stage || 'evidence';

      // Get all stages
      const stages = await this.getPipelineStages(sessionId);

      // Find current stage
      const currentStage = stages.find((stage) => stage.id === currentStageId);

      if (!currentStage) {
        // Default to first stage if current stage not found
        return stages[0];
      }

      return currentStage;
    } catch (error) {
      logger.error(`Error getting current stage for session ${sessionId}:`, error);
      return DEFAULT_PIPELINE_STAGES[0];
    }
  }

  /**
   * Set current pipeline stage for a session
   */
  async setCurrentStage(sessionId: number, stageId: string): Promise<PipelineStage> {
    try {
      // Get all stages
      const stages = await this.getPipelineStages(sessionId);

      // Find stage by ID
      const stage = stages.find((s) => s.id === stageId);

      if (!stage) {
        throw new Error(`Stage ${stageId} not found`);
      }

      // Update session
      await this.prisma.conversations.update({
        where: { id: sessionId },
        data: {
          current_stage: stageId,
          updated_at: new Date(),
        },
      });

      return stage;
    } catch (error) {
      logger.error(`Error setting current stage for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Move to the next pipeline stage
   */
  async moveToNextStage(sessionId: number): Promise<PipelineStage> {
    try {
      // Get current stage
      const currentStage = await this.getCurrentStage(sessionId);

      // Get all stages
      const stages = await this.getPipelineStages(sessionId);

      // Find current stage index
      const currentIndex = stages.findIndex((stage) => stage.id === currentStage.id);

      if (currentIndex === -1 || currentIndex === stages.length - 1) {
        // Already at last stage or stage not found
        return currentStage;
      }

      // Get next stage
      const nextStage = stages[currentIndex + 1];

      // Update session
      await this.setCurrentStage(sessionId, nextStage.id);

      return nextStage;
    } catch (error) {
      logger.error(`Error moving to next stage for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Create custom pipeline stages for a session
   */
  async createCustomPipeline(
    sessionId: number,
    stages: Omit<PipelineStage, 'order'>[],
  ): Promise<PipelineStage[]> {
    try {
      // Add order to stages
      const orderedStages = stages.map((stage, index) => ({
        ...stage,
        order: index + 1,
      }));

      // Update session
      await this.prisma.conversations.update({
        where: { id: sessionId },
        data: {
          agent_pipeline_stages: orderedStages,
          current_stage: orderedStages[0].id,
          updated_at: new Date(),
        },
      });

      return orderedStages;
    } catch (error) {
      logger.error(`Error creating custom pipeline for session ${sessionId}:`, error);
      throw error;
    }
  }
}
