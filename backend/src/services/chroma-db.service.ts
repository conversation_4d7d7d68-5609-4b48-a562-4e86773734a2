/**
 * ChromaDB Service
 *
 * This service provides an interface to interact with ChromaDB for semantic search functionality.
 * It handles connecting to ChromaDB, managing collections, indexing documentation,
 * and performing semantic searches using vector embeddings.
 */

import { ChromaClient, Collection } from 'chromadb';
import * as dotenv from 'dotenv';

import { logger } from '../common/logger';
import { xenovaEmbeddingService } from './xenova-embedding.service';

// Define the interface for the embedding function
interface EmbeddingFunctionType {
  embedDocuments(texts: string[]): Promise<number[][]>;
  embedQuery(text: string): Promise<number[]>;
  // Add generate method to match ChromaDB's EmbeddingFunction interface
  generate(texts: string[]): Promise<number[][]>;
}

// Load environment variables
dotenv.config();

// Configuration interface
interface ChromaConfig {
  host: string;
  port: number;
  collectionName: string;
  embeddingModel: string;
  apiKey?: string;
}

export class ChromaDBService {
  private client: ChromaClient;
  private collection: Collection | null = null;
  private embeddingFunction: EmbeddingFunctionType | undefined;
  private config: ChromaConfig;
  private isInitialized = false;

  constructor() {
    // Default configuration
    this.config = {
      host: process.env.CHROMA_HOST || 'localhost',
      port: parseInt(process.env.CHROMA_PORT || '8000', 10),
      // Always use Xenova collection to ensure consistent embeddings
      collectionName: 'documentation_embeddings_xenova',
      // Force use of Xenova embeddings to avoid dimension mismatches
      embeddingModel: 'xenova',
      apiKey: process.env.AZURE_API_KEY || process.env.OPENAI_API_KEY
    };

    // Initialize ChromaDB client
    this.client = new ChromaClient({
      path: `http://${this.config.host}:${this.config.port}`
    });

    // Set up embedding function
    // Always use Xenova embeddings to ensure consistent dimensions
    this.embeddingFunction = {
      // Implement embedDocuments function for batch processing
      embedDocuments: async (texts: string[]): Promise<number[][]> => {
        // Always use Xenova embeddings for consistent dimensions
        logger.info(`Using Xenova embeddings for ${texts.length} documents`);
        return await xenovaEmbeddingService.embedTexts(texts);
      },

      // Implement embedQuery function for single queries
      embedQuery: async (text: string): Promise<number[]> => {
        // Always use Xenova embeddings for consistent dimensions
        logger.info(`Using Xenova embedding for query: ${text.substring(0, 100)}...`);
        return await xenovaEmbeddingService.embedText(text);
      },

      // Implement generate method required by ChromaDB's interface
      generate: async (texts: string[]): Promise<number[][]> => {
        return this.embeddingFunction!.embedDocuments(texts);
      }
    };
  }



  /**
   * Initialize the ChromaDB connection and collection
   */
  public async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return;
      }

      // Create ChromaDB client
      this.client = new ChromaClient({
        path: `http://${this.config.host}:${this.config.port}`,
      });

      // Test connection to ensure ChromaDB is healthy
      try {
        await this.client.version();
        logger.info('ChromaDB server connection successful');
      } catch (connectionError) {
        logger.error(
          'Failed to connect to ChromaDB server:',
          connectionError instanceof Error ? connectionError.message : String(connectionError),
        );
        throw new Error(
          `ChromaDB server is not accessible at ${this.config.host}:${this.config.port}. Please ensure ChromaDB is running.`,
        );
      }

      // Try to get or create collection using a more robust approach
      logger.info(`Attempting to get or create collection: ${this.config.collectionName}`);
      
      try {
        // First try to get the collection
        this.collection = await this.client.getCollection({
          name: this.config.collectionName,
          embeddingFunction: this.embeddingFunction,
        });
        logger.info(`Successfully connected to existing collection: ${this.config.collectionName}`);
      } catch (error) {
        // If collection doesn't exist, create it
        if (error instanceof Error && error.message.includes('not found')) {
          logger.info(`Collection not found, creating new collection: ${this.config.collectionName}`);
          this.collection = await this.client.createCollection({
            name: this.config.collectionName,
            embeddingFunction: this.embeddingFunction,
          });
          logger.info(`Created new collection: ${this.config.collectionName}`);
        } else {
          // Some other error occurred
          logger.error(`Error accessing collection: ${error instanceof Error ? error.message : String(error)}`);
          throw error;
        }
      }

      // Verify collection exists
      if (!this.collection) {
        throw new Error('Failed to create collection');
      }

      // Get collection count
      const count = await this.collection.count();
      logger.info(`Collection ${this.config.collectionName} has ${count} documents`);

      this.isInitialized = true;
      logger.info('ChromaDB client initialized successfully');
    } catch (error) {
      logger.error(
        'Failed to initialize ChromaDB client:',
        error instanceof Error ? error.message : String(error),
      );
      logger.error(
        `Error stack: ${error instanceof Error ? error.stack : 'No stack trace available'}`,
      );
      throw new Error(
        `Failed to initialize ChromaDB client: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Add a document to the collection
   * @param id Document ID
   * @param content Document content
   * @param metadata Document metadata
   */
  public async addDocument(
    id: string,
    content: string,
    metadata: Record<string, string | number | boolean>,
  ): Promise<void> {
    try {
      logger.info(`Adding document ${id} to ChromaDB collection '${this.config.collectionName}'`);
      logger.info(`Document metadata: ${JSON.stringify(metadata)}`);
      logger.info(`Document content length: ${content.length} chars, preview: ${content.substring(0, 100)}...`);

      if (!this.isInitialized || !this.collection) {
        logger.info('Collection not initialized, initializing now...');
        await this.initialize();
      }

      if (!this.collection) {
        throw new Error('Collection not initialized after initialization attempt');
      }

      // Log collection information before adding
      const countBefore = await this.collection.count();
      logger.info(`Collection '${this.config.collectionName}' has ${countBefore} documents before addition`);

      // Generate embeddings directly
      const embedding = await xenovaEmbeddingService.embedText(content);
      logger.info(`Generated embedding with dimension: ${embedding.length}`);

      // Add the document with explicit embeddings
      await this.collection.add({
        ids: [id],
        documents: [content],
        metadatas: [metadata],
        embeddings: [embedding],
      });

      // Verify document was added
      const countAfter = await this.collection.count();
      logger.info(`Collection '${this.config.collectionName}' has ${countAfter} documents after addition`);

      if (countAfter > countBefore) {
        logger.info(`Document ${id} added successfully`);
      } else {
        logger.warn(`Document ${id} may not have been added - collection count did not increase`);
      }
    } catch (error) {
      logger.error(`Failed to add document ${id}:`, error instanceof Error ? error.message : String(error));
      logger.error(`Error stack: ${error instanceof Error ? error.stack : 'No stack trace available'}`);
      throw new Error(`Failed to add documents to ChromaDB: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Add multiple documents to the vector database in batch
   * @param ids Array of unique document identifiers
   * @param contents Array of text contents for embedding
   * @param metadatas Array of metadata objects
   */
  public async addDocuments(
    ids: string[],
    contents: string[],
    metadatas: Record<string, string | number | boolean>[],
  ): Promise<void> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      if (ids.length !== contents.length || ids.length !== metadatas.length) {
        throw new Error('Arrays of ids, contents, and metadatas must have the same length');
      }

      logger.debug(`Adding ${ids.length} documents to ChromaDB in batch`);

      if (this.collection) {
        await this.collection.add({
          ids: ids,
          documents: contents,
          metadatas: metadatas,
        });
      }

      logger.debug(`${ids.length} documents added successfully`);
    } catch (error) {
      logger.error('Failed to add documents in batch:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to add documents to ChromaDB: ${errorMessage}`);
    }
  }

  /**
   * Perform a semantic search using natural language query
   * @param query Natural language search query
   * @param limit Maximum number of results to return
   * @param filters Optional metadata filters
   * @returns Array of search results with document content and metadata
   */
  public async semanticSearch(
    query: string,
    limit: number = 10,
    filters: Record<string, string | number | boolean> = {},
  ): Promise<Array<{
      id: string;
      content: string;
      metadata: Record<string, string | number | boolean>;
      distance: number;
    }
  >> {
    try {
      logger.info(`Performing semantic search with query: "${query}", limit: ${limit}, filters: ${JSON.stringify(filters)}`);
      await this.initialize();

      // Log collection information
      const collectionInfo = await this.collection.count();
      logger.info(`Collection '${this.config.collectionName}' has ${collectionInfo} documents`);

      if (collectionInfo === 0) {
        logger.warn('Collection is empty - no documents have been indexed yet');
        return [];
      }

      // Generate query embedding using the same Xenova service used for documents
      // This ensures consistency between document embedding and query embedding
      logger.info('Generating query embedding using Xenova service...');
      const queryEmbedding = await xenovaEmbeddingService.embedText(query);
      logger.info(`Generated query embedding with dimension: ${queryEmbedding.length}`);

      // Perform the query using queryEmbeddings for consistency
      const queryParams = {
        queryEmbeddings: [queryEmbedding],
        nResults: limit,
        include: ['documents', 'metadatas', 'distances'] as any,
        ...(Object.keys(filters).length > 0 ? { where: filters } : {}),
      };

      logger.info(
        `Executing ChromaDB query with queryEmbeddings (dimension: ${queryEmbedding.length}), nResults: ${limit}`,
      );
      const results = await this.collection.query(queryParams);

      // Log raw results for debugging
      logger.info(`Raw query results: ${JSON.stringify({
          idsLength: results.ids?.[0]?.length || 0,
          documentsLength: results.documents?.[0]?.length || 0,
          metadatasLength: results.metadatas?.[0]?.length || 0,
          distancesLength: results.distances?.[0]?.length || 0,
          sampleDistances: results.distances?.[0]?.slice(0, 3) || [],
        })}`);

      // Format results with proper distance handling
      const formattedResults = results.ids[0]?.map((id, index) => {
        const distance = results.distances?.[0]?.[index];
        const actualDistance = distance !== undefined && distance !== null ? distance : 1.0;

        return {
          id,
          content: results.documents?.[0]?.[index] || '',
          metadata: results.metadatas?.[0]?.[index] || {},
          distance: actualDistance,
        };
      }) || [];

      // Sort by distance (lower is better for similarity)
      formattedResults.sort((a, b) => a.distance - b.distance);

      logger.info(`Returning ${formattedResults.length} formatted results with distances: ${formattedResults.slice(0, 3).map(r => r.distance.toFixed(4)).join(', ')}`);
      return formattedResults;
    } catch (error) {
      logger.error(`Error performing semantic search: ${error instanceof Error ? error.message : String(error)}`);
      logger.error(`Error stack: ${error instanceof Error ? error.stack : 'No stack trace available'}`);
      throw error;
    }
  }

  /**
   * Delete documents from the collection
   * @param ids Array of document IDs to delete
   */
  public async deleteDocuments(ids: string[]): Promise<void> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      logger.debug(`Deleting ${ids.length} documents from ChromaDB`);

      if (this.collection) {
        await this.collection.delete({ ids });
      }

      logger.debug('Documents deleted successfully');
    } catch (error) {
      logger.error('Failed to delete documents:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to delete documents from ChromaDB: ${errorMessage}`);
    }
  }

  /**
   * Check if the ChromaDB service is available
   * @returns Boolean indicating if service is healthy
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Simple health check by getting collection count
      const collections = await this.client.listCollections();
      logger.debug(`ChromaDB health check: Found ${collections.length} collections`);
      return true;
    } catch (error) {
      logger.error('ChromaDB health check failed:', error);
      return false;
    }
  }
}

// Create singleton instance
export const chromaDBService = new ChromaDBService();
