{"path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/services/document-processing.service.ts", "contentHash": "9dde4dbc95be7702551c9a344715f9caee903eb4ea1e21f4651776c8b194243d", "commit": "1b54a030ed1b088c138034b5554ddddd69e29d2b", "timestamp": "2025-07-10T15:36:17+05:30", "units": [{"unitName": "DocumentProcessingService", "unitType": "class", "purpose": "This class processes various types of documentation and textual data from a project, formats them, and generates vector embeddings for storage and retrieval.", "humanReadableExplanation": "The `DocumentProcessingService` acts as a central hub for preparing diverse textual content for a vector database, likely for AI-powered search or knowledge retrieval. It handles four main types of data:\n\n1.  **Automated Documentation (`.doc.json` files):** It scans a specified project directory for these structured JSON files, parses their content (e.g., purpose, unit, inputs), and formats them into a comprehensive text string.\n2.  **README Files:** It identifies and processes README files within the project, using their raw content.\n3.  **Interview Responses:** It takes structured interview data, including questions, answers, and code context, and formats them into a coherent document.\n4.  **Code Comments:** It extracts both block and line comments from provided code files, filtering out very short ones.\n\nFor all these data types, the service prepares the content and then utilizes an injected `VectorService` to update or generate their numerical embeddings, which are essential for semantic search. It also includes a private utility (`chunkText`) to break down long texts into smaller, manageable chunks suitable for embedding models, ensuring context is maintained through overlapping segments. The actual embedding generation for these chunks is delegated to an `azureService`. The class is robust, incorporating error handling and logging throughout its operations.", "dependencies": [{"type": "internal", "name": "VectorService"}, {"type": "internal", "name": "TYPES"}, {"type": "internal", "name": "logger"}, {"type": "internal", "name": "BadRequestError"}, {"type": "internal", "name": "InterviewResponse"}, {"type": "internal", "name": "CodeFile"}, {"type": "external", "name": "fs"}, {"type": "external", "name": "path"}, {"type": "external", "name": "@injectable"}, {"type": "external", "name": "@inject"}, {"type": "external", "name": "azureService"}], "inputs": [{"name": "projectPath", "type": "string", "description": "The root path of the project to scan for documentation files."}, {"name": "projectId", "type": "string", "description": "The ID of the project to associate the embeddings with."}, {"name": "responses", "type": "InterviewResponse[]", "description": "An array of structured interview responses to process."}, {"name": "files", "type": "CodeFile[]", "description": "An array of code file objects containing content and metadata for comment extraction."}, {"name": "texts", "type": "string[]", "description": "An array of text strings for which to generate embeddings."}], "outputs": {"type": "Promise<void> | Promise<number[][]>", "description": "Public methods `processAutomatedDocs`, `processInterviewResponses`, and `processCodeComments` return a Promise that resolves to `void` upon successful completion. The `generateEmbeddings` method returns a Promise that resolves to a 2D array of numbers, representing the generated embeddings.", "throws": ["BadRequestError"]}, "visualDiagram": "```mermaid\nclassDiagram\n    class DocumentProcessingService {\n        -CHUNK_SIZE: number\n        -CHUNK_OVERLAP: number\n        -vectorService: VectorService\n        +constructor(vectorService: VectorService)\n        +processAutomatedDocs(projectPath: string, projectId: string): Promise<void>\n        +processInterviewResponses(responses: InterviewResponse[], projectId: string): Promise<void>\n        +processCodeComments(files: CodeFile[], projectId: string): Promise<void>\n        +generateEmbeddings(texts: string[]): Promise<number[][]>\n        -chunkText(text: string): string[]\n        -formatAutomatedDoc(docData: any): string\n        -formatInterviewResponse(response: InterviewResponse): string\n        -extractComments(file: CodeFile): Array<Comment>\n        -findDocumentationFiles(projectPath: string): Promise<string[]>\n        -findReadmeFiles(projectPath: string): Promise<string[]>\n    }\n\n    class VectorService {\n        +updateDocumentEmbeddings(projectId: string, documents: Document[]): Promise<void>\n    }\n\n    class AzureService {\n        +generateEmbeddings(texts: string[]): Promise<{embeddings: number[][]}>\n    }\n\n    class InterviewResponse {\n        +question: string\n        +answer: string\n        +timestamp: string\n        +context: object\n    }\n\n    class CodeFile {\n        +path: string\n        +content: string\n        +language: string\n    }\n\n    class Document {\n        +filePath: string\n        +content: string\n        +type: 'auto-doc' | 'interview' | 'readme' | 'comment'\n        +metadata?: Record<string, any>\n    }\n\n    class Comment {\n        +content: string\n        +type: 'block' | 'line' | 'docstring'\n        +startLine: number\n        +endLine: number\n    }\n\n    class BadRequestError {\n        // ...\n    }\n\n    DocumentProcessingService --> VectorService : uses\n    DocumentProcessingService --> AzureService : uses\n    DocumentProcessingService ..> InterviewResponse : processes\n    DocumentProcessingService ..> CodeFile : processes\n    DocumentProcessingService ..> Document : creates\n    DocumentProcessingService ..> Comment : extracts\n    DocumentProcessingService ..> BadRequestError : throws\n```"}]}