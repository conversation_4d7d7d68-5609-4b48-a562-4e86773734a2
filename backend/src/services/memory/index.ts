/**
 * Memory Module
 *
 * This module provides services for managing memory and context for AI interactions.
 * It includes components for tracking user context, project business and technical context,
 * code structure, and active tasks.
 */

import { ContextManagerService } from './context-manager.service';
import { UserContextService } from './user-context.service';
import { BusinessContextService } from './business-context.service';
import { TechnicalContextService } from './technical-context.service';
import { CodeContextService } from './code-context.service';
import { TaskContextService } from './task-context.service';
import { SlideBuilderService } from './slide-builder.service';
import { DocumentationSummarizerService } from './documentation-summarizer.service';
import { TokenBudgetService } from './token-budget.service';
import { FreshnessMonitorService } from './freshness-monitor.service';
import { AiContextIntegrationService } from './ai-context-integration.service';
import { ConversationMemoryService } from './conversation-memory.service';

export {
  ContextManagerService,
  UserContextService,
  BusinessContextService,
  TechnicalContextService,
  CodeContextService,
  TaskContextService,
  SlideBuilderService,
  DocumentationSummarizerService,
  TokenBudgetService,
  FreshnessMonitorService,
  AiContextIntegrationService,
  ConversationMemoryService,
};
