/**
 * Conversation Memory Service
 *
 * This service provides optimized memory management for conversations,
 * helping to reduce token usage and improve performance for long conversations.
 * It implements sliding window approaches and token budget management for
 * conversation context retrieval.
 */

import { PrismaService } from '../../db/prisma.service';
import { getConversationRepository } from '../../db/repositories/conversation.repository';
import { getSimpleConversationRepository } from '../../db/repositories/conversation.repository.simple';
import { logger } from '../../common/logger';
import { TokenBudgetService } from './token-budget.service';
import { ConversationRepository } from '../../db/repositories/conversation.repository';

// Define the message type to match what conversationRepository returns
interface ConversationMessage {
  role: string;
  content: string;
  createdAt: Date;
}

// Enum for message importance levels
enum MessageImportance {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
}

// Helper function to ensure messages have createdAt property
function ensureCreatedAt(messages: any[]): ConversationMessage[] {
  return messages.map((msg) => {
    if (!msg.createdAt) {
      return {
        ...msg,
        createdAt: new Date(),
      };
    }
    return msg;
  });
}

export class ConversationMemoryService {
  private conversationRepository: ConversationRepository;

  constructor(
    private _prisma: PrismaService,
    private tokenBudget: TokenBudgetService,
  ) {
    // Initialize conversation repository
    try {
      this.conversationRepository = getConversationRepository();
      logger.info('ConversationMemoryService: Using Inversify conversation repository');
    } catch (error) {
      logger.warn('ConversationMemoryService: Failed to get repository from container, using simple instance:', error);
      this.conversationRepository = getSimpleConversationRepository();
      logger.info('ConversationMemoryService: Using simple conversation repository');
    }
  }

  /**
   * Determine the importance of a message based on various factors
   * This helps prioritize which messages to include in limited context windows
   */
  private getMessageImportance(
    message: ConversationMessage,
    index: number,
    totalMessages: number,
  ): MessageImportance {
    // First message is always critical for context
    if (index === 0) {
      return MessageImportance.CRITICAL;
    }

    // Messages near the beginning are important for context
    if (index < 3) {
      return MessageImportance.HIGH;
    }

    // Messages near the end (but not in the most recent set) are more important
    // than those in the middle
    const isNearEnd = index > totalMessages - 10;
    if (isNearEnd) {
      return MessageImportance.HIGH;
    }

    // Check content characteristics
    const content = message.content.toLowerCase();

    // Messages with code blocks are typically important
    if (content.includes('```') || content.includes('    ') || content.includes('\t')) {
      return MessageImportance.HIGH;
    }

    // Messages with URLs or file paths might be important references
    if (
      content.includes('http') ||
      content.includes('www.') ||
      content.includes('/') ||
      content.includes('\\')
    ) {
      return MessageImportance.HIGH;
    }

    // Longer messages often contain more information
    if (content.length > 500) {
      return MessageImportance.MEDIUM;
    }

    // Default importance for other messages
    return MessageImportance.LOW;
  }

  /**
   * Get context-optimized messages for a conversation
   * This uses a sliding window approach for long conversations
   */
  async getOptimizedMessages(
    conversationId: number,
    options: {
      maxMessages?: number;
      maxTokens?: number;
      recentMessagesWeight?: number;
    } = {},
  ): Promise<ConversationMessage[]> {
    try {
      const allMessages = await this.conversationRepository.getMessagesForLlm(conversationId);

      // Early return if conversation is short
      const maxMessages = options.maxMessages || 10;
      if (allMessages.length <= maxMessages) {
        return ensureCreatedAt(allMessages);
      }

      // Calculate how many recent and earlier messages to include
      const recentMessagesWeight = options.recentMessagesWeight || 0.7;
      const recentMessageCount = Math.ceil(maxMessages * recentMessagesWeight);
      const earlierMessageCount = maxMessages - recentMessageCount;

      logger.info(
        `Using optimized context for conversation ${conversationId}: ` +
          `including ${recentMessageCount} recent and ${earlierMessageCount} earlier messages`,
      );

      // Get the most recent messages
      const recentMessages = allMessages.slice(-recentMessageCount);

      // Handle the case where we don't need any earlier messages
      if (earlierMessageCount <= 0) {
        return ensureCreatedAt(recentMessages);
      }

      // Get a selection of earlier messages
      const earlierMessages = allMessages.slice(0, -recentMessageCount);
      const selectedEarlierMessages = [];

      if (earlierMessages.length > 0) {
        // Strategy 1: If the earlier part is small enough, take all messages
        if (earlierMessages.length <= earlierMessageCount) {
          selectedEarlierMessages.push(...earlierMessages);
        }
        // Strategy 2: Take key messages - first few, a sample from the middle, and a few before recent
        else {
          // Always take the first message for context
          selectedEarlierMessages.push(earlierMessages[0]);

          // If we have enough slots, take the second message too
          if (earlierMessageCount > 2 && earlierMessages.length > 1) {
            selectedEarlierMessages.push(earlierMessages[1]);
          }

          // Calculate remaining slots after taking first messages
          const remainingSlots = earlierMessageCount - selectedEarlierMessages.length;

          // If we have remaining slots, distribute them between middle and pre-recent
          if (remainingSlots > 0) {
            // Prioritize pre-recent messages
            const preRecentCount = Math.min(Math.ceil(remainingSlots / 2), 2);
            const middleCount = remainingSlots - preRecentCount;

            // Get pre-recent messages (just before the recent section)
            const preRecentMessages = earlierMessages.slice(-preRecentCount);
            selectedEarlierMessages.push(...preRecentMessages);

            // Get messages from the middle if we have slots left
            if (middleCount > 0 && earlierMessages.length > 4) {
              // Spread the middle messages evenly
              const middleStart = 2; // Skip the first two
              const middleEnd = earlierMessages.length - preRecentCount;
              const middleRange = middleEnd - middleStart;

              // If we have more middle range than slots, spread them out
              if (middleRange > middleCount) {
                const step = Math.floor(middleRange / middleCount);
                for (let i = 0; i < middleCount; i++) {
                  const index = middleStart + Math.floor(i * step);
                  selectedEarlierMessages.push(earlierMessages[index]);
                }
              }
              // Otherwise just take the first few
              else {
                selectedEarlierMessages.push(
                  ...earlierMessages.slice(middleStart, middleStart + middleCount),
                );
              }
            }
          }
        }
      }

      // Combine the selections and sort by created date
      const combinedMessages = ensureCreatedAt([
        ...selectedEarlierMessages,
        ...recentMessages,
      ]).sort((a, b) => {
        const msgA = a as ConversationMessage;
        const msgB = b as ConversationMessage;
        const dateA = msgA.createdAt instanceof Date ? msgA.createdAt : new Date(msgA.createdAt);
        const dateB = msgB.createdAt instanceof Date ? msgB.createdAt : new Date(msgB.createdAt);
        return dateA.getTime() - dateB.getTime();
      });

      logger.info(
        `Optimized context: returning ${combinedMessages.length} messages ` +
          `(${selectedEarlierMessages.length} earlier + ${recentMessages.length} recent)`,
      );

      return combinedMessages;
    } catch (error) {
      logger.error(`Error getting optimized messages for conversation ${conversationId}:`, error);
      // Fallback to regular message retrieval
      const messages = await this.conversationRepository.getMessagesForLlm(conversationId);
      return ensureCreatedAt(messages);
    }
  }

  /**
   * Estimate token count for conversation context
   * This is useful for checking if we're approaching token limits
   */
  estimateContextTokens(
    messages: Array<{ role: string; content: string; createdAt?: Date }>,
  ): number {
    // Use the token budget service's estimation method if available
    if (this.tokenBudget) {
      return messages.reduce((total, msg) => {
        return total + this.tokenBudget.estimateTokenCount(msg.role + ': ' + msg.content);
      }, 0);
    }

    // Fallback to simple estimation if token budget service is not available
    if (!messages || messages.length === 0) {
      return 0;
    }

    // Simple estimation based on character count
    // A rough rule of thumb is 4 characters per token for English text
    const charsPerToken = 4;

    // Calculate total character count
    let totalChars = 0;

    // Add characters from message content
    for (const msg of messages) {
      // Add role prefixes (e.g., "user: " or "assistant: ")
      totalChars += msg.role.length + 2; // +2 for ": "

      // Add message content
      totalChars += msg.content.length;

      // Add newlines between messages
      totalChars += 2; // "\n\n"
    }

    // Convert to estimated tokens
    return Math.ceil(totalChars / charsPerToken);
  }

  /**
   * Use sliding context window for optimal token usage
   * This function retrieves conversation context optimized for the model's context window
   */
  async getContextWindowOptimizedMessages(
    conversationId: number,
    options: {
      targetTokens?: number;
      maxTokens?: number;
      mustIncludeLatest?: number;
    } = {},
  ): Promise<ConversationMessage[]> {
    try {
      // Set defaults
      const targetTokens = options.targetTokens || 2048; // Target number of tokens to include
      const maxTokens = options.maxTokens || 4096; // Maximum token limit
      const mustIncludeLatest = options.mustIncludeLatest || 4; // Always include the N most recent messages

      // Get all messages
      const allMessages = await this.conversationRepository.getMessagesForLlm(conversationId);
      const messagesWithDates: ConversationMessage[] = allMessages.map((msg) => ({
        role: msg.role,
        content: msg.content,
        createdAt: new Date(), // Since we know the original messages don't have createdAt
      }));

      // Log the number of messages found for debugging
      logger.info(`Found ${messagesWithDates.length} messages for conversation ${conversationId}`);

      // If conversation is empty or short, just return all messages
      if (messagesWithDates.length === 0) {
        logger.info(`No messages found for conversation ${conversationId} - returning empty array`);
        return [];
      }

      if (messagesWithDates.length <= mustIncludeLatest) {
        logger.info(
          `Conversation ${conversationId} is short (${messagesWithDates.length} messages) - returning all messages`,
        );
        return messagesWithDates;
      }

      // Always include the most recent messages
      const recentMessages = messagesWithDates.slice(-mustIncludeLatest);

      // Early estimate of tokens for recent messages
      let recentTokens = this.estimateContextTokens(recentMessages);

      // If recent messages already exceed our target, just return them
      if (recentTokens >= targetTokens) {
        logger.info(
          `Recent ${mustIncludeLatest} messages already exceed token target (${recentTokens} > ${targetTokens})`,
        );
        return recentMessages;
      }

      // Available token budget for earlier messages
      const earlierTokenBudget = Math.min(maxTokens - recentTokens, targetTokens - recentTokens);
      logger.info(`Token budget for earlier messages: ${earlierTokenBudget}`);

      // Gather earlier messages until we approach the token budget
      const earlierMessages: ConversationMessage[] = messagesWithDates.slice(0, -mustIncludeLatest);

      // Enhanced selection algorithm:
      // 1. Assign importance to each message
      // 2. Sort by importance and recency
      // 3. Select messages until budget is exhausted

      // Prepare messages with importance scores
      interface ScoredMessage {
        message: ConversationMessage;
        importance: MessageImportance;
        tokenCount: number;
        index: number;
      }

      const scoredMessages: ScoredMessage[] = earlierMessages.map(
        (msg: ConversationMessage, idx) => ({
          message: msg,
          importance: this.getMessageImportance(
            { ...msg, createdAt: msg.createdAt || new Date() },
            idx,
            messagesWithDates.length,
          ),
          tokenCount: this.estimateContextTokens([msg]),
          index: idx,
        }),
      );

      // Sort by importance (high to low) and then by recency (newer first)
      scoredMessages.sort((a: ScoredMessage, b: ScoredMessage) => {
        // First sort by importance
        if (a.importance !== b.importance) {
          return b.importance - a.importance;
        }

        // Then by recency (newer first)
        const msgA = a.message;
        const msgB = b.message;
        const dateA = msgA.createdAt instanceof Date ? msgA.createdAt : new Date(msgA.createdAt);
        const dateB = msgB.createdAt instanceof Date ? msgB.createdAt : new Date(msgB.createdAt);
        return dateB.getTime() - dateA.getTime();
      });

      // Select messages until budget is exhausted
      const selectedEarlierMessages = [];
      let usedTokens = 0;

      // First, always include the first message for context
      if (earlierMessages.length > 0) {
        const firstMessage = earlierMessages[0];
        const firstMessageTokens = this.estimateContextTokens([firstMessage]);
        selectedEarlierMessages.push(firstMessage);
        usedTokens += firstMessageTokens;

        // Remove the first message from consideration if it's in the scored messages
        const firstMessageIndex = scoredMessages.findIndex((sm) => sm.index === 0);
        if (firstMessageIndex !== -1) {
          scoredMessages.splice(firstMessageIndex, 1);
        }
      }

      // Then add messages by importance until budget is exhausted
      for (const scoredMsg of scoredMessages) {
        if (usedTokens + scoredMsg.tokenCount <= earlierTokenBudget) {
          selectedEarlierMessages.push(scoredMsg.message);
          usedTokens += scoredMsg.tokenCount;
        } else if (scoredMsg.importance >= MessageImportance.HIGH) {
          // For high importance messages, try to include them even if we go slightly over budget
          // but only if we're not exceeding maxTokens
          if (usedTokens + scoredMsg.tokenCount <= maxTokens - recentTokens) {
            selectedEarlierMessages.push(scoredMsg.message);
            usedTokens += scoredMsg.tokenCount;
            logger.info(`Including high importance message despite exceeding target budget`);
          }
        }
      }

      logger.info(
        `Selected ${selectedEarlierMessages.length} earlier messages using ${usedTokens} tokens`,
      );

      // Combine selected earlier messages with recent messages and sort by timestamp
      const combinedMessages = ensureCreatedAt([
        ...selectedEarlierMessages,
        ...recentMessages,
      ]).sort((a, b) => {
        const msgA = a as ConversationMessage;
        const msgB = b as ConversationMessage;
        const dateA = msgA.createdAt instanceof Date ? msgA.createdAt : new Date(msgA.createdAt);
        const dateB = msgB.createdAt instanceof Date ? msgB.createdAt : new Date(msgB.createdAt);
        return dateA.getTime() - dateB.getTime();
      });

      // Log what we're doing
      const totalEstimatedTokens = this.estimateContextTokens(combinedMessages);
      logger.info(
        `Token-optimized context: ${combinedMessages.length} messages, estimated ${totalEstimatedTokens} tokens`,
      );

      return combinedMessages;
    } catch (error) {
      logger.error(
        `Error getting token-optimized messages for conversation ${conversationId}:`,
        error,
      );
      // Fallback to regular message retrieval
      const messages = await this.conversationRepository.getMessagesForLlm(conversationId);
      return ensureCreatedAt(messages);
    }
  }

  /**
   * Get messages optimized for semantic relevance to a query
   * This is useful for retrieving context that's most relevant to the current question
   */
  async getSemanticRelevantMessages(
    conversationId: number,
    query: string,
    options: {
      maxMessages?: number;
      includeLatest?: number;
    } = {},
  ): Promise<ConversationMessage[]> {
    try {
      // Set defaults
      const maxMessages = options.maxMessages || 10;
      const includeLatest = options.includeLatest || 2;

      // Get all messages
      const allMessages = await this.conversationRepository.getMessagesForLlm(conversationId);

      // If conversation is short, just return all messages
      if (allMessages.length <= maxMessages) {
        return ensureCreatedAt(allMessages);
      }

      // Always include the latest messages
      const latestMessages = allMessages.slice(-includeLatest);

      // For the remaining slots, select messages based on semantic relevance
      // Since we don't have a vector DB or embedding model here, we'll use a simple
      // keyword-based approach as a fallback
      const remainingSlots = maxMessages - includeLatest;

      if (remainingSlots <= 0) {
        return ensureCreatedAt(latestMessages);
      }

      // Extract keywords from the query (simple approach)
      const keywords = query
        .toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter((word) => word.length > 3); // Only consider words longer than 3 chars

      // Score earlier messages based on keyword matches
      const earlierMessages = allMessages.slice(0, -includeLatest);
      const scoredMessages = earlierMessages.map((msg) => {
        const content = msg.content.toLowerCase();
        // Count keyword occurrences
        const score = keywords.reduce((total, keyword) => {
          const regex = new RegExp(keyword, 'g');
          const matches = content.match(regex);
          return total + (matches ? matches.length : 0);
        }, 0);

        return { message: msg, score };
      });

      // Sort by score (highest first)
      scoredMessages.sort((a, b) => b.score - a.score);

      // Select top scoring messages
      const selectedMessages = scoredMessages.slice(0, remainingSlots).map((item) => item.message);

      // Combine and sort by timestamp
      const combinedMessages = ensureCreatedAt([...selectedMessages, ...latestMessages]).sort(
        (a, b) => {
          const msgA = a as ConversationMessage;
          const msgB = b as ConversationMessage;
          const dateA = msgA.createdAt instanceof Date ? msgA.createdAt : new Date(msgA.createdAt);
          const dateB = msgB.createdAt instanceof Date ? msgB.createdAt : new Date(msgB.createdAt);
          return dateA.getTime() - dateB.getTime();
        },
      );

      return combinedMessages;
    } catch (error) {
      logger.error(
        `Error getting semantically relevant messages for conversation ${conversationId}:`,
        error,
      );
      // Fallback to regular optimization
      return this.getOptimizedMessages(conversationId, { maxMessages: options.maxMessages });
    }
  }
}
