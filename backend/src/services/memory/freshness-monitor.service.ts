import { PrismaService } from '../../db/prisma.service';
// import { Cron } from '@nestjs/schedule'; // Removed NestJS dependency
import { DocumentationSummarizerService } from './documentation-summarizer.service';
import * as path from 'path';

/**
 * Service for monitoring and updating the freshness of context components
 */

export class FreshnessMonitorService {
  constructor(
    private prisma: PrismaService,
    private documentationSummarizer: DocumentationSummarizerService,
  ) {}

  /**
   * Check and update freshness of context components
   */
  // Run every 6 hours (removed NestJS Cron decorator)
  async checkFreshness(): Promise<void> {
    console.log('Running freshness check...');

    // Check for stale file summaries based on age
    await this.markOldFileSummariesStale();

    // Check for stale module summaries based on file changes
    await this.markModuleSummariesStale();

    // Check for outdated memory contexts
    await this.markOutdatedMemoryContexts();

    // Refresh stale summaries for active projects
    await this.refreshStaleContextsForActiveProjects();

    console.log('Freshness check completed');
  }

  /**
   * Process git changes to mark affected files as stale
   */
  async processGitChanges(projectId: number, changedFiles: string[]): Promise<void> {
    console.log(
      `Processing git changes for project ${projectId}, ${changedFiles.length} files changed`,
    );

    // Mark changed files as stale
    await this.documentationSummarizer.markStaleFiles(projectId, changedFiles);

    // Mark associated contexts as outdated
    await this.markAssociatedContextsOutdated(projectId, changedFiles);
  }

  /**
   * Mark old file summaries as stale
   */
  private async markOldFileSummariesStale(): Promise<void> {
    const staleDate = new Date();
    staleDate.setDate(staleDate.getDate() - 7); // 7 days old

    const result = await this.prisma.file_summaries.updateMany({
      where: {
        last_updated: {
          lt: staleDate,
        },
      },
      data: {
        is_stale: true,
      },
    });

    console.log(`Marked ${result.count} old file summaries as stale`);
  }

  /**
   * Mark module summaries as stale if any of their files are stale
   */
  private async markModuleSummariesStale(): Promise<void> {
    // Find all projects
    const projects = await this.prisma.projects.findMany({
      where: {
        is_active: true,
      },
      select: { id: true },
    });

    let totalStaleModules = 0;

    for (const project of projects) {
      // Find stale file summaries for this project
      const staleFiles = await this.prisma.file_summaries.findMany({
        where: {
          project_id: project.id,
          is_stale: true,
        },
        select: {
          file_path: true,
        },
      });

      // Get unique module paths from stale files
      const modulePaths = new Set<string>();

      for (const file of staleFiles) {
        const modulePath = path.dirname(file.file_path);
        modulePaths.add(modulePath);

        // Also add parent directories
        let parentPath = modulePath;
        while (parentPath && parentPath !== '.') {
          parentPath = path.dirname(parentPath);
          modulePaths.add(parentPath);
        }
      }

      if (modulePaths.size > 0) {
        // Mark module summaries as stale
        const result = await this.prisma.module_summaries.updateMany({
          where: {
            project_id: project.id,
            module_path: {
              in: Array.from(modulePaths),
            },
          },
          data: {
            is_stale: true,
          },
        });

        totalStaleModules += result.count;
      }
    }

    console.log(`Marked ${totalStaleModules} module summaries as stale`);
  }

  /**
   * Mark memory contexts as outdated based on slide deck changes
   */
  private async markOutdatedMemoryContexts(): Promise<void> {
    // Find slide decks that have been updated
    const updatedSlideDecks = await this.prisma.slide_decks.findMany({
      where: {
        updated_at: {
          gt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
      select: {
        id: true,
        project_id: true,
      },
    });

    if (updatedSlideDecks.length > 0) {
      // Mark associated memory contexts for update
      const result = await this.prisma.memory_contexts.updateMany({
        where: {
          slide_deck_id: {
            in: updatedSlideDecks.map((deck: { id: number }) => deck.id),
          },
        },
        data: {
          last_synced_at: null, // Null indicates needs sync
        },
      });

      console.log(`Marked ${result.count} memory contexts as outdated due to slide deck changes`);
    }
  }

  /**
   * Mark associated contexts as outdated when files change
   */
  private async markAssociatedContextsOutdated(
    projectId: number,
    changedFiles: string[],
  ): Promise<void> {
    // Check if any of the changed files are code files
    const codeFileExtensions = ['.js', '.ts', '.py', '.go', '.java', '.c', '.cpp', '.cs'];
    const hasCodeChanges = changedFiles.some((file) =>
      codeFileExtensions.some((ext) => file.endsWith(ext)),
    );

    if (hasCodeChanges) {
      // Mark code context as outdated
      const result = await this.prisma.memory_contexts.updateMany({
        where: {
          project_id: projectId,
          contextType: 'CODE',
        },
        data: {
          last_synced_at: null,
        },
      });

      console.log(`Marked ${result.count} code contexts as outdated`);
    }

    // Check for documentation files
    const docFileExtensions = ['.md', '.txt', '.rst', '.adoc', '.html'];
    const hasDocChanges = changedFiles.some((file) =>
      docFileExtensions.some((ext) => file.endsWith(ext)),
    );

    if (hasDocChanges) {
      // Mark technical context as potentially outdated
      const result = await this.prisma.memory_contexts.updateMany({
        where: {
          project_id: projectId,
          contextType: 'TECHNICAL',
        },
        data: {
          last_synced_at: null,
        },
      });

      console.log(`Marked ${result.count} technical contexts as outdated`);
    }
  }

  /**
   * Refresh stale contexts for active projects
   */
  private async refreshStaleContextsForActiveProjects(): Promise<void> {
    // Find active projects with stale contexts
    const activeProjects = await this.prisma.projects.findMany({
      where: {
        is_active: true,
        memory_contexts: {
          some: {
            last_synced_at: null,
          },
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    console.log(`Found ${activeProjects.length} active projects with stale contexts`);

    // Process each project
    for (const project of activeProjects) {
      console.log(`Refreshing stale contexts for project ${project.name} (ID: ${project.id})`);

      // Refresh stale file and module summaries
      await this.documentationSummarizer.refreshStaleSummaries(project.id);

      // Find stale memory contexts
      const staleContexts = await this.prisma.memory_contexts.findMany({
        where: {
          project_id: project.id,
          last_synced_at: null,
        },
        select: {
          id: true,
          contextType: true,
          name: true,
        },
      });

      console.log(`Found ${staleContexts.length} stale memory contexts for project ${project.id}`);

      // Mark contexts as refreshed - we don't actually refresh them here
      // because they will be refreshed on-demand when accessed by the context providers
      await this.prisma.memory_contexts.updateMany({
        where: {
          id: {
            in: staleContexts.map((ctx: { id: number }) => ctx.id),
          },
        },
        data: {
          last_synced_at: new Date(),
        },
      });
    }
  }

  /**
   * Check for unused file summaries
   */
  async cleanupUnusedSummaries(): Promise<void> {
    // Find summaries for files that no longer exist
    const projects = await this.prisma.projects.findMany({
      where: {
        is_active: true,
        local_path: {
          not: null,
        },
      },
      select: {
        id: true,
        local_path: true,
      },
    });

    let totalRemoved = 0;

    for (const project of projects) {
      if (!project.local_path) continue;

      // Get all file summaries for this project
      const fileSummaries = await this.prisma.file_summaries.findMany({
        where: {
          project_id: project.id,
        },
        select: {
          id: true,
          file_path: true,
        },
      });

      // Check if files exist
      const nonExistentFiles = [];

      for (const summary of fileSummaries) {
        const fullPath = path.join(project.local_path, summary.file_path);

        try {
          const exists = require('fs').existsSync(fullPath);
          if (!exists) {
            nonExistentFiles.push(summary.id);
          }
        } catch (error) {
          // If we can't check, assume it doesn't exist
          nonExistentFiles.push(summary.id);
        }
      }

      if (nonExistentFiles.length > 0) {
        // Delete summaries for non-existent files
        const result = await this.prisma.file_summaries.deleteMany({
          where: {
            id: {
              in: nonExistentFiles,
            },
          },
        });

        totalRemoved += result.count;
      }
    }

    console.log(`Removed ${totalRemoved} summaries for non-existent files`);
  }

  /**
   * Force refresh all contexts for a project
   */
  async forceRefreshProject(projectId: number): Promise<void> {
    console.log(`Force refreshing project ${projectId}`);

    // Mark all summaries as stale
    await this.prisma.file_summaries.updateMany({
      where: { project_id: projectId },
      data: { is_stale: true },
    });

    await this.prisma.module_summaries.updateMany({
      where: { project_id: projectId },
      data: { is_stale: true },
    });

    // Mark all contexts as needing refresh
    await this.prisma.memory_contexts.updateMany({
      where: { project_id: projectId },
      data: { last_synced_at: null },
    });

    // Refresh file and module summaries
    await this.documentationSummarizer.generateProjectSummaries(projectId, {
      forceRefresh: true,
    });

    console.log(`Project ${projectId} refresh completed`);
  }

  /**
   * Check if a project has stale contexts
   */
  async hasStaleContexts(projectId: number): Promise<boolean> {
    // Check for stale file summaries
    const staleFiles = await this.prisma.file_summaries.count({
      where: {
        project_id: projectId,
        is_stale: true,
      },
    });

    if (staleFiles > 0) return true;

    // Check for stale module summaries
    const staleModules = await this.prisma.module_summaries.count({
      where: {
        project_id: projectId,
        is_stale: true,
      },
    });

    if (staleModules > 0) return true;

    // Check for stale memory contexts
    const staleContexts = await this.prisma.memory_contexts.count({
      where: {
        project_id: projectId,
        last_synced_at: null,
      },
    });

    return staleContexts > 0;
  }

  /**
   * Get freshness status for a project
   */
  async getProjectFreshnessStatus(projectId: number): Promise<{
    freshnessPercentage: number;
    staleFiles: number;
    staleModules: number;
    staleContexts: number;
    totalFiles: number;
    totalModules: number;
    totalContexts: number;
    needsRefresh: boolean;
  }> {
    const staleFiles = await this.prisma.file_summaries.count({
      where: {
        project_id: projectId,
        is_stale: true,
      },
    });

    const staleModules = await this.prisma.module_summaries.count({
      where: {
        project_id: projectId,
        is_stale: true,
      },
    });

    const staleContexts = await this.prisma.memory_contexts.count({
      where: {
        project_id: projectId,
        last_synced_at: null,
      },
    });

    const totalFiles = await this.prisma.file_summaries.count({
      where: { project_id: projectId },
    });

    const totalModules = await this.prisma.module_summaries.count({
      where: { project_id: projectId },
    });

    const totalContexts = await this.prisma.memory_contexts.count({
      where: { project_id: projectId },
    });

    // Calculate overall freshness percentage
    const totalItems = totalFiles + totalModules + totalContexts;
    const staleItems = staleFiles + staleModules + staleContexts;

    const freshnessPercentage =
      totalItems > 0 ? Math.round(((totalItems - staleItems) / totalItems) * 100) : 100;

    return {
      freshnessPercentage,
      staleFiles,
      staleModules,
      staleContexts,
      totalFiles,
      totalModules,
      totalContexts,
      needsRefresh: staleItems > 0,
    };
  }

  /**
   * Set up monitoring for a project
   */
  async setupProjectMonitoring(projectId: number): Promise<void> {
    // Initialize by generating summaries
    await this.documentationSummarizer.generateProjectSummaries(projectId);

    // Mark all contexts as fresh
    await this.prisma.memory_contexts.updateMany({
      where: { project_id: projectId },
      data: { last_synced_at: new Date() },
    });

    console.log(`Project ${projectId} monitoring setup completed`);
  }
}
