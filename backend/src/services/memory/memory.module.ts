import prismaService from '../../db/prisma.service';
import { ContextManagerService } from './context-manager.service';
import { UserContextService } from './user-context.service';
import { BusinessContextService } from './business-context.service';
import { TechnicalContextService } from './technical-context.service';
import { CodeContextService } from './code-context.service';
import { TaskContextService } from './task-context.service';
import { SlideBuilderService } from './slide-builder.service';
import { DocumentationSummarizerService } from './documentation-summarizer.service';
import { TokenBudgetService } from './token-budget.service';
import { FreshnessMonitorService } from './freshness-monitor.service';
import { AiContextIntegrationService } from './ai-context-integration.service';
import { ConversationMemoryService } from './conversation-memory.service';

/**
 * Memory module that provides context management for AI
 */
class MemoryModule {
  private static _instance: MemoryModule;

  // Service instances
  private _contextManager: ContextManagerService;
  private _userContext: UserContextService;
  private _businessContext: BusinessContextService;
  private _technicalContext: TechnicalContextService;
  private _codeContext: CodeContextService;
  private _taskContext: TaskContextService;
  private _slideBuilder: SlideBuilderService;
  private _documentationSummarizer: DocumentationSummarizerService;
  private _tokenBudget: TokenBudgetService;
  private _freshnessMonitor: FreshnessMonitorService; // Used for future feature implementation
  private _aiContextIntegration: AiContextIntegrationService;
  private _conversationMemory: ConversationMemoryService;

  private constructor() {
    // Initialize services
    this._tokenBudget = new TokenBudgetService(prismaService);
    this._userContext = new UserContextService(prismaService);
    this._businessContext = new BusinessContextService(prismaService);
    this._technicalContext = new TechnicalContextService(prismaService);
    this._codeContext = new CodeContextService(prismaService);
    this._taskContext = new TaskContextService(prismaService);
    this._slideBuilder = new SlideBuilderService(prismaService);
    this._documentationSummarizer = new DocumentationSummarizerService(prismaService);

    // Initialize conversation memory service
    this._conversationMemory = new ConversationMemoryService(prismaService, this._tokenBudget);

    // Initialize freshness monitor with documentation summarizer
    this._freshnessMonitor = new FreshnessMonitorService(
      prismaService,
      this._documentationSummarizer,
    );

    // Initialize context manager with all context providers
    this._contextManager = new ContextManagerService(
      prismaService,
      this._userContext,
      this._businessContext,
      this._technicalContext,
      this._codeContext,
      this._taskContext,
      this._tokenBudget,
    );

    // Initialize AI context integration
    this._aiContextIntegration = new AiContextIntegrationService(this._contextManager);
  }

  public static getInstance(): MemoryModule {
    if (!MemoryModule._instance) {
      MemoryModule._instance = new MemoryModule();
    }
    return MemoryModule._instance;
  }

  // Getters for services
  get contextManager(): ContextManagerService {
    return this._contextManager;
  }

  get userContext(): UserContextService {
    return this._userContext;
  }

  get slideBuilder(): SlideBuilderService {
    return this._slideBuilder;
  }

  get documentationSummarizer(): DocumentationSummarizerService {
    return this._documentationSummarizer;
  }

  get aiContextIntegration(): AiContextIntegrationService {
    return this._aiContextIntegration;
  }

  get conversationMemory(): ConversationMemoryService {
    return this._conversationMemory;
  }
}

// Export the class, not an instance, to allow lazy initialization
export default MemoryModule;
