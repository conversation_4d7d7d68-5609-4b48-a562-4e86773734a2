import { PrismaService } from '../../db/prisma.service';
import {
  ContextBlock,
  ContextProviderOptions,
  SummaryOptions,
} from '../../types/memory/context.types';
import { ContextFormat, ContextType } from '../../generated/prisma';
import { estimateTokenCount } from './utils/token-counting';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Service for managing code context
 */

export class CodeContextService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get the context priority
   */
  getPriority(): number {
    return 1; // Default priority for code context
  }

  /**
   * Get context type
   */
  getContextType(): ContextType {
    return ContextType.CODE;
  }

  /**
   * Get maximum tokens for this context type
   */
  getMaxTokens(): number {
    return 300;
  }

  /**
   * Get code context for a project
   */
  async getContext(projectId: number, _options?: ContextProviderOptions): Promise<ContextBlock> {
    // Find existing code context
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.CODE,
        is_active: true,
      },
    });

    // If context exists and not stale, return it
    if (contextMemory && contextMemory.last_synced_at) {
      // Get the latest version
      const latestVersion = await this.prisma.context_versions.findFirst({
        where: {
          context_id: contextMemory.id,
        },
        orderBy: {
          version: 'desc',
        },
      });

      if (latestVersion) {
        return {
          type: ContextType.CODE,
          content: latestVersion.content,
          format: latestVersion.format,
          tokenCount: latestVersion.token_count || estimateTokenCount(latestVersion.content),
          metadata: {
            context_id: contextMemory.id,
            version: latestVersion.version,
          },
        };
      }
    }

    // Need to create or refresh code context from file summaries
    return await this.generateCodeContext(projectId, contextMemory);
  }

  /**
   * Generate code context from file and module summaries
   */
  private async generateCodeContext(
    projectId: number,
    existingContext?: any,
  ): Promise<ContextBlock> {
    // Get project to check if there's a local path
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: { local_path: true },
    });

    if (!project?.local_path) {
      // No local path, use any existing context or create minimal one
      if (existingContext) {
        // Get the latest version
        const latestVersion = await this.prisma.context_versions.findFirst({
          where: {
            context_id: existingContext.id,
          },
          orderBy: {
            version: 'desc',
          },
        });

        if (latestVersion) {
          return {
            type: ContextType.CODE,
            content: latestVersion.content,
            format: latestVersion.format,
            tokenCount: latestVersion.token_count || estimateTokenCount(latestVersion.content),
            metadata: {
              context_id: existingContext.id,
              version: latestVersion.version,
            },
          };
        }
      }

      // Create minimal context
      return await this.createMinimalCodeContext(projectId);
    }

    // Ensure we have module summaries
    await this.summarizeProjectModules(projectId, project.local_path);

    // Get module summaries
    const moduleSummaries = await this.prisma.module_summaries.findMany({
      where: {
        project_id: projectId,
        is_stale: false,
      },
      orderBy: {
        module_path: 'asc',
      },
    });

    // Create markdown content from module summaries
    let markdownContent = `# Code Structure\n\n`;

    moduleSummaries.forEach((module: any) => {
      const depth = module.module_path.split('/').filter(Boolean).length;
      const prefix = '#'.repeat(Math.min(depth + 2, 6)); // H2-H6 based on depth
      const displayPath = module.module_path || 'Root';

      markdownContent += `${prefix} ${displayPath}\n\n${module.summary}\n\n`;
    });

    // Get important file summaries (top 10)
    const fileSummaries = await this.prisma.file_summaries.findMany({
      where: {
        project_id: projectId,
        is_stale: false,
      },
      orderBy: {
        file_path: 'asc',
      },
      take: 10,
    });

    if (fileSummaries.length > 0) {
      markdownContent += `## Key Files\n\n`;

      fileSummaries.forEach((file: any) => {
        markdownContent += `- **${file.file_path}**: ${file.summary}\n`;
      });
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Create or update context
    if (existingContext) {
      // Update existing context
      const newVersion = await this.prisma.context_versions.create({
        data: {
          context_id: existingContext.id,
          version: existingContext.version + 1,
          content: markdownContent,
          format: ContextFormat.MARKDOWN,
          token_count: tokenCount,
          changed_by: 'system',
          change_reason: 'Updated from code summaries',
        },
      });

      await this.prisma.memory_contexts.update({
        where: { id: existingContext.id },
        data: {
          version: newVersion.version,
          token_count: tokenCount,
          last_updated: new Date(),
          last_synced_at: new Date(),
        },
      });

      return {
        type: ContextType.CODE,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        tokenCount,
        metadata: {
          context_id: existingContext.id,
          version: newVersion.version,
        },
      };
    } else {
      // Create new context
      const newContext = await this.prisma.memory_contexts.create({
        data: {
          project_id: projectId,
          contextType: ContextType.CODE,
          name: 'Code Context',
          description: 'Summary of code structure and key files',
          token_count: tokenCount,
          last_synced_at: new Date(),
          context_versions: {
            create: {
              version: 1,
              content: markdownContent,
              format: ContextFormat.MARKDOWN,
              token_count: tokenCount,
              changed_by: 'system',
              change_reason: 'Initial creation from code summaries',
            },
          },
        },
        include: {
          context_versions: true,
        },
      });

      return {
        type: ContextType.CODE,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        tokenCount,
        metadata: {
          context_id: newContext.id,
          version: 1,
        },
      };
    }
  }

  /**
   * Create minimal code context when no local path exists
   */
  private async createMinimalCodeContext(projectId: number): Promise<ContextBlock> {
    const content = `# Code Structure\n\nNo local path is configured for this project. Code context is limited.\n\nSet up the local path to enable automatic code analysis and summaries.`;

    const tokenCount = estimateTokenCount(content);

    const newContext = await this.prisma.memory_contexts.create({
      data: {
        project_id: projectId,
        contextType: ContextType.CODE,
        name: 'Code Context',
        description: 'Limited code context information',
        token_count: tokenCount,
        last_synced_at: new Date(),
        context_versions: {
          create: {
            version: 1,
            content,
            format: ContextFormat.MARKDOWN,
            token_count: tokenCount,
            changed_by: 'system',
            change_reason: 'Initial minimal creation',
          },
        },
      },
      include: {
        context_versions: true,
      },
    });

    return {
      type: ContextType.CODE,
      content,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        context_id: newContext.id,
        version: 1,
      },
    };
  }

  /**
   * Summarize project modules and files
   */
  async summarizeProjectModules(
    projectId: number,
    localPath: string,
    options?: SummaryOptions,
  ): Promise<void> {
    const { recursive = true, depth = 2 } = options || {};

    if (!fs.existsSync(localPath)) {
      throw new Error(`Local path does not exist: ${localPath}`);
    }

    // Get directory structure
    const files = await this.getProjectFiles(localPath, recursive, depth);

    // Group files by module (directory)
    const moduleFiles = new Map<string, string[]>();

    files.forEach((file) => {
      const modulePath = path.dirname(file);
      if (!moduleFiles.has(modulePath)) {
        moduleFiles.set(modulePath, []);
      }
      moduleFiles.get(modulePath)?.push(file);
    });

    // Summarize each module
    for (const [modulePath, moduleFileList] of moduleFiles.entries()) {
      // Get summaries for files in this module
      const fileSummaries = await Promise.all(
        moduleFileList.map(async (file) => {
          const relativePath = path.relative(localPath, file);
          return await this.getFileSummary(projectId, relativePath, file);
        }),
      );

      // Generate module summary based on file summaries
      const moduleRelativePath = path.relative(localPath, modulePath);
      const moduleName = path.basename(modulePath);

      // Compile file summaries into a single text
      const summariesText = fileSummaries
        .filter((s) => s) // Filter out null summaries
        .map((s) => `${path.basename(s.filePath)}: ${s.summary}`)
        .join('\n');

      // Generate module summary
      const summary = await this.generateModuleSummary(
        moduleName,
        moduleRelativePath,
        summariesText,
      );

      // Save module summary
      await this.prisma.module_summaries.upsert({
        where: {
          project_id_module_path: {
            project_id: projectId,
            module_path: moduleRelativePath || '.',
          },
        },
        update: {
          summary,
          token_count: estimateTokenCount(summary),
          last_updated: new Date(),
          is_stale: false,
        },
        create: {
          project_id: projectId,
          module_path: moduleRelativePath || '.',
          summary,
          token_count: estimateTokenCount(summary),
          is_stale: false,
        },
      });
    }
  }

  /**
   * Get file summary
   */
  private async getFileSummary(
    projectId: number,
    relativePath: string,
    fullPath: string,
  ): Promise<any> {
    // Check if we already have a summary that is not stale
    const existingSummary = await this.prisma.file_summaries.findUnique({
      where: {
        project_id_file_path: {
          project_id: projectId,
          file_path: relativePath,
        },
      },
    });

    if (existingSummary && !existingSummary.is_stale) {
      return existingSummary;
    }

    // Generate new summary
    try {
      // Read file content
      const fileContent = fs.readFileSync(fullPath, 'utf-8');

      // Extract existing @summary comments
      const existingComment = this.extractSummaryComment(fileContent);

      let summary;
      if (existingComment) {
        // Use existing summary from comment
        summary = existingComment;
      } else {
        // Generate simple summary based on file type
        summary = this.generateSimpleSummary(fileContent, relativePath);
      }

      // Save summary
      const result = await this.prisma.file_summaries.upsert({
        where: {
          project_id_file_path: {
            project_id: projectId,
            file_path: relativePath,
          },
        },
        update: {
          summary,
          token_count: estimateTokenCount(summary),
          last_updated: new Date(),
          is_stale: false,
        },
        create: {
          project_id: projectId,
          file_path: relativePath,
          summary,
          token_count: estimateTokenCount(summary),
          is_stale: false,
        },
      });

      return result;
    } catch (error) {
      console.error(`Error summarizing file ${relativePath}:`, error);
      return null;
    }
  }

  /**
   * Extract @summary comment from file content
   */
  private extractSummaryComment(fileContent: string): string | null {
    // Different regex patterns for different languages
    const patterns = [
      /\/\*\*\s*@summary\s*([\s\S]*?)\*\//, // JS/TS block comment
      /\/\/\s*@summary\s*(.*)$/m, // JS/TS line comment
      /#\s*@summary\s*(.*)$/m, // Python/Ruby comment
      /<!--\s*@summary\s*([\s\S]*?)-->/, // HTML/XML comment
    ];

    for (const pattern of patterns) {
      const match = fileContent.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Generate a simple summary based on file type and content
   */
  private generateSimpleSummary(fileContent: string, filePath: string): string {
    // In a real implementation, we would use AI here
    // For now, use a simple heuristic approach
    const ext = path.extname(filePath).toLowerCase();
    const filename = path.basename(filePath);

    // Check for common file types
    if (ext === '.ts' || ext === '.js') {
      // Look for class and function declarations
      const classMatch = fileContent.match(/class\s+(\w+)/);
      const fnMatch = fileContent.match(/function\s+(\w+)/);
      const exportMatch = fileContent.match(
        /export\s+(const|let|var|function|class|default)\s+(\w+)/,
      );

      if (classMatch) {
        return `Defines ${classMatch[1]} class for ${this.inferPurpose(filename)}.`;
      } else if (fnMatch) {
        return `Contains ${fnMatch[1]} function for ${this.inferPurpose(filename)}.`;
      } else if (exportMatch) {
        return `Exports ${exportMatch[2]} ${exportMatch[1]} for ${this.inferPurpose(filename)}.`;
      }
    } else if (ext === '.json') {
      return `Configuration file containing ${this.countJsonEntries(fileContent)} entries.`;
    } else if (ext === '.html') {
      return `HTML template for ${this.inferPurpose(filename)}.`;
    } else if (ext === '.css' || ext === '.scss') {
      return `Stylesheet for ${this.inferPurpose(filename)}.`;
    } else if (ext === '.md') {
      const firstLine = fileContent.split('\n')[0].replace(/^#\s+/, '');
      return `Documentation: ${firstLine || this.inferPurpose(filename)}`;
    }

    // Default summary
    return `${filename}: File with ${fileContent.split('\n').length} lines.`;
  }

  /**
   * Generate module summary from file summaries
   */
  private async generateModuleSummary(
    moduleName: string,
    modulePath: string,
    _summariesText: string, // This would be used in a real AI implementation
  ): Promise<string> {
    // In a real implementation, we would use AI here with the summariesText
    // For now, provide a generic summary based on module path

    if (modulePath === '.' || modulePath === '') {
      return `Root directory of the project containing primary configuration files and entry points.`;
    }

    // Check for common module types
    if (modulePath.includes('src')) {
      if (modulePath.includes('controllers') || modulePath.includes('routes')) {
        return `Controllers/routes handling API endpoints and request processing for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('models') || modulePath.includes('schemas')) {
        return `Data models and schemas defining the structure for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('services')) {
        return `Services implementing business logic for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('utils') || modulePath.includes('helpers')) {
        return `Utility functions and helpers for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('components')) {
        return `UI components for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('views') || modulePath.includes('pages')) {
        return `View templates/pages for ${this.inferPurpose(moduleName)}.`;
      } else if (modulePath.includes('tests')) {
        return `Test files for validating ${this.inferPurpose(moduleName)} functionality.`;
      }
    }

    // Generic module summary
    return `Module for ${this.inferPurpose(moduleName)} functionality.`;
  }

  /**
   * Infer purpose from filename
   */
  private inferPurpose(filename: string): string {
    // Remove extension
    const name = filename.replace(/\.\w+$/, '');

    // Convert camelCase/PascalCase to spaces
    const words = name
      .replace(/([A-Z])/g, ' $1')
      .replace(/[-_]/g, ' ')
      .toLowerCase()
      .trim();

    return words;
  }

  /**
   * Count entries in a JSON file
   */
  private countJsonEntries(jsonContent: string): number {
    try {
      const parsed = JSON.parse(jsonContent);
      return Object.keys(parsed).length;
    } catch {
      return 0;
    }
  }

  /**
   * Get project files recursively
   */
  private async getProjectFiles(
    dir: string,
    recursive: boolean = true,
    maxDepth: number = Infinity,
    currentDepth: number = 0,
  ): Promise<string[]> {
    const files: string[] = [];

    if (currentDepth > maxDepth) {
      return files;
    }

    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        // Skip node_modules, .git, etc.
        if (
          entry.name.startsWith('.') ||
          entry.name === 'node_modules' ||
          entry.name === 'dist' ||
          entry.name === 'build'
        ) {
          continue;
        }

        if (entry.isDirectory() && recursive) {
          const nestedFiles = await this.getProjectFiles(
            fullPath,
            recursive,
            maxDepth,
            currentDepth + 1,
          );
          files.push(...nestedFiles);
        } else if (entry.isFile()) {
          // Skip binary files and very large files
          const stat = fs.statSync(fullPath);
          const isBinary = this.isBinaryFile(entry.name);
          const isTooLarge = stat.size > 100 * 1024; // 100 KB limit

          if (!isBinary && !isTooLarge) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error);
    }

    return files;
  }

  /**
   * Check if a file is likely binary
   */
  private isBinaryFile(filename: string): boolean {
    const binaryExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.ico',
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.zip',
      '.tar',
      '.gz',
      '.rar',
      '.exe',
      '.dll',
      '.so',
      '.dylib',
      '.ttf',
      '.woff',
      '.woff2',
      '.mp3',
      '.mp4',
      '.avi',
      '.mov',
      '.sqlite',
      '.db',
    ];

    const ext = path.extname(filename).toLowerCase();
    return binaryExtensions.includes(ext);
  }

  /**
   * Mark file summaries as stale
   */
  async markFilesStale(projectId: number, filePaths: string[]): Promise<void> {
    await this.prisma.file_summaries.updateMany({
      where: {
        project_id: projectId,
        file_path: {
          in: filePaths,
        },
      },
      data: {
        is_stale: true,
      },
    });

    // Also mark related module summaries as stale
    const modulePaths = new Set<string>();

    filePaths.forEach((filePath) => {
      const modulePath = path.dirname(filePath);
      modulePaths.add(modulePath);
    });

    await this.prisma.module_summaries.updateMany({
      where: {
        project_id: projectId,
        module_path: {
          in: Array.from(modulePaths),
        },
      },
      data: {
        is_stale: true,
      },
    });

    // Mark the code context as needing refresh
    await this.prisma.memory_contexts.updateMany({
      where: {
        project_id: projectId,
        contextType: ContextType.CODE,
      },
      data: {
        last_synced_at: null,
      },
    });
  }
}
