# KAPI Memory Architecture

This module implements KAPI's memory architecture for AI context management. It maintains an efficient, token-optimized context system for providing relevant information to AI models during conversations.

## Overview

The memory architecture uses a 5,000 token context window, split between static and dynamic content:

- **70% Static (~3,500 tokens)**: Core memory decks in slide format
- **30% Dynamic (~1,500 tokens)**: Real-time task data, file content, error logs

The system is based on five core context types:

1. **User Context**: Preferences, skills, and learning focus
2. **Project Business Context**: Why the project exists, target audience, value proposition
3. **Project Technical Context**: Architecture, stack, constraints
4. **Code Context**: File layout, modules, key functions
5. **Task Context**: Current task objectives, status, blockers

## Implementation Status

The implementation includes:

- Database schema for storing and managing context
- Context compression and token management utilities
- User context management service
- Integration with AI services

Several components need further implementation:

- Business, Technical, and Code context services (placeholders exist)
- Slide deck management service
- Documentation summarizer for code files
- Freshness monitoring service

## Usage

### Integration with NestJS

```typescript
// In your main AppModule:
import { Module } from '@nestjs/common';
import { MemoryModule } from './services/memory/memory.module';

@Module({
  imports: [
    MemoryModule,
    // Other modules...
  ],
})
export class AppModule {}
```

### Enhancing AI Services

```typescript
// In your AI service provider:
import { Injectable } from '@nestjs/common';
import { AiContextIntegrationService } from './services/memory/ai-context-integration.service';
import { ClaudeService } from './your-ai-provider.service';

@Injectable()
export class EnhancedAiService {
  constructor(
    private claudeService: ClaudeService,
    private contextIntegration: AiContextIntegrationService,
  ) {
    // Enhance the AI service with context capabilities
    this.contextIntegration.enhanceAiService(this.claudeService);
  }

  // Your AI methods will now automatically include context
  async generateCompletion(prompt: string, options: any) {
    // If options includes projectId, context will be added automatically
    return this.claudeService.generateCompletion(prompt, options);
  }
}
```

### Using Context in API Routes

```typescript
// In your controller:
@Post('generate')
async generateWithContext(
  @Body() body: { prompt: string, projectId: number },
  @Req() request
) {
  const userId = request.user.id;

  return this.aiService.generateCompletion(body.prompt, {
    projectId: body.projectId,
    userId,
    conversationId: body.conversationId,
    // Enable context enhancement
    enhanceWithContext: true
  });
}
```

## Database Setup

Run the migration to add the memory architecture tables:

```bash
# Review merged schema
node prisma/apply_memory_schema.js

# Apply the migration
npx prisma migrate dev --name add_memory_architecture

# Seed the context registry
node prisma/seed_context_registry.js
```

## Next Steps

1. Implement the remaining context services:

   - Business context service
   - Technical context service
   - Code context service
   - Task context service

2. Complete the slide builder service

3. Implement documentation summarizer to extract code summaries

4. Add the freshness monitor service to keep context up to date

5. Create API endpoints for managing context

## Architecture

The memory architecture follows a layered design:

1. **Context Providers**: Services that retrieve specific types of context
2. **Context Manager**: Aggregates and prioritizes context based on token budget
3. **AI Integration**: Enhances AI services with context capabilities
4. **Utilities**: Token counting, context compression, markdown handling

Services communicate through shared interfaces and types defined in `src/types/memory`.
