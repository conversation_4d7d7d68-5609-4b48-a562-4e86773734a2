import { PrismaService } from '../../db/prisma.service';
import { ContextBlock, ContextProviderOptions } from '../../types/memory/context.types';
import { ContextFormat, ContextType, SlideDeckType } from '../../generated/prisma';
import { estimateTokenCount } from './utils/token-counting';

/**
 * Service for managing project technical context
 */

export class TechnicalContextService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get the context priority
   */
  getPriority(): number {
    return 2; // Default priority for technical context
  }

  /**
   * Get context type
   */
  getContextType(): ContextType {
    return ContextType.TECHNICAL;
  }

  /**
   * Get maximum tokens for this context type
   */
  getMaxTokens(): number {
    return 800;
  }

  /**
   * Get technical context for a project
   */
  async getContext(projectId: number, _options?: ContextProviderOptions): Promise<ContextBlock> {
    // Find technical context memory for this project
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.TECHNICAL,
        is_active: true,
      },
      include: {
        slide_decks: {
          include: {
            memory_slides: {
              orderBy: {
                order: 'asc',
              },
            },
          },
        },
      },
    });

    if (!contextMemory) {
      // No existing context, create default
      return await this.createDefaultTechnicalContext(projectId);
    }

    // Get the latest version
    const latestVersion = await this.prisma.context_versions.findFirst({
      where: {
        context_id: contextMemory.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    if (!latestVersion) {
      // No versions found, create default
      return await this.createDefaultTechnicalContext(projectId);
    }

    return {
      type: ContextType.TECHNICAL,
      content: latestVersion.content,
      format: latestVersion.format,
      tokenCount: latestVersion.token_count || estimateTokenCount(latestVersion.content),
      metadata: {
        contextId: contextMemory.id,
        version: latestVersion.version,
        slideDeckId: contextMemory.slide_deck_id || undefined,
      },
    };
  }

  /**
   * Create default technical context
   */
  private async createDefaultTechnicalContext(projectId: number): Promise<ContextBlock> {
    // Get project details to create initial context
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: {
        name: true,
        language: true,
        framework: true,
        tech_stack: true,
      },
    });

    if (!project) {
      throw new Error(`Project with id ${projectId} not found`);
    }

    // Create slide deck data
    const slideDeckData = {
      project_id: projectId,
      title: 'Project Technical Context',
      description: 'Technical architecture and stack information',
      type: SlideDeckType.TECHNICAL,
    };

    // Extract tech stack information
    let techStackContent = '';

    if (project.tech_stack && typeof project.tech_stack === 'object') {
      // Use tech_stack JSON if available
      try {
        const techStack =
          typeof project.tech_stack === 'string'
            ? JSON.parse(project.tech_stack)
            : project.tech_stack;

        techStackContent = Object.entries(techStack)
          .map(([key, value]) => `- ${key}: ${value}`)
          .join('\n');
      } catch (error) {
        console.error('Error parsing tech_stack JSON:', error);
        // Fallback to simple representation
        techStackContent = `- Language: ${project.language || 'Not specified'}\n`;
        techStackContent += `- Framework: ${project.framework || 'Not specified'}`;
      }
    } else {
      // Use language and framework if available
      techStackContent = `- Language: ${project.language || 'Not specified'}\n`;
      techStackContent += `- Framework: ${project.framework || 'Not specified'}`;
    }

    // Create slides
    const slideDeck = await this.prisma.slide_decks.create({
      data: {
        ...slideDeckData,
        memory_slides: {
          create: [
            {
              title: 'Tech Stack',
              content: techStackContent,
              order: 1,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Architecture Overview',
              content: '```mermaid\ngraph TD\n  A[Client] --> B[API]\n  B --> C[Database]\n```',
              order: 2,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Key Components',
              content:
                '- Component 1: Description\n- Component 2: Description\n- Component 3: Description',
              order: 3,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Data Flow',
              content: 'Describe how data flows through the system.',
              order: 4,
              format: ContextFormat.MARKDOWN,
            },
          ],
        },
      },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    // Format markdown content from slides
    let markdownContent = `# ${slideDeck.title}\n\n`;
    if (slideDeck.description) {
      markdownContent += `${slideDeck.description}\n\n`;
    }

    // Add each slide
    for (const slide of slideDeck.memory_slides) {
      markdownContent += `## ${slide.title || 'Slide ' + slide.order}\n\n`;
      markdownContent += `${slide.content}\n\n`;
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Create memory context
    const memoryContext = await this.prisma.memory_contexts.create({
      data: {
        project_id: projectId,
        slide_deck_id: slideDeck.id,
        contextType: ContextType.TECHNICAL,
        name: 'Technical Context',
        description: 'Technical architecture and stack information',
        token_count: tokenCount,
        context_versions: {
          create: {
            version: 1,
            content: markdownContent,
            format: ContextFormat.MARKDOWN,
            token_count: tokenCount,
            changed_by: 'system',
            change_reason: 'Initial creation',
          },
        },
      },
      include: {
        context_versions: true,
      },
    });

    return {
      type: ContextType.TECHNICAL,
      content: markdownContent,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        contextId: memoryContext.id,
        version: 1,
        slideDeckId: slideDeck.id,
      },
    };
  }

  /**
   * Update technical context
   */
  async updateTechnicalContext(
    projectId: number,
    slideDeckId: number,
    slides: Array<{
      id?: number;
      title: string;
      content: string;
      order: number;
    }>,
  ): Promise<ContextBlock> {
    // Update slide deck
    await this.prisma.$transaction(async (tx) => {
      // Update existing slides and add new ones
      for (const slide of slides) {
        if (slide.id) {
          // Update existing slide
          await tx.memory_slides.update({
            where: { id: slide.id },
            data: {
              title: slide.title,
              content: slide.content,
              order: slide.order,
              last_updated: new Date(),
            },
          });
        } else {
          // Create new slide
          await tx.memory_slides.create({
            data: {
              slide_deck_id: slideDeckId,
              title: slide.title,
              content: slide.content,
              order: slide.order,
              format: ContextFormat.MARKDOWN,
            },
          });
        }
      }

      // Update slide deck's lastUpdated timestamp
      await tx.slide_decks.update({
        where: { id: slideDeckId },
        data: {
          last_updated: new Date(),
        },
      });
    });

    // Get updated slide deck
    const updatedSlideDeck = await this.prisma.slide_decks.findUnique({
      where: { id: slideDeckId },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    if (!updatedSlideDeck) {
      throw new Error(`Slide deck with id ${slideDeckId} not found`);
    }

    // Format markdown content from slides
    let markdownContent = `# ${updatedSlideDeck.title}\n\n`;
    if (updatedSlideDeck.description) {
      markdownContent += `${updatedSlideDeck.description}\n\n`;
    }

    // Add each slide
    for (const slide of updatedSlideDeck.memory_slides) {
      markdownContent += `## ${slide.title || 'Slide ' + slide.order}\n\n`;
      markdownContent += `${slide.content}\n\n`;
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Get memory context
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        slide_deck_id: slideDeckId,
        contextType: ContextType.TECHNICAL,
      },
      include: {
        context_versions: {
          orderBy: {
            version: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!contextMemory) {
      throw new Error(`Technical context for project ${projectId} not found`);
    }

    const latestVersion = contextMemory.context_versions[0];
    if (!latestVersion) {
      throw new Error('No context version found');
    }

    // Create new version
    const newVersion = await this.prisma.context_versions.create({
      data: {
        context_id: contextMemory.id,
        version: latestVersion.version + 1,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        token_count: tokenCount,
        changed_by: 'user',
        change_reason: 'Updated technical context',
      },
    });

    // Update memory context
    await this.prisma.memory_contexts.update({
      where: { id: contextMemory.id },
      data: {
        version: newVersion.version,
        token_count: tokenCount,
        last_updated: new Date(),
        last_synced_at: new Date(),
      },
    });

    return {
      type: ContextType.TECHNICAL,
      content: markdownContent,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        contextId: contextMemory.id,
        version: newVersion.version,
        slideDeckId,
      },
    };
  }

  /**
   * Update architecture diagram
   */
  async updateArchitectureDiagram(projectId: number, diagramContent: string): Promise<void> {
    const memoryContext = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.TECHNICAL,
        is_active: true,
      },
      select: {
        slide_deck_id: true,
      },
    });

    if (!memoryContext?.slide_deck_id) {
      throw new Error(`Technical context for project ${projectId} not found`);
    }

    // Find architecture diagram slide
    const architectureSlide = await this.prisma.memory_slides.findFirst({
      where: {
        slide_deck_id: memoryContext.slide_deck_id,
        title: {
          contains: 'Architecture',
        },
      },
    });

    if (architectureSlide) {
      // Update existing slide
      await this.prisma.memory_slides.update({
        where: { id: architectureSlide.id },
        data: {
          content: diagramContent,
          last_updated: new Date(),
        },
      });
    } else {
      // Create new slide
      const slides = await this.prisma.memory_slides.findMany({
        where: {
          slide_deck_id: memoryContext.slide_deck_id,
        },
        orderBy: {
          order: 'desc',
        },
        take: 1,
      });

      const nextOrder = slides.length > 0 ? slides[0].order + 1 : 1;

      await this.prisma.memory_slides.create({
        data: {
          slide_deck_id: memoryContext.slide_deck_id,
          title: 'Architecture Overview',
          content: diagramContent,
          order: nextOrder,
          format: ContextFormat.MARKDOWN,
        },
      });
    }

    // Find the memory context by slide_deck_id
    const fullMemoryContext = await this.prisma.memory_contexts.findFirst({
      where: {
        slide_deck_id: memoryContext.slide_deck_id,
      },
    });

    if (fullMemoryContext) {
      // Trigger context update
      await this.prisma.memory_contexts.update({
        where: {
          id: fullMemoryContext.id,
        },
        data: {
          last_synced_at: null, // Mark for refresh
        },
      });
    }
  }

  /**
   * Get technical context slides
   */
  async getTechnicalContextSlides(projectId: number): Promise<any> {
    const memoryContext = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.TECHNICAL,
        is_active: true,
      },
      select: {
        slide_deck_id: true,
      },
    });

    if (!memoryContext?.slide_deck_id) {
      return null;
    }

    return await this.prisma.slide_decks.findUnique({
      where: {
        id: memoryContext.slide_deck_id,
      },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });
  }
}
