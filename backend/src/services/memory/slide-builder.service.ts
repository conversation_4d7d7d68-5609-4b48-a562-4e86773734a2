import { PrismaService } from '../../db/prisma.service';
import { ContextFormat, ContextType, SlideDeckType } from '../../generated/prisma';
import { SlideDeckData, SlideData } from '../../types/memory/context.types';
import { estimateTokenCount } from './utils/token-counting';

interface SlideDeck {
  id: number;
  title: string;
  description: string | null;
  type: SlideDeckType;
  project_id: number;
  token_count: number | null;
  created_at: Date;
  last_updated: Date;
  memory_slides: Array<{
    id: number;
    title: string | null;
    content: string;
    order: number;
    format: ContextFormat;
    token_count: number | null;
    last_updated: Date;
    slide_deck_id: number;
    created_at: Date;
    updated_at: Date;
  }>;
}

/**
 * Service for building and managing slide decks
 */
export class SlideBuilderService {
  constructor(private prisma: PrismaService) {}

  /**
   * Create a new slide deck with slides
   */
  async createSlideDeck(slideDeckData: SlideDeckData, slides: SlideData[]): Promise<SlideDeck> {
    // Create slide deck
    const tokenCount = this.estimateTokenCount(
      slides.reduce((acc, slide) => acc + slide.content, ''),
    );

    const slideDeck = await this.prisma.slide_decks.create({
      data: {
        project_id: slideDeckData.projectId,
        title: slideDeckData.title,
        description: slideDeckData.description,
        type: slideDeckData.type,
        token_count: tokenCount,
        memory_slides: {
          create: slides.map((slide) => ({
            title: slide.title,
            content: slide.content,
            order: slide.order,
            format: slide.format,
            token_count: this.estimateTokenCount(slide.content),
          })),
        },
      },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    // Create associated memory context
    const contextType = this.mapSlideDeckTypeToContextType(slideDeckData.type);

    if (contextType) {
      const aggregatedContent = this.formatSlideDeckAsMarkdown(slideDeck);

      await this.prisma.memory_contexts.create({
        data: {
          project_id: slideDeckData.projectId,
          slide_deck_id: slideDeck.id,
          contextType,
          name: slideDeckData.title,
          description: slideDeckData.description,
          token_count: tokenCount,
          context_versions: {
            create: {
              version: 1,
              content: aggregatedContent,
              format: ContextFormat.MARKDOWN,
              token_count: tokenCount,
              changed_by: 'system',
              change_reason: 'Initial creation from slide deck',
            },
          },
        },
      });
    }

    return slideDeck;
  }

  /**
   * Update an existing slide deck
   */
  async updateSlideDeck(
    slideDeckId: number,
    slideDeckData: Partial<SlideDeckData>,
    slideUpdates?: {
      create?: SlideData[];
      update?: Array<{ id: number } & Partial<SlideData>>;
      delete?: number[];
    },
  ): Promise<SlideDeck> {
    // Start a transaction
    return this.prisma.$transaction(async (tx) => {
      // Update slide deck
      const slideDeck = await tx.slide_decks.update({
        where: { id: slideDeckId },
        data: {
          ...slideDeckData,
          last_updated: new Date(),
        },
        include: {
          memory_slides: true,
        },
      });

      if (!slideDeck) {
        throw new Error(`Slide deck with id ${slideDeckId} not found`);
      }

      // Handle slide updates
      if (slideUpdates) {
        // Delete slides
        if (slideUpdates.delete && slideUpdates.delete.length > 0) {
          await tx.memory_slides.deleteMany({
            where: {
              id: {
                in: slideUpdates.delete,
              },
            },
          });
        }

        // Update slides
        if (slideUpdates.update && slideUpdates.update.length > 0) {
          for (const slideUpdate of slideUpdates.update) {
            await tx.memory_slides.update({
              where: { id: slideUpdate.id },
              data: {
                title: slideUpdate.title,
                content: slideUpdate.content,
                order: slideUpdate.order,
                format: slideUpdate.format,
                token_count: slideUpdate.content
                  ? this.estimateTokenCount(slideUpdate.content)
                  : undefined,
                last_updated: new Date(),
              },
            });
          }
        }

        // Create new slides
        if (slideUpdates.create && slideUpdates.create.length > 0) {
          for (const slideData of slideUpdates.create) {
            await tx.memory_slides.create({
              data: {
                slide_deck_id: slideDeckId,
                title: slideData.title,
                content: slideData.content,
                order: slideData.order,
                format: slideData.format,
                token_count: this.estimateTokenCount(slideData.content),
              },
            });
          }
        }
      }

      // Refresh the slide deck with updated slides
      const updatedSlideDeck = await tx.slide_decks.findUnique({
        where: { id: slideDeckId },
        include: {
          memory_slides: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });

      if (!updatedSlideDeck) {
        throw new Error(`Failed to refresh slide deck with id ${slideDeckId}`);
      }

      // Update the token count
      const tokenCount = this.estimateTokenCount(
        updatedSlideDeck.memory_slides
          ? updatedSlideDeck.memory_slides.reduce(
              (acc: string, slide: { content: string }) => acc + slide.content,
              '',
            )
          : '',
      );

      await tx.slide_decks.update({
        where: { id: slideDeckId },
        data: {
          token_count: tokenCount,
        },
      });

      // Update the associated memory context
      const memoryContext = await tx.memory_contexts.findFirst({
        where: {
          slide_deck_id: slideDeckId,
        },
      });

      if (memoryContext) {
        const aggregatedContent = this.formatSlideDeckAsMarkdown(updatedSlideDeck);

        // Create new version
        const newVersion = await tx.context_versions.create({
          data: {
            context_id: memoryContext.id,
            version: memoryContext.version + 1,
            content: aggregatedContent,
            format: ContextFormat.MARKDOWN,
            token_count: tokenCount,
            changed_by: 'system',
            change_reason: 'Updated from slide deck changes',
          },
        });

        // Update memory context
        await tx.memory_contexts.update({
          where: { id: memoryContext.id },
          data: {
            version: newVersion.version,
            token_count: tokenCount,
            last_updated: new Date(),
          },
        });
      }

      return updatedSlideDeck;
    });
  }

  /**
   * Get slide deck by ID
   */
  async getSlideDeck(slideDeckId: number): Promise<SlideDeck | null> {
    return this.prisma.slide_decks.findUnique({
      where: { id: slideDeckId },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });
  }

  /**
   * Get slide decks for a project
   */
  async getProjectSlideDecks(projectId: number): Promise<SlideDeck[]> {
    return this.prisma.slide_decks.findMany({
      where: { project_id: projectId },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });
  }

  /**
   * Format a slide deck as markdown for context
   */
  formatSlideDeckAsMarkdown(slideDeck: SlideDeck): string {
    let markdown = `# ${slideDeck.title}\n\n`;

    if (slideDeck.description) {
      markdown += `${slideDeck.description}\n\n`;
    }

    // Add each slide
    for (const slide of slideDeck.memory_slides) {
      markdown += `## ${slide.title || 'Slide ' + slide.order}\n\n`;
      markdown += `${slide.content}\n\n`;
    }

    return markdown;
  }

  /**
   * Map slide deck type to context type
   */
  mapSlideDeckTypeToContextType(slideDeckType: SlideDeckType): ContextType | null {
    const mapping: Record<SlideDeckType, ContextType> = {
      [SlideDeckType.BUSINESS]: ContextType.BUSINESS,
      [SlideDeckType.TECHNICAL]: ContextType.TECHNICAL,
      [SlideDeckType.USER]: ContextType.USER,
      [SlideDeckType.CODE]: ContextType.CODE,
      [SlideDeckType.TASK]: ContextType.TASK,
    };

    return mapping[slideDeckType] || null;
  }

  /**
   * Estimate token count for a string
   */
  private estimateTokenCount(text: string): number {
    return estimateTokenCount(text);
  }

  /**
   * Create a default blank slide deck
   */
  async createDefaultSlideDeck(
    projectId: number,
    type: SlideDeckType,
    title?: string,
  ): Promise<SlideDeck> {
    const defaultTitle = this.getDefaultTitleForType(type);

    return this.createSlideDeck(
      {
        projectId,
        title: title || defaultTitle,
        description: `Default ${type.toLowerCase()} slide deck`,
        type,
      },
      [
        {
          slideDeckId: 0, // Will be set by createSlideDeck
          title: 'Introduction',
          content: 'Add your content here.',
          order: 1,
          format: ContextFormat.MARKDOWN,
        },
      ],
    );
  }

  /**
   * Get default title for slide deck type
   */
  private getDefaultTitleForType(type: SlideDeckType): string {
    switch (type) {
      case SlideDeckType.BUSINESS:
        return 'Business Context';
      case SlideDeckType.TECHNICAL:
        return 'Technical Context';
      case SlideDeckType.USER:
        return 'User Context';
      case SlideDeckType.CODE:
        return 'Code Structure';
      case SlideDeckType.TASK:
        return 'Project Tasks';
      default:
        return 'New Slide Deck';
    }
  }

  /**
   * Delete a slide deck
   */
  async deleteSlideDeck(slideDeckId: number): Promise<void> {
    // First find the associated memory context
    const memoryContext = await this.prisma.memory_contexts.findFirst({
      where: {
        slide_deck_id: slideDeckId,
      },
    });

    // Start a transaction
    await this.prisma.$transaction(async (tx) => {
      // Delete memory context versions if exists
      if (memoryContext) {
        await tx.context_versions.deleteMany({
          where: {
            context_id: memoryContext.id,
          },
        });

        // Delete memory context
        await tx.memory_contexts.delete({
          where: {
            id: memoryContext.id,
          },
        });
      }

      // Delete slides
      await tx.memory_slides.deleteMany({
        where: {
          slide_deck_id: slideDeckId,
        },
      });

      // Delete slide deck
      await tx.slide_decks.delete({
        where: {
          id: slideDeckId,
        },
      });
    });
  }

  /**
   * Copy a slide deck
   */
  async copySlideDeck(
    slideDeckId: number,
    newTitle?: string,
    targetProjectId?: number,
  ): Promise<SlideDeck> {
    // Get original slide deck
    const originalSlideDeck = await this.prisma.slide_decks.findUnique({
      where: { id: slideDeckId },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    if (!originalSlideDeck) {
      throw new Error(`Slide deck with id ${slideDeckId} not found`);
    }

    // Create new slide deck
    return this.createSlideDeck(
      {
        projectId: targetProjectId || originalSlideDeck.project_id,
        title: newTitle || `Copy of ${originalSlideDeck.title}`,
        description: originalSlideDeck.description || undefined,
        type: originalSlideDeck.type,
      },
      originalSlideDeck.memory_slides.map((slide) => ({
        slideDeckId: 0, // Will be set by createSlideDeck
        title: slide.title || undefined,
        content: slide.content,
        order: slide.order,
        format: slide.format as ContextFormat,
      })),
    );
  }
}
