import { PrismaService } from '../../db/prisma.service';
import {
  ContextBlock,
  TokenBudget,
  ContextSelectionStrategy,
} from '../../types/memory/context.types';
import { ContextType } from '../../generated/prisma';

// Interface for future context registry implementation
interface ContextRegistry {
  id: number;
  name: string;
  description: string | null;
  contextType: ContextType;
  is_enabled: boolean;
  priority: number;
  max_tokens: number;
  default_format: string;
  created_at: Date;
  updated_at: Date;
  metadata: any;
}

/**
 * Service for managing token budgets and context selection
 */
export class TokenBudgetService {
  private selectionStrategies: Map<string, ContextSelectionStrategy> = new Map();

  constructor(private prisma: PrismaService) {
    // Register default selection strategies
    this.registerSelectionStrategy('priority-based', {
      name: 'priority-based',
      selectContext: (
        availableContexts: ContextBlock[],
        tokenBudget: TokenBudget,
      ): ContextBlock[] => {
        // Call the async method but handle it synchronously for the interface
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _promise = this.priorityBasedSelection(availableContexts, tokenBudget);
        // Return an empty array as a fallback
        return [];
      },
    });
  }

  /**
   * Register a new context selection strategy
   */
  registerSelectionStrategy(name: string, strategy: ContextSelectionStrategy): void {
    this.selectionStrategies.set(name, strategy);
  }

  /**
   * Calculates token budget for context assembly
   */
  calculateBudget(totalBudget: number, staticRatio: number = 0.7): TokenBudget {
    const staticBudget = Math.floor(totalBudget * staticRatio);
    const dynamicBudget = totalBudget - staticBudget;

    return {
      total: totalBudget,
      static: staticBudget,
      dynamic: dynamicBudget,
      allocated: {},
      remaining: totalBudget,
    };
  }

  /**
   * Select context blocks based on token budget and priorities
   */
  selectContextBlocks(
    availableContexts: ContextBlock[],
    budget: TokenBudget,
    forceIncludeTypes: ContextType[] = [],
  ): ContextBlock[] {
    // Use the priority-based strategy by default
    const strategy = this.selectionStrategies.get('priority-based');
    if (!strategy) {
      throw new Error('No selection strategy available');
    }

    // If we have forced types, handle those first
    if (forceIncludeTypes.length > 0) {
      const forcedContexts = availableContexts.filter((ctx) =>
        forceIncludeTypes.includes(ctx.type),
      );

      const forcedTokens = forcedContexts.reduce((sum, ctx) => sum + ctx.tokenCount, 0);

      // Adjust budget for remaining contexts
      const remainingBudget = Math.max(0, budget.total - forcedTokens);
      const adjustedBudget: TokenBudget = {
        ...budget,
        total: remainingBudget,
        remaining: remainingBudget,
      };

      // Select additional contexts with remaining budget
      const remainingContexts = availableContexts.filter(
        (ctx) => !forceIncludeTypes.includes(ctx.type),
      );

      const selectedRemainingContexts = strategy.selectContext(remainingContexts, adjustedBudget);

      // Combine forced and selected contexts
      return [...forcedContexts, ...selectedRemainingContexts];
    }

    // Standard selection with no forced types
    return strategy.selectContext(availableContexts, budget);
  }

  /**
   * Priority-based context selection strategy
   */
  private async priorityBasedSelection(
    availableContexts: ContextBlock[],
    budget: TokenBudget,
  ): Promise<ContextBlock[]> {
    // First, get registry entries to determine priorities
    const registryEntries = await this.prisma.context_registry.findMany({
      where: {
        is_enabled: true,
      },
    });

    // Map context types to priorities
    const contextPriorities = new Map<ContextType, number>();

    // Start with default priorities
    contextPriorities.set(ContextType.TASK, 0); // Highest priority
    contextPriorities.set(ContextType.CODE, 1);
    contextPriorities.set(ContextType.TECHNICAL, 2);
    contextPriorities.set(ContextType.BUSINESS, 3);
    contextPriorities.set(ContextType.USER, 4);

    // Override with registry values if available
    registryEntries.forEach((entry) => {
      if (entry.contextType) {
        contextPriorities.set(entry.contextType, entry.priority);
      }
    });

    // Sort contexts by priority (lower number = higher priority)
    const sortedContexts = [...availableContexts].sort((a, b) => {
      const priorityA = contextPriorities.get(a.type) || 999;
      const priorityB = contextPriorities.get(b.type) || 999;
      return priorityA - priorityB;
    });

    // Select contexts until budget is exhausted
    const selectedContexts: ContextBlock[] = [];
    let remainingBudget = budget.total;

    for (const context of sortedContexts) {
      if (context.tokenCount <= remainingBudget) {
        selectedContexts.push(context);
        remainingBudget -= context.tokenCount;

        // Update allocated budget
        budget.allocated[context.type] = (budget.allocated[context.type] || 0) + context.tokenCount;
      } else {
        // Context too large, skip for now
        // In a more advanced implementation, we could truncate or summarize
        console.log(`Skipping context of type ${context.type} due to budget constraints`);
      }
    }

    // Update remaining budget
    budget.remaining = remainingBudget;

    return selectedContexts;
  }

  /**
   * Estimate token count for a string
   * In production, use a proper tokenizer like tiktoken
   */
  estimateTokenCount(text: string): number {
    // Very crude estimation - in production use a proper tokenizer
    return Math.ceil(text.length / 4);
  }
}
