import { PrismaService } from '../../db/prisma.service';
import { FileSummaryData, SummaryOptions } from '../../types/memory/context.types';
import { estimateTokenCount } from './utils/token-counting';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Service for summarizing project documentation and code files
 */

export class DocumentationSummarizerService {
  constructor(
    private prisma: PrismaService,
    private aiService?: any, // Optional AI service for generating summaries
  ) {}

  /**
   * Generate a summary for a file
   */
  async summarizeFile(projectId: number, filePath: string): Promise<FileSummaryData> {
    // Find project to get local path
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: { local_path: true },
    });

    if (!project?.local_path) {
      throw new Error('Project local path not defined');
    }

    // Check if file exists
    const fullPath = path.join(project.local_path, filePath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // Read file content
    const fileContent = fs.readFileSync(fullPath, 'utf-8');

    // Extract existing @summary comments
    const existingSummary = this.extractSummaryComment(fileContent);
    if (existingSummary) {
      // Use existing summary
      return this.saveFileSummary(projectId, filePath, existingSummary);
    }

    // Generate summary using AI or simple heuristics
    let summary: string;
    if (this.aiService) {
      // Use AI to generate summary
      summary = await this.generateAiSummary(fileContent, filePath);
    } else {
      // Use simple heuristics
      summary = this.generateSimpleSummary(fileContent, filePath);
    }

    // Save summary to database
    return this.saveFileSummary(projectId, filePath, summary);
  }

  /**
   * Extract @summary comment from file content
   */
  private extractSummaryComment(fileContent: string): string | null {
    // Different regex patterns for different languages
    const patterns = [
      /\/\*\*\s*@summary\s*([\s\S]*?)\*\//, // JS/TS block comment
      /\/\/\s*@summary\s*(.*)$/m, // JS/TS line comment
      /#\s*@summary\s*(.*)$/m, // Python/Ruby comment
      /<!--\s*@summary\s*([\s\S]*?)-->/, // HTML/XML comment
    ];

    for (const pattern of patterns) {
      const match = fileContent.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Generate a summary for file content using AI
   */
  private async generateAiSummary(fileContent: string, filePath: string): Promise<string> {
    // Determine file type based on extension
    const fileExt = path.extname(filePath).toLowerCase();

    // Truncate file content if too large
    const truncatedContent =
      fileContent.length > 10000 ? fileContent.substring(0, 10000) + '...[truncated]' : fileContent;

    // Construct prompt for AI
    const prompt = `
      Generate a concise 2-3 line summary of this ${fileExt} file.
      Focus on the main purpose and functionality, not implementation details.
      Keep the summary under 200 characters.

      File: ${path.basename(filePath)}

      Content:
      ${truncatedContent}
    `;

    try {
      // Call AI service to generate summary
      const aiResponse = await this.aiService.generateTextCompletion(prompt, {
        maxTokens: 100,
        temperature: 0.3,
      });

      // Clean up response
      return aiResponse.trim();
    } catch (error) {
      console.error('Error generating AI summary:', error);
      // Fall back to simple summary
      return this.generateSimpleSummary(fileContent, filePath);
    }
  }

  /**
   * Generate a simple summary based on file type and content
   */
  private generateSimpleSummary(fileContent: string, filePath: string): string {
    // Extract file info
    const ext = path.extname(filePath).toLowerCase();
    const filename = path.basename(filePath);
    const lines = fileContent.split('\n').length;

    // Check for common file types
    if (ext === '.ts' || ext === '.js') {
      // Look for class and function declarations
      const classMatch = fileContent.match(/class\s+(\w+)/);
      const fnMatch = fileContent.match(/function\s+(\w+)/);
      const exportMatch = fileContent.match(
        /export\s+(const|let|var|function|class|default)\s+(\w+)/,
      );

      if (classMatch) {
        return `Defines ${classMatch[1]} class for ${this.inferPurpose(filename)}.`;
      } else if (fnMatch) {
        return `Contains ${fnMatch[1]} function for ${this.inferPurpose(filename)}.`;
      } else if (exportMatch) {
        return `Exports ${exportMatch[2]} ${exportMatch[1]} for ${this.inferPurpose(filename)}.`;
      }
    } else if (ext === '.json') {
      return `Configuration file containing ${this.countJsonEntries(fileContent)} entries.`;
    } else if (ext === '.html') {
      return `HTML template for ${this.inferPurpose(filename)}.`;
    } else if (ext === '.css' || ext === '.scss') {
      return `Stylesheet for ${this.inferPurpose(filename)}.`;
    } else if (ext === '.md') {
      const firstLine = fileContent.split('\n')[0].replace(/^#\s+/, '');
      return `Documentation: ${firstLine || this.inferPurpose(filename)}`;
    }

    // Default summary
    return `${filename}: File with ${lines} lines.`;
  }

  /**
   * Infer purpose from filename
   */
  private inferPurpose(filename: string): string {
    // Remove extension
    const name = filename.replace(/\.\w+$/, '');

    // Convert camelCase/PascalCase to spaces
    const words = name
      .replace(/([A-Z])/g, ' $1')
      .replace(/[-_]/g, ' ')
      .toLowerCase()
      .trim();

    return words;
  }

  /**
   * Count entries in a JSON file
   */
  private countJsonEntries(jsonContent: string): number {
    try {
      const parsed = JSON.parse(jsonContent);
      return Object.keys(parsed).length;
    } catch {
      return 0;
    }
  }

  /**
   * Save file summary to database
   */
  private async saveFileSummary(
    projectId: number,
    filePath: string,
    summary: string,
  ): Promise<FileSummaryData> {
    const tokenCount = estimateTokenCount(summary);

    // Upsert to handle both create and update
    const result = await this.prisma.file_summaries.upsert({
      where: {
        project_id_file_path: {
          project_id: projectId,
          file_path: filePath,
        },
      },
      update: {
        summary,
        token_count: tokenCount,
        last_updated: new Date(),
        is_stale: false,
      },
      create: {
        project_id: projectId,
        file_path: filePath,
        summary,
        token_count: tokenCount,
        is_stale: false,
      },
    });

    return {
      projectId: result.project_id,
      filePath: result.file_path,
      summary: result.summary,
      tokenCount: result.token_count || 0,
      gitHash: result.git_hash || undefined,
    };
  }

  /**
   * Summarize a module (directory) recursively
   */
  async summarizeModule(
    projectId: number,
    modulePath: string,
    options: SummaryOptions = {},
  ): Promise<any> {
    const { recursive = true, depth = 2 } = options;

    // Find project to get local path
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: { local_path: true },
    });

    if (!project?.local_path) {
      throw new Error('Project local path not defined');
    }

    // Check if module exists
    const fullPath = path.join(project.local_path, modulePath);
    if (!fs.existsSync(fullPath) || !fs.statSync(fullPath).isDirectory()) {
      throw new Error(`Module not found or not a directory: ${modulePath}`);
    }

    // Collect file summaries within this module
    const fileSummaries = await this.prisma.file_summaries.findMany({
      where: {
        project_id: projectId,
        file_path: {
          startsWith: modulePath,
        },
      },
    });

    // If no summaries exist or they're stale, generate them
    if (
      fileSummaries.length === 0 ||
      fileSummaries.some((s: { is_stale: boolean }) => s.is_stale)
    ) {
      await this.summarizeAllFilesInModule(projectId, modulePath, recursive, depth);

      // Refresh summaries
      return this.summarizeModule(projectId, modulePath, options);
    }

    // Generate module summary based on file summaries
    const moduleSummary = await this.generateModuleSummary(projectId, modulePath, fileSummaries);

    return moduleSummary;
  }

  /**
   * Summarize all files in a module
   */
  private async summarizeAllFilesInModule(
    projectId: number,
    modulePath: string,
    recursive: boolean,
    depth: number,
  ): Promise<void> {
    // Find project to get local path
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: { local_path: true },
    });

    if (!project?.local_path) {
      throw new Error('Project local path not defined');
    }

    const fullPath = path.join(project.local_path, modulePath);

    // Read directory
    const items = fs.readdirSync(fullPath);

    for (const item of items) {
      const itemPath = path.join(modulePath, item);
      const fullItemPath = path.join(fullPath, item);

      // Skip hidden files and directories
      if (item.startsWith('.') || item === 'node_modules' || item === 'dist') {
        continue;
      }

      try {
        const stat = fs.statSync(fullItemPath);

        if (stat.isFile()) {
          // Skip large files and binary files
          const isLarge = stat.size > 1024 * 1024; // 1MB
          const isBinary = this.isBinaryFile(item);

          if (!isLarge && !isBinary) {
            // Summarize file
            await this.summarizeFile(projectId, itemPath);
          }
        } else if (stat.isDirectory() && recursive && depth > 0) {
          // Recursively summarize subdirectory
          await this.summarizeAllFilesInModule(projectId, itemPath, recursive, depth - 1);
        }
      } catch (error) {
        console.error(`Error processing ${itemPath}:`, error);
      }
    }
  }

  /**
   * Check if a file is likely binary
   */
  private isBinaryFile(filename: string): boolean {
    const binaryExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.ico',
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.zip',
      '.tar',
      '.gz',
      '.rar',
      '.exe',
      '.dll',
      '.so',
      '.dylib',
      '.ttf',
      '.woff',
      '.woff2',
      '.mp3',
      '.mp4',
      '.avi',
      '.mov',
      '.sqlite',
      '.db',
    ];

    const ext = path.extname(filename).toLowerCase();
    return binaryExtensions.includes(ext);
  }

  /**
   * Generate a summary for a module based on file summaries
   */
  private async generateModuleSummary(
    projectId: number,
    modulePath: string,
    fileSummaries: any[],
  ): Promise<any> {
    // Compile file summaries into a single text
    const summariesText = fileSummaries
      .map((s) => `${path.basename(s.file_path)}: ${s.summary}`)
      .join('\n');

    // Generate module summary
    let summary: string;

    if (this.aiService) {
      // Use AI to generate summary
      summary = await this.generateAiModuleSummary(modulePath, summariesText);
    } else {
      // Generate simple summary
      summary = this.generateSimpleModuleSummary(modulePath, fileSummaries);
    }

    const tokenCount = estimateTokenCount(summary);

    // Save module summary
    const result = await this.prisma.module_summaries.upsert({
      where: {
        project_id_module_path: {
          project_id: projectId,
          module_path: modulePath || '.',
        },
      },
      update: {
        summary,
        token_count: tokenCount,
        last_updated: new Date(),
        is_stale: false,
      },
      create: {
        project_id: projectId,
        module_path: modulePath || '.',
        summary,
        token_count: tokenCount,
        is_stale: false,
      },
    });

    return result;
  }

  /**
   * Generate a module summary using AI
   */
  private async generateAiModuleSummary(
    modulePath: string,
    summariesText: string,
  ): Promise<string> {
    const prompt = `
      Based on these file summaries, create a concise overview of the module's purpose and functionality.
      Focus on the overall responsibility of this module, not individual files.
      Limit the summary to 3 sentences and 300 characters.

      Module: ${modulePath}

      File Summaries:
      ${summariesText}
    `;

    try {
      // Call AI service to generate summary
      const aiResponse = await this.aiService.generateTextCompletion(prompt, {
        maxTokens: 150,
        temperature: 0.3,
      });

      // Clean up response
      return aiResponse.trim();
    } catch (error) {
      console.error('Error generating AI module summary:', error);
      // Fall back to simple summary
      return `Module containing files related to ${path.basename(modulePath || '.')} functionality.`;
    }
  }

  /**
   * Generate a simple module summary based on file summaries
   */
  private generateSimpleModuleSummary(modulePath: string, fileSummaries: any[]): string {
    const moduleBaseName = path.basename(modulePath || '.');

    // Check for common module types
    if (moduleBaseName === 'src' || moduleBaseName === 'source') {
      return 'Source code directory containing the main application code.';
    } else if (moduleBaseName === 'controllers' || moduleBaseName === 'routes') {
      return 'Controllers/routes handling API endpoints and request processing.';
    } else if (moduleBaseName === 'models' || moduleBaseName === 'schemas') {
      return 'Data models and schemas defining the data structure for the application.';
    } else if (moduleBaseName === 'services') {
      return 'Services implementing business logic and handling operations.';
    } else if (moduleBaseName === 'utils' || moduleBaseName === 'helpers') {
      return 'Utility functions and helpers providing shared functionality.';
    } else if (moduleBaseName === 'components') {
      return 'UI components for rendering the user interface.';
    } else if (moduleBaseName === 'tests' || moduleBaseName === 'spec') {
      return 'Test files for validating application functionality.';
    } else if (moduleBaseName === 'config' || moduleBaseName === 'configuration') {
      return 'Configuration files for setting up the application.';
    } else if (moduleBaseName === 'docs' || moduleBaseName === 'documentation') {
      return 'Documentation files explaining the application.';
    }

    // Count file types to determine the primary purpose
    const fileTypes: Record<string, number> = {};
    fileSummaries.forEach((file) => {
      const ext = path.extname(file.file_path).toLowerCase();
      fileTypes[ext] = (fileTypes[ext] || 0) + 1;
    });

    // Determine the most common file type
    let mostCommonType = '';
    let maxCount = 0;
    for (const [type, count] of Object.entries(fileTypes)) {
      if (count > maxCount) {
        mostCommonType = type;
        maxCount = count;
      }
    }

    // Generic module summary based on most common file type
    if (mostCommonType === '.ts' || mostCommonType === '.js') {
      return `Module containing ${fileSummaries.length} TypeScript/JavaScript files for ${this.inferPurpose(moduleBaseName)} functionality.`;
    } else if (mostCommonType === '.html') {
      return `Module containing ${fileSummaries.length} HTML templates for ${this.inferPurpose(moduleBaseName)}.`;
    } else if (mostCommonType === '.css' || mostCommonType === '.scss') {
      return `Module containing ${fileSummaries.length} style files for ${this.inferPurpose(moduleBaseName)}.`;
    } else if (mostCommonType === '.json') {
      return `Module containing ${fileSummaries.length} JSON configuration files for ${this.inferPurpose(moduleBaseName)}.`;
    } else if (mostCommonType === '.md') {
      return `Module containing ${fileSummaries.length} documentation files about ${this.inferPurpose(moduleBaseName)}.`;
    }

    // Default summary
    return `Module for ${this.inferPurpose(moduleBaseName)} functionality with ${fileSummaries.length} files.`;
  }

  /**
   * Mark file summaries as stale based on git changes
   */
  async markStaleFiles(projectId: number, changedFiles: string[]): Promise<number> {
    // Mark individual files as stale
    const result = await this.prisma.file_summaries.updateMany({
      where: {
        project_id: projectId,
        file_path: {
          in: changedFiles,
        },
      },
      data: {
        is_stale: true,
      },
    });

    // Mark associated module summaries as stale
    const modulePaths = new Set<string>();

    changedFiles.forEach((filePath) => {
      const modulePath = path.dirname(filePath);
      modulePaths.add(modulePath);

      // Also add parent directories
      let parentPath = modulePath;
      while (parentPath && parentPath !== '.') {
        parentPath = path.dirname(parentPath);
        modulePaths.add(parentPath);
      }
    });

    await this.prisma.module_summaries.updateMany({
      where: {
        project_id: projectId,
        module_path: {
          in: Array.from(modulePaths),
        },
      },
      data: {
        is_stale: true,
      },
    });

    // Mark the code context as needing refresh
    await this.prisma.memory_contexts.updateMany({
      where: {
        project_id: projectId,
        contextType: 'CODE',
      },
      data: {
        last_synced_at: null,
      },
    });

    return result.count;
  }

  /**
   * Refresh stale summaries
   */
  async refreshStaleSummaries(projectId: number): Promise<void> {
    // Find stale file summaries
    const staleFiles = await this.prisma.file_summaries.findMany({
      where: {
        project_id: projectId,
        is_stale: true,
      },
      select: {
        file_path: true,
      },
    });

    // Refresh each file summary
    for (const file of staleFiles) {
      try {
        await this.summarizeFile(projectId, file.file_path);
      } catch (error) {
        console.error(`Error refreshing summary for ${file.file_path}:`, error);
      }
    }

    // Find stale module summaries
    const staleModules = await this.prisma.module_summaries.findMany({
      where: {
        project_id: projectId,
        is_stale: true,
      },
      select: {
        module_path: true,
      },
    });

    // Refresh each module summary
    for (const module of staleModules) {
      try {
        await this.summarizeModule(projectId, module.module_path);
      } catch (error) {
        console.error(`Error refreshing summary for module ${module.module_path}:`, error);
      }
    }
  }

  /**
   * Generate summaries for a project
   */
  async generateProjectSummaries(
    projectId: number,
    options: {
      recursive?: boolean;
      depth?: number;
      forceRefresh?: boolean;
    } = {},
  ): Promise<void> {
    const { recursive = true, depth = 3, forceRefresh = false } = options;

    // Find project
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: { local_path: true },
    });

    if (!project?.local_path) {
      throw new Error('Project local path not defined');
    }

    // If force refresh, mark all summaries as stale
    if (forceRefresh) {
      await this.prisma.file_summaries.updateMany({
        where: { project_id: projectId },
        data: { is_stale: true },
      });

      await this.prisma.module_summaries.updateMany({
        where: { project_id: projectId },
        data: { is_stale: true },
      });
    }

    // Summarize root module
    await this.summarizeModule(projectId, '', { recursive, depth });

    // Refresh any remaining stale summaries
    await this.refreshStaleSummaries(projectId);
  }
}
