/**
 * KAPI Memory Architecture Integration Example
 *
 * This file demonstrates how to integrate the memory architecture with existing AI services.
 */

import { PrismaService } from '../../../db/prisma.service';
import { AiContextIntegrationService } from '../ai-context-integration.service';
import { ContextManagerService } from '../context-manager.service';
import { ContextType } from '../../../generated/prisma';
import { SlideBuilderService } from '../slide-builder.service';
import { CodeContextService } from '../code-context.service';
import { UserContextService } from '../user-context.service';
import { BusinessContextService } from '../business-context.service';
import { TechnicalContextService } from '../technical-context.service';
import { TaskContextService } from '../task-context.service';
import { TokenBudgetService } from '../token-budget.service';

/**
 * Example class showing how to enhance AI services with context
 */
export class EnhancedAiService {
  constructor(
    private aiService: any, // Your existing AI service
    private contextIntegration: AiContextIntegrationService,
    private prisma: PrismaService,
  ) {
    // Enhance the AI service with context capabilities
    this.contextIntegration.enhanceAiService(this.aiService);
  }

  /**
   * Method to show how to use enhanced AI completion with context
   */
  async generateCompletionWithContext(
    prompt: string,
    options: {
      projectId: number;
      userId?: number;
      conversationId?: number;
      includeContextTypes?: ContextType[];
    },
  ) {
    // Let the enhanced AI service handle context injection
    return this.aiService.generateCompletion(prompt, {
      ...options,
      // Enable context enhancement
      enhanceWithContext: true,
      // Optional: Force include specific context types
      forceIncludeTypes: options.includeContextTypes,
    });
  }

  /**
   * Method to show how to directly get the assembled context
   */
  async getContextForDebugging(
    projectId: number,
    options: {
      userId?: number;
      conversationId?: number;
    } = {},
  ) {
    // Get the context manager service (need to be injected in your real service)
    // Using type assertion to satisfy TypeScript requirements for example purposes
    const contextManager = new ContextManagerService(
      this.prisma,
      {} as UserContextService,
      {} as BusinessContextService,
      {} as TechnicalContextService,
      {} as CodeContextService,
      {} as TaskContextService,
      {} as TokenBudgetService,
    );

    // Assemble context
    const context = await contextManager.assembleContext(projectId, options);

    // Format for display
    const formattedContext = contextManager.formatAssembledContext(context);

    return {
      context,
      formattedContext,
      totalTokens: context.totalTokens,
    };
  }

  /**
   * Method showing how to use memory in a conversation
   */
  async handleConversation(conversationId: number, userMessage: string) {
    // First get the conversation
    const conversation = await this.prisma.conversations.findUnique({
      where: { id: conversationId },
      select: {
        id: true,
        project_id: true,
        user_id: true,
      },
    });

    // Check if conversation exists
    if (!conversation) {
      throw new Error(`Conversation with id ${conversationId} not found`);
    }

    // Get project and user IDs
    const projectId = conversation.project_id;
    const userId = conversation.user_id;

    // Save user message
    await this.prisma.messages.create({
      data: {
        conversation_id: conversationId,
        role: 'user',
        content: userMessage,
        project_id: projectId,
      },
    });

    // Generate AI response with context
    const aiResponse = await this.generateCompletionWithContext(userMessage, {
      projectId: projectId as number, // Type assertion to ensure it's treated as a number
      userId,
      conversationId,
    });

    // Save AI response with token usage
    await this.prisma.messages.create({
      data: {
        conversation_id: conversationId,
        role: 'assistant',
        content: aiResponse.text,
        prompt_tokens: aiResponse.usage?.promptTokens,
        completion_tokens: aiResponse.usage?.completionTokens,
        cost: aiResponse.usage?.cost,
        project_id: projectId,
        model: aiResponse.model,
      },
    });

    // Save token usage
    if (aiResponse.usage && userId) {
      await this.prisma.model_usage.create({
        data: {
          user_id: userId,
          model_name: aiResponse.model || 'unknown',
          provider: aiResponse.provider || 'other',
          prompt_tokens: aiResponse.usage.promptTokens || 0,
          completion_tokens: aiResponse.usage.completionTokens || 0,
          total_tokens:
            (aiResponse.usage.promptTokens || 0) + (aiResponse.usage.completionTokens || 0),
          estimated_cost: aiResponse.usage.cost || 0,
          success: true,
          taskType: 'chat',
        },
      });
    }

    return aiResponse.text;
  }

  /**
   * Method showing how to work with a specific context type
   */
  async updateBusinessContext(
    projectId: number,
    updates: {
      purpose?: string;
      targetAudience?: string;
      valueProposition?: string;
      differentiators?: string[];
    },
  ) {
    // First get the current business context slides
    const businessContext = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.BUSINESS,
      },
      include: {
        slide_decks: {
          include: {
            memory_slides: {
              orderBy: {
                order: 'asc',
              },
            },
          },
        },
      },
    });

    if (!businessContext?.slide_decks) {
      throw new Error('Business context not initialized for this project');
    }

    const slideDeck = businessContext.slide_decks;
    const updatedSlides = [...slideDeck.memory_slides];

    // Update slides based on the provided updates
    for (const slide of updatedSlides) {
      if (slide.title && slide.title.includes('Purpose') && updates.purpose) {
        slide.content = updates.purpose;
      } else if (slide.title && slide.title.includes('Target Audience') && updates.targetAudience) {
        slide.content = updates.targetAudience;
      } else if (
        slide.title &&
        slide.title.includes('Value Proposition') &&
        updates.valueProposition
      ) {
        slide.content = updates.valueProposition;
      } else if (
        slide.title &&
        slide.title.includes('Differentiators') &&
        updates.differentiators
      ) {
        slide.content = updates.differentiators.map((d) => `- ${d}`).join('\n');
      }
    }

    // Update slide deck
    const slideBuilder = new SlideBuilderService(this.prisma);
    await slideBuilder.updateSlideDeck(
      slideDeck.id,
      {}, // No changes to slideDeck metadata
      {
        update: updatedSlides.map((slide) => ({
          id: slide.id,
          title: slide.title || undefined, // Convert null to undefined to match expected type
          content: slide.content,
          order: slide.order,
        })),
      },
    );

    return 'Business context updated successfully';
  }

  /**
   * Method showing how to handle file changes
   */
  async handleFileChanges(projectId: number, changedFiles: string[]) {
    // Get code context service
    const codeContextService = new CodeContextService(this.prisma);

    // Mark files as stale
    await codeContextService.markFilesStale(projectId, changedFiles);

    // This will cause the code context to be refreshed the next time it's retrieved
    console.log(`Marked ${changedFiles.length} files as stale for project ${projectId}`);
  }
}

// Example of how these would be used in a NestJS controller

/**
 * @Controller('ai')
 */
export class AiController {
  constructor(private enhancedAiService: EnhancedAiService) {}

  /**
   * @Post('complete')
   */
  async generateCompletion(
    /* @Body() */ body: {
      prompt: string;
      projectId: number;
      userId?: number;
      conversationId?: number;
    },
  ) {
    return this.enhancedAiService.generateCompletionWithContext(body.prompt, {
      projectId: body.projectId,
      userId: body.userId,
      conversationId: body.conversationId,
    });
  }

  /**
   * @Post('conversation/:id/message')
   */
  async sendMessage(
    /* @Param('id') */ conversationId: number,
    /* @Body() */ body: { message: string },
  ) {
    return this.enhancedAiService.handleConversation(conversationId, body.message);
  }

  /**
   * @Get('context/:projectId')
   */
  async getContext(
    /* @Param('projectId') */ projectId: number,
    /* @Query('userId') */ userId?: number,
    /* @Query('conversationId') */ conversationId?: number,
  ) {
    return this.enhancedAiService.getContextForDebugging(projectId, {
      userId,
      conversationId,
    });
  }

  /**
   * @Post('context/:projectId/business')
   */
  async updateBusinessContext(
    /* @Param('projectId') */ projectId: number,
    /* @Body() */ updates: any,
  ) {
    return this.enhancedAiService.updateBusinessContext(projectId, updates);
  }

  /**
   * @Post('files/changed')
   */
  async handleFileChanges(
    /* @Body() */ body: {
      projectId: number;
      files: string[];
    },
  ) {
    return this.enhancedAiService.handleFileChanges(body.projectId, body.files);
  }
}
