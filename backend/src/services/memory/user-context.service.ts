import { PrismaService } from '../../db/prisma.service';
import { ContextBlock, ContextProviderOptions } from '../../types/memory/context.types';
import { ContextFormat, ContextType } from '../../generated/prisma';
import { estimateTokenCount } from './utils/token-counting';
import { objectToYaml } from './utils/markdown-utils';

/**
 * Service for managing user context
 */

export class UserContextService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get the context priority
   */
  getPriority(): number {
    return 4; // Default priority for user context
  }

  /**
   * Get context type
   */
  getContextType(): ContextType {
    return ContextType.USER;
  }

  /**
   * Get maximum tokens for this context type
   */
  getMaxTokens(): number {
    return 200;
  }

  /**
   * Get user context for a project
   */
  async getContext(projectId: number, options?: ContextProviderOptions): Promise<ContextBlock> {
    const userId = options?.userId;

    if (!userId) {
      throw new Error('User ID is required for user context');
    }

    // Find user context memory for this project
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.USER,
        is_active: true,
      },
    });

    if (!contextMemory) {
      // No existing context, create default
      return await this.createDefaultUserContext(projectId, userId);
    }

    // Get the latest version
    const latestVersion = await this.prisma.context_versions.findFirst({
      where: {
        context_id: contextMemory.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    if (!latestVersion) {
      // No versions found, create default
      return await this.createDefaultUserContext(projectId, userId);
    }

    return {
      type: ContextType.USER,
      content: latestVersion.content,
      format: latestVersion.format,
      tokenCount: latestVersion.token_count || estimateTokenCount(latestVersion.content),
      metadata: {
        context_id: contextMemory.id,
        version: latestVersion.version,
      },
    };
  }

  /**
   * Create default user context
   */
  private async createDefaultUserContext(projectId: number, userId: number): Promise<ContextBlock> {
    // Get user and project details to create initial context
    const user = await this.prisma.users.findUnique({
      where: { id: userId },
      select: {
        username: true,
        first_name: true,
        last_name: true,
        preferred_ide: true,
        learning_style: true,
        developer_strengths: true,
        preferred_ai_models: true,
        elo_rating: true,
      },
    });

    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: {
        project_motivation: true,
      },
    });

    // Build context object
    const userContextObj = {
      user_context: {
        username: user?.username || 'Anonymous',
        name: user?.first_name ? `${user.first_name} ${user.last_name || ''}` : undefined,
        skill_level: this.determineSkillLevel(user),
        preferred_ide: user?.preferred_ide || 'Unknown',
        learning_style: user?.learning_style || 'Unknown',
        developer_strengths: user?.developer_strengths || [],
        preferred_ai_models: user?.preferred_ai_models || [],
        current_motivation_mode: project?.project_motivation || 'builder',
      },
    };

    // Convert to YAML
    const yamlContent = objectToYaml(userContextObj);
    const tokenCount = estimateTokenCount(yamlContent);

    // Create memory context
    const newContext = await this.prisma.memory_contexts.create({
      data: {
        project_id: projectId,
        contextType: ContextType.USER,
        name: 'User Context',
        description: 'User preferences and settings',
        token_count: tokenCount,
        context_versions: {
          create: {
            version: 1,
            content: yamlContent,
            format: ContextFormat.YAML,
            token_count: tokenCount,
            changed_by: 'system',
            change_reason: 'Initial creation',
          },
        },
      },
      include: {
        context_versions: true,
      },
    });

    return {
      type: ContextType.USER,
      content: yamlContent,
      format: ContextFormat.YAML,
      tokenCount,
      metadata: {
        context_id: newContext.id,
        version: 1,
      },
    };
  }

  /**
   * Update user context
   */
  async updateUserContext(
    projectId: number,
    userId: number,
    updates: Record<string, any>,
  ): Promise<ContextBlock> {
    // Find existing context
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.USER,
        is_active: true,
      },
    });

    if (!contextMemory) {
      // Create default and then update
      await this.createDefaultUserContext(projectId, userId);
      return this.updateUserContext(projectId, userId, updates);
    }

    // Get the latest version
    const latestVersion = await this.prisma.context_versions.findFirst({
      where: {
        context_id: contextMemory.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    if (!latestVersion) {
      // Create default and then update
      await this.createDefaultUserContext(projectId, userId);
      return this.updateUserContext(projectId, userId, updates);
    }
    const latestContent = latestVersion.content;

    // Parse existing YAML
    let userContext: any;
    try {
      // In a real implementation, use a proper YAML parser
      userContext = this.parseYaml(latestContent);
    } catch (error) {
      // If parsing fails, create a new object
      userContext = { user_context: {} };
    }

    // Apply updates
    for (const [key, value] of Object.entries(updates)) {
      userContext.user_context[key] = value;
    }

    // Convert back to YAML
    const updatedYaml = objectToYaml(userContext);
    const tokenCount = estimateTokenCount(updatedYaml);

    // Create new version
    const newVersion = await this.prisma.context_versions.create({
      data: {
        context_id: contextMemory.id,
        version: latestVersion.version + 1,
        content: updatedYaml,
        format: ContextFormat.YAML,
        token_count: tokenCount,
        changed_by: `user:${userId}`,
        change_reason: 'User context update',
      },
    });

    // Update memory context version reference
    await this.prisma.memory_contexts.update({
      where: { id: contextMemory.id },
      data: {
        version: newVersion.version,
        token_count: tokenCount,
        last_updated: new Date(),
      },
    });

    return {
      type: ContextType.USER,
      content: updatedYaml,
      format: ContextFormat.YAML,
      tokenCount,
      metadata: {
        context_id: contextMemory.id,
        version: newVersion.version,
      },
    };
  }

  /**
   * Determine user skill level based on various metrics
   */
  private determineSkillLevel(user: any): string {
    if (!user) return 'Beginner';

    // Use ELO rating if available
    if (user.elo_rating) {
      if (user.elo_rating >= 1800) return 'Expert';
      if (user.elo_rating >= 1400) return 'Intermediate';
      return 'Beginner';
    }

    // Fallback to developer strengths length
    const strengths = user.developer_strengths || [];
    if (Array.isArray(strengths)) {
      if (strengths.length > 5) return 'Expert';
      if (strengths.length > 2) return 'Intermediate';
    }

    return 'Beginner';
  }

  /**
   * Simple YAML parser (placeholder - use a proper library in production)
   */
  private parseYaml(yaml: string): any {
    // This is a very simplified parser - in production use a proper YAML library
    const result: any = {};
    let currentSection: any = null;

    const lines = yaml.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      // Check for section
      if (trimmed.endsWith(':') && !trimmed.includes(' ')) {
        const sectionName = trimmed.slice(0, -1);
        result[sectionName] = {};
        currentSection = result[sectionName];
      }
      // Check for key-value pair
      else if (trimmed.includes(':')) {
        const [key, valueRaw] = trimmed.split(':', 2);
        const keyTrim = key.trim();
        const value = valueRaw.trim();

        if (currentSection) {
          currentSection[keyTrim] = value;
        } else {
          result[keyTrim] = value;
        }
      }
    }

    return result;
  }
}
