import { PrismaService } from '../../db/prisma.service';
import {
  ContextBlock,
  AssembledContext,
  TokenBudget,
  ContextProviderOptions,
} from '../../types/memory/context.types';
import { ContextType } from '../../generated/prisma';
import { UserContextService } from './user-context.service';
import { BusinessContextService } from './business-context.service';
import { TechnicalContextService } from './technical-context.service';
import { CodeContextService } from './code-context.service';
import { TaskContextService } from './task-context.service';
import { TokenBudgetService } from './token-budget.service';
import { summarizeContext } from './utils/context-compression';

/**
 * Central service for managing and assembling context
 */

export class ContextManagerService {
  private contextProviders: Map<ContextType, any> = new Map();

  constructor(
    private prisma: PrismaService,
    private userContext: UserContextService,
    private businessContext: BusinessContextService,
    private technicalContext: TechnicalContextService,
    private codeContext: CodeContextService,
    private taskContext: TaskContextService,
    private tokenBudget: TokenBudgetService,
  ) {
    // Register context providers
    this.registerContextProvider(ContextType.USER, this.userContext);
    this.registerContextProvider(ContextType.BUSINESS, this.businessContext);
    this.registerContextProvider(ContextType.TECHNICAL, this.technicalContext);
    this.registerContextProvider(ContextType.CODE, this.codeContext);
    this.registerContextProvider(ContextType.TASK, this.taskContext);
  }

  /**
   * Register a context provider
   */
  private registerContextProvider(type: ContextType, provider: any): void {
    this.contextProviders.set(type, provider);
  }

  /**
   * Assembles the full context for a project based on token budget
   */
  async assembleContext(
    projectId: number,
    options: {
      userId?: number;
      conversationId?: number;
      totalBudget?: number;
      staticRatio?: number;
      forceIncludeTypes?: ContextType[];
    } = {},
  ): Promise<AssembledContext> {
    const {
      userId,
      conversationId,
      totalBudget = 5000,
      staticRatio = 0.7,
      forceIncludeTypes = [],
    } = options;

    // Calculate token budget
    const budget: TokenBudget = this.tokenBudget.calculateBudget(totalBudget, staticRatio);

    // Get contexts from all providers
    const availableContexts: ContextBlock[] = [];
    const providerOptions: ContextProviderOptions = {
      userId,
      conversationId,
    };

    for (const [type, provider] of this.contextProviders.entries()) {
      try {
        const contextBlock = await provider.getContext(projectId, providerOptions);
        if (contextBlock) {
          availableContexts.push(contextBlock);
        }
      } catch (error) {
        console.error(`Error getting context from provider ${type}:`, error);
      }
    }

    // Select and prioritize contexts
    const selectedBlocks = await this.tokenBudget.selectContextBlocks(
      availableContexts,
      budget,
      forceIncludeTypes,
    );

    // Track context usage
    if (conversationId) {
      await this.trackContextUsage(conversationId, selectedBlocks);
    }

    const totalTokens = selectedBlocks.reduce((sum, block) => sum + block.tokenCount, 0);

    return {
      blocks: selectedBlocks,
      totalTokens,
      strategy: 'priority-based',
      timestamp: new Date(),
    };
  }

  /**
   * Records which context blocks were used in a conversation
   */
  private async trackContextUsage(conversationId: number, blocks: ContextBlock[]): Promise<void> {
    await this.prisma.chat_contexts.create({
      data: {
        conversation_id: conversationId,
        context_snapshot: JSON.stringify(blocks),
        token_usage: blocks.reduce((sum, block) => sum + block.tokenCount, 0),
      },
    });
  }

  /**
   * Formats the assembled context into a string ready for AI consumption
   */
  formatAssembledContext(assembled: AssembledContext): string {
    const sections: string[] = [];

    // Add a context summary for transparency
    const contextSummary = summarizeContext(
      assembled.blocks.map((block) => ({
        type: block.type,
        content: block.content,
        tokenCount: block.tokenCount,
      })),
    );

    sections.push(contextSummary + '\n\n');

    // Add each context block
    for (const block of assembled.blocks) {
      // Format based on block type and format
      const header = `## ${block.type} CONTEXT\n`;
      sections.push(header + block.content + '\n');
    }

    return sections.join('\n');
  }

  /**
   * Get available context types for a project
   */
  async getAvailableContextTypes(projectId: number): Promise<ContextType[]> {
    const contexts = await this.prisma.memory_contexts.findMany({
      where: {
        project_id: projectId,
        is_active: true,
      },
      select: {
        contextType: true,
      },
    });

    return contexts.map((ctx: { contextType: ContextType }) => ctx.contextType);
  }

  /**
   * Get user context preferences
   */
  async getUserContextPreferences(userId: number): Promise<any> {
    return await this.prisma.user_context_preferences.findMany({
      where: {
        user_id: userId,
      },
    });
  }

  /**
   * Update user context preferences
   */
  async updateUserContextPreference(
    userId: number,
    contextType: ContextType,
    updates: {
      isPinned?: boolean;
      isDisabled?: boolean;
      priority?: number;
    },
  ): Promise<any> {
    await this.prisma.user_context_preferences.upsert({
      where: {
        user_id_contextType: {
          user_id: userId,
          contextType,
        },
      },
      update: updates,
      create: {
        user_id: userId,
        contextType,
        ...updates,
      },
    });

    const updatedPreference = await this.prisma.user_context_preferences.findUnique({
      where: {
        user_id_contextType: {
          user_id: userId,
          contextType,
        },
      },
    });

    return updatedPreference;
  }
}
