import { PrismaService } from '../../db/prisma.service';
import { ContextBlock, ContextProviderOptions } from '../../types/memory/context.types';
import { ContextFormat, ContextType } from '../../generated/prisma';
import { estimateTokenCount } from './utils/token-counting';

/**
 * Service for managing task context
 */

export class TaskContextService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get the context priority
   */
  getPriority(): number {
    return 0; // Highest priority for task context
  }

  /**
   * Get context type
   */
  getContextType(): ContextType {
    return ContextType.TASK;
  }

  /**
   * Get maximum tokens for this context type
   */
  getMaxTokens(): number {
    return 1500;
  }

  /**
   * Get task context for a project
   */
  async getContext(projectId: number, options?: ContextProviderOptions): Promise<ContextBlock> {
    const conversationId = options?.conversationId;

    // If conversation ID is provided, get conversation-specific task context
    if (conversationId) {
      const conversationContext = await this.getConversationTaskContext(conversationId);
      if (conversationContext) {
        return conversationContext;
      }
    }

    // Otherwise get project-level task context
    return await this.getProjectTaskContext(projectId);
  }

  /**
   * Get task context for a specific conversation
   */
  private async getConversationTaskContext(conversationId: number): Promise<ContextBlock | null> {
    // Get conversation with related details
    const conversation = await this.prisma.conversations.findUnique({
      where: { id: conversationId },
      select: {
        project_id: true,
        key_objective: true,
        category: true,
        agent_status: true,
        agent_progress: true,
        agent_commands_executed: true,
        messages: {
          orderBy: {
            created_at: 'desc',
          },
          take: 3,
        },
      },
    });

    if (!conversation) {
      return null;
    }

    // Generate markdown content
    let markdownContent = `# Current Task Context\n\n`;

    if (conversation.key_objective) {
      markdownContent += `## Objective\n\n${conversation.key_objective}\n\n`;
    }

    if (conversation.category) {
      markdownContent += `**Category**: ${conversation.category}\n\n`;
    }

    if (conversation.agent_status) {
      markdownContent += `## Status\n\n${conversation.agent_status}\n\n`;
    }

    if (conversation.agent_progress) {
      markdownContent += `## Progress\n\n`;

      try {
        const progress = JSON.parse(conversation.agent_progress.toString());
        if (Array.isArray(progress)) {
          progress.forEach((item, index) => {
            markdownContent += `${index + 1}. ${item}\n`;
          });
        } else if (typeof progress === 'object') {
          Object.entries(progress).forEach(([key, value]) => {
            markdownContent += `- **${key}**: ${value}\n`;
          });
        } else {
          markdownContent += `${progress}\n`;
        }
      } catch {
        // If parsing fails, just add as string
        markdownContent += `${conversation.agent_progress}\n`;
      }

      markdownContent += `\n`;
    }

    if (conversation.agent_commands_executed) {
      markdownContent += `## Recent Actions\n\n`;

      try {
        const commands = JSON.parse(conversation.agent_commands_executed.toString());
        if (Array.isArray(commands)) {
          commands.slice(-5).forEach((cmd, index) => {
            markdownContent += `${index + 1}. ${cmd}\n`;
          });
        }
      } catch {
        // Skip if parsing fails
      }

      markdownContent += `\n`;
    }

    // Add recent message summary
    if (conversation.messages.length > 0) {
      markdownContent += `## Recent Focus\n\n`;

      conversation.messages.forEach((msg: { content: string }) => {
        // Extract the first sentence or up to 100 chars
        const summary = msg.content.split(/[.!?]/, 1)[0].substring(0, 100);
        markdownContent += `- ${summary}...\n`;
      });
    }

    const tokenCount = estimateTokenCount(markdownContent);

    return {
      type: ContextType.TASK,
      content: markdownContent,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        conversationId,
      },
    };
  }

  /**
   * Get project-level task context
   */
  private async getProjectTaskContext(projectId: number): Promise<ContextBlock> {
    // Get active tasks for the project
    const tasks = await this.prisma.tasks.findMany({
      where: {
        project_id: projectId,
        status: {
          notIn: ['COMPLETED', 'CANCELLED'],
        },
      },
      orderBy: [
        {
          priority: 'desc',
        },
        {
          due_date: 'asc',
        },
      ],
      take: 5,
    });

    // Get recent conversations for the project
    const conversations = await this.prisma.conversations.findMany({
      where: {
        project_id: projectId,
        status: 'active',
      },
      orderBy: {
        updated_at: 'desc',
      },
      select: {
        id: true,
        title: true,
        key_objective: true,
        category: true,
        updated_at: true,
      },
      take: 3,
    });

    // Generate markdown content
    let markdownContent = `# Project Task Context\n\n`;

    // Add active tasks
    if (tasks.length > 0) {
      markdownContent += `## Active Tasks\n\n`;

      tasks.forEach(
        (task: {
          title: string;
          description: string | null;
          status: string;
          priority: number;
          due_date: Date | null;
          tags: any;
        }) => {
          markdownContent += `### ${task.title}\n\n`;

          if (task.description) {
            markdownContent += `${task.description}\n\n`;
          }

          markdownContent += `**Status**: ${task.status}\n`;
          markdownContent += `**Priority**: ${task.priority}\n`;

          if (task.due_date) {
            markdownContent += `**Due Date**: ${task.due_date.toISOString().split('T')[0]}\n`;
          }

          // Add tags if available
          try {
            if (task.tags) {
              const tags = JSON.parse(task.tags.toString());
              if (Array.isArray(tags) && tags.length > 0) {
                markdownContent += `**Tags**: ${tags.join(', ')}\n`;
              }
            }
          } catch {
            // Skip if parsing fails
          }

          markdownContent += `\n`;
        },
      );
    } else {
      markdownContent += `No active tasks currently.\n\n`;
    }

    // Add recent conversations
    if (conversations.length > 0) {
      markdownContent += `## Recent Conversations\n\n`;

      conversations.forEach(
        (conversation: {
          title: string | null;
          key_objective: string | null;
          category: string | null;
          updated_at: Date;
        }) => {
          markdownContent += `### ${conversation.title || 'Untitled Conversation'}\n\n`;

          if (conversation.key_objective) {
            markdownContent += `**Objective**: ${conversation.key_objective}\n`;
          }

          if (conversation.category) {
            markdownContent += `**Type**: ${conversation.category}\n`;
          }

          markdownContent += `**Last Updated**: ${conversation.updated_at.toISOString().split('T')[0]}\n\n`;
        },
      );
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Check if we already have a memory context for task
    const existingContext = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.TASK,
        is_active: true,
      },
      include: {
        context_versions: {
          orderBy: {
            version: 'desc',
          },
          take: 1,
        },
      },
    });

    if (existingContext) {
      // Update existing context
      const latestVersion = existingContext.context_versions[0];

      // Create new version
      const newVersion = await this.prisma.context_versions.create({
        data: {
          context_id: existingContext.id,
          version: latestVersion.version + 1,
          content: markdownContent,
          format: ContextFormat.MARKDOWN,
          token_count: tokenCount,
          changed_by: 'system',
          change_reason: 'Updated task context',
        },
      });

      // Update memory context
      await this.prisma.memory_contexts.update({
        where: { id: existingContext.id },
        data: {
          version: newVersion.version,
          token_count: tokenCount,
          last_updated: new Date(),
          last_synced_at: new Date(),
        },
      });

      return {
        type: ContextType.TASK,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        tokenCount,
        metadata: {
          contextId: existingContext.id,
          version: newVersion.version,
        },
      };
    } else {
      // Create new context
      const newContext = await this.prisma.memory_contexts.create({
        data: {
          project_id: projectId,
          contextType: ContextType.TASK,
          name: 'Task Context',
          description: 'Project tasks and recent conversations',
          token_count: tokenCount,
          last_synced_at: new Date(),
          context_versions: {
            create: {
              version: 1,
              content: markdownContent,
              format: ContextFormat.MARKDOWN,
              token_count: tokenCount,
              changed_by: 'system',
              change_reason: 'Initial creation',
            },
          },
        },
        include: {
          context_versions: true,
        },
      });

      return {
        type: ContextType.TASK,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        tokenCount,
        metadata: {
          contextId: newContext.id,
          version: 1,
        },
      };
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(taskId: number, status: string): Promise<void> {
    await this.prisma.tasks.update({
      where: { id: taskId },
      data: {
        status,
        completed_at: status === 'COMPLETED' ? new Date() : null,
      },
    });

    // Get the project ID for this task
    const task = await this.prisma.tasks.findUnique({
      where: { id: taskId },
      select: { project_id: true },
    });

    if (task) {
      // Mark task context as needing refresh
      await this.prisma.memory_contexts.updateMany({
        where: {
          project_id: task.project_id,
          contextType: ContextType.TASK,
        },
        data: {
          last_synced_at: null,
        },
      });
    }
  }

  /**
   * Create a new task
   */
  async createTask(
    projectId: number,
    data: {
      title: string;
      description?: string;
      status?: string;
      priority?: number;
      dueDate?: Date;
      assignedToUserId?: number;
      tags?: string[];
    },
  ): Promise<any> {
    const task = await this.prisma.tasks.create({
      data: {
        project_id: projectId,
        title: data.title,
        description: data.description,
        status: data.status || 'TODO',
        priority: data.priority || 1,
        due_date: data.dueDate,
        assigned_to_user_id: data.assignedToUserId,
        tags: data.tags ? JSON.stringify(data.tags) : '[]',
      },
    });

    // Mark task context as needing refresh
    await this.prisma.memory_contexts.updateMany({
      where: {
        project_id: projectId,
        contextType: ContextType.TASK,
      },
      data: {
        last_synced_at: null,
      },
    });

    return task;
  }

  /**
   * Get tasks for a project
   */
  async getProjectTasks(projectId: number): Promise<any[]> {
    return this.prisma.tasks.findMany({
      where: {
        project_id: projectId,
      },
      orderBy: [
        {
          status: 'asc',
        },
        {
          priority: 'desc',
        },
        {
          due_date: 'asc',
        },
      ],
    });
  }

  /**
   * Update conversation objective
   */
  async updateConversationObjective(conversationId: number, objective: string): Promise<void> {
    await this.prisma.conversations.update({
      where: { id: conversationId },
      data: {
        key_objective: objective,
      },
    });
  }

  /**
   * Update agent progress
   */
  async updateAgentProgress(conversationId: number, progress: any): Promise<void> {
    await this.prisma.conversations.update({
      where: { id: conversationId },
      data: {
        agent_progress: typeof progress === 'string' ? progress : JSON.stringify(progress),
      },
    });
  }
}
