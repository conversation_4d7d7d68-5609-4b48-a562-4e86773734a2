import { ContextManagerService } from './context-manager.service';
import { ContextType } from '../../generated/prisma';
import { calculateTokenUsage } from './utils/token-counting';

/**
 * Service for integrating context with AI models
 */
export class AiContextIntegrationService {
  constructor(private contextManager: ContextManagerService) {}

  /**
   * Generate a prompt with context for AI
   */
  async generatePromptWithContext(
    projectId: number,
    basePrompt: string,
    options: {
      userId?: number;
      conversationId?: number;
      totalBudget?: number;
      forceIncludeTypes?: ContextType[];
    } = {},
  ): Promise<string> {
    // Get assembled context
    const context = await this.contextManager.assembleContext(projectId, options);

    // Format context for prompt
    const formattedContext = this.contextManager.formatAssembledContext(context);

    // Combine context with base prompt
    return `
${formattedContext}

Given the context above, please respond to the following:

${basePrompt}
`;
  }

  /**
   * Wrap an AI service method to include context
   */
  enhanceAiService<T extends Record<string, any>>(
    originalService: T,
    methodName: string = 'generateCompletion',
  ): T {
    const self = this;
    const originalMethod = originalService[methodName];

    if (!originalMethod || typeof originalMethod !== 'function') {
      console.warn(`Method ${methodName} not found on service or is not a function`);
      return originalService;
    }

    // Replace the original method with our enhanced version
    // Use type assertion to fix the TypeScript error
    (originalService[methodName] as any) = async function (
      this: any,
      prompt: string,
      options: any = {},
    ) {
      // Check if context enhancement is requested
      if (options.projectId && options.enhanceWithContext !== false) {
        try {
          const enhancedPrompt = await self.generatePromptWithContext(options.projectId, prompt, {
            userId: options.userId,
            conversationId: options.conversationId,
            totalBudget: options.contextBudget,
            forceIncludeTypes: options.forceIncludeTypes,
          });

          // Calculate original vs enhanced token usage for logging
          const originalTokens = calculateTokenUsage(prompt, '').promptTokens;
          const enhancedTokens = calculateTokenUsage(enhancedPrompt, '').promptTokens;

          console.log(
            `Context enhancement: ${originalTokens} -> ${enhancedTokens} tokens (+${enhancedTokens - originalTokens})`,
          );

          // Call original method with enhanced prompt
          return originalMethod.call(this, enhancedPrompt, {
            ...options,
            // Mark as already enhanced to avoid double enhancement
            enhanceWithContext: false,
          });
        } catch (error) {
          console.error('Error enhancing prompt with context:', error);
          // Fall back to original method if enhancement fails
          return originalMethod.call(this, prompt, options);
        }
      }

      // Call original method without modification
      return originalMethod.call(this, prompt, options);
    };

    return originalService;
  }

  /**
   * Estimate additional tokens from context enhancement
   */
  async estimateContextTokens(
    projectId: number,
    options: {
      userId?: number;
      conversationId?: number;
      forceIncludeTypes?: ContextType[];
    } = {},
  ): Promise<number> {
    try {
      const context = await this.contextManager.assembleContext(projectId, {
        userId: options.userId,
        conversationId: options.conversationId,
        forceIncludeTypes: options.forceIncludeTypes,
      });

      return context.totalTokens;
    } catch (error) {
      console.error('Error estimating context tokens:', error);
      return 0;
    }
  }

  /**
   * Get context debug information
   */
  async getContextDebugInfo(
    projectId: number,
    options: {
      userId?: number;
      conversationId?: number;
    } = {},
  ): Promise<any> {
    try {
      const context = await this.contextManager.assembleContext(projectId, options);

      return {
        totalTokens: context.totalTokens,
        blockCount: context.blocks.length,
        blockSummary: context.blocks.map((block) => ({
          type: block.type,
          format: block.format,
          tokenCount: block.tokenCount,
          contentPreview: block.content.substring(0, 100) + '...',
        })),
        timestamp: context.timestamp,
      };
    } catch (error) {
      console.error('Error getting context debug info:', error);
      return {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date(),
      };
    }
  }
}
