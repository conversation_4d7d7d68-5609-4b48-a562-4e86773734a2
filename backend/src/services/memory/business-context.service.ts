import { PrismaService } from '../../db/prisma.service';
import {
  ContextBlock,
  ContextProviderOptions,
  SlideDeckData,
} from '../../types/memory/context.types';
import { ContextFormat, ContextType, SlideDeckType } from '../../generated/prisma';
import { estimateTokenCount } from './utils/token-counting';

/**
 * Service for managing project business context
 */
export class BusinessContextService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get the context priority
   */
  getPriority(): number {
    return 3; // Default priority for business context
  }

  /**
   * Get context type
   */
  getContextType(): ContextType {
    return ContextType.BUSINESS;
  }

  /**
   * Get maximum tokens for this context type
   */
  getMaxTokens(): number {
    return 300;
  }

  /**
   * Get business context for a project
   */
  async getContext(projectId: number, _options?: ContextProviderOptions): Promise<ContextBlock> {
    // Find business context memory for this project
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.BUSINESS,
        is_active: true,
      },
      include: {
        slide_decks: {
          include: {
            memory_slides: {
              orderBy: {
                order: 'asc',
              },
            },
          },
        },
      },
    });

    if (!contextMemory) {
      // No existing context, create default
      return await this.createDefaultBusinessContext(projectId);
    }

    // Get the latest version
    const latestVersion = await this.prisma.context_versions.findFirst({
      where: {
        context_id: contextMemory.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    if (!latestVersion) {
      // No versions found, create default
      return await this.createDefaultBusinessContext(projectId);
    }

    let slideDeckId: number | undefined = undefined;
    if (
      Array.isArray(contextMemory.slide_decks) &&
      contextMemory.slide_decks.length > 0 &&
      'id' in contextMemory.slide_decks[0]
    ) {
      slideDeckId = (contextMemory.slide_decks[0] as any).id;
    }
    return {
      type: ContextType.BUSINESS,
      content: latestVersion.content,
      format: latestVersion.format,
      tokenCount: latestVersion.token_count || estimateTokenCount(latestVersion.content),
      metadata: {
        contextId: contextMemory.id,
        version: latestVersion.version,
        slideDeckId,
      },
    };
  }

  /**
   * Create default business context
   */
  private async createDefaultBusinessContext(projectId: number): Promise<ContextBlock> {
    // Get project details to create initial context
    const project = await this.prisma.projects.findUnique({
      where: { id: projectId },
      select: {
        name: true,
        description: true,
        project_type: true,
        domain: true,
        target_audience: true,
        constraints: true,
      },
    });

    if (!project) {
      throw new Error(`Project with id ${projectId} not found`);
    }

    // Format target audience
    let targetAudienceContent = 'General users';
    if (project.target_audience) {
      try {
        const audience = Array.isArray(project.target_audience)
          ? project.target_audience
          : JSON.parse(project.target_audience.toString());

        if (Array.isArray(audience) && audience.length > 0) {
          targetAudienceContent = audience.join(', ');
        }
      } catch {
        // Use default if parsing fails
      }
    }

    // Create slide deck data
    const slideDeckData: SlideDeckData = {
      projectId,
      title: 'Project Business Context',
      description: 'Business overview of the project',
      type: SlideDeckType.BUSINESS,
    };

    // Create slides
    const slideDeck = await this.prisma.slide_decks.create({
      data: {
        project_id: slideDeckData.projectId,
        title: slideDeckData.title,
        description: slideDeckData.description,
        type: slideDeckData.type,
        memory_slides: {
          create: [
            {
              title: 'Purpose',
              content: `Build ${project.name || 'a new project'} that ${project.description || 'solves user needs'}.`,
              order: 1,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Target Audience',
              content: targetAudienceContent,
              order: 2,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Core Value Proposition',
              content:
                project.description ||
                'Provides value to users by addressing their needs effectively.',
              order: 3,
              format: ContextFormat.MARKDOWN,
            },
            {
              title: 'Domain',
              content: project.domain || 'General',
              order: 4,
              format: ContextFormat.MARKDOWN,
            },
          ],
        },
      },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    // Format markdown content from slides
    let markdownContent = `# ${slideDeck.title}\n\n`;
    if (slideDeck.description) {
      markdownContent += `${slideDeck.description}\n\n`;
    }

    // Add each slide
    for (const slide of slideDeck.memory_slides) {
      markdownContent += `## ${slide.title || 'Slide ' + slide.order}\n\n`;
      markdownContent += `${slide.content}\n\n`;
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Create memory context
    const memoryContext = await this.prisma.memory_contexts.create({
      data: {
        project_id: projectId,
        slide_deck_id: slideDeck.id,
        contextType: ContextType.BUSINESS,
        name: 'Business Context',
        description: 'Business overview of the project',
        token_count: tokenCount,
        context_versions: {
          create: {
            version: 1,
            content: markdownContent,
            format: ContextFormat.MARKDOWN,
            token_count: tokenCount,
            changed_by: 'system',
            change_reason: 'Initial creation',
          },
        },
      },
    });

    return {
      type: ContextType.BUSINESS,
      content: markdownContent,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        contextId: memoryContext.id,
        version: 1,
        slideDeckId: slideDeck.id,
      },
    };
  }

  /**
   * Update business context
   */
  async updateBusinessContext(
    projectId: number,
    slideDeckId: number,
    slides: Array<{
      id?: number;
      title: string;
      content: string;
      order: number;
    }>,
  ): Promise<ContextBlock> {
    // Update slide deck
    await this.prisma.$transaction(async (tx) => {
      // Update existing slides and add new ones
      for (const slide of slides) {
        if (slide.id) {
          // Update existing slide
          await tx.memory_slides.update({
            where: { id: slide.id },
            data: {
              title: slide.title,
              content: slide.content,
              order: slide.order,
              last_updated: new Date(),
            },
          });
        } else {
          // Create new slide
          await tx.memory_slides.create({
            data: {
              slide_deck_id: slideDeckId,
              title: slide.title,
              content: slide.content,
              order: slide.order,
              format: ContextFormat.MARKDOWN,
            },
          });
        }
      }

      // Update slide deck's lastUpdated timestamp
      await tx.slide_decks.update({
        where: { id: slideDeckId },
        data: {
          last_updated: new Date(),
        },
      });
    });

    // Get updated slide deck
    const updatedSlideDeck = await this.prisma.slide_decks.findUnique({
      where: { id: slideDeckId },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    if (!updatedSlideDeck) {
      throw new Error(`Slide deck with id ${slideDeckId} not found`);
    }

    // Format markdown content from slides
    let markdownContent = `# ${updatedSlideDeck.title}\n\n`;
    if (updatedSlideDeck.description) {
      markdownContent += `${updatedSlideDeck.description}\n\n`;
    }

    // Add each slide
    for (const slide of updatedSlideDeck.memory_slides) {
      markdownContent += `## ${slide.title || 'Slide ' + slide.order}\n\n`;
      markdownContent += `${slide.content}\n\n`;
    }

    const tokenCount = estimateTokenCount(markdownContent);

    // Get memory context
    const contextMemory = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        slide_deck_id: slideDeckId,
        contextType: ContextType.BUSINESS,
      },
    });

    if (!contextMemory) {
      throw new Error(`Business context for project ${projectId} not found`);
    }

    const latestVersion = await this.prisma.context_versions.findFirst({
      where: {
        context_id: contextMemory.id,
      },
      orderBy: {
        version: 'desc',
      },
    });

    if (!latestVersion) {
      throw new Error('No context version found');
    }

    // Create new version
    const newVersion = await this.prisma.context_versions.create({
      data: {
        context_id: contextMemory.id,
        version: latestVersion.version + 1,
        content: markdownContent,
        format: ContextFormat.MARKDOWN,
        token_count: tokenCount,
        changed_by: 'user',
        change_reason: 'Updated business context',
      },
    });

    // Update memory context
    await this.prisma.memory_contexts.update({
      where: { id: contextMemory.id },
      data: {
        version: newVersion.version,
        token_count: tokenCount,
        last_updated: new Date(),
        last_synced_at: new Date(),
      },
    });

    return {
      type: ContextType.BUSINESS,
      content: markdownContent,
      format: ContextFormat.MARKDOWN,
      tokenCount,
      metadata: {
        contextId: contextMemory.id,
        version: newVersion.version,
        slideDeckId: slideDeckId,
      },
    };
  }

  /**
   * Get business context slides
   */
  async getBusinessContextSlides(projectId: number): Promise<any> {
    const memoryContext = await this.prisma.memory_contexts.findFirst({
      where: {
        project_id: projectId,
        contextType: ContextType.BUSINESS,
        is_active: true,
      },
      select: {
        slide_deck_id: true,
      },
    });

    if (!memoryContext?.slide_deck_id) {
      return null;
    }

    return await this.prisma.slide_decks.findUnique({
      where: {
        id: memoryContext.slide_deck_id,
      },
      include: {
        memory_slides: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });
  }
}
