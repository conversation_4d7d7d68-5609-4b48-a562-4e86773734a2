/**
 * Utilities for compressing context to fit within token limits
 */
import { estimateTokenCount } from './token-counting';
import { extractMarkdownHeaders, extractContentBetweenHeaders } from './markdown-utils';

/**
 * Compress markdown by reducing level of detail
 */
export function compressMarkdown(markdown: string, targetTokens: number): string {
  if (!markdown) return '';

  const currentTokens = estimateTokenCount(markdown);
  if (currentTokens <= targetTokens) return markdown;

  // First strategy: Remove code blocks
  const withoutCode = removeCodeBlocks(markdown);
  const withoutCodeTokens = estimateTokenCount(withoutCode);

  if (withoutCodeTokens <= targetTokens) {
    return withoutCode;
  }

  // Second strategy: Keep only headers and first paragraph after each
  const compressed = keepHeadersAndFirstParagraph(withoutCode);
  const compressedTokens = estimateTokenCount(compressed);

  if (compressedTokens <= targetTokens) {
    return compressed;
  }

  // Third strategy: Keep only main sections
  const mainSections = keepMainSections(compressed);
  const mainSectionsTokens = estimateTokenCount(mainSections);

  if (mainSectionsTokens <= targetTokens) {
    return mainSections;
  }

  // Final strategy: Truncate
  return truncateToTokenLimit(mainSections, targetTokens);
}

/**
 * Remove code blocks from markdown
 */
function removeCodeBlocks(markdown: string): string {
  return markdown.replace(/```[\s\S]*?```/g, '*[Code block removed for brevity]*');
}

/**
 * Keep only headers and first paragraph after each
 */
function keepHeadersAndFirstParagraph(markdown: string): string {
  const lines = markdown.split('\n');
  const result: string[] = [];
  let isCapturingParagraph = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Keep headers
    if (/^#+\s+/.test(line)) {
      result.push(line);
      isCapturingParagraph = true;
      continue;
    }

    // Keep empty lines
    if (line.trim() === '') {
      result.push(line);
      continue;
    }

    // Keep first paragraph after header
    if (isCapturingParagraph) {
      result.push(line);

      // Check if end of paragraph (empty line follows)
      if (i + 1 < lines.length && lines[i + 1].trim() === '') {
        isCapturingParagraph = false;
      }
    }
  }

  return result.join('\n');
}

/**
 * Keep only main sections (level 1 and 2 headers with their first paragraphs)
 */
function keepMainSections(markdown: string): string {
  const lines = markdown.split('\n');
  const result: string[] = [];
  let isImportantSection = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Keep main headers (level 1 and 2)
    if (/^#{1,2}\s+/.test(line)) {
      result.push(line);
      isImportantSection = true;
      continue;
    }

    // Stop capturing after less important headers
    if (/^#{3,}\s+/.test(line)) {
      isImportantSection = false;
      continue;
    }

    // Keep empty lines
    if (line.trim() === '') {
      result.push(line);
      continue;
    }

    // Keep content in important sections
    if (isImportantSection) {
      result.push(line);
    }
  }

  return result.join('\n');
}

/**
 * Simple truncation to meet token limit
 */
function truncateToTokenLimit(markdown: string, tokenLimit: number): string {
  const lines = markdown.split('\n');
  let result = '';
  let currentTokens = 0;

  for (const line of lines) {
    const lineTokens = estimateTokenCount(line + '\n');

    if (currentTokens + lineTokens <= tokenLimit - 10) {
      // Leave room for truncation message
      result += line + '\n';
      currentTokens += lineTokens;
    } else {
      break;
    }
  }

  return result + '\n*[Content truncated to fit token limit]*';
}

/**
 * Extract important sections based on keywords
 */
export function extractImportantSections(
  markdown: string,
  keywords: string[],
  tokenLimit: number,
): string {
  if (!markdown) return '';

  // Get all headers
  const headers = extractMarkdownHeaders(markdown);
  let result = '';

  // Find headers that contain keywords
  const matchingHeaders = headers.filter((header) =>
    keywords.some((keyword) => header.text.toLowerCase().includes(keyword.toLowerCase())),
  );

  // Extract content from matching sections
  for (const header of matchingHeaders) {
    // Build the header text for this section
    const _headerText = '#'.repeat(header.level) + ' ' + header.text;
    const nextHeaderIndex = headers.findIndex((h) => h.text === header.text) + 1;

    const nextHeader =
      nextHeaderIndex < headers.length
        ? '#'.repeat(headers[nextHeaderIndex].level) + ' ' + headers[nextHeaderIndex].text
        : undefined;

    const sectionContent = extractContentBetweenHeaders(
      markdown,
      header.text,
      nextHeader ? headers[nextHeaderIndex].text : undefined,
    );

    if (sectionContent) {
      result += sectionContent + '\n\n';
    }
  }

  // If we don't have any matches, extract the first section
  if (result.trim() === '' && headers.length > 0) {
    const firstHeader = headers[0];
    const nextHeader = headers.length > 1 ? headers[1].text : undefined;

    const firstSection = extractContentBetweenHeaders(markdown, firstHeader.text, nextHeader);
    result = firstSection;
  }

  // Check if we need to compress
  const resultTokens = estimateTokenCount(result);
  if (resultTokens > tokenLimit) {
    return compressMarkdown(result, tokenLimit);
  }

  return result;
}

/**
 * Summarize context for debugging and transparency
 */
export function summarizeContext(
  contexts: Array<{ type: string; content: string; tokenCount: number }>,
): string {
  let summary = '## Context Summary\n\n';
  let totalTokens = 0;

  contexts.forEach((ctx) => {
    totalTokens += ctx.tokenCount;

    // Extract first line or header
    const firstLine = ctx.content.split('\n')[0].replace(/^#+\s+/, '');
    summary += `- **${ctx.type}**: ${firstLine} (${ctx.tokenCount} tokens)\n`;
  });

  summary += `\nTotal: ${totalTokens} tokens`;
  return summary;
}
