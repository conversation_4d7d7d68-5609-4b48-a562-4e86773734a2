/**
 * Utility functions for working with markdown and tokens
 */

/**
 * Estimate token count for a string
 * In production, use a proper tokenizer like tiktoken
 */
export function estimateTokenCount(text: string): number {
  if (!text) return 0;
  // Very crude estimation - in production use a proper tokenizer
  return Math.ceil(text.length / 4);
}

/**
 * Extract the first few sentences from markdown text
 */
export function extractSummaryFromMarkdown(markdown: string, maxLength: number = 200): string {
  if (!markdown) return '';

  // Remove markdown formatting
  let text = markdown
    .replace(/#+\s+(.*)/g, '$1') // Remove headings
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .replace(/\s+/g, ' ') // Normalize spaces
    .trim();

  // Extract first few sentences
  const sentences = text.split(/[.!?]+/);
  let summary = '';
  let i = 0;

  while (summary.length < maxLength && i < sentences.length) {
    if (i > 0) summary += '. ';
    summary += sentences[i].trim();
    i++;
  }

  // Truncate if still too long
  if (summary.length > maxLength) {
    summary = summary.substring(0, maxLength - 3) + '...';
  } else if (summary.length > 0 && !summary.endsWith('.')) {
    summary += '.';
  }

  return summary;
}

/**
 * Extract headers from markdown
 */
export function extractMarkdownHeaders(markdown: string): { level: number; text: string }[] {
  if (!markdown) return [];

  const headerRegex = /^(#{1,6})\s+(.+)$/gm;
  const headers: { level: number; text: string }[] = [];
  let match;

  while ((match = headerRegex.exec(markdown)) !== null) {
    headers.push({
      level: match[1].length,
      text: match[2].trim(),
    });
  }

  return headers;
}

/**
 * Format markdown for AI consumption with consistent structure
 */
export function formatMarkdownForAI(markdown: string): string {
  if (!markdown) return '';

  // Ensure headers start with a newline
  let formatted = markdown.replace(/([^\n])#{1,6}\s+/g, '$1\n\n#');

  // Ensure code blocks have newlines before and after
  formatted = formatted.replace(/([^\n])```/g, '$1\n\n```');
  formatted = formatted.replace(/```([^\n])/g, '```\n\n$1');

  // Normalize bullet points
  formatted = formatted.replace(/([^\n])[*\-+]\s+/g, '$1\n\n- ');

  return formatted.trim();
}

/**
 * Generate a table of contents from markdown
 */
export function generateTableOfContents(markdown: string): string {
  if (!markdown) return '';

  const headers = extractMarkdownHeaders(markdown);
  if (headers.length === 0) return '';

  let toc = '## Table of Contents\n\n';

  headers.forEach((header) => {
    // Skip level 1 headers (title)
    if (header.level === 1) return;

    // Add indentation based on header level
    const indent = '  '.repeat(header.level - 2);
    const anchor = header.text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    toc += `${indent}- [${header.text}](#${anchor})\n`;
  });

  return toc;
}

/**
 * Convert YAML to markdown
 */
export function yamlToMarkdown(yaml: string): string {
  if (!yaml) return '';

  try {
    // This is a simple conversion that expects key-value pairs
    const lines = yaml.split('\n');
    let markdown = '';
    let currentSection = '';

    for (const line of lines) {
      if (line.trim() === '') continue;

      // Check for section
      if (!line.startsWith(' ') && line.endsWith(':')) {
        currentSection = line.slice(0, -1).trim();
        markdown += `## ${currentSection}\n\n`;
      }
      // Check for key-value
      else if (line.includes(':')) {
        const [key, value] = line.split(':').map((part) => part.trim());
        markdown += `- **${key}**: ${value}\n`;
      }
      // Just add the line
      else {
        markdown += `${line.trim()}\n`;
      }
    }

    return markdown;
  } catch (error) {
    console.error('Error converting YAML to markdown:', error);
    return yaml; // Return original if conversion fails
  }
}

/**
 * Truncate markdown to fit within token limit
 */
export function truncateMarkdownToTokenLimit(markdown: string, tokenLimit: number): string {
  if (!markdown) return '';
  if (estimateTokenCount(markdown) <= tokenLimit) return markdown;

  // Split by different sections
  const sections = markdown.split(/\n\s*\n/);
  let result = '';
  let currentTokens = 0;

  // Always include the first section (usually title/intro)
  if (sections.length > 0) {
    result += sections[0] + '\n\n';
    currentTokens += estimateTokenCount(sections[0]);
  }

  // Add sections until we hit the limit
  for (let i = 1; i < sections.length; i++) {
    const sectionTokens = estimateTokenCount(sections[i]);

    if (currentTokens + sectionTokens <= tokenLimit) {
      result += sections[i] + '\n\n';
      currentTokens += sectionTokens;
    } else {
      // If we can't add the whole section, add a note
      result += '\n\n...(content truncated to fit token limit)';
      break;
    }
  }

  return result.trim();
}

/**
 * Parse YAML to JavaScript object
 */
export function parseYaml(yaml: string): any {
  if (!yaml) return {};

  try {
    // Simple YAML parser for key-value pairs
    const lines = yaml.split('\n');
    const result: Record<string, any> = {};
    let currentSection: Record<string, any> = result;
    let currentKey = '';
    let indentLevel = 0;

    for (const line of lines) {
      if (line.trim() === '') continue;

      const currentIndent = line.search(/\S/);
      const isIndented = currentIndent > indentLevel;

      if (line.includes(':')) {
        const [key, value] = line.split(':').map((part) => part.trim());

        if (value === '') {
          // New section
          currentKey = key;
          result[currentKey] = {};
          currentSection = result[currentKey];
          indentLevel = currentIndent;
        } else {
          // Key-value pair
          if (isIndented) {
            currentSection[key] = value;
          } else {
            result[key] = value;
          }
        }
      }
    }

    return result;
  } catch (error) {
    console.error('Error parsing YAML:', error);
    return {}; // Return empty object if parsing fails
  }
}

/**
 * Convert JavaScript object to YAML
 */
export function objectToYaml(obj: any, indent: number = 0): string {
  if (!obj) return '';

  let yaml = '';
  const indentStr = ' '.repeat(indent);

  Object.entries(obj).forEach(([key, value]) => {
    if (typeof value === 'object' && value !== null) {
      yaml += `${indentStr}${key}:\n`;
      yaml += objectToYaml(value, indent + 2);
    } else {
      yaml += `${indentStr}${key}: ${value}\n`;
    }
  });

  return yaml;
}

/**
 * Extract content between markdown headers
 */
export function extractContentBetweenHeaders(
  markdown: string,
  startHeader: string,
  endHeader?: string,
): string {
  if (!markdown) return '';

  const lines = markdown.split('\n');
  let capturing = false;
  let result = [];

  const startRegex = new RegExp(`^#+\\s+${startHeader}`, 'i');
  const endRegex = endHeader ? new RegExp(`^#+\\s+${endHeader}`, 'i') : null;

  for (const line of lines) {
    if (!capturing) {
      if (startRegex.test(line)) {
        capturing = true;
        result.push(line);
      }
    } else {
      if (endRegex && endRegex.test(line)) {
        break;
      } else if (!endRegex && /^#+\s+/.test(line) && !startRegex.test(line)) {
        break;
      } else {
        result.push(line);
      }
    }
  }

  return result.join('\n');
}
