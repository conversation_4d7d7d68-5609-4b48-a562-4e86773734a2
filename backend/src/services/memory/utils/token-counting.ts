/**
 * Utilities for token counting and budget management
 */

/**
 * Estimate token count for a string
 * In production, use a proper tokenizer like tiktoken
 */
export function estimateTokenCount(text: string): number {
  if (!text) return 0;
  // Very crude estimation - in production use a proper tokenizer
  return Math.ceil(text.length / 4);
}

/**
 * Allocate a token budget across different context types
 */
export function allocateTokenBudget(
  totalBudget: number,
  contextTypes: string[],
  weights: Record<string, number> = {},
): Record<string, number> {
  const allocation: Record<string, number> = {};

  // Default equal weights if not provided
  if (Object.keys(weights).length === 0) {
    const equalWeight = 1 / contextTypes.length;
    contextTypes.forEach((type) => {
      weights[type] = equalWeight;
    });
  }

  // Normalize weights to ensure they sum to 1
  const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
  const normalizedWeights: Record<string, number> = {};

  Object.entries(weights).forEach(([type, weight]) => {
    normalizedWeights[type] = weight / totalWeight;
  });

  // Allocate tokens based on normalized weights
  contextTypes.forEach((type) => {
    if (normalizedWeights[type]) {
      allocation[type] = Math.floor(totalBudget * normalizedWeights[type]);
    } else {
      allocation[type] = 0;
    }
  });

  // Distribute any remaining tokens due to rounding
  const allocatedTokens = Object.values(allocation).reduce((sum, tokens) => sum + tokens, 0);
  const remainingTokens = totalBudget - allocatedTokens;

  if (remainingTokens > 0) {
    // Find the context type with the highest weight and add remaining tokens
    const highestWeightType = Object.entries(normalizedWeights).sort(([, a], [, b]) => b - a)[0][0];

    allocation[highestWeightType] += remainingTokens;
  }

  return allocation;
}

/**
 * Check if text can fit within token budget, truncate if needed
 */
export function fitTextToTokenBudget(text: string, budget: number): string {
  if (!text) return '';

  const tokenCount = estimateTokenCount(text);
  if (tokenCount <= budget) return text;

  // Simple truncation strategy - in production use a smarter approach
  const ratio = budget / tokenCount;
  const targetLength = Math.floor(text.length * ratio);

  return text.substring(0, targetLength - 3) + '...';
}

/**
 * Calculate token usage
 */
export function calculateTokenUsage(
  prompt: string,
  completion: string,
): {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
} {
  const promptTokens = estimateTokenCount(prompt);
  const completionTokens = estimateTokenCount(completion);

  return {
    promptTokens,
    completionTokens,
    totalTokens: promptTokens + completionTokens,
  };
}

/**
 * Prioritize content for token budget
 */
export function prioritizeContent(
  contents: Array<{ content: string; priority: number }>,
  budget: number,
): string[] {
  // Sort by priority (higher number = higher priority)
  const sortedContents = [...contents].sort((a, b) => b.priority - a.priority);

  const selectedContents: string[] = [];
  let remainingBudget = budget;

  for (const item of sortedContents) {
    const tokenCount = estimateTokenCount(item.content);

    if (tokenCount <= remainingBudget) {
      selectedContents.push(item.content);
      remainingBudget -= tokenCount;
    } else if (remainingBudget > 20) {
      // If we have at least 20 tokens left, try to fit a truncated version
      const truncated = fitTextToTokenBudget(item.content, remainingBudget);
      selectedContents.push(truncated);
      remainingBudget = 0;
    }

    if (remainingBudget <= 0) break;
  }

  return selectedContents;
}
