import { pipeline, Pipeline } from '@xenova/transformers';
import { logger } from '../common/logger';

/**
 * Xenova Transformers.js embedding service
 * Uses local sentence transformers model for generating embeddings
 */
export class XenovaEmbeddingService {
  private static instance: XenovaEmbeddingService;
  private pipeline: Pipeline | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  // Model configuration
  private readonly modelName = 'Xenova/all-MiniLM-L6-v2';
  private readonly maxLength = 512; // Model's max sequence length

  private constructor() {}

  public static getInstance(): XenovaEmbeddingService {
    if (!XenovaEmbeddingService.instance) {
      XenovaEmbeddingService.instance = new XenovaEmbeddingService();
    }
    return XenovaEmbeddingService.instance;
  }

  /**
   * Initialize the embedding pipeline
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._initialize();
    return this.initializationPromise;
  }

  private async _initialize(): Promise<void> {
    try {
      logger.info(`Initializing Xenova embedding service with model: ${this.modelName}`);

      // Create the feature extraction pipeline
      this.pipeline = await pipeline('feature-extraction', this.modelName, {
        quantized: true, // Use quantized model for better performance
      });

      this.isInitialized = true;
      logger.info('Xenova embedding service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Xenova embedding service:', error);
      throw new Error(`Failed to initialize embedding service: ${error}`);
    }
  }

  /**
   * Generate embeddings for a single text
   */
  public async embedText(text: string): Promise<number[]> {
    await this.initialize();

    if (!this.pipeline) {
      throw new Error('Embedding pipeline not initialized');
    }

    try {
      // Truncate text if it's too long
      const truncatedText = this.truncateText(text);

      logger.debug(`Generating embedding for text (${truncatedText.length} chars)`);

      // Generate embedding
      const output = await this.pipeline(truncatedText, {
        pooling: 'mean',
        normalize: true,
      });

      // Extract the embedding array
      const embedding = Array.from(output.data) as number[];

      logger.debug(`Generated embedding with dimension: ${embedding.length}`);
      return embedding;
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error}`);
    }
  }

  /**
   * Generate embeddings for multiple texts
   */
  public async embedTexts(texts: string[]): Promise<number[][]> {
    await this.initialize();

    if (!this.pipeline) {
      throw new Error('Embedding pipeline not initialized');
    }

    try {
      logger.info(`Generating embeddings for ${texts.length} texts`);

      const embeddings: number[][] = [];

      // Process texts in batches to avoid memory issues
      const batchSize = 10;
      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(texts.length / batchSize)}`);

        const batchEmbeddings = await Promise.all(
          batch.map(text => this.embedText(text))
        );

        embeddings.push(...batchEmbeddings);
      }

      logger.info(`Generated ${embeddings.length} embeddings successfully`);
      return embeddings;
    } catch (error) {
      logger.error('Error generating batch embeddings:', error);
      throw new Error(`Failed to generate batch embeddings: ${error}`);
    }
  }

  /**
   * Truncate text to fit model's max length
   */
  private truncateText(text: string): string {
    // Simple truncation - could be improved with proper tokenization
    if (text.length <= this.maxLength * 4) { // Rough estimate: 4 chars per token
      return text;
    }

    const truncated = text.substring(0, this.maxLength * 4);
    logger.debug(`Truncated text from ${text.length} to ${truncated.length} characters`);
    return truncated;
  }

  /**
   * Get embedding dimension
   */
  public getEmbeddingDimension(): number {
    // all-MiniLM-L6-v2 produces 384-dimensional embeddings
    return 384;
  }

  /**
   * Check if service is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.pipeline !== null;
  }

  /**
   * Get model information
   */
  public getModelInfo(): { name: string; dimension: number; maxLength: number } {
    return {
      name: this.modelName,
      dimension: this.getEmbeddingDimension(),
      maxLength: this.maxLength,
    };
  }
}

// Export singleton instance
export const xenovaEmbeddingService = XenovaEmbeddingService.getInstance();
