/**
 * File Storage Service
 *
 * This module provides a simple service for storing and retrieving markdown files and images
 * for the blog system.
 */
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';
import { finished } from 'stream/promises';
import { logger } from '../../common/logger';

interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

export class FileStorageService {
  private markdownDir: string;
  private imagesDir: string;

  /**
   * Initialize the file storage service with a base path.
   * If no path is provided, it uses './storage' relative to the current directory.
   *
   * @param basePath - Base directory for storing files
   */
  constructor(basePath?: string) {
    if (!basePath) {
      // Default to a storage directory in the project
      basePath = path.join(process.cwd(), 'storage');
    }

    // Create specific directories for content types
    this.markdownDir = path.join(basePath, 'markdown');
    this.imagesDir = path.join(basePath, 'images');

    // Ensure directories exist
    fs.mkdirSync(this.markdownDir, { recursive: true });
    fs.mkdirSync(this.imagesDir, { recursive: true });

    logger.info(`FileStorageService initialized with base path: ${basePath}`);
    logger.info(`Markdown directory: ${this.markdownDir}`);
    logger.info(`Images directory: ${this.imagesDir}`);
  }

  /**
   * Save markdown content to a file.
   *
   * @param content - The markdown content to save
   * @param slug - Slug used as the filename
   * @returns Relative path to the saved markdown file
   */
  async saveMarkdown(content: string, slug: string): Promise<string> {
    // Create a clean filename from the slug
    const filename = `${slug}.md`;
    const filePath = path.join(this.markdownDir, filename);

    try {
      // Write content to file
      await fs.promises.writeFile(filePath, content, 'utf-8');

      logger.info(`Saved markdown file: ${filePath}`);
      return `markdown/${filename}`;
    } catch (error) {
      logger.error(
        `Error saving markdown file: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Read markdown content from a file.
   *
   * @param filePath - Relative path to the markdown file
   * @returns The content of the markdown file or null if not found
   */
  async getMarkdown(filePath: string): Promise<string | null> {
    // Handle the path - might be with or without the "markdown/" prefix
    const filename = path.basename(filePath);
    const fullPath = path.join(this.markdownDir, filename);

    try {
      if (!fs.existsSync(fullPath)) {
        logger.warn(`Markdown file not found: ${fullPath}`);
        return null;
      }

      const content = await fs.promises.readFile(fullPath, 'utf-8');

      logger.info(`Read markdown file: ${fullPath}`);
      return content;
    } catch (error) {
      logger.error(
        `Error reading markdown file: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Save an uploaded image file.
   *
   * @param image - The uploaded image file
   * @returns Relative path to the saved image
   */
  async saveImage(image: UploadedFile): Promise<string> {
    try {
      // Keep the original filename but ensure it's safe
      const filename = path.basename(image.originalname);
      let filePath = path.join(this.imagesDir, filename);

      // Handle duplicate filenames by adding a number if needed
      let counter = 1;
      while (fs.existsSync(filePath)) {
        const name = path.parse(filename).name;
        const ext = path.parse(filename).ext;
        const newFilename = `${name}_${counter}${ext}`;
        filePath = path.join(this.imagesDir, newFilename);
        counter += 1;

        // Prevent infinite loops
        if (counter > 1000) {
          throw new Error('Too many filename collisions');
        }
      }

      // Save the uploaded file
      await fs.promises.writeFile(filePath, image.buffer);

      logger.info(`Saved image file: ${filePath}`);
      return `images/${path.basename(filePath)}`;
    } catch (error) {
      logger.error(
        `Error saving image file: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  /**
   * Get the full path to an image file.
   *
   * @param filename - Filename of the image
   * @returns Full path to the image file
   */
  getImagePath(filename: string): string {
    return path.join(this.imagesDir, filename);
  }

  /**
   * Delete a file.
   *
   * @param filePath - Relative path to the file
   * @returns True if successful, False otherwise
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      let fullPath: string;

      if (filePath.startsWith('markdown/')) {
        fullPath = path.join(this.markdownDir, filePath.replace('markdown/', ''));
      } else if (filePath.startsWith('images/')) {
        fullPath = path.join(this.imagesDir, filePath.replace('images/', ''));
      } else {
        // Try to determine the directory based on extension
        if (filePath.endsWith('.md')) {
          fullPath = path.join(this.markdownDir, path.basename(filePath));
        } else {
          fullPath = path.join(this.imagesDir, path.basename(filePath));
        }
      }

      if (!fs.existsSync(fullPath)) {
        logger.warn(`File not found for deletion: ${fullPath}`);
        return false;
      }

      await fs.promises.unlink(fullPath);
      logger.info(`Deleted file: ${fullPath}`);
      return true;
    } catch (error) {
      logger.error(
        `Error deleting file: ${error instanceof Error ? error.message : String(error)}`,
      );
      return false;
    }
  }
}
