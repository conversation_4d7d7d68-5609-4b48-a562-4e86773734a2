import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';

import { TextToSpeechClient, protos } from '@google-cloud/text-to-speech';
import FormData from 'form-data';
import { OpenAI } from 'openai';

import { config } from '../../config/config';

// Type definitions
interface VoiceConfig {
  languageCode: string;
  name: string;
  ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender;
}

interface VoiceConfigMap {
  [key: string]: VoiceConfig;
}

/**
 * Service class for audio processing.
 * Handles both text-to-speech (using Google Cloud TTS) and speech-to-text (using Azure OpenAI) functionality.
 */
export class AudioService {
  private azureClient: OpenAI;
  private googleTtsClient: TextToSpeechClient;
  private googleAudioConfig: protos.google.cloud.texttospeech.v1.IAudioConfig;
  private sttModel: string = 'whisper';
  private sttDeploymentName: string = 'whisper';

  // Voice configurations for Google TTS
  private readonly VOICE_CONFIGS: VoiceConfigMap = {
    // Standard voices with specific names
    'standard-a': {
      languageCode: 'en-US',
      name: 'en-US-Standard-A',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    'standard-b': {
      languageCode: 'en-US',
      name: 'en-US-Standard-B',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    'standard-c': {
      languageCode: 'en-US',
      name: 'en-US-Standard-C',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    'standard-d': {
      languageCode: 'en-US',
      name: 'en-US-Standard-D',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    'standard-e': {
      languageCode: 'en-US',
      name: 'en-US-Standard-E',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    'standard-f': {
      languageCode: 'en-US',
      name: 'en-US-Standard-F',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },

    // Neural2 voices (premium)
    'neural2-a': {
      languageCode: 'en-US',
      name: 'en-US-Neural2-A',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    'neural2-c': {
      languageCode: 'en-US',
      name: 'en-US-Neural2-C',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    'neural2-d': {
      languageCode: 'en-US',
      name: 'en-US-Neural2-D',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    'neural2-e': {
      languageCode: 'en-US',
      name: 'en-US-Neural2-E',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    'neural2-f': {
      languageCode: 'en-US',
      name: 'en-US-Neural2-F',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },

    // Generic gender-based voices (maps to specific voices)
    male: {
      languageCode: 'en-US',
      name: 'en-US-Standard-B',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    female: {
      languageCode: 'en-US',
      name: 'en-US-Standard-C',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },

    // Map Azure voice names to Google voices for compatibility
    alloy: {
      languageCode: 'en-US',
      name: 'en-US-Standard-A',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    echo: {
      languageCode: 'en-US',
      name: 'en-US-Standard-B',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    fable: {
      languageCode: 'en-US',
      name: 'en-US-Standard-C',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    onyx: {
      languageCode: 'en-US',
      name: 'en-US-Standard-D',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.MALE,
    },
    nova: {
      languageCode: 'en-US',
      name: 'en-US-Standard-E',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
    shimmer: {
      languageCode: 'en-US',
      name: 'en-US-Standard-F',
      ssmlGender: protos.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE,
    },
  };

  /**
   * Initialize the service with necessary credentials.
   */
  constructor() {
    // --- Azure STT Initialization ---
    const apiKey = config.ai.azureApiKey;
    const whisperEndpoint = config.ai.azureWhisperEndpoint;

    if (!apiKey || !whisperEndpoint) {
      throw new Error('Azure API key or Whisper endpoint not found in configuration');
    }

    // Initialize Azure OpenAI client for speech-to-text
    this.azureClient = new OpenAI({
      apiKey,
      baseURL: whisperEndpoint.endsWith('/') ? whisperEndpoint.slice(0, -1) : whisperEndpoint,
      defaultQuery: { 'api-version': '2024-06-01' },
      defaultHeaders: { 'api-key': apiKey },
    });

    // --- Google TTS Initialization ---
    try {
      let credentials;
      
      // Check if we have base64 encoded credentials (cleanest for .env files)
      if (config.ai.googleTtsCredentialsBase64) {
        const decodedJson = Buffer.from(config.ai.googleTtsCredentialsBase64, 'base64').toString('utf-8');
        credentials = JSON.parse(decodedJson);
        this.googleTtsClient = new TextToSpeechClient({
          credentials: credentials,
          projectId: credentials.project_id
        });
      }
      // Check if we have credentials as JSON string
      else if (config.ai.googleTtsCredentialsJson) {
        credentials = JSON.parse(config.ai.googleTtsCredentialsJson);
        this.googleTtsClient = new TextToSpeechClient({
          credentials: credentials,
          projectId: credentials.project_id
        });
      } else {
        // Fallback to file path (for local development)
        const googleCredentialsPath = config.ai.googleApplicationCredentials;
        
        // Only set the environment variable if the file exists
        if (fs.existsSync(googleCredentialsPath)) {
          process.env.GOOGLE_APPLICATION_CREDENTIALS = googleCredentialsPath;
          this.googleTtsClient = new TextToSpeechClient();
        } else {
          throw new Error('Google TTS credentials not found. Please set GOOGLE_TTS_CREDENTIALS_BASE64 environment variable.');
        }
      }

      // Default Google TTS audio configuration
      this.googleAudioConfig = {
        audioEncoding: protos.google.cloud.texttospeech.v1.AudioEncoding.MP3,
      };
    } catch (error) {
      console.error('Error initializing Google TTS client:', error);
      // Initialize with null - methods will handle this case
      this.googleTtsClient = null as any;
      this.googleAudioConfig = {
        audioEncoding: protos.google.cloud.texttospeech.v1.AudioEncoding.MP3,
      };
    }
  }

  /**
   * Transcribe speech from an audio stream to text using Azure.
   *
   * @param audioStream - The audio data as a readable stream
   * @param filename - Name to use for the file (helps with format validation)
   * @returns The transcribed text
   */
  async transcribeAudioStream(
    audioStream: NodeJS.ReadableStream,
    filename: string = 'audio.mp3',
  ): Promise<string> {
    try {
      // Validate the filename format
      if (!filename.endsWith('.mp3') && !filename.endsWith('.wav') && !filename.endsWith('.m4a')) {
        filename = 'audio.mp3'; // Default to mp3 if unknown format
      }

      // Create a temporary file with the audio data
      const tempFilePath = path.join(
        process.cwd(),
        'temp_audio_stream_' + Date.now() + '_' + filename,
      );

      try {
        // Create a write stream to the temporary file
        const writeStream = fs.createWriteStream(tempFilePath);

        // Pipe the input stream to the file
        await new Promise<void>((resolve, reject) => {
          audioStream.pipe(writeStream).on('finish', resolve).on('error', reject);
        });

        // Use the Azure OpenAI client with the file
        // For Azure OpenAI Whisper, we need to use the correct path
        const response = await this.azureClient.audio.transcriptions.create({
          file: fs.createReadStream(tempFilePath),
          model: this.sttModel,
        });

        // Clean up the temporary file
        fs.unlinkSync(tempFilePath);

        return response.text;
      } catch (innerError) {
        // Clean up the temporary file if it exists
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
        throw innerError;
      }
    } catch (error) {
      console.error('Transcription error:', error);
      throw new Error(`Azure Transcription failed: ${(error as Error).message}`);
    }
  }

  /**
   * Transcribe speech from audio bytes to text using Azure.
   *
   * @param audioBytes - Bytes containing audio data
   * @param filename - Name to use for the file (helps with format validation)
   * @returns The transcribed text
   */
  async transcribeAudioBytes(audioBytes: Buffer, filename: string = 'audio.mp3'): Promise<string> {
    try {
      // Validate the filename format
      if (!filename.endsWith('.mp3') && !filename.endsWith('.wav') && !filename.endsWith('.m4a')) {
        filename = 'audio.mp3'; // Default to mp3 if unknown format
      }

      // Create a temporary file with the audio data
      const tempFilePath = path.join(process.cwd(), 'temp_audio_' + Date.now() + '_' + filename);

      try {
        // Write the buffer to a temporary file
        fs.writeFileSync(tempFilePath, audioBytes);

        // Create a file object from the temporary file
        const fileStream = fs.createReadStream(tempFilePath);

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', fileStream, { filename });
        formData.append('model', this.sttModel);

        // We're already using the Azure client initialized in the constructor
        // No need to get the API key and endpoint again

        // Use the Azure OpenAI client with the FormData
        // For Azure OpenAI Whisper, we need to use the correct path
        const response = await this.azureClient.audio.transcriptions.create({
          file: fs.createReadStream(tempFilePath),
          model: this.sttModel,
        });

        // Clean up the temporary file
        fs.unlinkSync(tempFilePath);

        return response.text;
      } catch (innerError) {
        // Clean up the temporary file if it exists
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
        throw innerError;
      }
    } catch (error) {
      console.error('Transcription error:', error);
      throw new Error(`Azure Transcription failed: ${(error as Error).message}`);
    }
  }

  /**
   * Convert text to speech audio using Google Cloud TTS.
   *
   * @param text - The text to convert to speech
   * @param voice - The voice style to use
   * @returns Audio data in MP3 format
   */
  async textToSpeech(text: string, voice: string = 'alloy'): Promise<Buffer> {
    try {
      // Check if Google TTS client is available
      if (!this.googleTtsClient) {
        console.warn('Google TTS client not available. Returning dummy audio buffer for testing.');
        // Return a dummy MP3 header buffer for testing
        return Buffer.from([
          0xff, 0xfb, 0x50, 0xc4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
          0x00,
        ]);
      }

      const synthesisInput: protos.google.cloud.texttospeech.v1.ISynthesisInput = {
        text,
      };

      const voiceConfig =
        this.VOICE_CONFIGS[voice.toLowerCase()] || this.VOICE_CONFIGS['standard-a'];

      const voiceParams: protos.google.cloud.texttospeech.v1.IVoiceSelectionParams = {
        languageCode: voiceConfig.languageCode,
        name: voiceConfig.name,
        ssmlGender: voiceConfig.ssmlGender,
      };

      const [response] = await this.googleTtsClient.synthesizeSpeech({
        input: synthesisInput,
        voice: voiceParams,
        audioConfig: this.googleAudioConfig,
      });

      return Buffer.from(response.audioContent as Uint8Array);
    } catch (error) {
      console.error('Google Text-to-speech failed:', error);
      // Return a dummy MP3 header buffer for testing when an error occurs
      return Buffer.from([
        0xff, 0xfb, 0x50, 0xc4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00,
      ]);
    }
  }

  /**
   * Get a list of available voices from Google TTS.
   *
   * @returns Dictionary of voice names and their configurations
   */
  getAvailableVoices(): VoiceConfigMap {
    return this.VOICE_CONFIGS;
  }
}

export default AudioService;
