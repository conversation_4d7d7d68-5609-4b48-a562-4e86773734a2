import crypto from 'crypto';

import axios from 'axios';

/**
 * Service to interact with the Clerk API
 */
export class ClerkService {
  private readonly apiKey: string;
  private readonly webhookSecret: string | undefined;
  private readonly baseUrl: string = 'https://api.clerk.com/v1';

  constructor() {
    if (!process.env.CLERK_SECRET_KEY) {
      throw new Error('CLERK_SECRET_KEY environment variable is not set');
    }
    this.apiKey = process.env.CLERK_SECRET_KEY;
    this.webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
  }

  /**
   * Get a user by their Clerk ID
   * @param userId The Clerk user ID
   * @returns The user data
   */
  async getUserById(userId: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/users/${userId}`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user from Clerk:', error);
      throw error;
    }
  }

  /**
   * Get a user by their email address
   * @param email The user's email
   * @returns The user data
   */
  async getUserByEmail(email: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/users`, {
        params: {
          email_address: email,
        },
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data.data[0] || null;
    } catch (error) {
      console.error('Error fetching user by email from Clerk:', error);
      throw error;
    }
  }

  /**
   * Update user metadata
   * @param userId The Clerk user ID
   * @param metadata The metadata to update
   * @returns The updated user data
   */
  async updateUserMetadata(userId: string, metadata: Record<string, any>) {
    try {
      const response = await axios.patch(
        `${this.baseUrl}/users/${userId}/metadata`,
        { publicMetadata: metadata },
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error updating user metadata in Clerk:', error);
      throw error;
    }
  }

  /**
   * Create a new organization
   * @param name The organization name
   * @param userId The user ID who will be the organization admin
   * @returns The created organization data
   */
  async createOrganization(name: string, userId: string) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/organizations`,
        {
          name,
          created_by: userId,
        },
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error creating organization in Clerk:', error);
      throw error;
    }
  }

  /**
   * Get all organizations for a user
   * @param userId The Clerk user ID
   * @returns List of organizations the user belongs to
   */
  async getUserOrganizations(userId: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/users/${userId}/organization_memberships`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching user organizations from Clerk:', error);
      throw error;
    }
  }

  /**
   * Verify a webhook signature from Clerk
   * This ensures the webhook request actually came from Clerk
   * @param svixId The SVIX ID from request headers
   * @param svixTimestamp The SVIX timestamp from request headers
   * @param svixSignature The SVIX signature from request headers
   * @param body The raw request body as a string
   * @returns True if the signature is valid, false otherwise
   */
  verifyWebhookSignature(
    svixId: string,
    svixTimestamp: string,
    svixSignature: string,
    body: string,
  ): boolean {
    if (!this.webhookSecret) {
      console.warn('CLERK_WEBHOOK_SECRET not set, skipping webhook signature verification');
      return true;
    }

    if (!svixId || !svixTimestamp || !svixSignature) {
      console.error('Missing Svix headers for webhook verification');
      return false;
    }

    try {
      // Create the message to sign
      const message = `${svixId}.${svixTimestamp}.${body}`;

      // Compute the expected signature
      const hmac = crypto.createHmac('sha256', this.webhookSecret);
      hmac.update(message);
      const expectedSignature = hmac.digest('hex');

      // Compare with the provided signature
      // In a real-world scenario, you would need to handle multiple signatures
      // and timestamp validation
      return crypto.timingSafeEqual(Buffer.from(expectedSignature), Buffer.from(svixSignature));
    } catch (error) {
      console.error('Error verifying webhook signature:', error);
      return false;
    }
  }
}

// Export a singleton instance
export const clerkService = new ClerkService();
