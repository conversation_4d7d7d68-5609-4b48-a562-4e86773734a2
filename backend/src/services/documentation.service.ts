import { spawn } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import glob from 'glob';

// Priority levels for documentation generation
export enum DocumentationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Interface for selective documentation generation
export interface SelectiveDocumentationOptions {
  projectPath: string;
  selectedFiles?: string[];
  directoryPath?: string;
  priority?: DocumentationPriority;
  includeSubdirectories?: boolean;
}

// File importance scoring based on patterns
const FILE_IMPORTANCE_PATTERNS = {
  critical: [
    /main\.(ts|js|py)$/i,
    /index\.(ts|js|py)$/i,
    /app\.(ts|js|py)$/i,
    /server\.(ts|js|py)$/i,
    /config\.(ts|js|py)$/i
  ],
  high: [
    /service\.(ts|js|py)$/i,
    /controller\.(ts|js|py)$/i,
    /router?\.(ts|js|py)$/i,
    /middleware\.(ts|js|py)$/i,
    /api\.(ts|js|py)$/i,
    /core\.(ts|js|py)$/i
  ],
  normal: [
    /component\.(ts|js|py)$/i,
    /util(s)?\.(ts|js|py)$/i,
    /helper(s)?\.(ts|js|py)$/i,
    /model\.(ts|js|py)$/i
  ],
  low: [
    /test\.(ts|js|py)$/i,
    /spec\.(ts|js|py)$/i,
    /\.test\.(ts|js|py)$/i,
    /\.spec\.(ts|js|py)$/i
  ]
};

/**
 * Determine file importance based on filename patterns and directory structure
 */
export function getFileImportance(filePath: string): DocumentationPriority {
  const fileName = path.basename(filePath);
  const dirName = path.dirname(filePath);

  // Check for critical files
  if (FILE_IMPORTANCE_PATTERNS.critical.some(pattern => pattern.test(fileName))) {
    return DocumentationPriority.CRITICAL;
  }

  // Check for high importance files
  if (FILE_IMPORTANCE_PATTERNS.high.some(pattern => pattern.test(fileName))) {
    return DocumentationPriority.HIGH;
  }

  // Check if it's in a core directory
  if (dirName.includes('/src/') || dirName.includes('/lib/') || dirName.includes('/core/')) {
    return DocumentationPriority.HIGH;
  }

  // Check for normal importance files
  if (FILE_IMPORTANCE_PATTERNS.normal.some(pattern => pattern.test(fileName))) {
    return DocumentationPriority.NORMAL;
  }

  // Check for low importance files
  if (FILE_IMPORTANCE_PATTERNS.low.some(pattern => pattern.test(fileName))) {
    return DocumentationPriority.LOW;
  }

  return DocumentationPriority.NORMAL;
}

/**
 * Get files from a directory with optional subdirectory inclusion
 */
export function getFilesFromDirectory(directoryPath: string, includeSubdirectories: boolean = true): string[] {
  const pattern = includeSubdirectories
    ? path.join(directoryPath, '**', '*.{ts,js,py}')
    : path.join(directoryPath, '*.{ts,js,py}');

  return glob.sync(pattern, {
    ignore: ['**/node_modules/**', '**/venv/**', '**/dist/**', '**/build/**']
  });
}

/**
 * Generate documentation for selected files with priority-based processing
 */
export async function generateDocumentationSelective(
  options: SelectiveDocumentationOptions
): Promise<void> {
  const {
    projectPath,
    selectedFiles,
    directoryPath,
    priority = DocumentationPriority.NORMAL,
    includeSubdirectories = true
  } = options;

  console.log('Starting selective documentation generation with options:', options);

  let filesToProcess: string[] = [];

  // Collect files based on input
  if (selectedFiles && selectedFiles.length > 0) {
    filesToProcess = selectedFiles.filter(file => fs.existsSync(file));
    console.log(`Processing ${filesToProcess.length} selected files`);
  } else if (directoryPath) {
    filesToProcess = getFilesFromDirectory(directoryPath, includeSubdirectories);
    console.log(`Found ${filesToProcess.length} files in directory: ${directoryPath}`);
  }

  if (filesToProcess.length === 0) {
    console.log('No files to process');
    return;
  }

  // Sort files by importance and priority
  const filesByPriority = filesToProcess.map(file => ({
    path: file,
    importance: getFileImportance(file),
    requestedPriority: priority
  })).sort((a, b) => {
    // Sort by importance first, then by requested priority
    const importanceOrder = { critical: 4, high: 3, normal: 2, low: 1 };
    const importanceDiff = importanceOrder[b.importance] - importanceOrder[a.importance];

    if (importanceDiff !== 0) return importanceDiff;

    return importanceOrder[b.requestedPriority] - importanceOrder[a.requestedPriority];
  });

  console.log('Files prioritized for documentation:');
  filesByPriority.forEach((file, index) => {
    console.log(`  ${index + 1}. [${file.importance.toUpperCase()}] ${file.path}`);
  });

  // Process files in priority order
  for (const fileInfo of filesByPriority) {
    try {
      console.log(`Generating documentation for: ${fileInfo.path} (${fileInfo.importance})`);
      await generateDocumentation(projectPath, fileInfo.path);
    } catch (error) {
      console.error(`Failed to generate documentation for ${fileInfo.path}:`, error);
      // Continue with other files even if one fails
    }
  }

  console.log('Selective documentation generation completed');
}

export async function generateDocumentation(
  projectPath: string,
  filePath?: string
): Promise<void> {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(__dirname, '..', '..', 'scripts', 'generate_docs.py');
    const args: string[] = [];

    if (filePath) {
      args.push('--file', filePath);
    } else {
      args.push('--dir', projectPath);
    }

    console.log(`Running documentation generation script for: ${filePath || projectPath}`);
    console.log(`Executing: python3 ${scriptPath} ${args.join(' ')}`);

    const scriptsDir = path.dirname(scriptPath);

    const pythonProcess = spawn('poetry', ['run', 'python3', scriptPath, ...args], { cwd: scriptsDir });

    pythonProcess.stdout.on('data', (data) => {
      console.log(`[DocGen Script]: ${data}`);
    });

    pythonProcess.stderr.on('data', (data) => {
      console.error(`[DocGen Script Error]: ${data}`);
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Documentation generation script finished successfully.');
        resolve();
      } else {
        console.error(`Documentation generation script exited with code ${code}`);
        reject(new Error(`Documentation generation failed with exit code ${code}`));
      }
    });

    pythonProcess.on('error', (err) => {
        console.error('Failed to start documentation generation script:', err);
        reject(err);
    });
  });
}
