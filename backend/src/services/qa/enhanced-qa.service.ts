/**
 * Enhanced QA Service
 *
 * This service handles question and answer functionality, including karma management.
 * It uses the repository pattern for better separation of concerns.
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import { logger } from '../../common/logger';
import { QuestionRepository, AnswerRepository, TagRepository } from '../../db/repositories/qa';
import { UserRepository } from '../../db/repositories';
import { KarmaRepository } from '../../db/repositories/gamification/karma.repository';
import {
  QuestionResponse,
  AnswerResponse,
  TagResponse,
} from '../../common/types/qa/qa.types';

interface Vote {
  user_id: number;
  vote_value: number;
}

interface AnswerWithVotes {
  id: number;
  content: string;
  question_id: number;
  author_id: number;
  code_snippet?: string;
  is_accepted: boolean;
  timestamp: Date;
  users: {
    id: number;
    username: string | null;
    email: string;
    karma: number;
    profile_image_url: string | null;
  };
  answer_votes: Vote[];
}

interface QuestionWithVotes {
  id: number;
  content: string;
  author_id: number;
  code_snippet?: string;
  is_resolved: boolean;
  timestamp: Date;
  users: {
    id: number;
    username: string | null;
    email: string;
    karma: number;
    profile_image_url: string | null;
  };
  answers: AnswerWithVotes[];
  question_tags: Array<{
    tags: {
      id: number;
      name: string;
      description: string | null;
    };
  }>;
  question_votes: Vote[];
  _count?: {
    answers: number;
    question_votes: number;
  };
}

/**
 * Enhanced QA Service using repositories
 */
@injectable()
export class EnhancedQAService {
  constructor(
    @inject(TYPES.QuestionRepository) private questionRepository: QuestionRepository,
    @inject(TYPES.AnswerRepository) private answerRepository: AnswerRepository,
    @inject(TYPES.TagRepository) private tagRepository: TagRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
    @inject(TYPES.KarmaRepository) private karmaRepository: KarmaRepository,
  ) {}

  /**
   * Get questions with optional filtering
   */
  async getQuestions(
    options: {
      skip?: number;
      take?: number;
      resolved?: boolean;
      authorId?: number;
      tagIds?: number[];
    } = {},
  ): Promise<QuestionResponse[]> {
    try {
      const questions = await this.questionRepository.findAll(options);
      return questions.map((question) => ({
        id: question.id,
        content: question.content,
        authorId: question.author_id,
        author: {
          id: question.users.id,
          username: question.users.username || 'Anonymous',
          avatarUrl: question.users.profile_image_url || undefined,
        },
        tags: question.question_tags.map((qt) => ({
          id: qt.tags.id,
          name: qt.tags.name,
          description: qt.tags.description || undefined,
        })),
        answerCount: question._count.answers,
        voteCount: question._count.question_votes,
        timestamp: question.timestamp,
        isResolved: question.is_resolved,
        codeSnippet: question.code_snippet || undefined,
      }));
    } catch (error) {
      logger.error('Error getting questions:', error);
      throw error;
    }
  }

  /**
   * Get a question by ID
   */
  async getQuestionById(id: number, userId?: number): Promise<QuestionResponse | null> {
    try {
      const question = (await this.questionRepository.findById(id)) as QuestionWithVotes;
      if (!question) return null;

      const userVote = userId
        ? question.question_votes.find((v) => v.user_id === userId)
        : undefined;

      return {
        id: question.id,
        content: question.content,
        authorId: question.author_id,
        author: {
          id: question.users.id,
          username: question.users.username || 'Anonymous',
          avatarUrl: question.users.profile_image_url || undefined,
        },
        tags: question.question_tags.map((qt) => ({
          id: qt.tags.id,
          name: qt.tags.name,
          description: qt.tags.description || undefined,
        })),
        answers: question.answers.map((answer) => ({
          id: answer.id,
          content: answer.content,
          questionId: answer.question_id,
          authorId: answer.author_id,
          author: {
            id: answer.users.id,
            username: answer.users.username || 'Anonymous',
            avatarUrl: answer.users.profile_image_url || undefined,
          },
          voteCount: answer.answer_votes.reduce(
            (sum: number, vote: Vote) => sum + vote.vote_value,
            0,
          ),
          userVote: userId
            ? answer.answer_votes.find((v: Vote) => v.user_id === userId)?.vote_value
            : undefined,
          isAccepted: answer.is_accepted,
          timestamp: answer.timestamp,
          codeSnippet: answer.code_snippet || undefined,
        })),
        answerCount: question.answers.length,
        voteCount: question.question_votes.reduce(
          (sum: number, vote: Vote) => sum + vote.vote_value,
          0,
        ),
        userVote: userVote?.vote_value,
        timestamp: question.timestamp,
        isResolved: question.is_resolved,
        codeSnippet: question.code_snippet || undefined,
      };
    } catch (error) {
      logger.error(`Error getting question with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new question
   */
  async createQuestion(data: {
    content: string;
    authorId: number;
    codeSnippet?: string;
    tags?: string[];
  }): Promise<QuestionResponse | { statusCode: number; detail: string }> {
    try {
      // Check if user has enough karma (1 point required)
      const hasKarma = await this.hasEnoughKarma(data.authorId, 1);
      if (!hasKarma) {
        return {
          statusCode: 402,
          detail: 'Insufficient karma to ask a question',
        };
      }

      // Process tags if provided
      let tagIds: number[] | undefined;
      if (data.tags && data.tags.length > 0) {
        tagIds = await this.tagRepository.findOrCreateByNames(data.tags);
      }

      // Deduct karma
      await this.adjustKarma(data.authorId, -1, 'ASK_QUESTION');

      // Create question
      const question = await this.questionRepository.create({
        title: data.content.substring(0, 100), // Use first 100 chars of content as title
        content: data.content,
        authorId: data.authorId,
        codeSnippet: data.codeSnippet,
        tagIds,
      });

      return {
        id: question.id,
        content: question.content,
        authorId: question.author_id,
        codeSnippet: question.code_snippet || undefined,
        isResolved: question.is_resolved,
        timestamp: question.timestamp,
        author: {
          id: question.users.id,
          username: question.users.username || '',
          email: question.users.email,
          karma: question.users.karma,
          avatarUrl: question.users.profile_image_url || undefined,
        },
        tags: question.question_tags.map((tag) => ({
          id: tag.tags.id,
          name: tag.tags.name,
          description: tag.tags.description || undefined,
        })),
        answerCount: 0,
        voteCount: 0,
      };
    } catch (error) {
      logger.error('Error creating question:', error);
      return {
        statusCode: 500,
        detail: `Error creating question: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Update a question
   */
  async updateQuestion(
    id: number,
    userId: number,
    data: {
      content?: string;
      codeSnippet?: string;
      isResolved?: boolean;
      tags?: string[];
    },
  ): Promise<QuestionResponse | { statusCode: number; detail: string }> {
    try {
      const question = (await this.questionRepository.findById(id)) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      // Check if user is authorized to update this question
      if (question.author_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to update this question',
        };
      }

      // Process tags if provided
      let tagIds: number[] | undefined;
      if (data.tags && data.tags.length > 0) {
        tagIds = await this.tagRepository.findOrCreateByNames(data.tags);
      }

      // Update question
      const updatedQuestion = await this.questionRepository.update(id, {
        content: data.content,
        codeSnippet: data.codeSnippet,
        isResolved: data.isResolved,
        tagIds,
      });

      if (!updatedQuestion) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      const questionWithVotes = (await this.questionRepository.findById(id)) as QuestionWithVotes;

      return {
        id: questionWithVotes.id,
        content: questionWithVotes.content,
        authorId: questionWithVotes.author_id,
        codeSnippet: questionWithVotes.code_snippet || undefined,
        isResolved: questionWithVotes.is_resolved,
        timestamp: questionWithVotes.timestamp,
        author: {
          id: questionWithVotes.users.id,
          username: questionWithVotes.users.username || '',
          email: questionWithVotes.users.email,
          karma: questionWithVotes.users.karma,
          avatarUrl: questionWithVotes.users.profile_image_url || undefined,
        },
        tags: questionWithVotes.question_tags.map((tag) => ({
          id: tag.tags.id,
          name: tag.tags.name,
          description: tag.tags.description || undefined,
        })),
        answers: questionWithVotes.answers.map((answer) => ({
          id: answer.id,
          content: answer.content,
          questionId: answer.question_id,
          authorId: answer.author_id,
          codeSnippet: answer.code_snippet || undefined,
          isAccepted: answer.is_accepted,
          timestamp: answer.timestamp,
          author: {
            id: answer.users.id,
            username: answer.users.username || '',
            email: answer.users.email,
            karma: answer.users.karma,
            avatarUrl: answer.users.profile_image_url || undefined,
          },
          voteCount:
            answer.answer_votes?.reduce((sum: number, vote: Vote) => sum + vote.vote_value, 0) || 0,
          userVote: userId
            ? answer.answer_votes?.find((v: Vote) => v.user_id === userId)?.vote_value
            : undefined,
        })),
        voteCount:
          questionWithVotes.question_votes?.reduce(
            (sum: number, vote: Vote) => sum + vote.vote_value,
            0,
          ) || 0,
        userVote: userId
          ? questionWithVotes.question_votes?.find((v: Vote) => v.user_id === userId)?.vote_value
          : undefined,
      };
    } catch (error) {
      logger.error(`Error updating question ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error updating question: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Delete a question
   */
  async deleteQuestion(
    id: number,
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const question = (await this.questionRepository.findById(id)) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      // Check if user is authorized to delete this question
      if (question.author_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to delete this question',
        };
      }

      await this.questionRepository.delete(id);
      return true;
    } catch (error) {
      logger.error(`Error deleting question ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error deleting question: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Vote on a question
   */
  async voteQuestion(
    questionId: number,
    userId: number,
    value: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const question = (await this.questionRepository.findById(questionId)) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      // Check if user is voting on their own question
      if (question.author_id === userId) {
        return {
          statusCode: 400,
          detail: 'Cannot vote on your own question',
        };
      }

      // Record the vote
      await this.questionRepository.vote(questionId, userId, value);

      // Adjust karma for question author
      await this.adjustKarma(
        question.author_id,
        value,
        value > 0 ? 'QUESTION_UPVOTE' : 'QUESTION_DOWNVOTE',
      );

      return true;
    } catch (error) {
      logger.error(`Error voting on question ${questionId}:`, error);
      return {
        statusCode: 500,
        detail: `Error voting on question: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Check if a user has enough karma
   */
  async hasEnoughKarma(userId: number, amount: number): Promise<boolean> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) return false;
      return user.karma >= amount;
    } catch (error) {
      logger.error(`Error checking karma for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Get answers for a question
   */
  async getAnswersByQuestionId(
    questionId: number,
    options: {
      skip?: number;
      take?: number;
    } = {},
    userId?: number,
  ): Promise<AnswerResponse[] | { statusCode: number; detail: string }> {
    try {
      const question = (await this.questionRepository.findById(questionId)) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      const answers = (await this.answerRepository.findByQuestionId(
        questionId,
        options,
      )) as AnswerWithVotes[];

      return answers.map((answer) => ({
        id: answer.id,
        content: answer.content,
        questionId: answer.question_id,
        authorId: answer.author_id,
        codeSnippet: answer.code_snippet || undefined,
        isAccepted: answer.is_accepted,
        timestamp: answer.timestamp,
        author: {
          id: answer.users.id,
          username: answer.users.username || '',
          email: answer.users.email,
          karma: answer.users.karma,
          avatarUrl: answer.users.profile_image_url || undefined,
        },
        voteCount: answer.answer_votes.reduce(
          (sum: number, vote: Vote) => sum + vote.vote_value,
          0,
        ),
        userVote: userId
          ? answer.answer_votes.find((v: Vote) => v.user_id === userId)?.vote_value
          : undefined,
      }));
    } catch (error) {
      logger.error(`Error getting answers for question ${questionId}:`, error);
      return {
        statusCode: 500,
        detail: `Error getting answers: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Create a new answer
   */
  async createAnswer(data: {
    content: string;
    questionId: number;
    authorId: number;
    codeSnippet?: string;
  }): Promise<AnswerResponse | { statusCode: number; detail: string }> {
    try {
      // Check if question exists
      const question = (await this.questionRepository.findById(
        data.questionId,
      )) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      // Check if user has enough karma (1 point required)
      const hasKarma = await this.hasEnoughKarma(data.authorId, 1);
      if (!hasKarma) {
        return {
          statusCode: 402,
          detail: 'Insufficient karma to post an answer',
        };
      }

      // Deduct karma
      await this.adjustKarma(data.authorId, -1, 'POST_ANSWER');

      // Create answer
      const answer = (await this.answerRepository.create({
        content: data.content,
        questionId: data.questionId,
        authorId: data.authorId,
        codeSnippet: data.codeSnippet,
      })) as AnswerWithVotes;

      return {
        id: answer.id,
        content: answer.content,
        questionId: answer.question_id,
        authorId: answer.author_id,
        codeSnippet: answer.code_snippet || undefined,
        isAccepted: answer.is_accepted,
        timestamp: answer.timestamp,
        author: {
          id: answer.users.id,
          username: answer.users.username || '',
          email: answer.users.email,
          karma: answer.users.karma,
          avatarUrl: answer.users.profile_image_url || undefined,
        },
        voteCount: 0,
      };
    } catch (error) {
      logger.error('Error creating answer:', error);
      return {
        statusCode: 500,
        detail: `Error creating answer: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Update an answer
   */
  async updateAnswer(
    id: number,
    userId: number,
    data: {
      content?: string;
      codeSnippet?: string;
    },
  ): Promise<AnswerResponse | { statusCode: number; detail: string }> {
    try {
      const answer = (await this.answerRepository.findById(id)) as AnswerWithVotes;
      if (!answer) {
        return {
          statusCode: 404,
          detail: 'Answer not found',
        };
      }

      // Check if user is authorized to update this answer
      if (answer.author_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to update this answer',
        };
      }

      // Update answer
      const updatedAnswer = (await this.answerRepository.update(id, {
        content: data.content,
        codeSnippet: data.codeSnippet,
      })) as AnswerWithVotes;

      return {
        id: updatedAnswer.id,
        content: updatedAnswer.content,
        questionId: updatedAnswer.question_id,
        authorId: updatedAnswer.author_id,
        codeSnippet: updatedAnswer.code_snippet || undefined,
        isAccepted: updatedAnswer.is_accepted,
        timestamp: updatedAnswer.timestamp,
        author: {
          id: updatedAnswer.users.id,
          username: updatedAnswer.users.username || '',
          email: updatedAnswer.users.email,
          karma: updatedAnswer.users.karma,
          avatarUrl: updatedAnswer.users.profile_image_url || undefined,
        },
        voteCount:
          updatedAnswer.answer_votes?.reduce(
            (sum: number, vote: Vote) => sum + vote.vote_value,
            0,
          ) || 0,
        userVote: userId
          ? updatedAnswer.answer_votes?.find((v: Vote) => v.user_id === userId)?.vote_value
          : undefined,
      };
    } catch (error) {
      logger.error(`Error updating answer ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error updating answer: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Delete an answer
   */
  async deleteAnswer(
    id: number,
    userId: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const answer = (await this.answerRepository.findById(id)) as AnswerWithVotes;
      if (!answer) {
        return {
          statusCode: 404,
          detail: 'Answer not found',
        };
      }

      // Check if user is authorized to delete this answer
      if (answer.author_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Not authorized to delete this answer',
        };
      }

      await this.answerRepository.delete(id);
      return true;
    } catch (error) {
      logger.error(`Error deleting answer ${id}:`, error);
      return {
        statusCode: 500,
        detail: `Error deleting answer: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Accept an answer
   */
  async acceptAnswer(
    answerId: number,
    userId: number,
  ): Promise<AnswerResponse | { statusCode: number; detail: string }> {
    try {
      const answer = (await this.answerRepository.findById(answerId)) as AnswerWithVotes;
      if (!answer) {
        return {
          statusCode: 404,
          detail: 'Answer not found',
        };
      }

      const question = (await this.questionRepository.findById(
        answer.question_id,
      )) as QuestionWithVotes;
      if (!question) {
        return {
          statusCode: 404,
          detail: 'Question not found',
        };
      }

      // Check if user is authorized to accept this answer (must be question author)
      if (question.author_id !== userId) {
        return {
          statusCode: 403,
          detail: 'Only the question author can accept an answer',
        };
      }

      // Accept the answer
      const updatedAnswer = (await this.answerRepository.accept(answerId)) as AnswerWithVotes;

      // Award karma to answer author
      await this.adjustKarma(updatedAnswer.author_id, 15, 'ANSWER_ACCEPTED', updatedAnswer.id);

      return {
        id: updatedAnswer.id,
        content: updatedAnswer.content,
        questionId: updatedAnswer.question_id,
        authorId: updatedAnswer.author_id,
        codeSnippet: updatedAnswer.code_snippet || undefined,
        isAccepted: updatedAnswer.is_accepted,
        timestamp: updatedAnswer.timestamp,
        author: {
          id: updatedAnswer.users.id,
          username: updatedAnswer.users.username || '',
          email: updatedAnswer.users.email,
          karma: updatedAnswer.users.karma,
          avatarUrl: updatedAnswer.users.profile_image_url || undefined,
        },
        voteCount:
          updatedAnswer.answer_votes?.reduce(
            (sum: number, vote: Vote) => sum + vote.vote_value,
            0,
          ) || 0,
        userVote: userId
          ? updatedAnswer.answer_votes?.find((v: Vote) => v.user_id === userId)?.vote_value
          : undefined,
      };
    } catch (error) {
      logger.error(`Error accepting answer ${answerId}:`, error);
      return {
        statusCode: 500,
        detail: `Error accepting answer: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Vote on an answer
   */
  async voteAnswer(
    answerId: number,
    userId: number,
    value: number,
  ): Promise<boolean | { statusCode: number; detail: string }> {
    try {
      const answer = (await this.answerRepository.findById(answerId)) as AnswerWithVotes;
      if (!answer) {
        return {
          statusCode: 404,
          detail: 'Answer not found',
        };
      }

      // Check if user is voting on their own answer
      if (answer.author_id === userId) {
        return {
          statusCode: 400,
          detail: 'Cannot vote on your own answer',
        };
      }

      // Record the vote
      await this.answerRepository.vote(answerId, userId, value);

      // Adjust karma for answer author
      await this.adjustKarma(
        answer.author_id,
        value,
        value > 0 ? 'ANSWER_UPVOTE' : 'ANSWER_DOWNVOTE',
        answerId,
      );

      return true;
    } catch (error) {
      logger.error(`Error voting on answer ${answerId}:`, error);
      return {
        statusCode: 500,
        detail: `Error voting on answer: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Get tags
   */
  async getTags(
    options: {
      skip?: number;
      take?: number;
    } = {},
  ): Promise<TagResponse[]> {
    try {
      const tags = await this.tagRepository.findAll(options);
      return tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        description: tag.description || undefined,
      }));
    } catch (error) {
      logger.error('Error getting tags:', error);
      throw error;
    }
  }

  /**
   * Get popular tags
   */
  async getPopularTags(limit: number = 10): Promise<TagResponse[]> {
    try {
      const tags = await this.tagRepository.getPopularTags(limit);
      return tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        description: tag.description || undefined,
        questionCount: tag._count.question_tags,
      }));
    } catch (error) {
      logger.error('Error getting popular tags:', error);
      throw error;
    }
  }

  /**
   * Adjust a user's karma
   */
  async adjustKarma(
    userId: number,
    amount: number,
    reason: string,
    relatedEntityId?: number,
  ): Promise<boolean> {
    try {
      // Get current user
      const user = await this.userRepository.findById(userId);
      if (!user) return false;

      // Record karma transaction
      await this.karmaRepository.recordKarmaTransaction({
        userId,
        amount,
        reason,
        relatedEntityId,
      });

      return true;
    } catch (error) {
      logger.error(`Error adjusting karma for user ${userId}:`, error);
      return false;
    }
  }
}
