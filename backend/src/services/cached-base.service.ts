/**
 * Enhanced base service class with caching support.
 *
 * This module extends the base service to provide Redis caching capabilities
 * for improved performance.
 */
import { logger } from '../common/logger';
import { PrismaClient } from '@prisma/client';
import { getRedisService, CacheKeys, CacheTTL } from './redis.service';
import { BaseService } from './base.service';

export class CachedBaseService extends BaseService {
  protected redis = getRedisService();

  /**
   * Initialize the service with a Prisma client.
   *
   * @param prismaClient - The Prisma client for database access
   */
  constructor(prismaClient: PrismaClient) {
    super(prismaClient);
  }

  /**
   * Execute a cached database query.
   * First checks Redis cache, then falls back to database if not found.
   *
   * @param cacheKey - The cache key
   * @param queryFn - The database query function
   * @param ttl - Cache TTL in seconds
   * @returns The query result
   */
  protected async cachedQuery<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    ttl: number = CacheTTL.MEDIUM
  ): Promise<T> {
    return await this.redis.cached(cacheKey, queryFn, ttl);
  }

  /**
   * Invalidate cache entries matching a pattern.
   *
   * @param pattern - The cache key pattern to invalidate
   */
  protected async invalidateCache(pattern: string): Promise<void> {
    await this.redis.invalidateCache(pattern);
  }

  /**
   * Execute a database operation and invalidate related cache.
   *
   * @param operation - The database operation
   * @param cachePatterns - Cache patterns to invalidate after operation
   * @returns The operation result
   */
  protected async executeWithCacheInvalidation<T>(
    operation: () => Promise<T>,
    cachePatterns: string[]
  ): Promise<T> {
    const result = await operation();
    
    // Invalidate all related cache patterns
    await Promise.all(
      cachePatterns.map(pattern => this.invalidateCache(pattern))
    );
    
    return result;
  }

  /**
   * Get user with caching.
   */
  protected async getCachedUser(userId: number) {
    const cacheKey = CacheKeys.user(userId);
    
    return this.cachedQuery(
      cacheKey,
      () => this.prisma.users.findUnique({
        where: { id: userId },
        include: {
          subscriptions: {
            where: { status: 'active' }
          }
        }
      }),
      CacheTTL.LONG
    );
  }

  /**
   * Get conversation with caching.
   */
  protected async getCachedConversation(conversationId: number) {
    const cacheKey = CacheKeys.conversation(conversationId);
    
    return this.cachedQuery(
      cacheKey,
      () => this.prisma.conversations.findUnique({
        where: { id: conversationId },
        include: {
          users: true,
          projects: true,
          ai_agents: true
        }
      }),
      CacheTTL.MEDIUM
    );
  }

  /**
   * Get conversation messages with caching.
   */
  protected async getCachedConversationMessages(
    conversationId: number,
    limit: number = 50
  ) {
    const cacheKey = CacheKeys.conversationMessages(conversationId);
    
    return this.cachedQuery(
      cacheKey,
      () => this.prisma.messages.findMany({
        where: { conversation_id: conversationId },
        orderBy: { created_at: 'desc' },
        take: limit
      }),
      CacheTTL.SHORT
    );
  }

  /**
   * Update conversation and invalidate cache.
   */
  protected async updateConversationWithCache(
    conversationId: number,
    data: any
  ) {
    return this.executeWithCacheInvalidation(
      () => this.prisma.conversations.update({
        where: { id: conversationId },
        data
      }),
      [
        CacheKeys.conversation(conversationId),
        CacheKeys.conversationPattern(conversationId)
      ]
    );
  }

  /**
   * Create message and invalidate conversation cache.
   */
  protected async createMessageWithCache(
    conversationId: number,
    messageData: any
  ) {
    return this.executeWithCacheInvalidation(
      () => this.prisma.messages.create({
        data: messageData
      }),
      [
        CacheKeys.conversationMessages(conversationId),
        CacheKeys.conversation(conversationId)
      ]
    );
  }
}
