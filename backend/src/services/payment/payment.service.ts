/**
 * Payment service for handling payment-related operations
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import { PaymentRepository } from '../../db/repositories/payment/payment.repository';
import { logger } from '../../common/logger';
import {
  CreatePaymentDto,
  CreateRefundDto,
  UpdateRefundDto,
  PaymentStatus,
  RefundStatus,
} from '../../api/payment/dto';
import { PaymentResponse, RefundResponse } from '../../common/types/payment';
// import config from '../../../config/config'; // For future use

/**
 * Service for payment operations
 */
@injectable()
export class PaymentService {
  constructor(@inject(TYPES.PaymentRepository) private paymentRepository: PaymentRepository) {}

  /**
   * Get payment by ID
   */
  async getPaymentById(id: number): Promise<PaymentResponse | null> {
    try {
      const payment = await this.paymentRepository.findById(id);
      if (!payment) return null;

      return this.mapPaymentToResponse(payment);
    } catch (error) {
      logger.error(`Error getting payment with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get payments by user ID
   */
  async getPaymentsByUserId(
    userId: number,
    options: { skip?: number; take?: number } = {},
  ): Promise<PaymentResponse[]> {
    try {
      const payments = await this.paymentRepository.findByUserId(userId, options);
      return payments.map((payment) => this.mapPaymentToResponse(payment));
    } catch (error) {
      logger.error(`Error getting payments for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new payment
   */
  async createPayment(data: CreatePaymentDto): Promise<PaymentResponse> {
    try {
      const payment = await this.paymentRepository.create({
        userId: data.userId,
        amount: data.amount,
        currency: data.currency,
        paymentMethod: data.paymentMethod,
        paymentProvider: data.paymentProvider,
        providerPaymentId: data.providerPaymentId,
        status: data.status,
        description: data.description,
        metadata: data.metadata,
      });

      return this.mapPaymentToResponse(payment);
    } catch (error) {
      logger.error('Error creating payment:', error);
      throw error;
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(id: number, status: string): Promise<PaymentResponse | null> {
    try {
      const payment = await this.paymentRepository.findById(id);
      if (!payment) return null;

      const completedAt = status === PaymentStatus.COMPLETED ? new Date() : undefined;
      const updatedPayment = await this.paymentRepository.updateStatus(id, status, completedAt);

      return this.mapPaymentToResponse(updatedPayment);
    } catch (error) {
      logger.error(`Error updating payment status for ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Process a refund
   */
  async createRefund(data: CreateRefundDto): Promise<RefundResponse | null> {
    try {
      const payment = await this.paymentRepository.findById(data.paymentId);
      if (!payment) return null;

      // Check if refund amount is valid
      if (data.amount > Number(payment.amount)) {
        throw new Error(`Refund amount ${data.amount} exceeds payment amount ${payment.amount}`);
      }

      const refund = await this.paymentRepository.createRefund({
        paymentId: data.paymentId,
        amount: data.amount,
        reason: data.reason,
        status: data.status,
        providerRefundId: data.providerRefundId,
        refundedByUserId: data.refundedByUserId,
      });

      // Update payment status based on refund amount
      const refundedAmount =
        payment.payment_refunds.reduce((total, r) => total + Number(r.amount), 0) +
        Number(data.amount);

      let newStatus = payment.status;
      if (refundedAmount >= Number(payment.amount)) {
        newStatus = PaymentStatus.REFUNDED;
      } else if (refundedAmount > 0) {
        newStatus = PaymentStatus.PARTIALLY_REFUNDED;
      }

      if (newStatus !== payment.status) {
        await this.paymentRepository.updateStatus(data.paymentId, newStatus);
      }

      return this.mapRefundToResponse(refund);
    } catch (error) {
      logger.error(`Error creating refund for payment ${data.paymentId}:`, error);
      throw error;
    }
  }

  /**
   * Update refund status
   */
  async updateRefundStatus(id: number, data: UpdateRefundDto): Promise<RefundResponse | null> {
    try {
      const updatedRefund = await this.paymentRepository.updateRefundStatus(
        id,
        data.status || RefundStatus.PENDING,
        data.completedAt,
      );

      return this.mapRefundToResponse(updatedRefund);
    } catch (error) {
      logger.error(`Error updating refund status for ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Map payment entity to response object
   */
  private mapPaymentToResponse(payment: any): PaymentResponse {
    return {
      id: payment.id,
      userId: payment.user_id,
      amount: Number(payment.amount),
      currency: payment.currency,
      paymentMethod: payment.payment_method,
      paymentProvider: payment.payment_provider,
      providerPaymentId: payment.provider_payment_id,
      status: payment.status,
      description: payment.description,
      metadata: payment.metadata ? JSON.parse(payment.metadata) : undefined,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
      completedAt: payment.completed_at,
      refunds: payment.payment_refunds?.map(
        (refund: {
          id: number;
          payment_id: number;
          amount: number;
          reason: string;
          status: string;
          provider_refund_id: string;
          refunded_by_user_id: number;
          created_at: Date;
          updated_at: Date;
          completed_at: Date | null;
        }) => this.mapRefundToResponse(refund),
      ),
    };
  }

  /**
   * Map refund entity to response object
   */
  private mapRefundToResponse(refund: any): RefundResponse {
    return {
      id: refund.id,
      paymentId: refund.payment_id,
      amount: Number(refund.amount),
      reason: refund.reason,
      status: refund.status,
      providerRefundId: refund.provider_refund_id,
      refundedByUserId: refund.refunded_by_user_id,
      createdAt: refund.created_at,
      updatedAt: refund.updated_at,
      completedAt: refund.completed_at,
    };
  }
}
