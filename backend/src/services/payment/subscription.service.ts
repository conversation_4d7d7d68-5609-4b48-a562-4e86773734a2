/**
 * Subscription service for handling subscription-related operations
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import { SubscriptionRepository } from '../../db/repositories/payment/subscription.repository';
import { PaymentRepository } from '../../db/repositories/payment/payment.repository';
import { logger } from '../../common/logger';
import {
  CreateSubscriptionDto,
  UpdateSubscriptionDto,
  CreateSubscriptionPaymentDto,
  CancelSubscriptionDto,
  SubscriptionStatus,
} from '../../api/payment/dto';
import { SubscriptionResponse, SubscriptionPaymentResponse } from '../../common/types/payment';
// import config from '../../../config/config'; // For future use

/**
 * Service for subscription operations
 */
@injectable()
export class SubscriptionService {
  constructor(
    @inject(TYPES.SubscriptionRepository) private subscriptionRepository: SubscriptionRepository,
    @inject(TYPES.PaymentRepository) private paymentRepository: PaymentRepository,
  ) {}

  /**
   * Get subscription by ID
   */
  async getSubscriptionById(id: number): Promise<SubscriptionResponse | null> {
    try {
      const subscription = await this.subscriptionRepository.findById(id);
      if (!subscription) return null;

      return this.mapSubscriptionToResponse(subscription);
    } catch (error) {
      logger.error(`Error getting subscription with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get active subscription by user ID
   */
  async getActiveSubscriptionByUserId(userId: number): Promise<SubscriptionResponse | null> {
    try {
      const subscription = await this.subscriptionRepository.findActiveByUserId(userId);
      if (!subscription) return null;

      return this.mapSubscriptionToResponse(subscription);
    } catch (error) {
      logger.error(`Error getting active subscription for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all subscriptions by user ID
   */
  async getSubscriptionsByUserId(userId: number): Promise<SubscriptionResponse[]> {
    try {
      const subscriptions = await this.subscriptionRepository.findByUserId(userId);
      return subscriptions.map((subscription) => this.mapSubscriptionToResponse(subscription));
    } catch (error) {
      logger.error(`Error getting subscriptions for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(data: CreateSubscriptionDto): Promise<SubscriptionResponse> {
    try {
      // Check if user already has an active subscription
      const activeSubscription = await this.subscriptionRepository.findActiveByUserId(data.userId);
      if (activeSubscription) {
        throw new Error(`User ${data.userId} already has an active subscription`);
      }

      const subscription = await this.subscriptionRepository.create({
        userId: data.userId,
        planName: data.planName,
        planId: data.planId,
        status: data.status,
        currentPeriodStart: data.currentPeriodStart,
        currentPeriodEnd: data.currentPeriodEnd,
        cancelAtPeriodEnd: data.cancelAtPeriodEnd,
        provider: data.provider,
        providerSubscriptionId: data.providerSubscriptionId,
        metadata: data.metadata,
      });

      return this.mapSubscriptionToResponse(subscription);
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Update subscription
   */
  async updateSubscription(
    id: number,
    data: UpdateSubscriptionDto,
  ): Promise<SubscriptionResponse | null> {
    try {
      const subscription = await this.subscriptionRepository.findById(id);
      if (!subscription) return null;

      const updatedSubscription = await this.subscriptionRepository.update(id, {
        status: data.status,
        currentPeriodStart: data.currentPeriodStart,
        currentPeriodEnd: data.currentPeriodEnd,
        cancelAtPeriodEnd: data.cancelAtPeriodEnd,
        canceledAt: data.canceledAt,
        endedAt: data.endedAt,
        metadata: data.metadata,
      });

      return this.mapSubscriptionToResponse(updatedSubscription);
    } catch (error) {
      logger.error(`Error updating subscription ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(
    id: number,
    data: CancelSubscriptionDto,
  ): Promise<SubscriptionResponse | null> {
    try {
      const subscription = await this.subscriptionRepository.findById(id);
      if (!subscription) return null;

      const canceledAt = new Date();
      let status = subscription.status;
      let endedAt = undefined;

      if (data.cancelImmediately) {
        status = SubscriptionStatus.CANCELED;
        endedAt = canceledAt;
      } else {
        // Set to cancel at period end
        status = SubscriptionStatus.ACTIVE;
      }

      const updatedSubscription = await this.subscriptionRepository.update(id, {
        status,
        cancelAtPeriodEnd: !data.cancelImmediately,
        canceledAt,
        endedAt,
        metadata: {
          ...(typeof subscription.metadata === 'string' ? JSON.parse(subscription.metadata) : {}),
          cancellationReason: data.reason,
        },
      });

      return this.mapSubscriptionToResponse(updatedSubscription);
    } catch (error) {
      logger.error(`Error canceling subscription ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create subscription payment link
   */
  async createSubscriptionPayment(
    data: CreateSubscriptionPaymentDto,
  ): Promise<SubscriptionPaymentResponse | null> {
    try {
      const subscription = await this.subscriptionRepository.findById(data.subscriptionId);
      if (!subscription) return null;

      const payment = await this.paymentRepository.findById(data.paymentId);
      if (!payment) return null;

      const subscriptionPayment = await this.subscriptionRepository.createSubscriptionPayment({
        subscriptionId: data.subscriptionId,
        paymentId: data.paymentId,
        billingPeriodStart: data.billingPeriodStart,
        billingPeriodEnd: data.billingPeriodEnd,
      });

      return this.mapSubscriptionPaymentToResponse(subscriptionPayment);
    } catch (error) {
      logger.error(`Error creating subscription payment link:`, error);
      throw error;
    }
  }

  /**
   * Map subscription entity to response object
   */
  private mapSubscriptionToResponse(subscription: any): SubscriptionResponse {
    return {
      id: subscription.id,
      userId: subscription.user_id,
      planName: subscription.plan_name,
      planId: subscription.plan_id,
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      provider: subscription.provider,
      providerSubscriptionId: subscription.provider_subscription_id,
      metadata: subscription.metadata ? JSON.parse(subscription.metadata) : undefined,
      createdAt: subscription.created_at,
      updatedAt: subscription.updated_at,
      canceledAt: subscription.canceled_at,
      endedAt: subscription.ended_at,
      payments: subscription.subscription_payments?.map(
        (payment: {
          id: number;
          subscription_id: number;
          payment_id: number;
          billing_period_start: Date;
          billing_period_end: Date;
          created_at: Date;
          payments: any;
        }) => this.mapSubscriptionPaymentToResponse(payment),
      ),
    };
  }

  /**
   * Map subscription payment entity to response object
   */
  private mapSubscriptionPaymentToResponse(subscriptionPayment: any): SubscriptionPaymentResponse {
    return {
      id: subscriptionPayment.id,
      subscriptionId: subscriptionPayment.subscription_id,
      paymentId: subscriptionPayment.payment_id,
      billingPeriodStart: subscriptionPayment.billing_period_start,
      billingPeriodEnd: subscriptionPayment.billing_period_end,
      createdAt: subscriptionPayment.created_at,
      payment: subscriptionPayment.payments,
    };
  }
}
