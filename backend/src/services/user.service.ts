/**
 * User service for handling user-related operations.
 *
 * This module provides services for user profile management while being mindful
 * of the <PERSON> integration for authentication and user management.
 */
import { logger } from '../common/logger';
import { UserCreate, UserUpdate, UserProfileUpdate } from '../common/types/user.types';
import { userRepository } from '../db/repositories/user.repository';
import { users as User, UserRole, Prisma } from '../generated/prisma';

class UserService {
  /**
   * Get a user by their ID.
   *
   * @param userId - The user's ID.
   * @returns The user if found, null otherwise.
   */
  async getById(userId: number): Promise<User | null> {
    try {
      return await userRepository.findById(userId);
    } catch (error) {
      logger.error(`Error getting user by ID ${userId}:`, error);
      return null;
    }
  }

  /**
   * Get a user by their Clerk ID.
   *
   * @param clerkId - The Clerk ID of the user.
   * @returns The user if found, null otherwise.
   */
  async getByClerkId(clerkId: string): Promise<User | null> {
    try {
      const users = await userRepository.findMany({
        where: { clerkId },
      });
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logger.error(`Error getting user by Clerk ID ${clerkId}:`, error);
      return null;
    }
  }

  /**
   * Get a user by their email address.
   *
   * @param email - The user's email address.
   * @returns The user if found, null otherwise.
   */
  async getByEmail(email: string): Promise<User | null> {
    try {
      const users = await userRepository.findMany({
        where: { email },
      });
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logger.error(`Error getting user by email ${email}:`, error);
      return null;
    }
  }

  /**
   * Get all users, optionally filtered by role and active status.
   *
   * @param role - Filter by user role (optional).
   * @param activeOnly - Only include active users if true (default: true).
   * @param skip - Number of records to skip (for pagination).
   * @param limit - Maximum number of records to return (for pagination).
   * @returns List of matching users.
   */
  async getAllUsers(
    role?: UserRole,
    activeOnly: boolean = true,
    skip: number = 0,
    limit: number = 100,
  ): Promise<User[]> {
    try {
      const where: Prisma.usersWhereInput = {};

      if (role !== undefined) {
        where.role = role;
      }

      if (activeOnly) {
        where.is_active = true;
      }

      return await userRepository.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });
    } catch (error) {
      logger.error('Error getting all users:', error);
      return [];
    }
  }

  /**
   * Create a new user from Clerk data.
   *
   * This method should be used when a new user signs up through Clerk
   * and needs to be synchronized with our database.
   *
   * @param userData - User data from Clerk.
   * @returns The created user if successful, null otherwise.
   */
  async createUser(userData: UserCreate): Promise<User | null> {
    try {
      // Ensure required fields are present
      if (!userData.email || !userData.clerkId) {
        logger.error('Missing required fields for user creation: email or clerkId');
        return null;
      }

      // Check if user already exists
      const existingUser = await this.getByEmail(userData.email);
      if (existingUser) {
        logger.warn(`User with email ${userData.email} already exists`);
        return existingUser;
      }

      // Create new user
      const newUser = await userRepository.create({
        email: userData.email,
        clerk_id: userData.clerkId,
        first_name: userData.firstName || null,
        last_name: userData.lastName || null,
        username: userData.username || null,
        bio: userData.bio || null,
        profile_image_url: userData.profileImageUrl || null,
        role: UserRole.FREE,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        karma: 10, // Default karma value
        elo_rating: 1200, // Default ELO rating
        developer_strengths: [], // Empty array as default
        preferred_ai_models: [], // Empty array as default
      });

      return newUser as User;
    } catch (error) {
      logger.error('Error creating user:', error);
      return null;
    }
  }

  /**
   * Update a user's profile information.
   *
   * @param userId - The user's ID.
   * @param profileData - Updated profile information.
   * @returns The updated user if successful, null otherwise.
   */
  async updateProfile(userId: number, profileData: UserProfileUpdate): Promise<User | null> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return null;
      }

      // Create a plain object for the update data
      const updateData: Record<string, any> = {};

      // Only update fields that are provided
      if (profileData.firstName !== undefined) updateData.first_name = profileData.firstName;
      if (profileData.lastName !== undefined) updateData.last_name = profileData.lastName;
      if (profileData.bio !== undefined) updateData.bio = profileData.bio;
      if (profileData.profileImageUrl !== undefined)
        updateData.profile_image_url = profileData.profileImageUrl;
      if (profileData.preferredIde !== undefined)
        updateData.preferred_ide = profileData.preferredIde;
      if (profileData.learningStyle !== undefined)
        updateData.learning_style = profileData.learningStyle;
      if (profileData.developerStrengths !== undefined)
        updateData.developer_strengths = profileData.developerStrengths;
      if (profileData.preferredAiModels !== undefined)
        updateData.preferred_ai_models = profileData.preferredAiModels;
      if (profileData.additionalInfo !== undefined)
        updateData.additional_info = profileData.additionalInfo;

      // Update timestamps
      updateData.updated_at = new Date();

      return await userRepository.update(userId, updateData);
    } catch (error) {
      logger.error(`Error updating profile for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Update a user's role.
   *
   * This is typically done by admins or during subscription changes.
   *
   * @param userId - The user's ID.
   * @param role - The new role.
   * @returns The updated user if successful, null otherwise.
   */
  async updateRole(userId: number, role: UserRole): Promise<User | null> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return null;
      }

      // Update role
      return await userRepository.update(userId, {
        role,
        updated_at: new Date(),
      });
    } catch (error) {
      logger.error(`Error updating role for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Record a successful login event.
   *
   * @param userId - The user's ID.
   * @returns True if successful, false otherwise.
   */
  async recordLogin(userId: number): Promise<boolean> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return false;
      }

      const now = new Date();

      // Update login timestamp and reset daily quota if needed
      const updateData: Record<string, any> = {
        last_login: now,
        last_active_date: now,
        updated_at: now,
      };

      // Reset daily token quota if needed
      if (this.shouldResetDailyQuota(user.quota_reset_date)) {
        updateData.daily_llm_token_usage = 0;
        updateData.quota_reset_date = now;
      }

      // Update user
      const updated = await userRepository.update(userId, updateData);
      return !!updated;
    } catch (error) {
      logger.error(`Error recording login for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Mark a user's onboarding as completed.
   *
   * @param userId - The user's ID.
   * @returns True if successful, false otherwise.
   */
  async completeOnboarding(userId: number): Promise<boolean> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return false;
      }

      // Update onboarding status
      const now = new Date();
      const updated = await userRepository.update(userId, {
        onboarding_completed: true,
        onboarding_completed_at: now,
        updated_at: now,
      });

      return !!updated;
    } catch (error) {
      logger.error(`Error completing onboarding for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Deactivate a user account.
   *
   * @param userId - The user's ID.
   * @returns True if successful, false otherwise.
   */
  async deactivateUser(userId: number): Promise<boolean> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return false;
      }

      // Deactivate user
      const updated = await userRepository.update(userId, {
        is_active: false,
        updated_at: new Date(),
      });

      return !!updated;
    } catch (error) {
      logger.error(`Error deactivating user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Reactivate a deactivated user account.
   *
   * @param userId - The user's ID.
   * @returns True if successful, false otherwise.
   */
  async reactivateUser(userId: number): Promise<boolean> {
    try {
      // Get current user
      const user = await this.getById(userId);
      if (!user) {
        logger.warn(`User with ID ${userId} not found`);
        return false;
      }

      // Reactivate user
      const updated = await userRepository.update(userId, {
        is_active: true,
        updated_at: new Date(),
      });

      return !!updated;
    } catch (error) {
      logger.error(`Error reactivating user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Helper method to check if daily quota should be reset.
   *
   * @param quotaResetDate - The date when the quota was last reset.
   * @returns True if quota should be reset, false otherwise.
   */
  private shouldResetDailyQuota(quotaResetDate?: Date | null): boolean {
    if (!quotaResetDate) return true;

    const today = new Date().toDateString();
    const resetDate = quotaResetDate.toDateString();

    return today !== resetDate;
  }
}

// Export a singleton instance
export const userService = new UserService();
export default userService;
