import { Injectable } from '@nestjs/common';
import { WorkshopRepository } from '../../db/repositories/workshop/workshop.repository';
import { CreateWorkshopDto, UpdateWorkshopDto } from '../../api/workshop/dto/workshop.dto';

@Injectable()
export class WorkshopService {
  constructor(private readonly workshopRepository: WorkshopRepository) {}

  async createWorkshop(data: CreateWorkshopDto) {
    return this.workshopRepository.createWorkshop(data);
  }

  async updateWorkshop(workshopId: number, data: UpdateWorkshopDto) {
    return this.workshopRepository.updateWorkshop(workshopId, data);
  }

  async getWorkshop(workshopId: number) {
    return this.workshopRepository.findWorkshopById(workshopId);
  }

  async getAllWorkshops() {
    return this.workshopRepository.findAllWorkshops();
  }

  async deleteWorkshop(workshopId: number) {
    return this.workshopRepository.deleteWorkshop(workshopId);
  }

  async addParticipant(workshopId: number, userId: number) {
    return this.workshopRepository.addParticipant(workshopId, userId);
  }

  async removeParticipant(workshopId: number, userId: number) {
    return this.workshopRepository.removeParticipant(workshopId, userId);
  }

  async getParticipants(workshopId: number) {
    return this.workshopRepository.getParticipants(workshopId);
  }
}
