import { Injectable } from '@nestjs/common';
import { WorkshopRepository } from '../../db/repositories/workshop/workshop.repository';
import {
  CreateModuleDto,
  UpdateModuleDto,
  CreateLessonDto,
  UpdateLessonDto,
  UpdateProgressDto,
  CompleteLessonDto,
} from '../../api/workshop/dto/module.dto';

@Injectable()
export class WorkshopProgressService {
  constructor(private readonly workshopRepository: WorkshopRepository) {}

  async createModule(workshopId: number, data: CreateModuleDto) {
    return this.workshopRepository.createModule(workshopId, data);
  }

  async updateModule(moduleId: number, data: UpdateModuleDto) {
    return this.workshopRepository.updateModule(moduleId, data);
  }

  async getModules(workshopId: number) {
    return this.workshopRepository.getModules(workshopId);
  }

  async createLesson(moduleId: number, data: CreateLessonDto) {
    return this.workshopRepository.createLesson(moduleId, data);
  }

  async updateLesson(lessonId: number, data: UpdateLessonDto) {
    return this.workshopRepository.updateLesson(lessonId, data);
  }

  async getLessons(moduleId: number) {
    return this.workshopRepository.getLessons(moduleId);
  }

  async updateProgress(
    workshopId: number,
    userId: number,
    moduleId: number,
    data: UpdateProgressDto,
  ) {
    return this.workshopRepository.updateProgress(workshopId, userId, moduleId, data);
  }

  async completeLesson(lessonId: number, userId: number, data: CompleteLessonDto) {
    return this.workshopRepository.completeLesson(lessonId, userId, data);
  }

  async getProgress(workshopId: number, userId: number) {
    return this.workshopRepository.getProgress(workshopId, userId);
  }
}
