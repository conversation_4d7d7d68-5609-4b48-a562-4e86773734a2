import { inject, injectable } from 'inversify';
import { TYPES } from '../types';
import { logger } from '../common/logger';
import azureService from './ai/azure.service';
import { VectorService } from './vector.service';
import { BadRequestError } from '../common/errors/http.error';
import * as fs from 'fs/promises';
import * as path from 'path';

interface ProcessedDocument {
  filePath: string;
  chunks: Array<{
    content: string;
    startLine?: number;
    endLine?: number;
    metadata: Record<string, any>;
  }>;
  type: 'auto-doc' | 'interview' | 'readme' | 'comment';
}

interface InterviewResponse {
  question: string;
  answer: string;
  context: {
    filePath: string;
    lineNumber?: number;
    codeSnippet?: string;
  };
  timestamp: Date;
}

interface CodeFile {
  path: string;
  content: string;
  language: string;
}

@injectable()
export class DocumentProcessingService {
  private readonly CHUNK_SIZE = 800; // tokens
  private readonly CHUNK_OVERLAP = 100; // tokens
  
  constructor(
    @inject(TYPES.VectorService) private vectorService: VectorService,
  ) {}

  /**
   * Process automated documentation files
   */
  async processAutomatedDocs(projectPath: string, projectId: string): Promise<void> {
    try {
      logger.info(`Processing automated docs for project: ${projectPath}`);

      // Look for .doc.json files in the project
      const docFiles = await this.findDocumentationFiles(projectPath);
      
      const documents: Array<{
        filePath: string;
        content: string;
        type: 'auto-doc' | 'interview' | 'readme' | 'comment';
        metadata?: Record<string, any>;
      }> = [];

      for (const docFile of docFiles) {
        try {
          const content = await fs.readFile(docFile, 'utf-8');
          const docData = JSON.parse(content);

          // Create a comprehensive document from the structured data
          const documentContent = this.formatAutomatedDoc(docData);
          
          documents.push({
            filePath: docData.path || docFile,
            content: documentContent,
            type: 'auto-doc',
            metadata: {
              unit: docData.unit,
              purpose: docData.purpose,
              commit: docData.commit,
              timestamp: docData.timestamp,
            },
          });
        } catch (error) {
          logger.error(`Error processing doc file ${docFile}:`, error);
        }
      }

      // Process README files
      const readmeFiles = await this.findReadmeFiles(projectPath);
      for (const readmeFile of readmeFiles) {
        const content = await fs.readFile(readmeFile, 'utf-8');
        documents.push({
          filePath: readmeFile,
          content,
          type: 'readme',
          metadata: {
            relativePath: path.relative(projectPath, readmeFile),
          },
        });
      }

      // Update embeddings for all documents
      await this.vectorService.updateDocumentEmbeddings(projectId, documents);

      logger.info(`Processed ${documents.length} documentation files`);
    } catch (error) {
      logger.error('Error processing automated docs:', error);
      throw new BadRequestError('Failed to process automated documentation');
    }
  }

  /**
   * Process interview responses
   */
  async processInterviewResponses(
    responses: InterviewResponse[],
    projectId: string
  ): Promise<void> {
    try {
      logger.info(`Processing ${responses.length} interview responses`);

      const documents = responses.map(response => ({
        filePath: response.context.filePath,
        content: this.formatInterviewResponse(response),
        type: 'interview' as const,
        metadata: {
          question: response.question,
          timestamp: response.timestamp,
          lineNumber: response.context.lineNumber,
          hasCodeSnippet: !!response.context.codeSnippet,
        },
      }));

      await this.vectorService.updateDocumentEmbeddings(projectId, documents);

      logger.info('Successfully processed interview responses');
    } catch (error) {
      logger.error('Error processing interview responses:', error);
      throw new BadRequestError('Failed to process interview responses');
    }
  }

  /**
   * Process code comments and docstrings
   */
  async processCodeComments(files: CodeFile[], projectId: string): Promise<void> {
    try {
      logger.info(`Processing code comments from ${files.length} files`);

      const documents: Array<{
        filePath: string;
        content: string;
        type: 'auto-doc' | 'interview' | 'readme' | 'comment';
        metadata?: Record<string, any>;
      }> = [];

      for (const file of files) {
        const comments = this.extractComments(file);
        
        for (const comment of comments) {
          documents.push({
            filePath: file.path,
            content: comment.content,
            type: 'comment',
            metadata: {
              language: file.language,
              commentType: comment.type,
              startLine: comment.startLine,
              endLine: comment.endLine,
            },
          });
        }
      }

      await this.vectorService.updateDocumentEmbeddings(projectId, documents);

      logger.info(`Processed ${documents.length} code comments`);
    } catch (error) {
      logger.error('Error processing code comments:', error);
      throw new BadRequestError('Failed to process code comments');
    }
  }

  /**
   * Generate embeddings for text chunks
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      const result = await azureService.generateEmbeddings(texts);
      return result.embeddings;
    } catch (error) {
      logger.error('Error generating embeddings:', error);
      throw new BadRequestError('Failed to generate embeddings');
    }
  }

  /**
   * Chunk text for embedding while preserving context
   */
  private chunkText(text: string): string[] {
    // Simple chunking by character count (roughly 4 chars per token)
    const charPerChunk = this.CHUNK_SIZE * 4;
    const overlapChars = this.CHUNK_OVERLAP * 4;
    const chunks: string[] = [];

    let start = 0;
    while (start < text.length) {
      const end = Math.min(start + charPerChunk, text.length);
      const chunk = text.substring(start, end);
      chunks.push(chunk);
      
      // Move start position with overlap
      start = end - overlapChars;
      if (start >= text.length - overlapChars) {
        break; // Avoid creating very small final chunks
      }
    }

    return chunks;
  }

  /**
   * Format automated documentation for embedding
   */
  private formatAutomatedDoc(docData: any): string {
    const parts: string[] = [];

    if (docData.purpose) {
      parts.push(`Purpose: ${docData.purpose}`);
    }

    if (docData.unit) {
      parts.push(`Function/Class: ${docData.unit}`);
    }

    if (docData.inputs && docData.inputs.length > 0) {
      const inputStr = docData.inputs
        .map((input: any) => `${input.name}: ${input.type}`)
        .join(', ');
      parts.push(`Inputs: ${inputStr}`);
    }

    if (docData.outputs) {
      parts.push(`Output: ${docData.outputs.type}`);
      if (docData.outputs.throws && docData.outputs.throws.length > 0) {
        parts.push(`Throws: ${docData.outputs.throws.join(', ')}`);
      }
    }

    if (docData.dependencies && docData.dependencies.length > 0) {
      parts.push(`Dependencies: ${docData.dependencies.join(', ')}`);
    }

    if (docData.intent) {
      parts.push(`Developer Intent: ${docData.intent}`);
    }

    return parts.join('\n\n');
  }

  /**
   * Format interview response for embedding
   */
  private formatInterviewResponse(response: InterviewResponse): string {
    const parts: string[] = [
      `Question: ${response.question}`,
      `Answer: ${response.answer}`,
      `File: ${response.context.filePath}`,
    ];

    if (response.context.lineNumber) {
      parts.push(`Line: ${response.context.lineNumber}`);
    }

    if (response.context.codeSnippet) {
      parts.push(`Related Code:\n${response.context.codeSnippet}`);
    }

    return parts.join('\n\n');
  }

  /**
   * Extract comments from code files
   */
  private extractComments(file: CodeFile): Array<{
    content: string;
    type: 'block' | 'line' | 'docstring';
    startLine: number;
    endLine: number;
  }> {
    const comments: Array<{
      content: string;
      type: 'block' | 'line' | 'docstring';
      startLine: number;
      endLine: number;
    }> = [];

    const lines = file.content.split('\n');
    
    // Simple comment extraction (can be enhanced with proper parsers)
    let inBlockComment = false;
    let blockCommentStart = 0;
    let blockCommentLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Handle different comment styles based on language
      if (file.language === 'typescript' || file.language === 'javascript') {
        // Block comments
        if (line.startsWith('/**') || line.startsWith('/*')) {
          inBlockComment = true;
          blockCommentStart = i + 1;
          blockCommentLines = [line];
        } else if (inBlockComment && line.includes('*/')) {
          blockCommentLines.push(line);
          comments.push({
            content: blockCommentLines.join('\n'),
            type: 'block',
            startLine: blockCommentStart,
            endLine: i + 1,
          });
          inBlockComment = false;
          blockCommentLines = [];
        } else if (inBlockComment) {
          blockCommentLines.push(line);
        }
        
        // Single line comments
        else if (line.startsWith('//') && line.length > 3) {
          comments.push({
            content: line.substring(2).trim(),
            type: 'line',
            startLine: i + 1,
            endLine: i + 1,
          });
        }
      }
      
      // Add support for other languages (Python, etc.) as needed
    }

    return comments.filter(comment => comment.content.length > 20); // Filter out very short comments
  }

  /**
   * Find documentation files in project
   */
  private async findDocumentationFiles(projectPath: string): Promise<string[]> {
    const docFiles: string[] = [];
    
    async function scan(dir: string) {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await scan(fullPath);
          } else if (entry.isFile() && entry.name.endsWith('.doc.json')) {
            docFiles.push(fullPath);
          }
        }
      } catch (error) {
        logger.warn(`Error scanning directory ${dir}:`, error);
      }
    }
    
    await scan(projectPath);
    return docFiles;
  }

  /**
   * Find README files in project
   */
  private async findReadmeFiles(projectPath: string): Promise<string[]> {
    const readmeFiles: string[] = [];
    
    async function scan(dir: string) {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await scan(fullPath);
          } else if (entry.isFile() && entry.name.toLowerCase().includes('readme')) {
            readmeFiles.push(fullPath);
          }
        }
      } catch (error) {
        logger.warn(`Error scanning directory ${dir}:`, error);
      }
    }
    
    await scan(projectPath);
    return readmeFiles;
  }
}
