/**
 * Base service class for the application.
 *
 * This module provides a base class for all service classes to ensure consistent
 * implementation patterns and proper database access.
 */
import { logger } from '../common/logger';
import { PrismaClient } from '@prisma/client';

export class BaseService {
  protected prisma: PrismaClient;

  /**
   * Initialize the service with a Prisma client.
   *
   * @param prismaClient - The Prisma client for database access
   */
  constructor(prismaClient: PrismaClient) {
    this.prisma = prismaClient;
  }

  /**
   * Execute a database transaction with error handling.
   *
   * @param callback - The transaction callback function
   * @returns The result of the transaction or null if an error occurred
   */
  protected async executeTransaction<T>(callback: () => Promise<T>): Promise<T | null> {
    try {
      return await this.prisma.$transaction(async () => {
        return await callback();
      });
    } catch (error) {
      logger.error(
        `Error executing transaction: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  /**
   * Handle API errors with a standard format.
   *
   * @param error - The error that occurred
   * @param message - Optional custom message
   * @returns Standardized error response
   */
  protected handleError(error: any, message?: string): { statusCode: number; detail: string } {
    const errorMessage =
      message || (error instanceof Error ? error.message : 'An unknown error occurred');

    logger.error(`Service error: ${errorMessage}`, { error });

    return {
      statusCode: 500,
      detail: errorMessage,
    };
  }
}
