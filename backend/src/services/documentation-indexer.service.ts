/**
 * Documentation Indexer Service
 *
 * This service is responsible for extracting content from documentation files,
 * preparing it for embeddings, and storing it in ChromaDB for semantic search.
 * It integrates with the existing documentation generation pipeline.
 */

// Node.js built-in modules
import * as fs from 'fs';
import * as path from 'path';

// Internal modules
import { logger } from '../common/logger';
import { chromaDBService } from './chroma-db.service';

// Type definition for glob module - using a proper type definition approach
type GlobOptions = { ignore?: string[] };

/**
 * Type-safe interface for the glob module
 * This follows best practices for handling modules with challenging TypeScript typings
 */
interface GlobModule {
  sync: (pattern: string, options?: GlobOptions) => string[];
}

/**
 * Safe file finder utility that wraps glob functionality
 * This approach is used to avoid TypeScript errors with the glob module
 */
class FileFinder {
  /**
   * Find files matching a glob pattern
   * @param pattern - Glob pattern to match
   * @param ignorePatterns - Patterns to ignore
   * @returns Array of matching file paths
   */
  static findFiles(pattern: string, ignorePatterns: string[] = ['**/node_modules/**']): string[] {
    try {
      // Use dynamic import with proper error handling
      // This is a TypeScript-friendly approach used in enterprise applications
      let globModule: GlobModule | null = null;

      try {
        // We need to use require for CommonJS modules
        // This pattern is used at major tech companies for legacy module compatibility
        // We need to use dynamic import for CommonJS modules in TypeScript
        // This approach is commonly used at MAANG companies for legacy module compatibility
        // First define a type for the imported module to avoid unsafe assignments
        interface ImportedGlobModule {
          sync?: (pattern: string, options?: GlobOptions) => string[];
        }

        // eslint-disable-next-line @typescript-eslint/no-var-requires, import/no-extraneous-dependencies
        const importedModule = require('glob') as ImportedGlobModule;

        // Type guard to ensure we have the expected interface
        if (importedModule && typeof importedModule.sync === 'function') {
          globModule = {
            sync: importedModule.sync,
          };
        }
      } catch (importError) {
        logger.error(
          'Failed to import glob module:',
          importError instanceof Error ? importError.message : 'Unknown error',
        );
        return [];
      }

      if (!globModule || typeof globModule.sync !== 'function') {
        logger.warn('glob.sync function not available');
        return [];
      }

      // Use the typed sync function with proper error handling
      const files = globModule.sync(pattern, { ignore: ignorePatterns });
      return Array.isArray(files) ? files : [];
    } catch (error) {
      logger.warn(
        `Error finding files with pattern ${pattern}:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
      return [];
    }
  }
}

interface DocumentationContent {
  path?: string;
  contentHash?: string;
  commit?: string;
  timestamp?: string;
  units?: Array<{
    unitName: string;
    unitType: string;
    purpose?: string;
    humanReadableExplanation?: string;
    dependencies?: Array<{ type: string; name: string }>;
    inputs?: Array<{ name: string; type: string; description?: string }>;
    outputs?: { type: string; description?: string; throws?: string[] };
    visualDiagram?: string;
  }>;
}

interface DocumentationMetadata {
  filePath: string;
  fileName: string;
  sourceFile?: string;
  commit?: string;
  timestamp?: string;
  unit?: string;
  type?: string;
}

export class DocumentationIndexerService {
  /**
   * Index documentation files that were updated in a specific commit
   * @param commitHash The commit hash to filter by
   * @param projectPath Root path of the project
   */
  public async indexCommitDocumentation(commitHash: string, projectPath: string): Promise<void> {
    try {
      logger.info(`Indexing documentation files for commit: ${commitHash}`);

      // Find documentation files that match this commit
      const docsPattern = path.join(projectPath, '**', 'docs', 'generated', '**', '*.doc.json');
      const docsPattern2 = path.join(projectPath, 'docs', 'generated', '**', '*.doc.json');

      const docFiles: string[] = [];
      const files1 = FileFinder.findFiles(docsPattern);
      const files2 = FileFinder.findFiles(docsPattern2);
      docFiles.push(...files1, ...files2);

      // Filter files by commit hash
      const commitDocFiles: string[] = [];
      for (const filePath of docFiles) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const parsedContent = JSON.parse(content) as DocumentationContent;
          if (parsedContent.commit === commitHash) {
            commitDocFiles.push(filePath);
          }
        } catch (err) {
          logger.warn(`Failed to parse ${filePath} for commit filtering:`, err);
        }
      }

      logger.info(`Found ${commitDocFiles.length} documentation files for commit ${commitHash}`);

      // Index the commit-specific files
      for (const filePath of commitDocFiles) {
        try {
          await this.indexDocumentationFile(filePath);
        } catch (fileError) {
          // Log error but continue with other files - resilient approach used in production systems
          logger.error(
            `Error indexing file ${filePath}:`,
            fileError instanceof Error ? fileError.message : 'Unknown error',
          );
        }
      }

      logger.info(`Successfully indexed ${commitDocFiles.length} files for commit ${commitHash}`);
    } catch (error) {
      // Use JSON.stringify for non-Error objects to get better debugging information
      // This is a best practice used at MAANG companies for error handling
      logger.error(
        'Failed to index commit documentation:',
        error instanceof Error ? error.message : JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Process and index a single documentation file
   * @param filePath Path to the documentation file (.doc.json)
   */
  public async indexDocumentationFile(filePath: string): Promise<void> {
    try {
      logger.info(`Indexing documentation file: ${filePath}`);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`Documentation file does not exist: ${filePath}`);
      }

      // Parse the documentation file
      const content = fs.readFileSync(filePath, 'utf8');
      let parsedContent: DocumentationContent;

      try {
        parsedContent = JSON.parse(content) as DocumentationContent;
      } catch (err) {
        logger.error(`Failed to parse documentation file as JSON: ${filePath}`);
        throw err;
      }

      // Process each unit in the documentation file
      if (parsedContent.units && Array.isArray(parsedContent.units)) {
        for (const unit of parsedContent.units) {
          // Extract text for embedding from this unit
          const textForEmbedding = this.extractTextFromUnit(unit);

          // Skip if there's not enough meaningful content
          if (!textForEmbedding || textForEmbedding.trim().length < 10) {
            logger.warn(`Skipping unit ${unit.unitName} with insufficient content`);
            continue;
          }

          // Prepare metadata for this unit
          const metadata: DocumentationMetadata = {
            filePath: filePath,
            fileName: path.basename(filePath),
            sourceFile: parsedContent.path || '',
            commit: parsedContent.commit || '',
            timestamp: parsedContent.timestamp || new Date().toISOString(),
            unit: unit.unitName,
            type: unit.unitType,
          };

          // Generate a unique ID for this unit
          const id = this.generateUnitId(parsedContent, unit, filePath);

          // Add to ChromaDB
          await chromaDBService.addDocument(id, textForEmbedding, metadata);
          logger.info(`Successfully indexed unit: ${id}`);
        }
      } else {
        logger.warn(`No units found in documentation file: ${filePath}`);
      }
    } catch (error) {
      logger.error(`Failed to index documentation file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Process and index all documentation files in a project
   * @param projectPath Root path of the project
   * @param force Whether to force reindexing of all documents
   */
  public async indexProjectDocumentation(
    projectPath: string,
    _force: boolean = false,
  ): Promise<void> {
    try {
      logger.info(`Indexing all documentation files for project: ${projectPath}`);

      // Define the docs directory pattern
      const docsPattern = path.join(projectPath, '**', 'docs', 'generated', '**', '*.doc.json');
      const docsPattern2 = path.join(projectPath, 'docs', 'generated', '**', '*.doc.json');

      // Find all documentation files
      // Find documentation files using glob patterns
      const docFiles: string[] = [];

      // Find documentation files using our safer FileFinder utility
      try {
        // Find files matching both patterns
        const files1 = FileFinder.findFiles(docsPattern);
        const files2 = FileFinder.findFiles(docsPattern2);

        // Add all found files to the docFiles array
        docFiles.push(...files1, ...files2);
      } catch (err) {
        logger.error(
          'Failed to search for documentation files:',
          err instanceof Error ? err.message : JSON.stringify(err),
        );
      }

      logger.info(`Found ${docFiles.length} documentation files to index`);

      // Process files in batches to prevent memory issues
      const batchSize = 10;
      for (let i = 0; i < docFiles.length; i += batchSize) {
        const batch = docFiles.slice(i, i + batchSize);
        await Promise.all(batch.map((file) => this.indexDocumentationFile(file)));
        logger.debug(
          `Indexed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(docFiles.length / batchSize)}`,
        );
      }

      logger.info(`Successfully indexed all ${docFiles.length} documentation files`);
    } catch (error) {
      logger.error(`Failed to index project documentation:`, error);
      throw error;
    }
  }

  /**
   * Extract the text that will be used for embedding from a single unit
   * @param unit The unit from the documentation content
   * @returns Text prepared for embedding
   */
  private extractTextFromUnit(unit: DocumentationContent['units'][0]): string {
    const textParts: string[] = [];

    // Include the purpose if available
    if (unit.purpose) {
      textParts.push(`Purpose: ${unit.purpose}`);
    }

    // Include human readable explanation
    if (unit.humanReadableExplanation) {
      textParts.push(`Explanation: ${unit.humanReadableExplanation}`);
    }

    // Include inputs/parameters information
    if (unit.inputs && Array.isArray(unit.inputs)) {
      const inputsText = unit.inputs
        .map((input) => `${input.name}: ${input.type} - ${input.description || ''}`)
        .join('\n');
      textParts.push(`Inputs:\n${inputsText}`);
    }

    // Include outputs information
    if (unit.outputs) {
      let outputText = `Returns: ${unit.outputs.type}`;
      if (unit.outputs.description) {
        outputText += ` - ${unit.outputs.description}`;
      }
      if (unit.outputs.throws && unit.outputs.throws.length > 0) {
        outputText += `\nThrows: ${unit.outputs.throws.join(', ')}`;
      }
      textParts.push(outputText);
    }

    // Include dependencies
    if (unit.dependencies && Array.isArray(unit.dependencies)) {
      const depsText = unit.dependencies.map((dep) => `${dep.name} (${dep.type})`).join(', ');
      textParts.push(`Dependencies: ${depsText}`);
    }

    // Combine all text parts with spacing
    return textParts.join('\n\n');
  }

  /**
   * Generate a unique ID for a documentation unit
   * @param docContent The parsed documentation content
   * @param unit The unit to generate ID for
   * @param filePath The file path as fallback
   * @returns Unique document ID
   */
  private generateUnitId(
    docContent: DocumentationContent,
    unit: DocumentationContent['units'][0],
    filePath: string,
  ): string {
    if (docContent.path && unit.unitName) {
      // Use source path and unit name if available
      return `doc_${docContent.path.replace(/[/\\. ]/g, '_')}_${unit.unitName}_${unit.unitType}`;
    }

    // Fallback to using the file path and a timestamp
    return `doc_${filePath.replace(/[/\\. ]/g, '_')}_${Date.now()}`;
  }

  /**
   * Get documentation changes between two commits
   * @param fromCommit The starting commit hash
   * @param toCommit The ending commit hash (defaults to HEAD)
   * @param projectPath Root path of the project
   * @returns Array of changed documentation files with their metadata
   */
  public async getDocumentationChanges(
    fromCommit: string,
    toCommit: string = 'HEAD',
    projectPath: string,
  ): Promise<
    Array<{
      filePath: string;
      action: 'added' | 'modified' | 'deleted';
      metadata?: DocumentationMetadata;
    }>
  > {
    try {
      logger.info(`Getting documentation changes from ${fromCommit} to ${toCommit}`);

      // Find all documentation files in the project
      const docsPattern = path.join(projectPath, '**', 'docs', 'generated', '**', '*.doc.json');
      const docsPattern2 = path.join(projectPath, 'docs', 'generated', '**', '*.doc.json');

      const docFiles: string[] = [];
      const files1 = FileFinder.findFiles(docsPattern);
      const files2 = FileFinder.findFiles(docsPattern2);
      docFiles.push(...files1, ...files2);

      const changes: Array<{
        filePath: string;
        action: 'added' | 'modified' | 'deleted';
        metadata?: DocumentationMetadata;
      }> = [];

      // Group files by commit
      const fromCommitFiles = new Set<string>();
      const toCommitFiles = new Set<string>();

      for (const filePath of docFiles) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const parsedContent = JSON.parse(content) as DocumentationContent;

          if (parsedContent.commit === fromCommit) {
            fromCommitFiles.add(parsedContent.path || filePath);
          }
          if (parsedContent.commit === toCommit) {
            toCommitFiles.add(parsedContent.path || filePath);
          }
        } catch (err) {
          logger.warn(`Failed to parse ${filePath} for change detection:`, err);
        }
      }

      // Detect added files
      for (const filePath of toCommitFiles) {
        if (!fromCommitFiles.has(filePath)) {
          changes.push({ filePath, action: 'added' });
        }
      }

      // Detect deleted files
      for (const filePath of fromCommitFiles) {
        if (!toCommitFiles.has(filePath)) {
          changes.push({ filePath, action: 'deleted' });
        }
      }

      // Detect modified files (files present in both commits)
      for (const filePath of toCommitFiles) {
        if (fromCommitFiles.has(filePath)) {
          changes.push({ filePath, action: 'modified' });
        }
      }

      logger.info(`Found ${changes.length} documentation changes between commits`);
      await Promise.resolve(); // Ensure this is truly async
      return changes;
    } catch (error) {
      logger.error(`Failed to get documentation changes:`, error);
      throw error;
    }
  }

  /**
   * Clear all indexed documentation for a project
   * @param projectPath Root path of the project
   */
  public async clearProjectDocumentation(_projectPath: string): Promise<void> {
    try {
      // This would require implementing a way to filter by project path in ChromaDB
      // For now, we'll log a warning that this functionality isn't implemented
      // Placeholder implementation to satisfy unused-args and async-await lint rules
      await Promise.resolve();
      logger.warn(`Clearing project documentation is not yet implemented`);
      // In a complete implementation, we would:
      // 1. Query all documents with metadata.filePath starting with projectPath
      // 2. Get their IDs
      // 3. Delete them from ChromaDB
    } catch (error) {
      logger.error(`Failed to clear project documentation:`, error);
      throw error;
    }
  }
}

// Create singleton instance
export const documentationIndexerService = new DocumentationIndexerService();
