import * as dotenv from 'dotenv';
import * as <PERSON><PERSON> from 'joi';

// Load environment variables
dotenv.config();

// Define validation schema
const envSchema = Joi.object({
  // Server Configuration
  NODE_ENV: Joi.string().valid('development', 'test', 'production').default('development'),
  PORT: Joi.number().default(3000),

  // Database
  DATABASE_URL: Joi.string().required(),

  // Authentication (Clerk)
  CLERK_SECRET_KEY: Joi.string().required(),
  CLERK_PUBLISHABLE_KEY: Joi.string().required(),
  CLERK_WEBHOOK_SECRET: Joi.string().optional(),

  // AI Services
  OPENAI_API_KEY: Joi.string().optional(),
  ANTHROPIC_API_KEY: Joi.string().optional(),
  GOOGLE_API_KEY: Joi.string().optional(),

  // AWS Configuration
  AWS_REGION: Joi.string().default('us-east-1'),
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),

  // Azure Configuration
  AZURE_TENANT_ID: Joi.string().optional(),
  AZURE_CLIENT_ID: Joi.string().optional(),
  AZURE_CLIENT_SECRET: Joi.string().optional(),

  // Google Cloud
  GOOGLE_APPLICATION_CREDENTIALS: Joi.string().optional(),

  // Session Secret
  SESSION_SECRET: Joi.string().required(),

  // JWT Configuration
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRATION: Joi.string().default('7d'),

  // API Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),

  // Logging
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  LOG_FORMAT: Joi.string().valid('json', 'simple').default('json'),

  // Feature Flags
  ENABLE_SWAGGER: Joi.boolean().default(true),
  ENABLE_METRICS: Joi.boolean().default(false),
}).unknown(); // Allow additional environment variables

// Validate environment variables
const { error, value: validatedEnv } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Environment validation error: ${error.message}`);
}

// Export validated and typed environment variables
export const env = {
  // Server Configuration
  NODE_ENV: validatedEnv.NODE_ENV as 'development' | 'test' | 'production',
  PORT: validatedEnv.PORT as number,

  // Database
  DATABASE_URL: validatedEnv.DATABASE_URL as string,

  // Authentication
  CLERK_SECRET_KEY: validatedEnv.CLERK_SECRET_KEY as string,
  CLERK_PUBLISHABLE_KEY: validatedEnv.CLERK_PUBLISHABLE_KEY as string,
  CLERK_WEBHOOK_SECRET: validatedEnv.CLERK_WEBHOOK_SECRET as string | undefined,

  // AI Services
  OPENAI_API_KEY: validatedEnv.OPENAI_API_KEY as string | undefined,
  ANTHROPIC_API_KEY: validatedEnv.ANTHROPIC_API_KEY as string | undefined,
  GOOGLE_API_KEY: validatedEnv.GOOGLE_API_KEY as string | undefined,

  // AWS Configuration
  AWS_REGION: validatedEnv.AWS_REGION as string,
  AWS_ACCESS_KEY_ID: validatedEnv.AWS_ACCESS_KEY_ID as string | undefined,
  AWS_SECRET_ACCESS_KEY: validatedEnv.AWS_SECRET_ACCESS_KEY as string | undefined,

  // Azure Configuration
  AZURE_TENANT_ID: validatedEnv.AZURE_TENANT_ID as string | undefined,
  AZURE_CLIENT_ID: validatedEnv.AZURE_CLIENT_ID as string | undefined,
  AZURE_CLIENT_SECRET: validatedEnv.AZURE_CLIENT_SECRET as string | undefined,

  // Google Cloud
  GOOGLE_APPLICATION_CREDENTIALS: validatedEnv.GOOGLE_APPLICATION_CREDENTIALS as string | undefined,

  // Session Secret
  SESSION_SECRET: validatedEnv.SESSION_SECRET as string,

  // JWT Configuration
  JWT_SECRET: validatedEnv.JWT_SECRET as string,
  JWT_EXPIRATION: validatedEnv.JWT_EXPIRATION as string,

  // API Rate Limiting
  RATE_LIMIT_WINDOW_MS: validatedEnv.RATE_LIMIT_WINDOW_MS as number,
  RATE_LIMIT_MAX_REQUESTS: validatedEnv.RATE_LIMIT_MAX_REQUESTS as number,

  // Logging
  LOG_LEVEL: validatedEnv.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug',
  LOG_FORMAT: validatedEnv.LOG_FORMAT as 'json' | 'simple',

  // Feature Flags
  ENABLE_SWAGGER: validatedEnv.ENABLE_SWAGGER as boolean,
  ENABLE_METRICS: validatedEnv.ENABLE_METRICS as boolean,
};

// Type export for use in other files
export type Env = typeof env;
