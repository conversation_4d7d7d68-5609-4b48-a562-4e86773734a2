import { logger } from '../common/logger';
import { PrismaClient } from '../generated/prisma';

import { prisma } from './client';
import { database } from './database';

/**
 * Service to handle database initialization and setup
 */
export class DatabaseService {
  /**
   * Initialize the database connection
   */
  async init(): Promise<void> {
    try {
      logger.info('Initializing database connection...');
      await database.connect();

      // Run a health check to verify the connection
      const isHealthy = await database.healthCheck();
      if (!isHealthy) {
        throw new Error('Database health check failed');
      }

      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  /**
   * Gracefully shutdown the database connection
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down database connection...');
      await database.disconnect();
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error shutting down database:', error);
      throw error;
    }
  }

  /**
   * Get the Prisma client instance
   */
  getClient(): PrismaClient {
    return prisma;
  }
}

// Export a singleton instance
export const databaseService = new DatabaseService();
