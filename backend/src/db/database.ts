import { logger } from '../common/logger';

import { prisma } from './client';

/**
 * Database utility for connecting and disconnecting from the database
 */
class Database {
  /**
   * Connect to the database
   */
  async connect(): Promise<void> {
    try {
      await prisma.$connect();
      logger.info('Connected to database successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the database
   */
  async disconnect(): Promise<void> {
    try {
      await prisma.$disconnect();
      logger.info('Disconnected from database successfully');
    } catch (error) {
      logger.error('Failed to disconnect from database:', error);
      throw error;
    }
  }

  /**
   * Run a health check on the database
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Execute a simple query to check if the database is responsive
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }
}

export const database = new Database();
export { prisma };
