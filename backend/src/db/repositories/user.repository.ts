/**
 * User repository for database operations related to users
 */
import { BaseRepository } from './base.repository';
import { PrismaClient } from '../../generated/prisma';

type UserRole = 'FREE' | 'DEVELOPER' | 'ADMIN';

type User = {
  id: number;
  email: string;
  username: string | null;
  first_name: string | null;
  last_name: string | null;
  clerk_id: string;
  role: UserRole;
  is_active: boolean;
  bio: string | null;
  profile_image_url: string | null;
  email_verified: boolean;
  email_verification_token: string | null;
  email_verification_sent_at: Date | null;
  elo_rating: number;
  karma: number;
  skills_graph: any | null;
  total_challenges_completed: number;
  streak_count: number;
  last_active_date: Date | null;
  referred_by: number | null;
  failed_login_attempts: number;
  account_locked_until: Date | null;
  last_password_change: Date | null;
  daily_llm_token_quota: number;
  daily_llm_token_usage: number;
  quota_reset_date: Date;
  created_at: Date;
  updated_at: Date;
  last_login: Date | null;
  preferred_ide: string | null;
  learning_style: string | null;
  developer_strengths: any[];
  preferred_ai_models: any[];
  additional_info: string | null;
  onboarding_completed: boolean;
  onboarding_completed_at: Date | null;
};

export class UserRepository extends BaseRepository<User> {
  constructor() {
    super('user');
  }

  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  /**
   * Find a user by Clerk ID
   */
  async findByClerkId(clerkId: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { clerkId },
    });
  }

  /**
   * Find a user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { username },
    });
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id: number): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data: {
        lastLogin: new Date(),
      },
    });
  }

  /**
   * Get users by role
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return this.prisma.user.findMany({
      where: { role },
    });
  }

  /**
   * Create a user session
   */
  async createSession(
    userId: number,
    sessionToken: string,
    expiresAt: Date,
    ipAddress?: string,
    userAgent?: string,
    deviceInfo?: any,
  ): Promise<any> {
    return this.prisma.userSession.create({
      data: {
        userId,
        sessionToken,
        expiresAt,
        ipAddress,
        userAgent,
        deviceInfo,
        lastActivity: new Date(),
      },
    });
  }

  /**
   * Find session by token
   */
  async findSessionByToken(sessionToken: string): Promise<any | null> {
    return this.prisma.userSession.findUnique({
      where: { sessionToken },
      include: { user: true },
    });
  }

  /**
   * Invalidate all user sessions
   */
  async invalidateAllSessions(userId: number): Promise<number> {
    const result = await this.prisma.userSession.updateMany({
      where: { userId },
      data: { isActive: false },
    });
    return result.count;
  }

  /**
   * Reset a user's daily token usage quota
   */
  async resetDailyQuota(userId: number): Promise<any> {
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        dailyLlmTokenUsage: 0,
        quotaResetDate: new Date(),
      },
    });
  }

  /**
   * Check if a user has enough tokens available
   */
  async hasAvailableTokens(userId: number, requestedTokens: number): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        dailyLlmTokenUsage: true,
        dailyLlmTokenQuota: true,
        quotaResetDate: true,
      },
    });

    if (!user) return false;

    // Check if quota needs reset (new day)
    const today = new Date();
    const resetDate = user.quotaResetDate;

    if (resetDate && resetDate.getDate() !== today.getDate()) {
      await this.resetDailyQuota(userId);
      return requestedTokens <= user.dailyLlmTokenQuota;
    }

    // Otherwise check current usage against quota
    return user.dailyLlmTokenUsage + requestedTokens <= user.dailyLlmTokenQuota;
  }

  /**
   * Record token usage against a user's quota
   */
  async useTokens(userId: number, tokenCount: number): Promise<number> {
    // Ensure quota is reset if needed first
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        dailyLlmTokenUsage: true,
        quotaResetDate: true,
      },
    });

    if (!user) throw new Error('User not found');

    // Check if quota needs reset (new day)
    const today = new Date();
    const resetDate = user.quotaResetDate;

    if (resetDate && resetDate.getDate() !== today.getDate()) {
      await this.resetDailyQuota(userId);

      // Set usage to tokenCount (instead of adding to previous day's usage)
      const updated = await this.prisma.user.update({
        where: { id: userId },
        data: { dailyLlmTokenUsage: tokenCount },
        select: { dailyLlmTokenUsage: true },
      });

      return updated.dailyLlmTokenUsage;
    }

    // Otherwise add to current usage
    const updated = await this.prisma.user.update({
      where: { id: userId },
      data: {
        dailyLlmTokenUsage: { increment: tokenCount },
      },
      select: { dailyLlmTokenUsage: true },
    });

    return updated.dailyLlmTokenUsage;
  }
}

// Export a singleton instance
export const userRepository = new UserRepository();
