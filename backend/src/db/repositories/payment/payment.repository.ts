/**
 * Payment repository for handling payment-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for payment operations
 */
@injectable()
export class PaymentRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find payment by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.payments.findUnique({
        where: { id },
        include: {
          users: true,
          payment_refunds: true,
          subscription_payments: true,
        },
      });
    } catch (error) {
      logger.error(`Error finding payment with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find payments by user ID
   */
  async findByUserId(userId: number, options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.payments.findMany({
        where: { user_id: userId },
        include: {
          payment_refunds: true,
          subscription_payments: true,
        },
        orderBy: { created_at: 'desc' },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding payments for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new payment
   */
  async create(data: {
    userId: number;
    amount: number;
    currency?: string;
    paymentMethod: string;
    paymentProvider: string;
    providerPaymentId?: string;
    status?: string;
    description?: string;
    metadata?: any;
  }) {
    try {
      return await this.prisma.payments.create({
        data: {
          user_id: data.userId,
          amount: data.amount,
          currency: data.currency || 'USD',
          payment_method: data.paymentMethod,
          payment_provider: data.paymentProvider,
          provider_payment_id: data.providerPaymentId,
          status: data.status || 'pending',
          description: data.description,
          metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error('Error creating payment:', error);
      throw error;
    }
  }

  /**
   * Update payment status
   */
  async updateStatus(id: number, status: string, completedAt?: Date) {
    try {
      return await this.prisma.payments.update({
        where: { id },
        data: {
          status,
          completed_at: completedAt,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating payment status for ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a payment refund
   */
  async createRefund(data: {
    paymentId: number;
    amount: number;
    reason?: string;
    status?: string;
    providerRefundId?: string;
    refundedByUserId?: number;
  }) {
    try {
      return await this.prisma.payment_refunds.create({
        data: {
          payment_id: data.paymentId,
          amount: data.amount,
          reason: data.reason,
          status: data.status || 'pending',
          provider_refund_id: data.providerRefundId,
          refunded_by_user_id: data.refundedByUserId,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error creating refund for payment ${data.paymentId}:`, error);
      throw error;
    }
  }

  /**
   * Update refund status
   */
  async updateRefundStatus(id: number, status: string, completedAt?: Date) {
    try {
      return await this.prisma.payment_refunds.update({
        where: { id },
        data: {
          status,
          completed_at: completedAt,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating refund status for ID ${id}:`, error);
      throw error;
    }
  }
}
