/**
 * Subscription repository for handling subscription-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for subscription operations
 */
@injectable()
export class SubscriptionRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find subscription by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.subscriptions.findUnique({
        where: { id },
        include: {
          users: true,
          subscription_payments: {
            include: {
              payments: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error finding subscription with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find active subscription by user ID
   */
  async findActiveByUserId(userId: number) {
    try {
      return await this.prisma.subscriptions.findFirst({
        where: {
          user_id: userId,
          status: 'active',
        },
        include: {
          subscription_payments: {
            include: {
              payments: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error finding active subscription for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Find all subscriptions by user ID
   */
  async findByUserId(userId: number) {
    try {
      return await this.prisma.subscriptions.findMany({
        where: { user_id: userId },
        include: {
          subscription_payments: {
            include: {
              payments: true,
            },
          },
        },
        orderBy: { created_at: 'desc' },
      });
    } catch (error) {
      logger.error(`Error finding subscriptions for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new subscription
   */
  async create(data: {
    userId: number;
    planName: string;
    planId: string;
    status?: string;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    cancelAtPeriodEnd?: boolean;
    provider: string;
    providerSubscriptionId?: string;
    metadata?: any;
  }) {
    try {
      return await this.prisma.subscriptions.create({
        data: {
          user_id: data.userId,
          plan_name: data.planName,
          plan_id: data.planId,
          status: data.status || 'active',
          current_period_start: data.currentPeriodStart,
          current_period_end: data.currentPeriodEnd,
          cancel_at_period_end: data.cancelAtPeriodEnd || false,
          provider: data.provider,
          provider_subscription_id: data.providerSubscriptionId,
          metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error creating subscription for user ${data.userId}:`, error);
      throw error;
    }
  }

  /**
   * Update subscription
   */
  async update(
    id: number,
    data: {
      status?: string;
      currentPeriodStart?: Date;
      currentPeriodEnd?: Date;
      cancelAtPeriodEnd?: boolean;
      canceledAt?: Date;
      endedAt?: Date;
      metadata?: any;
    },
  ) {
    try {
      return await this.prisma.subscriptions.update({
        where: { id },
        data: {
          ...(data.status && { status: data.status }),
          ...(data.currentPeriodStart && { current_period_start: data.currentPeriodStart }),
          ...(data.currentPeriodEnd && { current_period_end: data.currentPeriodEnd }),
          ...(data.cancelAtPeriodEnd !== undefined && {
            cancel_at_period_end: data.cancelAtPeriodEnd,
          }),
          ...(data.canceledAt && { canceled_at: data.canceledAt }),
          ...(data.endedAt && { ended_at: data.endedAt }),
          ...(data.metadata && { metadata: JSON.stringify(data.metadata) }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating subscription ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create subscription payment link
   */
  async createSubscriptionPayment(data: {
    subscriptionId: number;
    paymentId: number;
    billingPeriodStart: Date;
    billingPeriodEnd: Date;
  }) {
    try {
      return await this.prisma.subscription_payments.create({
        data: {
          subscription_id: data.subscriptionId,
          payment_id: data.paymentId,
          billing_period_start: data.billingPeriodStart,
          billing_period_end: data.billingPeriodEnd,
          created_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(
        `Error creating subscription payment link for subscription ${data.subscriptionId} and payment ${data.paymentId}:`,
        error,
      );
      throw error;
    }
  }
}
