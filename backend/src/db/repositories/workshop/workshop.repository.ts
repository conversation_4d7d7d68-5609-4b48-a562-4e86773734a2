import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { PrismaClient } from '../../../generated/prisma';
import { CreateWorkshopDto, UpdateWorkshopDto } from '../../../api/workshop/dto/workshop.dto';
import {
  CreateModuleDto,
  UpdateModuleDto,
  CreateLessonDto,
  UpdateLessonDto,
  UpdateProgressDto,
  CompleteLessonDto,
} from '../../../api/workshop/dto/module.dto';

@injectable()
export class WorkshopRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async createWorkshop(data: CreateWorkshopDto) {
    return this.prisma.workshop.create({
      data: {
        name: data.name,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        maxParticipants: data.maxParticipants,
      },
    });
  }

  async updateWorkshop(workshopId: number, data: UpdateWorkshopDto) {
    return this.prisma.workshop.update({
      where: { id: workshopId },
      data: {
        name: data.name,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        maxParticipants: data.maxParticipants,
      },
    });
  }

  async findWorkshopById(workshopId: number) {
    return this.prisma.workshop.findUnique({
      where: { id: workshopId },
      include: {
        modules: {
          include: {
            lessons: true,
          },
        },
        participants: true,
      },
    });
  }

  async findAllWorkshops() {
    return this.prisma.workshop.findMany({
      include: {
        modules: {
          include: {
            lessons: true,
          },
        },
        participants: true,
      },
    });
  }

  async deleteWorkshop(workshopId: number) {
    return this.prisma.workshop.delete({
      where: { id: workshopId },
    });
  }

  async addParticipant(workshopId: number, userId: number) {
    return this.prisma.workshopParticipant.create({
      data: {
        workshopId,
        userId,
        status: 'ENROLLED',
      },
    });
  }

  async removeParticipant(workshopId: number, userId: number) {
    return this.prisma.workshopParticipant.delete({
      where: {
        workshopId_userId: {
          workshopId,
          userId,
        },
      },
    });
  }

  async getParticipants(workshopId: number) {
    return this.prisma.workshopParticipant.findMany({
      where: { workshopId },
      include: {
        user: true,
      },
    });
  }

  async createModule(workshopId: number, data: CreateModuleDto) {
    return this.prisma.module.create({
      data: {
        workshopId,
        title: data.title,
        description: data.description,
        orderIndex: data.orderIndex,
      },
    });
  }

  async updateModule(moduleId: number, data: UpdateModuleDto) {
    return this.prisma.module.update({
      where: { id: moduleId },
      data: {
        title: data.title,
        description: data.description,
        orderIndex: data.orderIndex,
      },
    });
  }

  async getModules(workshopId: number) {
    return this.prisma.module.findMany({
      where: { workshopId },
      include: {
        lessons: {
          orderBy: {
            orderIndex: 'asc',
          },
        },
      },
      orderBy: {
        orderIndex: 'asc',
      },
    });
  }

  async createLesson(moduleId: number, data: CreateLessonDto) {
    return this.prisma.lesson.create({
      data: {
        moduleId,
        title: data.title,
        content: data.content,
        type: data.type,
        orderIndex: data.orderIndex,
        codeSnippet: data.codeSnippet,
        codeLanguage: data.codeLanguage,
      },
    });
  }

  async updateLesson(lessonId: number, data: UpdateLessonDto) {
    return this.prisma.lesson.update({
      where: { id: lessonId },
      data: {
        title: data.title,
        content: data.content,
        type: data.type,
        orderIndex: data.orderIndex,
        codeSnippet: data.codeSnippet,
        codeLanguage: data.codeLanguage,
      },
    });
  }

  async getLessons(moduleId: number) {
    return this.prisma.lesson.findMany({
      where: { moduleId },
      orderBy: {
        orderIndex: 'asc',
      },
    });
  }

  async updateProgress(
    workshopId: number,
    userId: number,
    moduleId: number,
    data: UpdateProgressDto,
  ) {
    return this.prisma.moduleProgress.upsert({
      where: {
        workshopId_userId_moduleId: {
          workshopId,
          userId,
          moduleId,
        },
      },
      create: {
        workshopId,
        userId,
        moduleId,
        completionPercentage: data.completionPercentage,
        notes: data.notes,
      },
      update: {
        completionPercentage: data.completionPercentage,
        notes: data.notes,
      },
    });
  }

  async completeLesson(lessonId: number, userId: number, data: CompleteLessonDto) {
    return this.prisma.lessonCompletion.create({
      data: {
        lessonId,
        userId,
        score: data.score,
        feedback: data.feedback,
      },
    });
  }

  async getProgress(workshopId: number, userId: number) {
    const [moduleProgress, lessonCompletions] = await Promise.all([
      this.prisma.moduleProgress.findMany({
        where: {
          workshopId,
          userId,
        },
        include: {
          module: true,
        },
      }),
      this.prisma.lessonCompletion.findMany({
        where: {
          lesson: {
            module: {
              workshopId,
            },
          },
          userId,
        },
        include: {
          lesson: true,
        },
      }),
    ]);

    return {
      moduleProgress,
      lessonCompletions,
    };
  }
}
