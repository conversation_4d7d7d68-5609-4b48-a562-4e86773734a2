import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import {
  CreateModuleDto,
  UpdateModuleDto,
  CreateLessonDto,
  UpdateLessonDto,
  RecordLessonCompletionDto,
  LessonType,
} from '../../../api/workshop/dto/module-lesson.dto';

@injectable()
export class ModuleRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async createModule(data: CreateModuleDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).module.create({
      data: {
        title: data.title,
        description: data.description,
        orderIndex: data.orderIndex || 0,
        workshopId: data.workshopId,
      },
    });
  }

  async updateModule(id: number, data: UpdateModuleDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).module.update({
      where: { id },
      data: {
        ...(data.title !== undefined && { title: data.title }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.orderIndex !== undefined && { orderIndex: data.orderIndex }),
      },
    });
  }

  async deleteModule(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).module.delete({
      where: { id },
    });
  }

  async getModulesByWorkshop(workshopId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).module.findMany({
      where: {
        workshopId,
      },
      include: {
        lessons: {
          orderBy: {
            orderIndex: 'asc',
          },
        },
      },
      orderBy: {
        orderIndex: 'asc',
      },
    });
  }

  async getModuleById(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).module.findUnique({
      where: { id },
      include: {
        lessons: {
          orderBy: {
            orderIndex: 'asc',
          },
        },
      },
    });
  }

  async createLesson(data: CreateLessonDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lesson.create({
      data: {
        title: data.title,
        description: data.description,
        content: data.content,
        lessonType: data.lessonType || LessonType.TEXT,
        durationMinutes: data.durationMinutes || 30,
        orderIndex: data.orderIndex || 0,
        moduleId: data.moduleId,
      },
    });
  }

  async updateLesson(id: number, data: UpdateLessonDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lesson.update({
      where: { id },
      data: {
        ...(data.title !== undefined && { title: data.title }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.content !== undefined && { content: data.content }),
        ...(data.lessonType !== undefined && { lessonType: data.lessonType }),
        ...(data.durationMinutes !== undefined && { durationMinutes: data.durationMinutes }),
        ...(data.orderIndex !== undefined && { orderIndex: data.orderIndex }),
      },
    });
  }

  async deleteLesson(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lesson.delete({
      where: { id },
    });
  }

  async getLessonById(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lesson.findUnique({
      where: { id },
      include: {
        module: true,
      },
    });
  }

  async recordLessonCompletion(data: RecordLessonCompletionDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lessonCompletion.upsert({
      where: {
        userId_lessonId: {
          userId: data.userId,
          lessonId: data.lessonId,
        },
      },
      update: {
        completedAt: new Date(),
        ...(data.score !== undefined && { score: data.score }),
        ...(data.feedback !== undefined && { feedback: data.feedback }),
      },
      create: {
        userId: data.userId,
        lessonId: data.lessonId,
        score: data.score,
        feedback: data.feedback,
      },
    });
  }

  async getLessonCompletions(userId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lessonCompletion.findMany({
      where: {
        userId,
      },
      include: {
        lesson: {
          include: {
            module: {
              include: {
                workshop: true,
              },
            },
          },
        },
      },
    });
  }

  async getUserLessonCompletions(userId: number, lessonId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).lessonCompletion.findUnique({
      where: {
        userId_lessonId: {
          userId,
          lessonId,
        },
      },
    });
  }
}
