import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { CreatePromptTemplateDto, UpdatePromptTemplateDto } from '../../../api/ai/dto';

@injectable()
export class AIPromptTemplateRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async create(data: CreatePromptTemplateDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        systemPrompt: data.systemPrompt,
        template: data.template,
        parameters: data.parameters || [],
        userId: data.userId,
        isPublic: data.isPublic !== undefined ? data.isPublic : false,
      },
    });
  }

  async findById(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.findUnique({
      where: { id },
      include: {
        user: true,
      },
    });
  }

  async findAll(filters?: { userId?: number; isPublic?: boolean }) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.findMany({
      where: {
        ...(filters?.userId && { userId: filters.userId }),
        ...(filters?.isPublic !== undefined && { isPublic: filters.isPublic }),
      },
      include: {
        user: true,
      },
    });
  }

  async update(id: number, data: UpdatePromptTemplateDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.update({
      where: { id },
      data: {
        ...(data.name !== undefined && { name: data.name }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.systemPrompt !== undefined && { systemPrompt: data.systemPrompt }),
        ...(data.template !== undefined && { template: data.template }),
        ...(data.parameters !== undefined && { parameters: data.parameters }),
        ...(data.userId !== undefined && { userId: data.userId }),
        ...(data.isPublic !== undefined && { isPublic: data.isPublic }),
        version: { increment: 1 },
      },
    });
  }

  async delete(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.delete({
      where: { id },
    });
  }

  async findByName(name: string) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.findFirst({
      where: { name },
    });
  }

  async findPublicTemplates() {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIPromptTemplate.findMany({
      where: { isPublic: true },
      orderBy: { name: 'asc' },
    });
  }
}
