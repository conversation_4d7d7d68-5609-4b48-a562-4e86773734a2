import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { CreateAIAgentDto, UpdateAIAgentDto, AIAgentActionDto } from '../../../api/ai/dto';

@injectable()
export class AIAgentRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async create(data: CreateAIAgentDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgent.create({
      data: {
        name: data.name,
        description: data.description,
        provider: data.provider,
        modelName: data.modelName,
        systemPrompt: data.systemPrompt,
        temperature: data.temperature || 0.7,
        maxTokens: data.maxTokens || 4000,
        capabilities: data.capabilities || [],
        dailyTokenLimit: data.dailyTokenLimit || 100000,
        monthlyTokenLimit: data.monthlyTokenLimit,
        costPerInputToken: data.costPerInputToken || 0.0,
        costPerOutputToken: data.costPerOutputToken || 0.0,
        userId: data.userId,
        projectId: data.projectId,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });
  }

  async findById(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgent.findUnique({
      where: { id },
      include: {
        user: true,
        project: true,
        actions: true,
        conversations: true,
      },
    });
  }

  async findAll(filters?: { userId?: number; projectId?: number; isActive?: boolean }) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgent.findMany({
      where: {
        ...(filters?.userId && { userId: filters.userId }),
        ...(filters?.projectId && { projectId: filters.projectId }),
        ...(filters?.isActive !== undefined && { isActive: filters.isActive }),
      },
      include: {
        user: true,
        project: true,
      },
    });
  }

  async update(id: number, data: UpdateAIAgentDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgent.update({
      where: { id },
      data: {
        ...(data.name !== undefined && { name: data.name }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.provider !== undefined && { provider: data.provider }),
        ...(data.modelName !== undefined && { modelName: data.modelName }),
        ...(data.systemPrompt !== undefined && { systemPrompt: data.systemPrompt }),
        ...(data.temperature !== undefined && { temperature: data.temperature }),
        ...(data.maxTokens !== undefined && { maxTokens: data.maxTokens }),
        ...(data.capabilities !== undefined && { capabilities: data.capabilities }),
        ...(data.dailyTokenLimit !== undefined && { dailyTokenLimit: data.dailyTokenLimit }),
        ...(data.monthlyTokenLimit !== undefined && { monthlyTokenLimit: data.monthlyTokenLimit }),
        ...(data.costPerInputToken !== undefined && { costPerInputToken: data.costPerInputToken }),
        ...(data.costPerOutputToken !== undefined && {
          costPerOutputToken: data.costPerOutputToken,
        }),
        ...(data.userId !== undefined && { userId: data.userId }),
        ...(data.projectId !== undefined && { projectId: data.projectId }),
        ...(data.isActive !== undefined && { isActive: data.isActive }),
      },
    });
  }

  async delete(id: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgent.delete({
      where: { id },
    });
  }

  async recordAction(data: AIAgentActionDto) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgentAction.create({
      data: {
        agentId: data.agentId,
        actionType: data.actionType,
        input: data.input,
        output: data.output,
        executionTime: data.executionTime,
        status: data.status || 'completed',
        errorMessage: data.errorMessage,
        metadata: data.metadata,
      },
    });
  }

  async getAgentActions(agentId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).AIAgentAction.findMany({
      where: { agentId },
      orderBy: { createdAt: 'desc' },
    });
  }
}
