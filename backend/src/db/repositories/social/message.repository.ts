/**
 * Social message repository for handling message-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for social message operations
 */
@injectable()
export class SocialMessageRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find message by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.social_messages.findUnique({
        where: { id },
        include: {
          users: true,
          channels: true,
          message_reactions: {
            include: {
              users: true,
            },
          },
          social_messages: {
            include: {
              users: true,
            },
          },
          other_social_messages: {
            include: {
              users: true,
            },
            orderBy: {
              created_at: 'asc',
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error finding message with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find messages by channel ID
   */
  async findByChannelId(
    channelId: number,
    options: {
      skip?: number;
      take?: number;
      parentOnly?: boolean;
    } = {},
  ) {
    try {
      const where: any = { channel_id: channelId };

      // If parentOnly is true, only return top-level messages (not replies)
      if (options.parentOnly) {
        where.parent_id = null;
      }

      return await this.prisma.social_messages.findMany({
        where,
        include: {
          users: true,
          message_reactions: {
            include: {
              users: true,
            },
          },
          _count: {
            select: {
              other_social_messages: true,
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding messages for channel ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Find replies to a message
   */
  async findReplies(parentId: number, options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.social_messages.findMany({
        where: { parent_id: parentId },
        include: {
          users: true,
          message_reactions: {
            include: {
              users: true,
            },
          },
        },
        orderBy: {
          created_at: 'asc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding replies for message ${parentId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new message
   */
  async create(data: {
    content: string;
    channelId: number;
    authorId: number;
    parentId?: number;
    attachments?: string[];
  }) {
    try {
      const message = await this.prisma.social_messages.create({
        data: {
          content: data.content,
          channel_id: data.channelId,
          user_id: data.authorId,
          parent_id: data.parentId,
          metadata: data.attachments ? { attachments: data.attachments } : undefined,
          created_at: new Date(),
        },
        include: {
          users: true,
          channels: true,
        },
      });

      // Update channel's updatedAt timestamp
      await this.prisma.channels.update({
        where: { id: data.channelId },
        data: { updated_at: new Date() },
      });

      return message;
    } catch (error) {
      logger.error('Error creating message:', error);
      throw error;
    }
  }

  /**
   * Update a message
   */
  async update(
    id: number,
    data: {
      content?: string;
      attachments?: string[];
    },
  ) {
    try {
      return await this.prisma.social_messages.update({
        where: { id },
        data: {
          ...(data.content !== undefined && { content: data.content }),
          ...(data.attachments !== undefined && { metadata: { attachments: data.attachments } }),
          is_edited: true,
          updated_at: new Date(),
        },
        include: {
          users: true,
          channels: true,
          message_reactions: {
            include: {
              users: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error updating message with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a message
   */
  async delete(id: number) {
    try {
      return await this.prisma.social_messages.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting message with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a reaction to a message
   */
  async addReaction(messageId: number, userId: number, emoji: string) {
    try {
      // Check if user has already reacted with this emoji
      const existingReaction = await this.prisma.message_reactions.findFirst({
        where: {
          message_id: messageId,
          user_id: userId,
          reaction_type: emoji,
        },
      });

      if (existingReaction) {
        return existingReaction;
      }

      return await this.prisma.message_reactions.create({
        data: {
          message_id: messageId,
          user_id: userId,
          reaction_type: emoji,
          created_at: new Date(),
        },
        include: {
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error adding reaction to message ${messageId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a reaction from a message
   */
  async removeReaction(messageId: number, userId: number, emoji: string) {
    try {
      return await this.prisma.message_reactions.deleteMany({
        where: {
          message_id: messageId,
          user_id: userId,
          reaction_type: emoji,
        },
      });
    } catch (error) {
      logger.error(`Error removing reaction from message ${messageId}:`, error);
      throw error;
    }
  }

  /**
   * Get reactions for a message
   */
  async getReactions(messageId: number) {
    try {
      return await this.prisma.message_reactions.findMany({
        where: { message_id: messageId },
        include: {
          users: true,
        },
        orderBy: {
          created_at: 'asc',
        },
      });
    } catch (error) {
      logger.error(`Error getting reactions for message ${messageId}:`, error);
      throw error;
    }
  }
}
