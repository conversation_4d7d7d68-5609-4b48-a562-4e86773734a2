/**
 * Notification repository for handling notification-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for notification operations
 */
@injectable()
export class NotificationRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find notification by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.notifications.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error finding notification with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find notifications for a user
   */
  async findByUserId(
    userId: number,
    options: {
      skip?: number;
      take?: number;
      unreadOnly?: boolean;
    } = {},
  ) {
    try {
      const where = { user_id: userId } as { user_id: number; is_read?: boolean };

      if (options.unreadOnly) {
        where.is_read = false;
      }

      return await this.prisma.notifications.findMany({
        where,
        orderBy: {
          created_at: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Count unread notifications for a user
   */
  async countUnread(userId: number) {
    try {
      return await this.prisma.notifications.count({
        where: {
          user_id: userId,
          is_read: false,
        },
      });
    } catch (error) {
      logger.error(`Error counting unread notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new notification
   */
  async create(data: {
    userId: number;
    type: string;
    content: string;
    sourceId?: number;
    sourceType?: string;
    actorId?: number;
  }) {
    try {
      return await this.prisma.notifications.create({
        data: {
          user_id: data.userId,
          type: data.type,
          message: data.content,
          related_entity_id: data.sourceId,
          related_entity_type: data.sourceType,
          // actor_id: data.actorId, // Field not in schema
          created_at: new Date(),
          is_read: false,
          title: data.type, // Required field
        },
      });
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(id: number) {
    try {
      return await this.prisma.notifications.update({
        where: { id },
        data: {
          is_read: true,
          // read_at: new Date() // Field not in schema
        },
      });
    } catch (error) {
      logger.error(`Error marking notification ${id} as read:`, error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: number) {
    try {
      return await this.prisma.notifications.updateMany({
        where: {
          user_id: userId,
          is_read: false,
        },
        data: {
          is_read: true,
          // read_at: new Date() // Field not in schema
        },
      });
    } catch (error) {
      logger.error(`Error marking all notifications as read for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a notification
   */
  async delete(id: number) {
    try {
      return await this.prisma.notifications.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting notification ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete all notifications for a user
   */
  async deleteAll(userId: number) {
    try {
      return await this.prisma.notifications.deleteMany({
        where: { user_id: userId },
      });
    } catch (error) {
      logger.error(`Error deleting all notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create a channel mention notification
   */
  async createChannelMentionNotification(data: {
    mentionedUserId: number;
    messageId: number;
    channelId: number;
    authorId: number;
    channelName: string;
  }) {
    try {
      return await this.create({
        userId: data.mentionedUserId,
        type: 'CHANNEL_MENTION',
        content: `You were mentioned in channel ${data.channelName}`,
        sourceId: data.messageId,
        sourceType: 'SOCIAL_MESSAGE',
        actorId: data.authorId,
      });
    } catch (error) {
      logger.error('Error creating channel mention notification:', error);
      throw error;
    }
  }

  /**
   * Create a message reply notification
   */
  async createMessageReplyNotification(data: {
    parentAuthorId: number;
    messageId: number;
    channelId: number;
    authorId: number;
    channelName: string;
  }) {
    try {
      // Don't notify if the author is replying to their own message
      if (data.parentAuthorId === data.authorId) {
        return null;
      }

      return await this.create({
        userId: data.parentAuthorId,
        type: 'MESSAGE_REPLY',
        content: `Someone replied to your message in ${data.channelName}`,
        sourceId: data.messageId,
        sourceType: 'SOCIAL_MESSAGE',
        actorId: data.authorId,
      });
    } catch (error) {
      logger.error('Error creating message reply notification:', error);
      throw error;
    }
  }

  /**
   * Create a reaction notification
   */
  async createReactionNotification(data: {
    messageAuthorId: number;
    messageId: number;
    channelId: number;
    reactorId: number;
    emoji: string;
    channelName: string;
  }) {
    try {
      // Don't notify if the author is reacting to their own message
      if (data.messageAuthorId === data.reactorId) {
        return null;
      }

      return await this.create({
        userId: data.messageAuthorId,
        type: 'MESSAGE_REACTION',
        content: `Someone reacted to your message with ${data.emoji} in ${data.channelName}`,
        sourceId: data.messageId,
        sourceType: 'SOCIAL_MESSAGE',
        actorId: data.reactorId,
      });
    } catch (error) {
      logger.error('Error creating reaction notification:', error);
      throw error;
    }
  }
}
