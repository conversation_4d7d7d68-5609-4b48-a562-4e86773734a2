/**
 * Channel repository for handling channel-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for channel operations
 */
@injectable()
export class ChannelRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find channel by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.channels.findUnique({
        where: { id },
        include: {
          channel_members: {
            include: {
              users: true,
            },
          },
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error finding channel with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find channels by user ID (channels the user is a member of)
   */
  async findByUserId(userId: number, options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.channels.findMany({
        where: {
          channel_members: {
            some: {
              user_id: userId,
            },
          },
        },
        include: {
          channel_members: {
            include: {
              users: true,
            },
          },
          users: true,
          _count: {
            select: {
              social_messages: true,
            },
          },
        },
        orderBy: {
          updated_at: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding channels for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Find public channels
   */
  async findPublicChannels(options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.channels.findMany({
        where: {
          is_private: false,
        },
        include: {
          users: true,
          _count: {
            select: {
              channel_members: true,
              social_messages: true,
            },
          },
        },
        orderBy: {
          updated_at: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error('Error finding public channels:', error);
      throw error;
    }
  }

  /**
   * Create a new channel
   */
  async create(data: {
    name: string;
    description?: string;
    ownerId: number;
    isPrivate?: boolean;
    memberIds?: number[];
  }) {
    try {
      // Always include the owner as a member
      const uniqueMemberIds = [...new Set([data.ownerId, ...(data.memberIds || [])])];

      return await this.prisma.channels.create({
        data: {
          name: data.name,
          description: data.description,
          creator_id: data.ownerId,
          is_private: data.isPrivate || false,
          created_at: new Date(),
          updated_at: new Date(),
          channel_members: {
            create: uniqueMemberIds.map((userId) => ({
              user_id: userId,
              joined_at: new Date(),
            })),
          },
        },
        include: {
          channel_members: {
            include: {
              users: true,
            },
          },
          users: true,
        },
      });
    } catch (error) {
      logger.error('Error creating channel:', error);
      throw error;
    }
  }

  /**
   * Update a channel
   */
  async update(
    id: number,
    data: {
      name?: string;
      description?: string;
      isPrivate?: boolean;
    },
  ) {
    try {
      return await this.prisma.channels.update({
        where: { id },
        data: {
          ...(data.name !== undefined && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.isPrivate !== undefined && { is_private: data.isPrivate }),
          updated_at: new Date(),
        },
        include: {
          channel_members: {
            include: {
              users: true,
            },
          },
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error updating channel with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a channel
   */
  async delete(id: number) {
    try {
      return await this.prisma.channels.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting channel with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a member to a channel
   */
  async addMember(channelId: number, userId: number) {
    try {
      // Check if user is already a member
      const existingMember = await this.prisma.channel_members.findFirst({
        where: {
          channel_id: channelId,
          user_id: userId,
        },
      });

      if (existingMember) {
        return existingMember;
      }

      return await this.prisma.channel_members.create({
        data: {
          channel_id: channelId,
          user_id: userId,
          joined_at: new Date(),
        },
        include: {
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error adding user ${userId} to channel ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a member from a channel
   */
  async removeMember(channelId: number, userId: number) {
    try {
      return await this.prisma.channel_members.deleteMany({
        where: {
          channel_id: channelId,
          user_id: userId,
        },
      });
    } catch (error) {
      logger.error(`Error removing user ${userId} from channel ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Check if user is a member of a channel
   */
  async isMember(channelId: number, userId: number) {
    try {
      const member = await this.prisma.channel_members.findFirst({
        where: {
          channel_id: channelId,
          user_id: userId,
        },
      });

      return !!member;
    } catch (error) {
      logger.error(`Error checking if user ${userId} is a member of channel ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Check if user is the owner of a channel
   */
  async isOwner(channelId: number, userId: number) {
    try {
      const channel = await this.prisma.channels.findUnique({
        where: { id: channelId },
        select: { creator_id: true },
      });

      return channel?.creator_id === userId;
    } catch (error) {
      logger.error(`Error checking if user ${userId} is the owner of channel ${channelId}:`, error);
      throw error;
    }
  }
}
