import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';

@injectable()
export class EloHistoryRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async recordEloChange(data: {
    userId: number;
    previous_rating: number;
    new_rating: number;
    change: number;
    reason: string;
    programming_session_id?: number;
    opponent_id?: number;
  }) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).elo_history.create({
      data: {
        userId: data.userId,
        previous_rating: data.previous_rating,
        new_rating: data.new_rating,
        change: data.change,
        reason: data.reason,
        programming_session_id: data.programming_session_id,
        opponent_id: data.opponent_id,
      },
    });
  }

  async getUserEloHistory(userId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).elo_history.findMany({
      where: {
        userId,
      },
      orderBy: {
        timestamp: 'desc',
      },
      include: {
        opponent: true,
        programming_session: true,
      },
    });
  }

  async calculateNewElo(
    current_rating: number,
    opponent_rating: number,
    outcome: number, // 1 for win, 0.5 for draw, 0 for loss
  ) {
    const kFactor = 32; // Standard K-factor for ELO
    const expected_outcome = 1 / (1 + Math.pow(10, (opponent_rating - current_rating) / 400));
    const rating_change = Math.round(kFactor * (outcome - expected_outcome));
    const new_rating = current_rating + rating_change;

    return {
      previous_rating: current_rating,
      new_rating,
      change: rating_change,
    };
  }

  async getLeaderboard(limit = 10) {
    return this.prisma.users.findMany({
      select: {
        id: true,
        username: true,
        first_name: true,
        last_name: true,
        profile_image_url: true,
        elo_rating: true,
      },
      orderBy: {
        elo_rating: 'desc',
      },
      take: limit,
    });
  }

  async getUserRank(userId: number) {
    // Get the user's elo
    const user = await this.prisma.users.findUnique({
      where: { id: userId },
      select: { elo_rating: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Count how many users have higher elo
    const higher_ranked = await this.prisma.users.count({
      where: {
        elo_rating: {
          gt: user.elo_rating,
        },
      },
    });

    // Rank is this count + 1 (1-indexed)
    return higher_ranked + 1;
  }
}
