import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';

@injectable()
export class KarmaRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  async recordKarmaTransaction(data: {
    userId: number;
    amount: number;
    reason: string;
    relatedEntityType?: string;
    relatedEntityId?: number;
  }) {
    const transaction = await this.prisma.$transaction(async (tx: any) => {
      // Create the transaction record
      const transaction = await tx.karmaTransaction.create({
        data: {
          userId: data.userId,
          amount: data.amount,
          reason: data.reason,
          relatedEntityType: data.relatedEntityType,
          relatedEntityId: data.relatedEntityId,
        },
      });

      // Update the user's karma balance
      await tx.user.update({
        where: { id: data.userId },
        data: {
          karma: {
            increment: data.amount,
          },
        },
      });

      return transaction;
    });

    return transaction;
  }

  async getUserKarmaHistory(userId: number) {
    // @ts-ignore - Prisma client doesn't recognize the model
    return (this.prisma as any).karmaTransaction.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getUserKarmaBalance(userId: number) {
    const user = await this.prisma.users.findUnique({
      where: { id: userId },
      select: { karma: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    return user.karma;
  }

  async getKarmaLeaderboard(limit = 10) {
    return this.prisma.users.findMany({
      select: {
        id: true,
        username: true,
        first_name: true,
        last_name: true,
        profile_image_url: true,
        karma: true,
      },
      orderBy: {
        karma: 'desc',
      },
      take: limit,
    });
  }
}
