/**
 * Base repository with common CRUD operations
 */
import { prisma } from '../client';

// Define PrismaClient type to avoid import errors
type PrismaClient = any;

export abstract class BaseRepository<T> {
  protected prisma: PrismaClient;
  protected model: string;

  constructor(model: string) {
    this.prisma = prisma;
    this.model = model;
  }

  // Helper method to dynamically access Prisma models
  protected get modelClient(): any {
    return this.prisma[this.model as keyof typeof prisma] as any;
  }

  /**
   * Find a single record by ID
   */
  async findById(id: number): Promise<T | null> {
    // Validate that id is a valid number
    if (id === null || id === undefined || isNaN(Number(id))) {
      throw new Error(`Invalid ID provided to findById: ${id} (type: ${typeof id})`);
    }
    
    return this.modelClient.findUnique({
      where: { id: Number(id) },
    });
  }

  /**
   * Find all records
   */
  async findAll(options: { skip?: number; take?: number } = {}): Promise<T[]> {
    return this.modelClient.findMany({
      skip: options.skip,
      take: options.take,
    });
  }

  /**
   * Create a new record
   */
  async create(data: Partial<T>): Promise<T> {
    return this.modelClient.create({
      data,
    });
  }

  /**
   * Update a record
   */
  async update(id: number, data: Partial<T>): Promise<T> {
    return this.modelClient.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a record
   */
  async delete(id: number): Promise<T> {
    return this.modelClient.delete({
      where: { id },
    });
  }

  /**
   * Count all records matching a filter
   */
  async count(where: Record<string, any> = {}): Promise<number> {
    return this.modelClient.count({
      where: where,
    });
  }

  /**
   * Find records with pagination and filters
   */
  async findMany(params: {
    where?: Record<string, any>;
    orderBy?: Record<string, any>;
    skip?: number;
    take?: number;
    include?: Record<string, any>;
    select?: Record<string, any>;
  }): Promise<T[]> {
    return this.modelClient.findMany(params);
  }
}
