/**
 * Project repository for database operations related to projects
 */
import {
  PrismaClient,
  projects as Project,
  ProjectType,
  ProjectMotivationType,
} from '../../generated/prisma';
import { BaseRepository } from './base.repository';

export class ProjectRepository extends BaseRepository<Project> {
  constructor() {
    super('projects');
  }

  /**
   * Find projects by user ID
   */
  async findByUserId(userId: number): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
    });
  }

  /**
   * Find projects by type
   */
  async findByType(projectType: ProjectType): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: { projectType },
    });
  }

  /**
   * Find projects by motivation type
   */
  async findByMotivation(motivation: ProjectMotivationType): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: { projectMotivation: motivation },
    });
  }

  /**
   * Find active projects
   */
  async findActiveProjects(): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: { isActive: true },
    });
  }

  /**
   * Find public projects
   */
  async findPublicProjects(): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: { isPublic: true },
    });
  }

  /**
   * Get project with complete relationship data
   */
  async getProjectWithDetails(id: number): Promise<any> {
    return this.prisma.projects.findUnique({
      where: { id },
      include: {
        user: true,
        project_objectives: true,
        project_tech_stacks: true,
        project_git_repos: true,
        project_slides: true,
        project_tests: true,
        project_quality_metrics: true,
        project_documentation: true,
        project_code_quality: true,
        project_dependencies: true,
        project_visualizations: true,
        tasks: true,
      },
    });
  }

  /**
   * Create a new project with initial objectives
   */
  async createWithObjectives(
    projectData: Omit<Project, 'objectives'>,
    objectives: Array<{ title: string; description?: string; priority?: number }>,
  ): Promise<Project> {
    return this.prisma.project.create({
      data: {
        ...projectData,
        objectives: {
          create: objectives,
        },
      },
      include: {
        objectives: true,
      },
    });
  }

  /**
   * Create a tech stack for a project
   */
  async addTechStack(
    projectId: number,
    technology: string,
    category?: string,
    version?: string,
  ): Promise<any> {
    return this.prisma.techStack.create({
      data: {
        projectId,
        technology,
        category,
        version,
      },
    });
  }

  /**
   * Get a project's feature state based on motivation
   */
  async getFeatureState(id: number): Promise<Record<string, boolean>> {
    const project = await this.prisma.project.findUnique({
      where: { id },
      select: { projectMotivation: true },
    });

    if (!project || !project.projectMotivation) {
      return {};
    }

    // Default feature matrix based on project motivation
    const featureMatrix: Record<string, Record<string, boolean>> = {
      learner: {
        explain_code: true,
        refactor_suggestion: false,
        generate_docs: true,
        generate_tests: true,
        sync_check: false,
        progress_quizzes: true,
        git_changelog: false,
        timeline_view: false,
      },
      contributor: {
        explain_code: true,
        refactor_suggestion: true,
        generate_docs: false,
        generate_tests: false,
        sync_check: false,
        progress_quizzes: false,
        git_changelog: true,
        timeline_view: false,
      },
      builder: {
        explain_code: true,
        refactor_suggestion: true,
        generate_docs: true,
        generate_tests: true,
        sync_check: true,
        progress_quizzes: false,
        git_changelog: true,
        timeline_view: true,
      },
    };

    const motivationKey = project.projectMotivation.toLowerCase();

    return featureMatrix[motivationKey] || {};
  }

  /**
   * Check if a specific feature is enabled for this project
   */
  async isFeatureEnabled(projectId: number, featureName: string): Promise<boolean> {
    const featureState = await this.getFeatureState(projectId);
    return featureState[featureName] || false;
  }

  /**
   * Get component model client
   * Provides access to project component models
   */
  getComponentModel(modelName: string): any {
    // Define valid model names and their corresponding Prisma client properties
    const validModels = [
      'project_objectives',
      'project_tech_stacks',
      'project_git_repos',
      'project_slides',
      'project_tests',
      'project_quality_metrics',
      'project_documentation',
      'project_code_quality',
      'project_dependencies',
      'project_visualizations',
    ];

    if (!validModels.includes(modelName)) {
      throw new Error(`Invalid component model: ${modelName}`);
    }

    // Use a type assertion to access the Prisma client
    // This is safe because we've validated the model name
    switch (modelName) {
      case 'project_objectives':
        return this.prisma.project_objectives;
      case 'project_tech_stacks':
        return this.prisma.project_tech_stacks;
      case 'project_git_repos':
        return this.prisma.project_git_repos;
      case 'project_slides':
        return this.prisma.project_slides;
      case 'project_tests':
        return this.prisma.project_tests;
      case 'project_quality_metrics':
        return this.prisma.project_quality_metrics;
      case 'project_documentation':
        return this.prisma.project_documentation;
      case 'project_code_quality':
        return this.prisma.project_code_quality;
      case 'project_dependencies':
        return this.prisma.project_dependencies;
      case 'project_visualizations':
        return this.prisma.project_visualizations;
      default:
        // This should never happen due to the validation above
        throw new Error(`Invalid component model: ${modelName}`);
    }
  }
}

// Export a singleton instance
export const projectRepository = new ProjectRepository();
