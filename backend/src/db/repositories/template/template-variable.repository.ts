import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for template variable operations
 */
@injectable()
export class TemplateVariableRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find template variable by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.template_variables.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error finding template variable with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all variables for a template
   */
  async findByTemplateId(templateId: number) {
    try {
      return await this.prisma.template_variables.findMany({
        where: { template_id: templateId },
        orderBy: { name: 'asc' },
      });
    } catch (error) {
      logger.error(`Error finding variables for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Update a template variable
   */
  async update(
    id: number,
    data: {
      name?: string;
      defaultValue?: string;
      description?: string;
      required?: boolean;
      variableType?: string;
      options?: any;
    },
  ) {
    try {
      return await this.prisma.template_variables.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.defaultValue !== undefined && { default_value: data.defaultValue }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.required !== undefined && { required: data.required }),
          ...(data.variableType && { variable_type: data.variableType }),
          ...(data.options && { options: data.options }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating template variable with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template variable
   */
  async delete(id: number) {
    try {
      return await this.prisma.template_variables.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting template variable with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find a variable by template ID and name
   */
  async findByName(templateId: number, name: string) {
    try {
      return await this.prisma.template_variables.findFirst({
        where: {
          template_id: templateId,
          name,
        },
      });
    } catch (error) {
      logger.error(`Error finding variable by name for template ${templateId}:`, error);
      throw error;
    }
  }
}
