import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for template collection operations
 */
@injectable()
export class TemplateCollectionRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Create a new template collection
   */
  async create(data: {
    name: string;
    description?: string;
    isPublic?: boolean;
    creatorId?: number;
  }) {
    try {
      return await this.prisma.template_collections.create({
        data: {
          name: data.name,
          description: data.description,
          is_public: data.isPublic !== undefined ? data.isPublic : false,
          creator_id: data.creatorId,
        },
      });
    } catch (error) {
      logger.error('Error creating template collection:', error);
      throw error;
    }
  }

  /**
   * Find template collection by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.template_collections.findUnique({
        where: { id },
        include: {
          users: true,
          collection_templates: {
            include: {
              templates: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error finding template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all template collections with optional filters
   */
  async findAll(filters?: { creatorId?: number; isPublic?: boolean }) {
    try {
      return await this.prisma.template_collections.findMany({
        where: {
          ...(filters?.creatorId && { creator_id: filters.creatorId }),
          ...(filters?.isPublic !== undefined && { is_public: filters.isPublic }),
        },
        include: {
          users: true,
          collection_templates: {
            include: {
              templates: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error finding template collections:', error);
      throw error;
    }
  }

  /**
   * Update a template collection
   */
  async update(
    id: number,
    data: {
      name?: string;
      description?: string;
      isPublic?: boolean;
    },
  ) {
    try {
      return await this.prisma.template_collections.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.isPublic !== undefined && { is_public: data.isPublic }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template collection
   */
  async delete(id: number) {
    try {
      return await this.prisma.template_collections.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting template collection with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a template to a collection
   */
  async addTemplate(collectionId: number, templateId: number) {
    try {
      return await this.prisma.collection_templates.create({
        data: {
          collection_id: collectionId,
          template_id: templateId,
        },
      });
    } catch (error) {
      logger.error(`Error adding template ${templateId} to collection ${collectionId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a template from a collection
   */
  async removeTemplate(collectionId: number, templateId: number) {
    try {
      return await this.prisma.collection_templates.delete({
        where: {
          collection_id_template_id: {
            collection_id: collectionId,
            template_id: templateId,
          },
        },
      });
    } catch (error) {
      logger.error(`Error removing template ${templateId} from collection ${collectionId}:`, error);
      throw error;
    }
  }
}
