import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for template file operations
 */
@injectable()
export class TemplateFileRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find template file by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.template_files.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error finding template file with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all files for a template
   */
  async findByTemplateId(templateId: number) {
    try {
      return await this.prisma.template_files.findMany({
        where: { template_id: templateId },
        orderBy: { path: 'asc' },
      });
    } catch (error) {
      logger.error(`Error finding files for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Update a template file
   */
  async update(
    id: number,
    data: {
      path?: string;
      content?: string;
      isExecutable?: boolean;
      isBinary?: boolean;
    },
  ) {
    try {
      return await this.prisma.template_files.update({
        where: { id },
        data: {
          ...(data.path && { path: data.path }),
          ...(data.content && { content: data.content }),
          ...(data.isExecutable !== undefined && { is_executable: data.isExecutable }),
          ...(data.isBinary !== undefined && { is_binary: data.isBinary }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating template file with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template file
   */
  async delete(id: number) {
    try {
      return await this.prisma.template_files.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting template file with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find a file by template ID and path
   */
  async findByPath(templateId: number, path: string) {
    try {
      return await this.prisma.template_files.findFirst({
        where: {
          template_id: templateId,
          path,
        },
      });
    } catch (error) {
      logger.error(`Error finding file by path for template ${templateId}:`, error);
      throw error;
    }
  }
}
