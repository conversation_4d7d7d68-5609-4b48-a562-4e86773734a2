import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for project template operations
 */
@injectable()
export class ProjectTemplateRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Create a new project template
   */
  async create(data: {
    name: string;
    description?: string;
    structure: any;
    defaultBranch?: string;
    creatorId?: number;
    isPublic?: boolean;
  }) {
    try {
      return await this.prisma.project_templates.create({
        data: {
          name: data.name,
          description: data.description,
          structure: data.structure,
          default_branch: data.defaultBranch || 'main',
          creator_id: data.creatorId,
          is_public: data.isPublic !== undefined ? data.isPublic : false,
        },
      });
    } catch (error) {
      logger.error('Error creating project template:', error);
      throw error;
    }
  }

  /**
   * Find project template by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.project_templates.findUnique({
        where: { id },
        include: {
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error finding project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all project templates with optional filters
   */
  async findAll(filters?: { creatorId?: number; isPublic?: boolean }) {
    try {
      return await this.prisma.project_templates.findMany({
        where: {
          ...(filters?.creatorId && { creator_id: filters.creatorId }),
          ...(filters?.isPublic !== undefined && { is_public: filters.isPublic }),
        },
        include: {
          users: true,
        },
      });
    } catch (error) {
      logger.error('Error finding project templates:', error);
      throw error;
    }
  }

  /**
   * Update a project template
   */
  async update(
    id: number,
    data: {
      name?: string;
      description?: string;
      structure?: any;
      defaultBranch?: string;
      isPublic?: boolean;
    },
  ) {
    try {
      return await this.prisma.project_templates.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.structure && { structure: data.structure }),
          ...(data.defaultBranch && { default_branch: data.defaultBranch }),
          ...(data.isPublic !== undefined && { is_public: data.isPublic }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a project template
   */
  async delete(id: number) {
    try {
      return await this.prisma.project_templates.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting project template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Increment usage count for a project template
   */
  async incrementUsageCount(id: number) {
    try {
      return await this.prisma.project_templates.update({
        where: { id },
        data: {
          usage_count: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      logger.error(`Error incrementing usage count for project template ${id}:`, error);
      throw error;
    }
  }
}
