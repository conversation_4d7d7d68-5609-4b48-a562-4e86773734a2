import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for template operations
 */
@injectable()
export class TemplateRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Create a new template
   */
  async create(data: {
    name: string;
    description?: string;
    category: string;
    version?: string;
    isPublic?: boolean;
    creatorId?: number;
    thumbnailUrl?: string;
    metadata?: any;
  }) {
    try {
      return await this.prisma.templates.create({
        data: {
          name: data.name,
          description: data.description,
          category: data.category,
          version: data.version || '1.0.0',
          is_public: data.isPublic !== undefined ? data.isPublic : false,
          creator_id: data.creatorId,
          thumbnail_url: data.thumbnailUrl,
          metadata: data.metadata || {},
        },
      });
    } catch (error) {
      logger.error('Error creating template:', error);
      throw error;
    }
  }

  /**
   * Find template by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.templates.findUnique({
        where: { id },
        include: {
          template_files: true,
          template_variables: true,
          template_to_tags: {
            include: {
              template_tags: true,
            },
          },
          users: true,
        },
      });
    } catch (error) {
      logger.error(`Error finding template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all templates with optional filters
   */
  async findAll(filters?: { creatorId?: number; isPublic?: boolean; category?: string }) {
    try {
      return await this.prisma.templates.findMany({
        where: {
          ...(filters?.creatorId && { creator_id: filters.creatorId }),
          ...(filters?.isPublic !== undefined && { is_public: filters.isPublic }),
          ...(filters?.category && { category: filters.category }),
        },
        include: {
          users: true,
          template_to_tags: {
            include: {
              template_tags: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error finding templates:', error);
      throw error;
    }
  }

  /**
   * Update a template
   */
  async update(
    id: number,
    data: {
      name?: string;
      description?: string;
      category?: string;
      version?: string;
      isPublic?: boolean;
      thumbnailUrl?: string;
      metadata?: any;
    },
  ) {
    try {
      return await this.prisma.templates.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.category && { category: data.category }),
          ...(data.version && { version: data.version }),
          ...(data.isPublic !== undefined && { is_public: data.isPublic }),
          ...(data.thumbnailUrl !== undefined && { thumbnail_url: data.thumbnailUrl }),
          ...(data.metadata && { metadata: data.metadata }),
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error(`Error updating template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a template
   */
  async delete(id: number) {
    try {
      return await this.prisma.templates.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting template with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Add a file to a template
   */
  async addFile(
    templateId: number,
    data: {
      path: string;
      content: string;
      isExecutable?: boolean;
      isBinary?: boolean;
    },
  ) {
    try {
      return await this.prisma.template_files.create({
        data: {
          template_id: templateId,
          path: data.path,
          content: data.content,
          is_executable: data.isExecutable || false,
          is_binary: data.isBinary || false,
        },
      });
    } catch (error) {
      logger.error(`Error adding file to template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Add a variable to a template
   */
  async addVariable(
    templateId: number,
    data: {
      name: string;
      defaultValue?: string;
      description?: string;
      required?: boolean;
      variableType?: string;
      options?: any;
    },
  ) {
    try {
      return await this.prisma.template_variables.create({
        data: {
          template_id: templateId,
          name: data.name,
          default_value: data.defaultValue,
          description: data.description,
          required: data.required || false,
          variable_type: data.variableType || 'string',
          options: data.options || {},
        },
      });
    } catch (error) {
      logger.error(`Error adding variable to template ${templateId}:`, error);
      throw error;
    }
  }
}
