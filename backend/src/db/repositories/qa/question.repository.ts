/**
 * Question repository for handling question-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for question operations
 */
@injectable()
export class QuestionRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find question by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.questions.findUnique({
        where: { id },
        include: {
          users: true,
          answers: {
            include: {
              users: true,
              answer_votes: true,
            },
            orderBy: {
              is_accepted: 'desc',
            },
          },
          question_votes: true,
          question_tags: {
            include: {
              tags: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error finding question with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find questions with optional filtering
   */
  async findAll(
    options: {
      skip?: number;
      take?: number;
      resolved?: boolean;
      authorId?: number;
      tagIds?: number[];
    } = {},
  ) {
    try {
      const where: any = {};

      if (options.resolved !== undefined) {
        where.is_resolved = options.resolved;
      }

      if (options.authorId !== undefined) {
        where.author_id = options.authorId;
      }

      if (options.tagIds && options.tagIds.length > 0) {
        where.tags = {
          some: {
            id: {
              in: options.tagIds,
            },
          },
        };
      }

      return await this.prisma.questions.findMany({
        where,
        include: {
          users: true,
          _count: {
            select: {
              answers: true,
              question_votes: true,
            },
          },
          question_tags: {
            include: {
              tags: true,
            },
          },
        },
        orderBy: {
          timestamp: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error('Error finding questions:', error);
      throw error;
    }
  }

  /**
   * Create a new question
   */
  async create(data: {
    title: string;
    content: string;
    authorId: number;
    codeSnippet?: string;
    tagIds?: number[];
  }) {
    try {
      return await this.prisma.questions.create({
        data: {
          title: data.title,
          content: data.content,
          author_id: data.authorId,
          code_snippet: data.codeSnippet,
          timestamp: new Date(),
          question_tags:
            data.tagIds && data.tagIds.length > 0
              ? {
                  create: data.tagIds.map((id) => ({
                    tags: {
                      connect: { id },
                    },
                  })),
                }
              : undefined,
        },
        include: {
          users: true,
          question_tags: {
            include: {
              tags: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error creating question:', error);
      throw error;
    }
  }

  /**
   * Update a question
   */
  async update(
    id: number,
    data: {
      content?: string;
      codeSnippet?: string;
      isResolved?: boolean;
      tagIds?: number[];
    },
  ) {
    try {
      const updateData: any = {
        ...(data.content !== undefined && { content: data.content }),
        ...(data.codeSnippet !== undefined && { code_snippet: data.codeSnippet }),
        ...(data.isResolved !== undefined && { is_resolved: data.isResolved }),
      };

      // Handle tag updates if provided
      if (data.tagIds) {
        // First disconnect all existing tags
        await this.prisma.questions.update({
          where: { id },
          data: {
            question_tags: {
              deleteMany: {},
            },
          },
        });

        // Then connect the new tags
        updateData.question_tags = {
          create: data.tagIds.map((id) => ({
            tags: {
              connect: { id },
            },
          })),
        };
      }

      return await this.prisma.questions.update({
        where: { id },
        data: updateData,
        include: {
          users: true,
          question_tags: {
            include: {
              tags: true,
            },
          },
          answers: {
            include: {
              users: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error updating question with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a question
   */
  async delete(id: number) {
    try {
      return await this.prisma.questions.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting question with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Vote on a question
   */
  async vote(questionId: number, userId: number, value: number) {
    try {
      // Check if user has already voted
      const existingVote = await this.prisma.question_votes.findFirst({
        where: {
          question_id: questionId,
          user_id: userId,
        },
      });

      if (existingVote) {
        // Update existing vote
        return await this.prisma.question_votes.update({
          where: {
            id: existingVote.id,
          },
          data: {
            vote_value: value,
          },
        });
      } else {
        // Create new vote
        return await this.prisma.question_votes.create({
          data: {
            question_id: questionId,
            user_id: userId,
            vote_value: value,
          },
        });
      }
    } catch (error) {
      logger.error(`Error voting on question ${questionId}:`, error);
      throw error;
    }
  }
}
