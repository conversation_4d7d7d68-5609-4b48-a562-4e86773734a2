/**
 * Tag repository for handling tag-related database operations
 */
import { PrismaClient } from '../../../generated/prisma';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

/**
 * Repository for tag operations
 */
@injectable()
export class TagRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find tag by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.tags.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error finding tag with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find tag by name
   */
  async findByName(name: string) {
    try {
      return await this.prisma.tags.findFirst({
        where: { name: { equals: name, mode: 'insensitive' } },
      });
    } catch (error) {
      logger.error(`Error finding tag with name ${name}:`, error);
      throw error;
    }
  }

  /**
   * Find all tags
   */
  async findAll(options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.tags.findMany({
        orderBy: {
          name: 'asc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error('Error finding tags:', error);
      throw error;
    }
  }

  /**
   * Create a new tag
   */
  async create(data: { name: string; description?: string }) {
    try {
      return await this.prisma.tags.create({
        data: {
          name: data.name,
          description: data.description,
        },
      });
    } catch (error) {
      logger.error('Error creating tag:', error);
      throw error;
    }
  }

  /**
   * Update a tag
   */
  async update(
    id: number,
    data: {
      name?: string;
      description?: string;
    },
  ) {
    try {
      return await this.prisma.tags.update({
        where: { id },
        data: {
          ...(data.name !== undefined && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
        },
      });
    } catch (error) {
      logger.error(`Error updating tag with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a tag
   */
  async delete(id: number) {
    try {
      return await this.prisma.tags.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting tag with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find or create tags by names
   */
  async findOrCreateByNames(names: string[]): Promise<number[]> {
    try {
      const tagIds: number[] = [];

      for (const name of names) {
        // Try to find existing tag
        let tag = await this.findByName(name);

        // Create if not exists
        if (!tag) {
          tag = await this.create({ name });
        }

        tagIds.push(tag.id);
      }

      return tagIds;
    } catch (error) {
      logger.error('Error finding or creating tags:', error);
      throw error;
    }
  }

  /**
   * Get popular tags with question count
   */
  async getPopularTags(limit: number = 10) {
    try {
      return await this.prisma.tags.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          _count: {
            select: {
              question_tags: true,
            },
          },
        },
        orderBy: {
          question_tags: {
            _count: 'desc',
          },
        },
        take: limit,
      });
    } catch (error) {
      logger.error('Error getting popular tags:', error);
      throw error;
    }
  }
}
