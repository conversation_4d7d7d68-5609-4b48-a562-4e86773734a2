/**
 * Answer repository for handling answer-related database operations
 */
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { DatabaseService } from '../../database.service';
import { logger } from '../../../common/logger';

// Use any type to avoid import errors
type PrismaClient = any;

/**
 * Repository for answer operations
 */
@injectable()
export class AnswerRepository {
  private prisma: PrismaClient;

  constructor(@inject(TYPES.DatabaseService) databaseService: DatabaseService) {
    this.prisma = databaseService.getClient();
  }

  /**
   * Find answer by ID
   */
  async findById(id: number) {
    try {
      return await this.prisma.answer.findUnique({
        where: { id },
        include: {
          author: true,
          question: true,
          votes: true,
        },
      });
    } catch (error) {
      logger.error(`Error finding answer with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find answers for a question
   */
  async findByQuestionId(questionId: number, options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.answer.findMany({
        where: { questionId },
        include: {
          author: true,
          votes: true,
        },
        orderBy: [{ isAccepted: 'desc' }, { timestamp: 'desc' }],
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding answers for question ${questionId}:`, error);
      throw error;
    }
  }

  /**
   * Find answers by user ID
   */
  async findByAuthorId(authorId: number, options: { skip?: number; take?: number } = {}) {
    try {
      return await this.prisma.answer.findMany({
        where: { authorId },
        include: {
          author: true,
          question: true,
          votes: true,
        },
        orderBy: {
          timestamp: 'desc',
        },
        skip: options.skip,
        take: options.take,
      });
    } catch (error) {
      logger.error(`Error finding answers for author ${authorId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new answer
   */
  async create(data: {
    content: string;
    questionId: number;
    authorId: number;
    codeSnippet?: string;
  }) {
    try {
      return await this.prisma.answer.create({
        data: {
          content: data.content,
          questionId: data.questionId,
          authorId: data.authorId,
          codeSnippet: data.codeSnippet,
          timestamp: new Date(),
        },
        include: {
          author: true,
          question: true,
        },
      });
    } catch (error) {
      logger.error('Error creating answer:', error);
      throw error;
    }
  }

  /**
   * Update an answer
   */
  async update(
    id: number,
    data: {
      content?: string;
      codeSnippet?: string;
    },
  ) {
    try {
      return await this.prisma.answer.update({
        where: { id },
        data: {
          ...(data.content !== undefined && { content: data.content }),
          ...(data.codeSnippet !== undefined && { codeSnippet: data.codeSnippet }),
        },
        include: {
          author: true,
          question: true,
        },
      });
    } catch (error) {
      logger.error(`Error updating answer with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an answer
   */
  async delete(id: number) {
    try {
      return await this.prisma.answer.delete({
        where: { id },
      });
    } catch (error) {
      logger.error(`Error deleting answer with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Mark an answer as accepted
   */
  async accept(id: number) {
    try {
      const answer = await this.prisma.answer.findUnique({
        where: { id },
        select: { questionId: true },
      });

      if (!answer) {
        throw new Error(`Answer with ID ${id} not found`);
      }

      // First, unmark any previously accepted answers for this question
      await this.prisma.answer.updateMany({
        where: {
          questionId: answer.questionId,
          isAccepted: true,
        },
        data: {
          isAccepted: false,
        },
      });

      // Then mark this answer as accepted
      const updatedAnswer = await this.prisma.answer.update({
        where: { id },
        data: {
          isAccepted: true,
        },
        include: {
          author: true,
          question: true,
        },
      });

      // Also mark the question as resolved
      await this.prisma.question.update({
        where: { id: answer.questionId },
        data: {
          isResolved: true,
        },
      });

      return updatedAnswer;
    } catch (error) {
      logger.error(`Error accepting answer with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Vote on an answer
   */
  async vote(answerId: number, userId: number, value: number) {
    try {
      // Check if user has already voted
      const existingVote = await this.prisma.answerVote.findFirst({
        where: {
          answerId,
          userId,
        },
      });

      if (existingVote) {
        // Update existing vote
        return await this.prisma.answerVote.update({
          where: {
            id: existingVote.id,
          },
          data: {
            value,
          },
        });
      } else {
        // Create new vote
        return await this.prisma.answerVote.create({
          data: {
            answerId,
            userId,
            value,
          },
        });
      }
    } catch (error) {
      logger.error(`Error voting on answer ${answerId}:`, error);
      throw error;
    }
  }
}
