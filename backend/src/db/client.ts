import { logger } from '../common/logger';
import { PrismaClient } from '../generated/prisma';

// Define types for Prisma event handlers
type PrismaEventTypes = 'query' | 'info' | 'warn' | 'error';

interface QueryEvent {
  timestamp: Date;
  query: string;
  params: string;
  duration: number;
  target: string;
}

interface LogEvent {
  timestamp: Date;
  message: string;
  target: string;
}

// Create a singleton instance of PrismaClient
const prismaClientSingleton = () => {
  return new PrismaClient({
    log:
      process.env.NODE_ENV === 'development'
        ? [
            { emit: 'event', level: 'query' },
            { emit: 'event', level: 'error' },
            { emit: 'event', level: 'info' },
            { emit: 'event', level: 'warn' },
          ]
        : ['error'],
  });
};

// Use `global` to maintain a cached connection across hot reloads in development
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
export const prisma = globalForPrisma.prisma || prismaClientSingleton();

// Add type definitions to make TypeScript happy
type ExtendedPrismaClient = PrismaClient & {
  $on(eventType: 'query', callback: (event: QueryEvent) => void): void;
  $on(eventType: 'info' | 'warn' | 'error', callback: (event: LogEvent) => void): void;
};

// Event listeners for logging in development mode
if (process.env.NODE_ENV === 'development') {
  (prisma as ExtendedPrismaClient).$on('query', (e: QueryEvent) => {
    //logger.debug(`Query: ${e.query}`);
    logger.debug(`Duration: ${e.duration}ms`);
  });

  (prisma as ExtendedPrismaClient).$on('error', (e: LogEvent) => {
    logger.error(`Prisma error: ${e.message}`);
  });

  (prisma as ExtendedPrismaClient).$on('info', (e: LogEvent) => {
    logger.info(`Prisma info: ${e.message}`);
  });

  (prisma as ExtendedPrismaClient).$on('warn', (e: LogEvent) => {
    logger.warn(`Prisma warning: ${e.message}`);
  });
}

// Prevent multiple instances of Prisma Client in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export default prisma;
