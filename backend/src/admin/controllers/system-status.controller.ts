import { Request, Response } from 'express';
import { logger } from '../../common/logger';

// Mock data for system components
const systemComponents = [
  {
    name: 'Conversation',
    components: 8,
    uptime: 99.91,
    status: 'operational',
    history: generateMockHistory(90, 0.95),
  },
  {
    name: 'Conversation Tasks',
    components: 5,
    uptime: 99.55,
    status: 'operational',
    history: generateMockHistory(90, 0.93),
  },
  {
    name: 'Azure',
    components: 3,
    uptime: 100.0,
    status: 'operational',
    history: generateMockHistory(90, 0.98),
  },
  {
    name: 'AWS',
    components: 4,
    uptime: 99.97,
    status: 'operational',
    history: generateMockHistory(90, 0.97),
  },
  {
    name: 'Gemini',
    components: 2,
    uptime: 99.92,
    status: 'operational',
    history: generateMockHistory(90, 0.96),
  },
];

// Generate mock history data for the last 90 days
function generateMockHistory(days: number, reliability: number): string[] {
  const statuses = ['operational', 'degraded', 'outage'];
  const weights = [reliability, (1 - reliability) * 0.8, (1 - reliability) * 0.2];

  return Array.from({ length: days }, () => {
    const random = Math.random();
    let statusIndex = 0;

    let cumulativeWeight = 0;
    for (let i = 0; i < weights.length; i++) {
      cumulativeWeight += weights[i];
      if (random <= cumulativeWeight) {
        statusIndex = i;
        break;
      }
    }

    return statuses[statusIndex];
  });
}

/**
 * Get system status data
 */
export const getSystemStatus = (_req: Request, res: Response) => {
  try {
    // Check if there are any non-operational components
    const hasIssues = systemComponents.some((component) => component.status !== 'operational');

    res.json({
      status: 'success',
      data: {
        overall: {
          status: hasIssues ? 'degraded' : 'operational',
          message: hasIssues
            ? "We're experiencing some issues with our systems."
            : "We're not aware of any issues affecting our systems.",
        },
        components: systemComponents,
        timeRange: {
          start: 'Aug 2023',
          end: 'Nov 2023',
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching system status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching system status',
    });
  }
};
