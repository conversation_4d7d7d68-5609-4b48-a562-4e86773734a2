import { Request, Response } from 'express';
import { prisma } from '../../db/client';
import { logger } from '../../common/logger';

/**
 * Process admin login
 */
export async function login(req: Request, res: Response) {
  try {
    const { email, password } = req.body;
    const redirect = req.query.redirect || '/admin';

    // Validate input
    if (!email || !password) {
      return res.render('auth/login.html', {
        title: 'Admin Login',
        error: 'Email and password are required',
      });
    }

    // Check environment-based admin credentials
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const ownerEmail = process.env.OWNER_EMAIL;
    const ownerPassword = process.env.OWNER_PASSWORD;

    // Verify against environment credentials
    let isValidAdmin = false;
    let isOwner = false;
    
    if (email === adminEmail && password === adminPassword) {
      isValidAdmin = true;
    } else if (ownerEmail && ownerPassword && email === ownerEmail && password === ownerPassword) {
      isValidAdmin = true;
      isOwner = true;
    }

    if (isValidAdmin) {
      // Create admin user in session based on login
      (req.session as any).admin_user = {
        id: isOwner ? 3 : 1,
        email: email,
        username: isOwner ? email.split('@')[0] : 'admin',
        role: 'ADMIN',
      };

      logger.info('Created admin user in session for:', email.replace(/(.{3}).*@/, '$1***@'));
      logger.info(
        `Session data (sample): ${JSON.stringify(req.session || {}).substring(0, 100)}...`,
      );

      // Save the session explicitly before redirecting
      req.session.save((err) => {
        if (err) {
          logger.error('Error saving session:', err);
          return res.render('auth/login.html', {
            title: 'Admin Login',
            error: 'Error saving session',
          });
        }

        logger.info('Session saved successfully, redirecting to:', redirect);

        // Redirect to dashboard or requested page
        return res.redirect(redirect as string);
      });
      return; // Exit the function here to prevent further execution
    }

    // In a real app, you would verify credentials properly
    try {
      const user = await prisma.users.findFirst({
        where: {
          email,
          role: 'ADMIN',
        },
      });

      if (user) {
        // In a real app, you would verify the password here
        // For now, we'll just accept any password for existing admin users

        // Store user in session
        (req.session as any).admin_user = {
          id: user.id,
          email: user.email,
          username: user.username || user.email.split('@')[0],
          role: user.role,
        };

        logger.info(`Created admin user in session for ${user.email.replace(/(.{3}).*@/, '$1***@')}`);
        logger.info(
          `Session data (sample): ${JSON.stringify(req.session || {}).substring(0, 100)}...`,
        );

        // Save the session explicitly before redirecting
        req.session.save((err) => {
          if (err) {
            logger.error('Error saving session:', err);
            return res.render('auth/login.html', {
              title: 'Admin Login',
              error: 'Error saving session',
            });
          }

          logger.info('Session saved successfully, redirecting to:', redirect);

          // Redirect to dashboard or requested page
          return res.redirect(redirect as string);
        });
        return; // Exit the function here to prevent further execution
      }
    } catch (dbError) {
      logger.error('Database error during login:', dbError);
    }

    // If we get here, authentication failed
    return res.render('auth/login.html', {
      title: 'Admin Login',
      error: 'Invalid email or password',
    });
  } catch (error) {
    logger.error('Error processing login:', error);
    return res.render('auth/login.html', {
      title: 'Admin Login',
      error: 'An error occurred during login',
    });
  }
}

/**
 * Process admin logout
 */
export async function logout(req: Request, res: Response) {
  try {
    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        logger.error('Error destroying session:', err);
      }

      // Redirect to login page
      res.redirect('/admin/login');
    });
  } catch (error) {
    logger.error('Error processing logout:', error);
    res.redirect('/admin/login');
  }
}
