import { Request, Response } from 'express';
import { prisma } from '../../db/client';
import { logger } from '../../common/logger';

/**
 * Get a list of all database tables
 */
export async function getAllTables(req: Request, res: Response) {
  try {
    // Get query parameters for sorting and filtering
    const { sort_by, sort_order, search } = req.query;

    // Build the SQL query to get basic table info
    let query = `
      SELECT
        table_name as name,
        (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) AS column_count,
        (SELECT COUNT(*) FROM pg_indexes WHERE tablename = t.table_name) AS index_count,
        (
          SELECT COUNT(*)
          FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
          WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = t.table_name
        ) AS foreign_key_count,
        0 AS row_count, -- We'll update this with accurate counts later
        (
          SELECT array_agg(column_name)::text[]
          FROM information_schema.columns
          WHERE table_name = t.table_name
        ) AS columns,
        (
          SELECT array_agg(ccu.column_name)::text[]
          FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
          WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = t.table_name
        ) AS primary_keys
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
    `;

    // Add search filter if provided
    if (search) {
      query += ` AND table_name ILIKE '%${search}%'`;
    }

    // Add sorting (except for row_count which we'll handle after fetching the data)
    const validSortColumns = [
      'name',
      'column_count',
      'row_count',
      'foreign_key_count',
      'index_count',
    ];
    const sortColumn = validSortColumns.includes(sort_by as string) ? sort_by : 'name';
    const sortDir = (sort_order as string)?.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    // For row_count, we'll sort the results after fetching the actual counts
    // For other columns, sort in the SQL query
    if (sortColumn !== 'row_count') {
      query += ` ORDER BY ${sortColumn} ${sortDir}`;
    } else {
      // Use a default sort for the initial query
      query += ` ORDER BY name ASC`;
    }

    // Execute the query to get basic table info
    const tables = await prisma.$queryRawUnsafe(query);

    // Get accurate row counts for each table
    logger.info('Getting accurate row counts for tables');
    for (const table of tables as any[]) {
      try {
        // Only count rows for tables that are likely to have a reasonable number of rows
        // Skip large tables like logs, events, etc.
        const skipLargeCount = ['logs', 'events', 'audit_logs', 'analytics'].some((pattern) =>
          table.name.includes(pattern),
        );

        if (skipLargeCount) {
          logger.info(`Skipping row count for potentially large table: ${table.name}`);
          table.row_count = 1000; // Use a reasonable default for sorting
          table.row_count_display = '> 1000'; // Use a string to indicate it's an estimate
          continue;
        }

        // Use a COUNT query with a timeout to get accurate row counts
        const countResult = await Promise.race([
          prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${table.name}"`),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Count query timeout')), 1000),
          ),
        ]);

        if (Array.isArray(countResult) && countResult.length > 0) {
          const count = parseInt(countResult[0].count, 10);
          table.row_count = count; // Numeric value for sorting
          table.row_count_display = count.toString(); // String value for display
          logger.info(`Updated row count for ${table.name}: ${table.row_count}`);
        }
      } catch (countError) {
        // If the count query times out or fails, use an estimate
        logger.warn(`Error getting row count for ${table.name}:`, countError);
        table.row_count = 1000; // Use a reasonable default for sorting
        table.row_count_display = '> 1000'; // Use a string to indicate it's an estimate
      }
    }

    // Sort by row_count if requested (now that we have the actual counts)
    if (sortColumn === 'row_count') {
      (tables as any[]).sort((a, b) => {
        const aCount = typeof a.row_count === 'number' ? a.row_count : 0;
        const bCount = typeof b.row_count === 'number' ? b.row_count : 0;
        return sortDir.toLowerCase() === 'asc' ? aCount - bCount : bCount - aCount;
      });
    }

    // Format the response for the API
    if (req.path.startsWith('/api/')) {
      return res.status(200).json({
        success: true,
        data: tables,
      });
    }

    // For UI routes, render the template with the data
    return res.render('admin/tables/table_list.html', {
      title: 'Database Tables',
      tables,
      sort_by: sortColumn,
      sort_order: sortDir.toLowerCase(),
      search: search || '',
      refreshed_at: new Date().toLocaleString(),
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching database tables:', error);

    // For API routes
    if (req.path.startsWith('/api/')) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch database tables',
      });
    }

    // For UI routes
    return res.status(500).render('error.html', {
      title: 'Error',
      error: 'Failed to fetch database tables',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}

/**
 * Get details of a specific table
 */
export async function getTableDetails(req: Request, res: Response) {
  try {
    const { tableName } = req.params;

    // Validate table name to prevent SQL injection
    const validTableName = /^[a-zA-Z0-9_]+$/.test(tableName);
    if (!validTableName) {
      if (req.path.startsWith('/api/')) {
        return res.status(400).json({
          success: false,
          error: 'Invalid table name',
        });
      } else {
        return res.status(400).render('error.html', {
          title: 'Error',
          error: 'Invalid table name',
          admin_user: (req.session as any)?.admin_user || null,
          request: {
            url: {
              path: req.path,
            },
          },
        });
      }
    }

    // Get table columns
    const columns = await prisma.$queryRaw`
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns
      WHERE table_name = ${tableName}
      ORDER BY ordinal_position
    `;

    // Get table indexes
    const indexes = await prisma.$queryRaw`
      SELECT
        indexname,
        indexdef
      FROM pg_indexes
      WHERE tablename = ${tableName}
    `;

    // Get foreign keys
    const foreignKeys = await prisma.$queryRaw`
      SELECT
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = ${tableName}
    `;

    // Get primary key columns
    const primaryKeys = await prisma.$queryRaw`
      SELECT
        ccu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_name = ${tableName}
    `;

    // Get sample data (first 10 rows, or more for messages table)
    // This is a bit tricky because we need to use dynamic SQL
    // We'll use a raw query with a safe table name (already validated above)
    const rowLimit = tableName === 'messages' ? 100 : 10;
    const orderBy = tableName === 'messages' ? 'ORDER BY id DESC' : '';

    logger.info(`Fetching sample data for table ${tableName} with limit ${rowLimit}`);
    const sampleData = await prisma.$queryRawUnsafe(`
      SELECT * FROM "${tableName}" ${orderBy} LIMIT ${rowLimit}
    `);

    // Get column names for the sample data
    let columnNames: string[] = [];
    if (Array.isArray(sampleData) && sampleData.length > 0) {
      columnNames = Object.keys(sampleData[0]);
    }

    // Format primary keys as an array
    const primaryKeyColumns = (primaryKeys as any[]).map((pk) => pk.column_name);

    // For API routes, return JSON
    if (req.path.startsWith('/api/')) {
      return res.status(200).json({
        success: true,
        data: {
          tableName,
          columns,
          indexes,
          foreignKeys,
          primaryKeys: primaryKeyColumns,
          sampleData,
        },
      });
    }

    // For UI routes, render the template
    return res.render('admin/tables/table_details.html', {
      title: `Table: ${tableName}`,
      table_name: tableName,
      columns,
      indexes,
      foreign_keys: foreignKeys,
      primary_keys: primaryKeyColumns,
      sample_data: sampleData,
      column_names: columnNames,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error(`Error fetching details for table ${req.params.tableName}:`, error);

    // For API routes
    if (req.path.startsWith('/api/')) {
      return res.status(500).json({
        success: false,
        error: `Failed to fetch details for table ${req.params.tableName}`,
      });
    }

    // For UI routes
    return res.status(500).render('error.html', {
      title: 'Error',
      error: `Failed to fetch details for table ${req.params.tableName}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}

/**
 * Query a table with filtering and sorting
 */
export async function queryTable(req: Request, res: Response) {
  try {
    const { tableName } = req.params;
    const { page = 1, limit = 20, sortBy = 'id', sortOrder = 'asc', filter = '' } = req.query;

    // Validate table name to prevent SQL injection
    const validTableName = /^[a-zA-Z0-9_]+$/.test(tableName);
    if (!validTableName) {
      return res.status(400).json({
        success: false,
        error: 'Invalid table name',
      });
    }

    // Validate sort column to prevent SQL injection
    const validSortColumn = /^[a-zA-Z0-9_]+$/.test(sortBy as string);
    if (!validSortColumn) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sort column',
      });
    }

    // Validate sort order
    const validSortOrder = ['asc', 'desc'].includes((sortOrder as string).toLowerCase());
    if (!validSortOrder) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sort order',
      });
    }

    // Calculate offset
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const offset = (pageNum - 1) * limitNum;

    // Build the query
    let query = `SELECT * FROM "${tableName}"`;
    let countQuery = `SELECT COUNT(*) FROM "${tableName}"`;
    const queryParams: any[] = [];

    // Add filter if provided
    if (filter) {
      // Get columns for the table
      const columns = await prisma.$queryRaw`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = ${tableName}
      `;

      // Build filter conditions for each text/varchar column
      const filterConditions = (columns as any[])
        .filter((col) =>
          ['text', 'character varying', 'varchar', 'char', 'character'].includes(col.data_type),
        )
        .map((col, index) => {
          queryParams.push(`%${filter}%`);
          return `"${col.column_name}"::text ILIKE $${index + 1}`;
        });

      if (filterConditions.length > 0) {
        const whereClause = `WHERE ${filterConditions.join(' OR ')}`;
        query += ` ${whereClause}`;
        countQuery += ` ${whereClause}`;
      }
    }

    // Add sorting and pagination
    query += ` ORDER BY "${sortBy}" ${sortOrder} LIMIT ${limitNum} OFFSET ${offset}`;

    // Execute the queries
    const data = await prisma.$queryRawUnsafe(query, ...queryParams);
    const countResult = await prisma.$queryRawUnsafe(countQuery, ...queryParams);
    const totalCount = parseInt((countResult as any[])[0].count, 10);

    return res.status(200).json({
      success: true,
      data,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    logger.error(`Error querying table ${req.params.tableName}:`, error);
    return res.status(500).json({
      success: false,
      error: `Failed to query table ${req.params.tableName}`,
    });
  }
}
