/**
 * Services controller for the admin interface
 * Provides information about services and routes in the application
 */
import { Request, Response } from 'express';
import { logger } from '../../common/logger';

/**
 * Get all services and routes
 * @param req Express request
 * @param res Express response
 */
export const getAllServices = async (_req: Request, res: Response) => {
  try {
    // Get services information
    const servicesInfo = getServicesInfo();

    // Get routes information
    const routesInfo = getRoutesInfo();

    // Return the data
    return res.status(200).json({
      success: true,
      data: {
        services: servicesInfo,
        routes: routesInfo,
      },
    });
  } catch (error) {
    logger.error('Error getting services and routes:', error);
    return res.status(500).json({
      success: false,
      error: 'Error getting services and routes',
    });
  }
};

/**
 * Get information about all services in the application
 * @returns Array of service information objects
 */
function getServicesInfo() {
  // Define the services manually based on our knowledge of the application
  return [
    {
      name: 'AI Service',
      description: 'Manages interactions with AI models',
      module: 'services/ai/index.ts',
      subServices: [
        {
          name: 'Azure Service',
          description: 'Handles Azure OpenAI API interactions',
          module: 'services/ai/azure.service.ts',
        },
        {
          name: 'Claude Service',
          description: 'Handles Anthropic Claude API interactions',
          module: 'services/ai/claude.service.ts',
        },
        {
          name: 'Gemini Service',
          description: 'Handles Google Gemini API interactions',
          module: 'services/ai/gemini.service.ts',
        },
        {
          name: 'Nova Service',
          description: 'Handles Amazon Bedrock API interactions',
          module: 'services/ai/nova.service.ts',
        },
      ],
    },
    {
      name: 'Clerk Service',
      description: 'Manages authentication with Clerk',
      module: 'services/clerk.service.ts',
    },
    {
      name: 'Unified Conversation Service',
      description: 'Manages conversation history and context with task-specific strategies',
      module: 'services/conversation/unified-conversation.service.ts',
    },
    {
      name: 'Model Usage Service',
      description: 'Tracks model usage and costs',
      module: 'services/model-usage.service.ts',
    },
  ];
}

/**
 * Get information about all routes in the application
 * @returns Array of route information objects
 */
function getRoutesInfo() {
  // Define the routes manually based on our knowledge of the application
  const routes = [
    // Root routes
    {
      path: '/health',
      method: 'GET',
      tag: 'root',
      description: 'Health check endpoint',
    },

    // API routes
    {
      path: '/api/users',
      method: 'GET',
      tag: 'users',
      description: 'Get all users',
    },
    {
      path: '/api/auth/me',
      method: 'GET',
      tag: 'authentication',
      description: 'Get current user profile',
    },
    {
      path: '/api/webhooks/clerk',
      method: 'POST',
      tag: 'webhooks',
      description: 'Handle Clerk webhook events',
    },
    {
      path: '/api/conversations',
      method: 'GET',
      tag: 'conversations',
      description: 'Get all conversations for the current user',
    },
    {
      path: '/api/conversations/:id',
      method: 'GET',
      tag: 'conversations',
      description: 'Get a specific conversation',
    },
    {
      path: '/api/conversations/:id/messages',
      method: 'GET',
      tag: 'conversations',
      description: 'Get messages for a specific conversation',
    },
    {
      path: '/api/conversations/:id/messages',
      method: 'POST',
      tag: 'conversations',
      description: 'Add a message to a conversation',
    },
    {
      path: '/api/tasks',
      method: 'GET',
      tag: 'tasks',
      description: 'Get all conversation tasks',
    },
    {
      path: '/api/tasks/:id',
      method: 'GET',
      tag: 'tasks',
      description: 'Get a specific conversation task',
    },
    {
      path: '/api/projects',
      method: 'GET',
      tag: 'projects',
      description: 'Get all projects',
    },
    {
      path: '/api/projects/:id',
      method: 'GET',
      tag: 'projects',
      description: 'Get a specific project',
    },

    // WebSocket routes
    {
      path: '/ws/nova-sonic-bidirectional',
      method: 'WS',
      tag: 'websockets',
      description: 'Bidirectional WebSocket for Nova Sonic',
    },

    // Admin API routes
    {
      path: '/api/admin/auth/login',
      method: 'POST',
      tag: 'admin',
      description: 'Admin login',
    },
    {
      path: '/api/admin/auth/logout',
      method: 'GET',
      tag: 'admin',
      description: 'Admin logout',
    },
    {
      path: '/api/admin/tables',
      method: 'GET',
      tag: 'admin',
      description: 'Get all database tables',
    },
    {
      path: '/api/admin/tables/:tableName',
      method: 'GET',
      tag: 'admin',
      description: 'Get details for a specific table',
    },
    {
      path: '/api/admin/models',
      method: 'GET',
      tag: 'admin',
      description: 'Get all models',
    },
    {
      path: '/api/admin/models/test',
      method: 'GET',
      tag: 'admin',
      description: 'Test models',
    },
    {
      path: '/api/admin/conversations',
      method: 'GET',
      tag: 'admin',
      description: 'Get all conversations',
    },
    {
      path: '/api/admin/services',
      method: 'GET',
      tag: 'admin',
      description: 'Get all services and routes',
    },
  ];

  return routes;
}
