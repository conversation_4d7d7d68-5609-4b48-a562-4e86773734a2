import { Request, Response, NextFunction } from 'express';
import { logger } from '../../common/logger';

/**
 * Middleware to check if user is authenticated
 */
export const isAuthenticated = (req: Request, res: Response, next: NextFunction): void => {
  // Log request information for debugging (without sensitive data)
  logger.debug(`Auth check for path: ${req.path}`);
  logger.debug(`Session exists: ${!!(req.session as any)?.admin_user}`);

  // Development mode still requires proper authentication
  // Only allow bypassing if explicitly enabled via environment variable
  if (process.env.NODE_ENV !== 'production' && process.env.DEVELOPMENT_AUTH_BYPASS === 'true') {
    logger.warn('SECURITY WARNING: Development authentication bypass is enabled');
    
    // Require explicit development credentials
    const devUser = process.env.DEV_ADMIN_USER;
    const devPass = process.env.DEV_ADMIN_PASS;
    
    if (!devUser || !devPass) {
      logger.error('Development bypass enabled but DEV_ADMIN_USER/DEV_ADMIN_PASS not set');
      res.status(401).json({
        success: false,
        error: 'Development credentials not configured',
      });
      return;
    }

    // Check if session already has authenticated user
    if ((req.session as any)?.admin_user) {
      res.locals.admin_user = (req.session as any)?.admin_user;
      next();
      return;
    }

    // For development, redirect to login rather than auto-authenticate
    logger.debug(`Development mode: Redirecting to login from ${req.originalUrl}`);
    res.redirect(`/admin/login?redirect=${encodeURIComponent(req.originalUrl)}`);
    return;
  }

  // Check if user is authenticated via session
  if ((req.session as any)?.admin_user) {
    logger.debug(`User authenticated with role: ${(req.session as any)?.admin_user?.role}`);
    res.locals.admin_user = (req.session as any)?.admin_user;
    next();
    return;
  }

  logger.warn('No admin user found in session');

  // If this is an API request, return 401
  if (req.path.startsWith('/api/')) {
    logger.warn(`Unauthorized API request to ${req.path}`);
    res.status(401).json({
      success: false,
      error: 'Unauthorized',
    });
    return;
  }

  // Otherwise redirect to login page
  logger.debug(`Redirecting to login page from ${req.originalUrl}`);
  res.redirect(`/admin/login?redirect=${encodeURIComponent(req.originalUrl)}`);
};

/**
 * Middleware to check if user is an admin
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  // Development mode still requires proper role checking
  if (process.env.NODE_ENV !== 'production' && process.env.DEVELOPMENT_AUTH_BYPASS === 'true') {
    logger.warn('SECURITY WARNING: Development admin bypass is enabled');
    
    // Still verify the user has admin role even in development
    if ((req.session as any)?.admin_user?.role === 'ADMIN') {
      next();
      return;
    }
    
    logger.warn('Development mode: User lacks admin role');
    res.status(403).json({
      success: false,
      error: 'Admin role required',
    });
    return;
  }

  // Check if user is authenticated and has admin role
  if ((req.session as any)?.admin_user?.role === 'ADMIN') {
    next();
    return;
  }

  // If this is an API request, return 403
  if (req.path.startsWith('/api/')) {
    res.status(403).json({
      success: false,
      error: 'Forbidden',
    });
    return;
  }

  // Otherwise redirect to login page with error
  res.redirect('/admin/login?error=You must be an admin to access this page');
};

/**
 * Middleware to add current admin user to response locals
 */
export const getCurrentAdminUser = (req: Request, res: Response, next: NextFunction): void => {
  res.locals.admin_user = (req.session as any)?.admin_user || null;
  next();
};
