import { Router, Request, Response, RequestHandler } from 'express';
import { logger } from '../../common/logger';
import config from '../../../config/config';
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

// Import AI services
import azureService from '../../services/ai/azure.service';
import claudeService from '../../services/ai/claude.service';
import geminiService, { ReasoningEffort } from '../../services/ai/gemini.service';
import novaService from '../../services/ai/nova.service';

const router = Router();

/**
 * @swagger
 * /admin/api/models:
 *   get:
 *     summary: Get all models with their priority information
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of models with priority information
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response) => {
  try {
    // Get models from config
    const models = config.models;

    // Debug logging
    logger.info('Models from config:', JSON.stringify(config.models));
    logger.info('Config YAML:', JSON.stringify(config.yamlConfig));

    if (!models || models.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'No models found in configuration',
      });
    }

    // Get priorities from config
    const priorities = config.yamlConfig.priorities || {};

    // Map models with their priority information
    const modelsWithPriorities = models.map((model: any) => {
      // Convert string values to appropriate types
      const modelData = {
        id: model.model_id,
        provider: model.provider,
        type: model.model_type,
        actualModelId: model.actual_model_id,
        region: model.region,
        supportsTemperature: model.supports_temperature === 'true',
        thinkingSupported: model.thinking_supported === 'true',
        toolCallingSupported: model.tool_calling_supported === 'true',
        inputPrice: parseFloat(model.input_price),
        outputPrice: parseFloat(model.output_price),
        rpmLimit: parseInt(model.rpm_limit, 10),
        tpmLimit: parseInt(model.tpm_limit, 10),
        maxConcurrent: parseInt(model.max_concurrent_requests, 10),
      };

      // Find which use cases this model is used in
      const usedIn: string[] = [];
      for (const [useCase, modelList] of Object.entries(priorities)) {
        if (Array.isArray(modelList) && modelList.includes(model.model_id)) {
          // Add the priority position (1-based index)
          const position = modelList.indexOf(model.model_id) + 1;
          usedIn.push(`${useCase} (#${position})`);
        }
      }

      return {
        ...modelData,
        usedIn,
      };
    });

    res.json({
      status: 'success',
      data: modelsWithPriorities,
    });
  } catch (error) {
    logger.error('Error getting models:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get models',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/models/priorities:
 *   get:
 *     summary: Get model priorities for all use cases
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Model priorities by use case
 *       500:
 *         description: Server error
 */
router.get('/priorities', (req: Request, res: Response) => {
  try {
    // Get priorities from config
    const priorities = config.yamlConfig.priorities || {};
    const useDescriptions = config.yamlConfig.use_case_descriptions || {};

    res.json({
      status: 'success',
      data: {
        priorities,
        descriptions: useDescriptions,
      },
    });
  } catch (error) {
    logger.error('Error fetching model priorities:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching model priorities',
    });
  }
});

/**
 * @swagger
 * /admin/api/models/test:
 *   get:
 *     summary: Get the parallel test page for models
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Parallel test page for models
 *       500:
 *         description: Server error
 */
router.get('/test', (async (req: Request, res: Response) => {
  try {
    res.render('models/parallel-test.html', {
      title: 'Parallel Model Testing',
    });
  } catch (error) {
    logger.error('Error rendering parallel test page:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error rendering parallel test page',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/models/test:
 *   post:
 *     summary: Test a model with a prompt
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - model
 *               - prompt
 *             properties:
 *               model:
 *                 type: string
 *                 description: The model ID to test
 *               prompt:
 *                 type: string
 *                 description: The prompt to test with
 *               temperature:
 *                 type: number
 *                 description: The temperature to use
 *               maxTokens:
 *                 type: number
 *                 description: The maximum number of tokens to generate
 *     responses:
 *       200:
 *         description: Model response
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
/**
 * @swagger
 * /admin/api/models/priorities/update:
 *   post:
 *     summary: Update model priorities for a specific use case
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - taskType
 *               - priorities
 *             properties:
 *               taskType:
 *                 type: string
 *                 description: The task type to update priorities for
 *               priorities:
 *                 type: array
 *                 description: Ordered list of model IDs
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Priorities updated successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
router.post('/priorities/update', async (req: Request, res: Response) => {
  try {
    const { taskType, priorities } = req.body;
    
    // Validate required fields
    if (!taskType || !priorities || !Array.isArray(priorities)) {
      return res.status(400).json({
        success: false,
        error: 'Task type and priorities array are required',
      });
    }
    
    // Get the current config
    const configPath = path.join(process.cwd(), 'config', 'config.yaml');
    let yamlConfig: any;
    
    try {
      const fileContents = fs.readFileSync(configPath, 'utf8');
      yamlConfig = yaml.load(fileContents);
      
      if (!yamlConfig.priorities) {
        yamlConfig.priorities = {};
      }
      
      // Update the priorities for the specified task type
      yamlConfig.priorities[taskType] = priorities;
      
      // Special handling for task types that share the same priority list via YAML anchors
      
      if (taskType === 'code_gen_big') {
        // These task types share the same priority list as code_gen_big
        const linkedTypes = ['svg_mockup', 'slides'];
        linkedTypes.forEach(type => {
          yamlConfig.priorities[type] = priorities;
        });
      } else if (taskType === 'code_gen_agentic') {
        // These task types share the same priority list as code_gen_agentic
        const linkedTypes = ['test_cases'];
        linkedTypes.forEach(type => {
          yamlConfig.priorities[type] = priorities;
        });
      }
      
      // Write the updated config back to the file
      fs.writeFileSync(configPath, yaml.dump(yamlConfig, { lineWidth: -1 }));
      
      // Update the config in memory
      config.yamlConfig = yamlConfig;
      
      logger.info(`Updated priorities for ${taskType}:`, priorities);
      
      return res.json({
        success: true,
        message: `Priorities for ${taskType} updated successfully`,
      });
    } catch (error) {
      logger.error('Error updating priorities:', error);
      return res.status(500).json({
        success: false,
        error: `Failed to update priorities: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  } catch (error) {
    logger.error('Error in priorities update endpoint:', error);
    return res.status(500).json({
      success: false,
      error: `Server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
    });
  }
});

router.post('/test', (async (req: Request, res: Response) => {
  try {
    const { model, prompt, temperature = 0.7, maxTokens = 100 } = req.body;

    if (!model || !prompt) {
      return res.status(400).json({
        success: false,
        error: 'Model and prompt are required',
      });
    }

    logger.info(`Testing model ${model} with prompt: ${prompt}`);

    // Start timing
    const startTime = Date.now();

    try {
      // Determine which service to use based on the model ID
      let response: any;
      let responseText: string = '';
      let usage: any = {};

      if (model.startsWith('gemini')) {
        // Use Gemini service with consistent response handling
        const useReasoning = model.includes('2.5');
        const modelConfig = config.getModelById(model);
        const modelToUse = modelConfig?.actual_model_id || model.replace('gemini-', '');
        
        response = await geminiService.generateText({
          prompt,
          model: modelToUse,
          maxTokens,
          temperature,
          reasoningEffort: useReasoning ? ReasoningEffort.LOW : ReasoningEffort.NONE,
          systemPrompt: 'You are a helpful assistant.',
        });
      } else if (model.startsWith('claude')) {
        // Extract the model version from the model ID
        const modelVersion = model.replace('claude-', '');
        response = await claudeService.invokeClaudeModel({
          prompt,
          modelType: modelVersion,
          maxTokens,
          temperature,
        });
      } else if (model.startsWith('nova')) {
        // Extract the model type from the model ID
        const modelType = model.replace('nova-', '');
        response = await novaService.generateText({
          prompt,
          modelType,
          maxTokens,
          temperature,
          topP: 0.9,
        });
      } else if (model.startsWith('gpt') || model.startsWith('o')) {
        // Use Azure service for GPT and O models
        response = await azureService.invokeModel(prompt, {
          modelType: model,
          maxTokens,
          temperature,
        });
      } else {
        throw new Error(`Unsupported model: ${model}`);
      }

      // Extract content and usage from the standardized response
      responseText = response.content || response || 'No response content';
      usage = response.usage || {};

      // Calculate time taken
      const timeTaken = ((Date.now() - startTime) / 1000).toFixed(2);

      // Log the response
      logger.info(
        `Model ${model} response (${responseText.length} chars): ${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}`,
      );

      // Use token counts and cost from the service response if available
      const promptTokens = usage.promptTokens || Math.floor(prompt.length / 4);
      const completionTokens = usage.completionTokens || Math.floor(responseText.length / 4);
      const totalTokens = usage.totalTokens || (promptTokens + completionTokens);
      const cost = usage.cost || 0;

      res.json({
        success: true,
        response: responseText,
        metadata: {
          model,
          promptTokens,
          completionTokens,
          totalTokens,
          timeTaken,
          cost: Number(cost.toFixed(6)),
        },
      });
    } catch (modelError: any) {
      logger.error(`Error testing model ${model}:`, modelError);
      res.status(500).json({
        success: false,
        error: `Error testing model ${model}: ${modelError.message}`,
      });
    }
  } catch (error: any) {
    logger.error('Error in model test endpoint:', error);
    res.status(500).json({
      success: false,
      error: `Server error: ${error.message}`,
    });
  }
}) as RequestHandler);

export default router;
