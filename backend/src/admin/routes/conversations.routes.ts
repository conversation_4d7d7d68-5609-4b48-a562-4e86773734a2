import { Router, Request, Response } from 'express';
import { logger } from '../../common/logger';
import { unifiedConversationService } from '../../services/conversation/unified-conversation.service';
import { ConversationStatus } from '../../generated/prisma';
import { getConversationRepository } from '../../db/repositories/conversation.repository';
import modelUsageService from '../../services/model-usage.service';
import { RequestHandler } from 'express';

interface MessageMetrics {
  promptTokens?: number;
  completionTokens?: number;
  cost?: number;
  durationMs?: number;
}

const router = Router();

/**
 * @swagger
 * /admin/api/conversations:
 *   get:
 *     summary: Get all conversations
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of conversations
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response): Promise<void> => {
  try {
    const skip = parseInt(req.query.skip as string, 10) || 0;
    const limit = Math.min(parseInt(req.query.limit as string, 10) || 20, 100);
    const status = (req.query.status as ConversationStatus) || undefined;

    // Get admin user ID from session
    let adminUserId = (req.session as any)?.admin_user?.id;
    logger.info(`Original admin user ID from session: ${adminUserId}, type: ${typeof adminUserId}`);

    // Ensure adminUserId is a number for database queries
    adminUserId = typeof adminUserId === 'string' ? parseInt(adminUserId, 10) : adminUserId;
    logger.info(`Converted admin user ID: ${adminUserId}, type: ${typeof adminUserId}`);

    // Log all session and cookie information for debugging
    logger.info(`Session data: ${JSON.stringify(req.session || {}).substring(0, 100)}...`);
    logger.info(`Cookies: ${JSON.stringify(req.cookies || {}).substring(0, 50)}...`);

    if (!adminUserId) {
      logger.error('No admin user ID found in session');

      // TEMPORARY DEBUG FIX - Use a default admin ID for testing
      adminUserId = 1; // Use a valid ID from your database
      logger.warn(`Using temporary default admin ID: ${adminUserId} for debugging`);

      // Alternatively, return 401 when in production
      if (process.env.NODE_ENV === 'production') {
        res.status(401).json({
          status: 'error',
          message: 'Unauthorized',
        });
        return;
      }
    }

    // Get all conversations for debugging
    logger.info('Fetching all conversations for debugging');
    const allConversations = await unifiedConversationService.getAllConversations();
    logger.info(`Found ${allConversations.length} total conversations in the database`);
    if (allConversations.length > 0) {
      logger.info(`Sample conversation: ${JSON.stringify(allConversations[0])}`);
      logger.info(
        `User IDs in conversations: ${allConversations.map((c: any) => c.user_id).join(', ')}`,
      );
    }

    // Get conversations for the admin user
    logger.info(`Fetching conversations for admin user ID: ${adminUserId}`);
    try {
      const result = await unifiedConversationService.getUserConversations(
        adminUserId,
        status,
        skip,
        limit,
      );

      logger.info(
        `Found ${result.conversations.length} conversations for admin user ID: ${adminUserId}`,
      );
      if (result.conversations.length > 0) {
        logger.info(`Conversation IDs: ${result.conversations.map((c: any) => c.id).join(', ')}`);
      } else {
        logger.warn(`No conversations found for admin user ID: ${adminUserId}`);
      }

      // Sanitize the response to ensure it's valid JSON
      const sanitizedConversations = result.conversations.map((conv: any) => ({
        id: conv.id,
        userId: conv.user_id,
        title: conv.title || `Conversation ${conv.id}`,
        status: conv.status,
        createdAt: conv.created_at.toISOString(),
        updatedAt: conv.updated_at.toISOString(),
      }));

      res.json({
        status: 'success',
        conversations: sanitizedConversations,
        total: result.total,
        skip,
        limit,
      });
    } catch (innerError) {
      logger.error(`Error in getUserConversations:`, innerError);
      throw innerError;
    }
  } catch (error) {
    logger.error('Error fetching conversations:', error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    logger.error(`Error details: ${errorMessage}`);
    if (errorStack) {
      logger.error(`Error stack: ${errorStack}`);
    }

    res.status(500).json({
      status: 'error',
      message: 'Error fetching conversations',
      details: errorMessage,
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/conversations/stream:
 *   get:
 *     summary: Stream a conversation response
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: conversationId
 *         schema:
 *           type: integer
 *         description: Conversation ID (optional, if not provided a new conversation will be created)
 *       - in: query
 *         name: prompt
 *         required: true
 *         schema:
 *           type: string
 *         description: The prompt to send to the model
 *       - in: query
 *         name: model
 *         required: true
 *         schema:
 *           type: string
 *         description: The model to use
 *       - in: query
 *         name: temperature
 *         schema:
 *           type: number
 *         description: The temperature to use
 *       - in: query
 *         name: maxTokens
 *         schema:
 *           type: integer
 *         description: The maximum number of tokens to generate
 *       - in: query
 *         name: memoryCount
 *         schema:
 *           type: integer
 *         description: Number of previous messages to include as context (0 = no memory)
 *     responses:
 *       200:
 *         description: Streaming response
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
// Disable compression for SSE endpoints
router.get('/stream', (async (req: Request, res: Response): Promise<void> => {
  // Disable compression for this route
  if (res.locals && typeof res.locals === 'object') {
    res.locals.skipCompression = true;
  }
  try {
    const conversationId = req.query.conversationId
      ? parseInt(req.query.conversationId as string, 10)
      : undefined;
    const prompt = req.query.prompt as string;
    const model = req.query.model as string;
    const temperature = parseFloat(req.query.temperature as string) || 0.7;
    const maxTokens = parseInt(req.query.maxTokens as string, 10) || 1000;
    const memoryCount = req.query.memoryCount
      ? parseInt(req.query.memoryCount as string, 10)
      : undefined;

    // Validate required parameters
    if (!prompt || !model) {
      res.status(400).json({
        status: 'error',
        message: 'Prompt and model are required',
      });
      return;
    }

    // Get admin user ID from session
    let adminUserId = (req.session as any)?.admin_user?.id;

    // Ensure adminUserId is a number
    adminUserId = typeof adminUserId === 'string' ? parseInt(adminUserId, 10) : adminUserId;

    if (!adminUserId) {
      logger.error('No admin user ID found in session');

      // Use a default ID for development
      if (process.env.NODE_ENV !== 'production') {
        adminUserId = 1;
        logger.warn(`Using default admin ID ${adminUserId} for development`);
      } else {
        res.status(401).json({
          status: 'error',
          message: 'Unauthorized',
        });
        return;
      }
    }

    // Set up SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // Disable Nginx buffering
    
    // Flush headers immediately
    res.flushHeaders();

    try {
      // Create new conversation if needed
      let actualConversationId = conversationId;
      if (!actualConversationId) {
        const conversation = await unifiedConversationService.createConversation(adminUserId, {
          title: prompt.substring(0, 50) + '...',
        });
        actualConversationId = conversation.id;
        logger.info(`Created new conversation ${actualConversationId} for streaming`);
      }

      // Add user message
      await unifiedConversationService.addMessage(actualConversationId, 'user', prompt);

      // Log memory count for debugging
      logger.info(
        `Streaming response with memory count: ${memoryCount !== undefined ? memoryCount : 'default'}`,
      );

      // If memoryCount is undefined, use a default of 10 to ensure memory works
      const effectiveMemoryCount = memoryCount !== undefined ? memoryCount : 10;

      // Stream the response
      const stream = await unifiedConversationService.streamResponse(actualConversationId, prompt, {
        modelId: model,
        temperature,
        maxTokens,
        userId: adminUserId,
        memoryCount: effectiveMemoryCount, // Pass the memoryCount parameter with a default
      });

      // Send the conversation ID to the client
      res.write(
        `data: ${JSON.stringify({ type: 'conversation_id', conversation_id: actualConversationId })}\n\n`,
      );
      
      // Send initial heartbeat to establish the connection
      res.write(': heartbeat\n\n');
      if (res.flush) {
        res.flush();
      }

      // Process the stream and collect content and usage information
      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = model;
      const startTime = Date.now();

      // Process the stream
      let chunkCount = 0;
      for await (const chunk of stream) {
        chunkCount++;
        logger.debug(`Received chunk ${chunkCount} from AI service`);
        // Extract content from chunk based on its structure
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          const timestamp = Date.now() - startTime;
          logger.debug(`Sending content chunk at ${timestamp}ms: ${content.substring(0, 50)}...`);
          res.write(`data: ${JSON.stringify({ type: 'content', content })}\n\n`);
          // Force flush the response to ensure real-time streaming
          if (res.flush) {
            res.flush();
          }
        }

        // If this is the final chunk with usage info, capture it
        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || Date.now() - startTime,
          };
        }

        // Capture the model used if available
        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      // Save the assistant message with token usage information
      logger.info(
        `Saving assistant message for conversation ${actualConversationId} with usage info`,
      );
      logger.info(
        `Memory count used: ${effectiveMemoryCount}, Prompt tokens: ${usage.prompt_tokens} (higher token count indicates memory is working)`,
      );

      const assistantMessage = await unifiedConversationService.addMessage(
        actualConversationId,
        'assistant',
        fullContent,
        {
          model: modelUsed,
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          cost: usage.cost,
          durationMs: usage.duration_ms,
        },
      );

      // Log model usage
      try {
        await modelUsageService.logLlmUsage({
          userId: adminUserId,
          modelRequested: model,
          modelUsed: modelUsed,
          taskType: 'chat',
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
          estimatedCost: usage.cost,
          processingTime: usage.duration_ms,
          status: 'success',
          metadata: {
            conversationId: actualConversationId,
            messageId: assistantMessage.id,
          },
        });
        logger.info(`Logged model usage for conversation ${actualConversationId}`);
      } catch (logError) {
        logger.error(`Error logging model usage: ${logError}`);
      }

      // Send metadata
      res.write(
      `data: ${JSON.stringify({
      type: 'metadata',
      metadata: {
      model: modelUsed,
      promptTokens: usage.prompt_tokens,
      completionTokens: usage.completion_tokens,
      totalTokens: usage.total_tokens,
      timeTaken: usage.duration_ms ? (usage.duration_ms / 1000).toFixed(2) : null,
      cost: usage.cost,
      },
      })}\n\n`,
      );

      // Send done event
      res.write(`data: ${JSON.stringify({ type: 'done' })}\n\n`);
      res.end();

        logger.info(
          `Streaming completed for conversation ${actualConversationId}. Total content length: ${fullContent.length} characters`,
        );
    } catch (error: any) {
      logger.error('Error streaming response:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Error streaming response';
      if (error.message) {
        if (error.message.includes('content management policy')) {
          errorMessage = 'Azure Content Filter: Your message was blocked by Azure\'s content policy. Please try a different prompt.';
        } else if (error.message.includes('content filtering')) {
          errorMessage = 'Content filtered: The AI provider blocked this request. Please modify your prompt.';
        } else {
          errorMessage = error.message;
        }
      }
      
      res.write(
        `data: ${JSON.stringify({ type: 'error', message: errorMessage })}\n\n`,
      );
      res.end();
    }
  } catch (error) {
    logger.error('Error setting up streaming:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error setting up streaming',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/conversations/{id}:
 *   get:
 *     summary: Get a conversation by ID
 *     tags: [Admin]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *     responses:
 *       200:
 *         description: Conversation details
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get('/:id', (async (req: Request, res: Response): Promise<void> => {
  try {
    const conversationId = parseInt(req.params.id, 10);

    // Get admin user ID from session
    let adminUserId = (req.session as any)?.admin_user?.id;

    // Ensure adminUserId is a number
    adminUserId = typeof adminUserId === 'string' ? parseInt(adminUserId, 10) : adminUserId;

    if (!adminUserId) {
      logger.error('No admin user ID found in session');

      // Use a default ID for development
      if (process.env.NODE_ENV !== 'production') {
        adminUserId = 1;
        logger.warn(`Using default admin ID ${adminUserId} for development`);
      } else {
        res.status(401).json({
          status: 'error',
          message: 'Unauthorized',
        });
        return;
      }
    }

    // Get conversation
    const conversation = await unifiedConversationService.getConversation(
      conversationId,
      adminUserId,
    );
    if (!conversation) {
      res.status(404).json({
        status: 'error',
        message: 'Conversation not found',
      });
      return;
    }

    // Get messages
    const messages = await unifiedConversationService.getMessages(conversationId);

    // Sanitize response to ensure valid JSON
    const sanitizedMessages = messages.map((msg) => {
      const metrics = msg.meta_data as MessageMetrics;
      return {
        id: msg.id,
        role: msg.role,
        content: msg.content,
        model: msg.model,
        createdAt: msg.created_at.toISOString(),
        metrics: {
          promptTokens: metrics?.promptTokens,
          completionTokens: metrics?.completionTokens,
          cost: metrics?.cost,
          duration: metrics?.durationMs ? metrics.durationMs / 1000 : undefined, // Convert ms to seconds
        },
      };
    });

    const sanitizedConversation = {
      id: conversation.id,
      userId: conversation.user_id,
      title: conversation.title || `Conversation ${conversation.id}`,
      status: conversation.status,
      createdAt: conversation.created_at.toISOString(),
      updatedAt: conversation.updated_at.toISOString(),
    };

    res.json({
      status: 'success',
      conversation: sanitizedConversation,
      messages: sanitizedMessages,
    });
  } catch (error) {
    logger.error(`Error fetching conversation ${req.params.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    res.status(500).json({
      status: 'error',
      message: 'Error fetching conversation',
      details: errorMessage,
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/conversations/create-direct-test:
 *   post:
 *     summary: Create a test conversation directly via repository
 *     tags: [Admin]
 *     responses:
 *       201:
 *         description: Test conversation created directly
 *       500:
 *         description: Server error
 */
router.post('/create-direct-test', async (_req: Request, res: Response) => {
  try {
    // Use a hardcoded admin user ID for simplicity
    const adminUserId = 1;
    const now = new Date();

    logger.info(`Creating direct test conversation for user ID: ${adminUserId}`);

    // Create conversation via service
    const conversation = await unifiedConversationService.createConversation(adminUserId, {
      title: `Direct Test Conversation ${now.toISOString()}`,
    });

    logger.info(`Created direct test conversation with ID: ${conversation.id}`);

    // Add test messages via service
    const userMessage = await unifiedConversationService.addMessage(
      conversation.id,
      'user',
      'This is a direct test message',
    );

    const assistantMessage = await unifiedConversationService.addMessage(
      conversation.id,
      'assistant',
      'This is a direct test response',
    );

    // Sanitize the conversation object for safe JSON response
    const sanitizedConversation = {
      id: conversation.id,
      userId: conversation.user_id,
      title: conversation.title,
      status: conversation.status,
      createdAt: conversation.created_at.toISOString(),
      updatedAt: conversation.updated_at.toISOString(),
    };

    res.status(201).json({
      status: 'success',
      conversation: sanitizedConversation,
      messages: [
        {
          id: userMessage.id,
          role: userMessage.role,
          content: userMessage.content,
        },
        {
          id: assistantMessage.id,
          role: assistantMessage.role,
          content: assistantMessage.content,
        },
      ],
    });
  } catch (error) {
    logger.error('Error creating direct test conversation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    res.status(500).json({
      status: 'error',
      message: 'Error creating direct test conversation',
      details: errorMessage,
    });
  }
});

/**
 * @swagger
 * /admin/api/conversations/create-test:
 *   post:
 *     summary: Create a test conversation for the admin user
 *     tags: [Admin]
 *     responses:
 *       201:
 *         description: Test conversation created
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/create-test', (async (req: Request, res: Response): Promise<void> => {
  try {
    // Get admin user ID from session
    let adminUserId = (req.session as any)?.admin_user?.id;
    logger.info(`Admin user ID from session: ${adminUserId}, type: ${typeof adminUserId}`);

    // Ensure adminUserId is a number
    adminUserId = typeof adminUserId === 'string' ? parseInt(adminUserId, 10) : adminUserId;

    if (!adminUserId) {
      logger.error('No admin user ID found in session');
      // Use a default ID for development
      if (process.env.NODE_ENV !== 'production') {
        adminUserId = 1;
        logger.warn(`Using default admin ID ${adminUserId} for development`);
      } else {
        res.status(401).json({
          status: 'error',
          message: 'Unauthorized',
        });
        return;
      }
    }

    // Create a test conversation
    logger.info(`Creating test conversation for admin user ID: ${adminUserId}`);
    try {
      const conversation = await unifiedConversationService.createConversation(adminUserId, {
        title: `Test Conversation ${new Date().toISOString()}`,
      });

      // Add a test message
      await unifiedConversationService.addMessage(
        conversation.id,
        'user',
        'This is a test message',
      );
      await unifiedConversationService.addMessage(
        conversation.id,
        'assistant',
        'This is a test response',
      );

      logger.info(`Created test conversation with ID: ${conversation.id}`);

      // For development - Fix existing conversations if they exist with a different user ID
      if (process.env.NODE_ENV !== 'production') {
        try {
          const allConversations = await unifiedConversationService.getAllConversations();
          if (allConversations.length > 0) {
            logger.info(
              `Found ${allConversations.length} total conversations - checking for ID mismatches`,
            );

            // Get unique user IDs from existing conversations
            const existingUserIds = [...new Set(allConversations.map((c) => c.user_id))].filter(
              (id) => id !== adminUserId,
            );

            if (existingUserIds.length > 0) {
              logger.warn(
                `Found conversations with different user IDs: ${existingUserIds.join(', ')}`,
              );
              logger.warn(
                `Will update first 5 conversations to match current admin ID: ${adminUserId}`,
              );

              // Update up to 5 conversations to match the current admin ID
              let updated = 0;
              for (const conv of allConversations) {
                if (conv.user_id !== adminUserId && updated < 5) {
                  const conversationRepo = getConversationRepository();
                  await conversationRepo.update(conv.id, { user_id: adminUserId });
                  logger.info(
                    `Updated conversation ${conv.id} from user_id ${conv.user_id} to ${adminUserId}`,
                  );
                  updated++;
                }
              }

              if (updated > 0) {
                logger.info(`Successfully updated ${updated} conversations to match admin ID`);
              }
            }
          }
        } catch (err) {
          logger.error('Error fixing conversation user IDs:', err);
        }
      }

      // Sanitize the conversation object for safe JSON response
      const sanitizedConversation = {
        id: conversation.id,
        userId: conversation.user_id,
        title: conversation.title,
        status: conversation.status,
        createdAt: conversation.created_at.toISOString(),
        updatedAt: conversation.updated_at.toISOString(),
      };

      res.status(201).json({
        status: 'success',
        conversation: sanitizedConversation,
      });
    } catch (createError) {
      logger.error('Error in conversationService.createConversation:', createError);

      // Try to create a conversation via service as fallback
      try {
        logger.info('Attempting service creation as fallback');
        const now = new Date();
        const directConversation = await unifiedConversationService.createConversation(
          adminUserId,
          {
            title: `Fallback Test Conversation ${now.toISOString()}`,
          },
        );

        // Add test messages via service
        await unifiedConversationService.addMessage(
          directConversation.id,
          'user',
          'This is a fallback test message',
        );

        await unifiedConversationService.addMessage(
          directConversation.id,
          'assistant',
          'This is a fallback test response',
        );

        logger.info(`Created fallback test conversation with ID: ${directConversation.id}`);

        // Sanitize the conversation object for safe JSON response
        const sanitizedConversation = {
          id: directConversation.id,
          userId: directConversation.user_id,
          title: directConversation.title,
          status: directConversation.status,
          createdAt: directConversation.created_at.toISOString(),
          updatedAt: directConversation.updated_at.toISOString(),
        };

        res.status(201).json({
          status: 'success',
          conversation: sanitizedConversation,
        });
      } catch (repositoryError) {
        logger.error('Error in fallback repository create:', repositoryError);
        throw createError; // Re-throw original error
      }
    }
  } catch (error) {
    logger.error('Error creating test conversation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    logger.error(`Error details: ${errorMessage}`);
    if (errorStack) {
      logger.error(`Error stack: ${errorStack}`);
    }

    res.status(500).json({
      status: 'error',
      message: 'Error creating test conversation',
      details: errorMessage,
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/api/conversations/create-multi-model-test:
 *   post:
 *     summary: Create a test conversation with multiple models
 *     responses:
 *       201:
 *         description: Test conversation created successfully
 *       500:
 *         description: Server error
 */
router.post('/create-multi-model-test', (async (req: Request, res: Response): Promise<void> => {
  try {
    // Get admin user ID from session
    let adminUserId = (req.session as any)?.admin_user?.id;
    if (!adminUserId || typeof adminUserId !== 'number') {
      // Use a default ID for development
      if (process.env.NODE_ENV !== 'production') {
        adminUserId = 1;
        logger.warn(`Using default admin ID ${adminUserId} for development`);
      } else {
        res.status(401).json({ status: 'error', message: 'Unauthorized' });
        return;
      }
    }

    // Create a test conversation
    const conversation = await unifiedConversationService.createConversation(adminUserId, {
      title: `Sequential Memory Test - ${new Date().toLocaleString()}`,
    });

    // Define test messages that build on each other to test memory
    const testMessages = [
      {
        role: 'user' as const,
        content: 'My name is Balaji. I am a software engineer from India.',
      },
      {
        role: 'user' as const,
        content: 'What is my name and what do I do?',
      },
      {
        role: 'user' as const,
        content: 'Where am I from?',
      },
      {
        role: 'user' as const,
        content: 'Can you summarize everything you know about me so far?',
      },
    ];

    // Get the cheapest model from each family
    const models = [
      'gemini-2.0-flash-lite', // Google
      'claude-3.5-haiku', // Anthropic
      'nova-micro', // Amazon
      'gpt-4.1-nano', // Azure
    ];

    // Add messages and get responses sequentially
    const messages = [];

    // Process each message with a different model
    for (let i = 0; i < testMessages.length; i++) {
      const userMessage = testMessages[i];
      const model = models[i % models.length]; // Cycle through models

      // Add user message
      const addedUserMessage = await unifiedConversationService.addMessage(
        conversation.id,
        userMessage.role,
        userMessage.content,
      );
      messages.push(addedUserMessage);

      try {
        // Use the streamResponse method directly to ensure memory is properly maintained
        logger.info(`Using model ${model} for message ${i + 1} with memory enabled`);

        // Set memory count to include all previous messages
        const memoryCount = i * 2; // Include all previous user+assistant messages

        // Stream the response using the unified conversation service
        const stream = await unifiedConversationService.streamResponse(
          conversation.id,
          userMessage.content,
          {
            modelId: model,
            temperature: 0.7,
            maxTokens: 500,
            userId: adminUserId,
            memoryCount: memoryCount, // Include all previous messages for context
          },
        );

        // Process the stream and collect content and usage information
        let fullContent = '';
        let usage = {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0,
          cost: 0,
          duration_ms: 0,
        };
        const startTime = Date.now();

        // Process the stream
        for await (const chunk of stream) {
          // Extract content from chunk based on its structure
          let content = '';
          if (typeof chunk === 'string') {
            content = chunk;
          } else if (chunk && typeof chunk === 'object') {
            if (chunk.content) {
              content = chunk.content;
            } else if (chunk.choices && chunk.choices.length > 0) {
              const delta = chunk.choices[0].delta;
              if (delta && delta.content) {
                content = delta.content;
              }
            }
          }

          if (content) {
            fullContent += content;
          }

          // If this is the final chunk with usage info, capture it
          if (chunk && typeof chunk === 'object' && chunk.usage) {
            usage = {
              prompt_tokens: chunk.usage.prompt_tokens || 0,
              completion_tokens: chunk.usage.completion_tokens || 0,
              total_tokens: chunk.usage.total_tokens || 0,
              cost: chunk.usage.cost || 0,
              duration_ms: chunk.usage.duration_ms || Date.now() - startTime,
            };
          }
        }

        // Add assistant message
        const addedMessage = await unifiedConversationService.addMessage(
          conversation.id,
          'assistant',
          fullContent,
          {
            model,
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            cost: usage.cost,
            durationMs: usage.duration_ms,
          },
        );
        messages.push(addedMessage);

        // Log the interaction with memory details
        logger.info(
          `Model ${model} processed message ${i + 1} with ${memoryCount} previous messages in memory`,
        );
        logger.info(`Response: ${fullContent.substring(0, 100)}...`);

        // Log token usage
        logger.info(
          `Token usage - Prompt: ${usage.prompt_tokens}, Completion: ${usage.completion_tokens}, Total: ${usage.total_tokens}`,
        );
      } catch (error) {
        logger.error(`Error processing with model ${model}:`, error);
      }
    }

    // Sanitize conversation object for safe JSON response
    const sanitizedConversation = {
      id: conversation.id,
      title: conversation.title,
      createdAt: conversation.created_at.toISOString(),
      updatedAt: conversation.updated_at.toISOString(),
    };

    res.status(201).json({
      status: 'success',
      conversation: sanitizedConversation,
      messages: messages.map((msg) => {
        const metrics = msg.meta_data as MessageMetrics;
        return {
          id: msg.id,
          role: msg.role,
          content: msg.content,
          model: msg.model,
          createdAt: msg.created_at.toISOString(),
          metrics: {
            promptTokens: metrics?.promptTokens,
            completionTokens: metrics?.completionTokens,
            cost: metrics?.cost,
            duration: metrics?.durationMs ? metrics.durationMs / 1000 : undefined, // Convert ms to seconds
          },
        };
      }),
    });
  } catch (error) {
    logger.error('Error creating multi-model test conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create test conversation',
    });
  }
}) as RequestHandler);

export default router;
