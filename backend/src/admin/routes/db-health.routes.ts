import { Router, Request, Response, RequestHandler } from 'express';
import { logger } from '../../common/logger';
import { prisma } from '../../db/client';
import { UserRole } from '../../generated/prisma';

const router = Router();

/**
 * Check database connection health
 */
router.get('/db-health', async (req: Request, res: Response) => {
  try {
    logger.info('Database health check called');

    // Attempt a simple query to check database connectivity
    const result = await prisma.$queryRaw`SELECT 1 as is_alive`;

    logger.info(`Database query result: ${JSON.stringify(result)}`);

    res.json({
      status: 'success',
      message: 'Database connection is healthy',
      details: {
        result,
      },
    });
  } catch (error) {
    logger.error('Database health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Database health check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * Check for specific message ID
 */
router.get('/check-message/:id', async (req: Request, res: Response) => {
  try {
    const messageId = parseInt(req.params.id, 10);

    if (isNaN(messageId)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid message ID',
      });
    }

    logger.info(`Checking for message ID ${messageId}`);

    // Query the message directly
    const message = await prisma.messages.findUnique({
      where: { id: messageId },
    });

    if (message) {
      logger.info(`Found message #${messageId}: ${JSON.stringify(message)}`);
      res.json({
        status: 'success',
        message: 'Message found',
        data: message,
      });
    } else {
      logger.warn(`Message #${messageId} not found in database`);
      res.status(404).json({
        status: 'error',
        message: `Message #${messageId} not found`,
      });
    }
  } catch (error) {
    logger.error('Error checking message:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check message',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * Create a test conversation directly via Prisma
 */
router.post('/create-test-conversation', async (req: Request, res: Response) => {
  try {
    logger.info('Creating test conversation via direct DB access');

    // Get admin user ID from session or use default
    let userId = (req.session as any)?.admin_user?.id || 1;

    // Ensure userId is a number
    if (typeof userId === 'string') {
      userId = parseInt(userId, 10);
    }

    logger.info(`Using user ID: ${userId} (type: ${typeof userId}) for test conversation`);

    // Get current time
    const now = new Date();

    // Get database schema information to diagnose issues
    try {
      logger.info('Checking database schema...');

      // Check if the conversations table exists
      const tableCheck = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'conversations'
        );
      `;
      logger.info(`Conversations table exists: ${JSON.stringify(tableCheck)}`);

      // Check the schema of the conversations table
      const tableColumns = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'conversations';
      `;
      logger.info(`Conversations table schema: ${JSON.stringify(tableColumns)}`);
    } catch (schemaError) {
      logger.error('Error checking database schema:', schemaError);
    }

    // Create the conversation
    let conversation;
    try {
      conversation = await prisma.conversations.create({
        data: {
          user_id: userId,
          title: `DB Test Conversation ${now.toISOString()}`,
          status: 'active',
          created_at: now,
          updated_at: now,
        },
      });
    } catch (createError) {
      logger.error('Error creating conversation with Prisma:', createError);

      // Try to create the conversation using raw SQL as a fallback
      logger.info('Prisma create failed, trying raw SQL...');
      try {
        const rawResult = await prisma.$queryRaw`
          INSERT INTO conversations (user_id, title, status, created_at, updated_at)
          VALUES (${userId}, ${`DB Test Conversation Raw SQL ${now.toISOString()}`}, 'active', ${now}, ${now})
          RETURNING id, user_id as "userId", title, status, created_at as "createdAt", updated_at as "updatedAt";
        `;

        logger.info(`Raw SQL insert result: ${JSON.stringify(rawResult)}`);

        if (Array.isArray(rawResult) && rawResult.length > 0) {
          conversation = rawResult[0];
        } else {
          throw new Error('Raw SQL insert did not return a result');
        }
      } catch (sqlError) {
        logger.error('Error with raw SQL insert:', sqlError);
        throw sqlError;
      }
    }

    logger.info(`Created test conversation with ID: ${conversation.id}`);

    // Add test messages
    let userMessage, assistantMessage;
    try {
      userMessage = await prisma.messages.create({
        data: {
          conversation_id: conversation.id,
          role: 'user',
          content: 'This is a test message created directly via Prisma',
          created_at: now,
        },
      });

      assistantMessage = await prisma.messages.create({
        data: {
          conversation_id: conversation.id,
          role: 'assistant',
          content: 'This is a test response created directly via Prisma',
          created_at: new Date(now.getTime() + 1000), // 1 second later
        },
      });
    } catch (messageError) {
      logger.error('Error creating test messages with Prisma:', messageError);

      // Try to create the messages using raw SQL as a fallback
      logger.info('Prisma message create failed, trying raw SQL...');
      try {
        const userResult = await prisma.$queryRaw`
          INSERT INTO messages (conversation_id, role, content, created_at)
          VALUES (${conversation.id}, 'user', 'This is a test message created directly via raw SQL', ${now})
          RETURNING id, conversation_id as "conversationId", role, content, created_at as "createdAt";
        `;

        if (Array.isArray(userResult) && userResult.length > 0) {
          userMessage = userResult[0];
        }

        const assistantResult = await prisma.$queryRaw`
          INSERT INTO messages (conversation_id, role, content, created_at)
          VALUES (${conversation.id}, 'assistant', 'This is a test response created directly via raw SQL', ${new Date(now.getTime() + 1000)})
          RETURNING id, conversation_id as "conversationId", role, content, created_at as "createdAt";
        `;

        if (Array.isArray(assistantResult) && assistantResult.length > 0) {
          assistantMessage = assistantResult[0];
        }

        logger.info(
          `Raw SQL insert result for messages: user=${userMessage?.id}, assistant=${assistantMessage?.id}`,
        );
      } catch (sqlError) {
        logger.error('Error with raw SQL insert for messages:', sqlError);
        throw sqlError;
      }
    }

    logger.info(`Added messages with IDs: ${userMessage.id}, ${assistantMessage.id}`);

    // Return the result
    res.status(201).json({
      status: 'success',
      message: 'Test conversation created successfully',
      data: conversation,
    });
  } catch (error) {
    logger.error('Error creating test conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create test conversation',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * Ensure admin user exists in the database
 */
router.post('/ensure-admin-user', async (req: Request, res: Response) => {
  try {
    logger.info('Ensuring admin user exists in the database');

    // Default admin user data
    const adminEmail = '<EMAIL>';
    const adminId = 1;

    // Check if admin user exists
    const existingUser = await prisma.users.findUnique({
      where: { id: adminId },
    });

    if (existingUser) {
      logger.info(`Admin user already exists with ID ${adminId}`);
      res.status(200).json({
        status: 'success',
        message: 'Admin user already exists',
        user: {
          id: existingUser.id,
          email: existingUser.email,
          role: existingUser.role,
        },
      });
      return;
    }

    // Create admin user if it doesn't exist
    logger.info(`Creating admin user with ID ${adminId}`);
    const now = new Date();

    const adminUser = await prisma.users.create({
      data: {
        id: adminId,
        email: adminEmail,
        clerk_id: 'admin_dev_' + Date.now(),
        role: UserRole.ADMIN,
        is_active: true,
        first_name: 'Development',
        last_name: 'Admin',
        created_at: now,
        updated_at: now,
        karma: 100,
        elo_rating: 1500,
        developer_strengths: [],
        preferred_ai_models: [],
      },
    });

    logger.info(`Created admin user with ID ${adminUser.id}`);

    // Return the created user
    res.status(201).json({
      status: 'success',
      message: 'Admin user created successfully',
      user: {
        id: adminUser.id,
        email: adminUser.email,
        role: adminUser.role,
      },
    });
  } catch (error) {
    logger.error('Error ensuring admin user exists:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      status: 'error',
      message: 'Failed to ensure admin user exists',
      details: errorMessage,
    });
  }
});

export default router;
