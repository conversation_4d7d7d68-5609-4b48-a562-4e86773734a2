import { Router, Request, Response, RequestHandler } from 'express';
import { getAllServices } from '../controllers/services.controller';
import { logger } from '../../common/logger';

const router = Router();

/**
 * @swagger
 * /admin/services:
 *   get:
 *     summary: Get all services and routes
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of services and routes
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response) => {
  try {
    // Call the controller function directly
    await getAllServices(req, res);
  } catch (error) {
    logger.error('Error getting services:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get services',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

export default router;
