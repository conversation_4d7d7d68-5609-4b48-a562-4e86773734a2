import { Router } from 'express';
import { login, logout } from '../controllers/auth.controller';

const router = Router();

/**
 * @swagger
 * /admin/auth/login:
 *   post:
 *     summary: Process admin login
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', login);

/**
 * @swagger
 * /admin/auth/logout:
 *   get:
 *     summary: Process admin logout
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.get('/logout', logout);

export default router;
