import { Router, Request, Response, RequestHandler } from 'express';
import { logger } from '../../common/logger';
import path from 'path';
import { login, logout } from '../controllers/auth.controller';
import { isAuthenticated, requireAdmin } from '../middleware/auth.middleware';

const router: Router = Router();

// Create a protected router for routes that need authentication
const protectedRouter: Router = Router();
protectedRouter.use(isAuthenticated, requireAdmin);

// Add a simple test route to verify the router is working
router.get('/test', (req: Request, res: Response) => {
  res.json({ message: 'Admin UI routes are working!', path: req.path });
});

/**
 * GET /admin/api-health
 * API health check page
 */
router.get('/api-health', async (req: Request, res: Response) => {
  try {
    // Make a request to the API health endpoint
    const apiHealthResponse = await fetch(`http://localhost:${process.env.PORT || 3000}/api/admin/api-health`);
    const healthData = await apiHealthResponse.json();
    
    res.render('admin/api-health.html', {
      title: 'API Health Status',
      admin_user: (req.session as any)?.admin_user || null,
      healthData: JSON.stringify(healthData, null, 2),
      healthStatus: healthData,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering API health page:', error);
    res.status(500).send('Error rendering API health page');
  }
});

/**
 * GET /admin
 * Admin dashboard (protected route)
 */
router.get('/', isAuthenticated, requireAdmin, ((req: Request, res: Response) => {
  try {
    res.render('admin/dashboard.html', {
      title: 'Admin Dashboard',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering admin dashboard:', error);
    res.status(500).send('Error rendering admin dashboard');
  }
}) as RequestHandler);

/**
 * GET /admin/tables
 * List database tables
 */
router.get('/tables', isAuthenticated, requireAdmin, (async (req: Request, res: Response) => {
  try {
    // Get table controller
    const tableController = await import('../controllers/tables.controller');

    // Call the controller function directly
    await tableController.getAllTables(req, res);
  } catch (error) {
    logger.error('Error rendering tables list:', error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: 'Error rendering tables list',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * GET /admin/tables/:tableName
 * View table details
 */
router.get('/tables/:tableName', isAuthenticated, requireAdmin, (async (req: Request, res: Response) => {
  try {
    const { tableName } = req.params;

    // Get table details from the controller
    const tableController = await import('../controllers/tables.controller');

    // Call the controller function directly
    return tableController.getTableDetails(req, res);
  } catch (error) {
    logger.error(`Error rendering table details for ${req.params.tableName}:`, error);
    res.status(500).render('admin/error.html', {
      title: 'Error',
      error: `Error rendering table details for ${req.params.tableName}`,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  }
}) as RequestHandler);

/**
 * GET /admin/models
 * List models
 */
router.get('/models', isAuthenticated, requireAdmin, (req: Request, res: Response) => {
  try {
    res.render('admin/models/list.html', {
      title: 'Models',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering models page:', error);
    res.status(500).send('Error rendering models page');
  }
});

/**
 * GET /admin/models/priorities
 * Model priorities page
 */
router.get('/models/priorities', (req: Request, res: Response) => {
  try {
    res.render('admin/models/priorities.html', {
      title: 'Model Priorities',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering model priorities page:', error);
    res.status(500).send('Error rendering model priorities page');
  }
});

/**
 * GET /admin/conversations
 * List conversations
 */
router.get('/conversations', (req: Request, res: Response) => {
  try {
    // Check if the file exists, otherwise use a placeholder
    res.render('admin/models/list.html', {
      title: 'Conversations',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering conversations list:', error);
    res.status(500).send('Error rendering conversations list');
  }
});

/**
 * GET /admin/conversations/test
 * Conversation testing page
 */
router.get('/conversations/test', (req: Request, res: Response) => {
  try {
    res.render('admin/conversations/test.html', {
      title: 'Conversation Testing',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering conversation testing page:', error);
    res.status(500).send('Error rendering conversation testing page');
  }
});

/**
 * GET /admin/conversations/:conversationId
 * View conversation details
 */
router.get('/conversations/:conversationId', (req: Request, res: Response) => {
  try {
    const { conversationId } = req.params;

    // Check if the file exists, otherwise use a placeholder
    res.render('admin/models/test.html', {
      title: `Conversation: ${conversationId}`,
      conversation_id: conversationId,
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error(`Error rendering conversation details for ${req.params.conversationId}:`, error);
    res.status(500).send(`Error rendering conversation details for ${req.params.conversationId}`);
  }
});

/**
 * GET /admin/services
 * List services and routes
 */
router.get('/services', (req: Request, res: Response) => {
  try {
    res.render('admin/services/list.html', {
      title: 'Services & Routes',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering services list:', error);
    res.status(500).send('Error rendering services list');
  }
});

/**
 * GET /admin/users
 * List users page
 */
router.get('/users', isAuthenticated, requireAdmin, (req: Request, res: Response) => {
  try {
    res.render('admin/users/list.html', {
      title: 'User Management',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering users list page:', error);
    res.status(500).send('Error rendering users list page');
  }
});

/**
 * GET /admin/users/create
 * Create user page
 */
router.get('/users/create', (req: Request, res: Response) => {
  try {
    res.render('admin/users/create.html', {
      title: 'Create User',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering create user page:', error);
    res.status(500).send('Error rendering create user page');
  }
});

/**
 * GET /admin/models/test
 * Models test page
 */
router.get('/models/test', (req: Request, res: Response) => {
  try {
    res.render('admin/models/parallel-test.html', {
      title: 'Parallel Model Testing',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering models test page:', error);
    res.status(500).send('Error rendering models test page');
  }
});

/**
 * GET /admin/nova-sonic/test
 * Nova Sonic test page
 */
router.get('/nova-sonic/test', (req: Request, res: Response) => {
  try {
    res.render('admin/nova_sonic/test.html', {
      title: 'Test Nova Sonic',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering Nova Sonic test page:', error);
    res.status(500).send('Error rendering Nova Sonic test page');
  }
});

/**
 * GET /admin/nova-sonic/connection-test
 * Nova Sonic connection test page
 */
router.get('/nova-sonic/connection-test', (req: Request, res: Response) => {
  try {
    res.render('admin/nova_sonic/connection-test.html', {
      title: 'Nova Sonic Connection Test',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering Nova Sonic connection test page:', error);
    res.status(500).send('Error rendering Nova Sonic connection test page');
  }
});

/**
 * GET /admin/nova-sonic/debug
 * Nova Sonic debug page
 */
router.get('/nova-sonic/debug', (req: Request, res: Response) => {
  try {
    res.render('admin/nova_sonic/debug.html', {
      title: 'Nova Sonic Debug',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering Nova Sonic debug page:', error);
    res.status(500).send('Error rendering Nova Sonic debug page');
  }
});

/**
 * GET /admin/login
 * Admin login page
 */
router.get('/login', (req: Request, res: Response) => {
  try {
    // If already logged in, redirect to dashboard
    if ((req.session as any)?.admin_user) {
      return res.redirect('/admin');
    }

    res.render('admin/login.html', {
      title: 'Admin Login',
      error: req.query.error || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering login page:', error);
    res.status(500).send('Error rendering login page');
  }
});

/**
 * GET /admin/system-status
 * System status page
 */
router.get('/system-status', (req: Request, res: Response) => {
  try {
    res.render('admin/system/status.html', {
      title: 'System Status',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering system status page:', error);
    res.status(500).send('Error rendering system status page');
  }
});

/**
 * GET /admin/memory/test
 * Memory service test page
 */
router.get('/memory/test', (req: Request, res: Response) => {
  try {
    res.render('admin/memory-test.html', {
      title: 'Memory Service Test',
      admin_user: (req.session as any)?.admin_user || null,
      request: {
        url: {
          path: req.path,
        },
      },
    });
  } catch (error) {
    logger.error('Error rendering memory test page:', error);
    res.status(500).send('Error rendering memory test page');
  }
});

/**
 * POST /admin/auth/login
 * Process admin login form submission
 */
router.post('/auth/login', login);

/**
 * GET /admin/auth/logout
 * Process admin logout
 */
router.get('/auth/logout', logout);

export default router;
