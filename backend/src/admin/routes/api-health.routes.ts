import { Router, Request, Response } from 'express';
import { logger } from '../../common/logger';
import { PrismaClient } from '@prisma/client';
import os from 'os';

const router = Router();

/**
 * @swagger
 * /admin/api-health:
 *   get:
 *     summary: Get comprehensive API health status
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: API health status
 *       500:
 *         description: Server error
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthStatus: any = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      node: {
        version: process.version,
        memory: process.memoryUsage(),
      },
      system: {
        platform: os.platform(),
        cpus: os.cpus().length,
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem(),
        },
        loadAverage: os.loadavg(),
      },
      services: {},
      routes: {
        admin: {
          api: [],
          ui: [],
        },
      },
    };

    // Check database connection
    try {
      const prisma = new PrismaClient();
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
      healthStatus.services.database = {
        status: 'connected',
        type: 'PostgreSQL',
      };
    } catch (dbError) {
      healthStatus.services.database = {
        status: 'error',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error',
      };
      healthStatus.status = 'degraded';
    }

    // Check session
    healthStatus.services.session = {
      status: req.session ? 'active' : 'inactive',
      sessionId: req.sessionID || 'none',
      adminUser: (req.session as any)?.admin_user ? 'authenticated' : 'not authenticated',
    };

    // List admin API routes
    if (req.app._router) {
      const adminApiRoutes: any[] = [];
      req.app._router.stack.forEach((middleware: any) => {
        if (middleware.name === 'router' && middleware.regexp.test('/api/admin')) {
          middleware.handle.stack.forEach((layer: any) => {
            if (layer.route) {
              adminApiRoutes.push({
                path: `/api/admin${layer.route.path}`,
                methods: Object.keys(layer.route.methods),
              });
            }
          });
        }
      });
      healthStatus.routes.admin.api = adminApiRoutes;
    }

    // Check Clerk configuration
    healthStatus.services.clerk = {
      publishableKey: process.env.CLERK_PUBLISHABLE_KEY ? 'configured' : 'not configured',
      secretKey: process.env.CLERK_SECRET_KEY ? 'configured' : 'not configured',
    };

    // Check AI service configurations
    healthStatus.services.aiProviders = {
      openai: process.env.OPENAI_API_KEY ? 'configured' : 'not configured',
      anthropic: process.env.ANTHROPIC_API_KEY ? 'configured' : 'not configured',
      google: process.env.GOOGLE_GENAI_API_KEY ? 'configured' : 'not configured',
      azure: process.env.AZURE_OPENAI_API_KEY ? 'configured' : 'not configured',
      amazon: process.env.AWS_ACCESS_KEY_ID ? 'configured' : 'not configured',
    };

    res.json(healthStatus);
  } catch (error) {
    logger.error('Error checking API health:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check API health',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @swagger
 * /admin/api-health/routes:
 *   get:
 *     summary: List all registered admin routes
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of admin routes
 */
router.get('/routes', (req: Request, res: Response) => {
  try {
    const routes: any = {
      api: [],
      ui: [],
      all: [],
    };

    // Get the admin router from the request
    const adminRouter = req.app._router;
    if (adminRouter && adminRouter.stack) {
      adminRouter.stack.forEach((middleware: any) => {
        if (middleware.route) {
          // Direct routes
          routes.all.push({
            path: middleware.route.path,
            methods: Object.keys(middleware.route.methods),
            type: 'direct',
          });
        } else if (middleware.name === 'router' && middleware.regexp) {
          // Sub-routers
          const regexpStr = middleware.regexp.toString();
          const basePath = regexpStr.match(/\\\/([^\\]+)/)?.[1] || '';
          
          if (middleware.handle && middleware.handle.stack) {
            middleware.handle.stack.forEach((layer: any) => {
              if (layer.route) {
                const fullPath = `/${basePath}${layer.route.path}`;
                const routeInfo = {
                  path: fullPath,
                  methods: Object.keys(layer.route.methods),
                  type: 'router',
                };
                
                routes.all.push(routeInfo);
                
                if (fullPath.startsWith('/api/admin')) {
                  routes.api.push(routeInfo);
                } else if (fullPath.startsWith('/admin') && !fullPath.startsWith('/api/admin')) {
                  routes.ui.push(routeInfo);
                }
              }
            });
          }
        }
      });
    }

    res.json({
      status: 'ok',
      routes,
      counts: {
        total: routes.all.length,
        api: routes.api.length,
        ui: routes.ui.length,
      },
    });
  } catch (error) {
    logger.error('Error listing routes:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to list routes',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /admin/api-health/test-error:
 *   get:
 *     summary: Test error handling
 *     tags: [Admin]
 *     responses:
 *       500:
 *         description: Test error response
 */
router.get('/test-error', (_req: Request, _res: Response) => {
  throw new Error('This is a test error for debugging purposes');
});

export default router;
