import { Router } from 'express';
import { getSystemStatus } from '../controllers/system-status.controller';

const router = Router();

/**
 * @swagger
 * /admin/api/system-status:
 *   get:
 *     summary: Get system status information
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: System status information
 *       500:
 *         description: Server error
 */
router.get('/', getSystemStatus);

export default router;
