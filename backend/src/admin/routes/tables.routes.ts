import { Router, Request, Response, RequestHandler } from 'express';
import { getAllTables, getTableDetails, queryTable } from '../controllers/tables.controller';
import { logger } from '../../common/logger';

const router = Router();

/**
 * @swagger
 * /admin/tables:
 *   get:
 *     summary: Get all database tables
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: List of database tables
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response) => {
  try {
    await getAllTables(req, res);
  } catch (error) {
    logger.error('Error getting tables:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tables',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/tables/{tableName}:
 *   get:
 *     summary: Get details of a specific table
 *     tags: [Admin]
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Table details
 *       400:
 *         description: Invalid table name
 *       500:
 *         description: Server error
 */
router.get('/:tableName', (async (req: Request, res: Response) => {
  try {
    await getTableDetails(req, res);
  } catch (error) {
    logger.error('Error getting table details:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get table details',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /admin/tables/{tableName}/query:
 *   get:
 *     summary: Query a table with filtering and sorting
 *     tags: [Admin]
 *     parameters:
 *       - in: path
 *         name: tableName
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: id
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Query results
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Server error
 */
router.get('/:tableName/query', (async (req: Request, res: Response) => {
  try {
    await queryTable(req, res);
  } catch (error) {
    logger.error('Error querying table:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to query table',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}) as RequestHandler);

export default router;
