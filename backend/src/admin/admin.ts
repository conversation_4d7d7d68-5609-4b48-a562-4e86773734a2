/**
 * Admin API endpoints and UI routes for the KAPI Node.js backend
 */

import express from 'express';
import session from 'express-session';

import { logger } from '../common/logger';

import { isAuthenticated, requireAdmin, getCurrentAdminUser } from './middleware/auth.middleware';
import apiHealthRoutes from './routes/api-health.routes';
import authRoutes from './routes/auth.routes';
import conversationsRoutes from './routes/conversations.routes';
import dbHealthRoutes from './routes/db-health.routes';
import modelsRoutes from './routes/models.routes';
// import schemaDebugRoutes from './routes/schema-debug.routes';
import servicesRoutes from './routes/services.routes';
import systemStatusRoutes from './routes/system-status.routes';
import tablesRoutes from './routes/tables.routes';
import uiRoutes from './routes/ui.routes';
import novaSonicRoutes from './routes/nova-sonic.routes';
import novaSonicStatusRoutes from './routes/nova-sonic-status-proxy.routes';
import usersRoutes from './routes/users.routes';

// Create a function to initialize admin routes
async function initializeAdmin() {
  try {
    // Create a router for admin API endpoints
    const apiRouter = express.Router();

    // Create a router for admin UI routes
    const uiRouter = express.Router();
    
    // Log initialization
    logger.info('Starting admin initialization...');

    // Add session middleware
    const sessionMiddleware = session({
      secret: process.env.SESSION_SECRET || 'admin-secret',
      resave: true, // Changed to true to ensure session is saved
      saveUninitialized: true, // Changed to true to create session for all requests
      name: 'kapi_admin_session', // Set a specific name for the cookie
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        httpOnly: true,
        path: '/', // Ensure cookie is available for all paths
        sameSite: 'lax', // Helps prevent CSRF
      },
    });

    logger.info('Session middleware configured');

    // Apply session middleware to both routers
    apiRouter.use(sessionMiddleware);
    uiRouter.use(sessionMiddleware);

    // Add current admin user middleware to UI router
    uiRouter.use(getCurrentAdminUser);

    // Add a simple health check endpoint
    apiRouter.get('/health', (_req, res, _next) => {
      res.status(200).json({
        status: 'ok',
        message: 'Admin API is running',
        timestamp: new Date().toISOString(),
      });
    });

    // Register admin API routes
    try {
      // Add API health check route (accessible without auth in dev)
      if (process.env.NODE_ENV !== 'production') {
        apiRouter.use('/api-health', apiHealthRoutes);
        logger.info('API health routes registered (no auth required in dev)');
      } else {
        apiRouter.use('/api-health', isAuthenticated, requireAdmin, apiHealthRoutes);
        logger.info('API health routes registered (auth required in production)');
      }
    } catch (e) {
      logger.error('Error registering API health routes:', e);
      throw e;
    }
    
    try {
      logger.info('[DEBUG] About to register auth routes...');
      logger.info('[DEBUG] authRoutes type:', typeof authRoutes);
      logger.info('[DEBUG] authRoutes stack length:', authRoutes.stack?.length);
      // Log each route in authRoutes
      if (authRoutes.stack) {
        authRoutes.stack.forEach((layer: any, index: number) => {
          if (layer.route) {
            logger.info(`[DEBUG] authRoutes[${index}] path: ${layer.route.path}`);
          }
        });
      }
      apiRouter.use('/auth', authRoutes);
      logger.info('Auth routes registered successfully');
    } catch (e) {
      logger.error('Error registering auth routes:', e);
      throw e;
    }
    
    try {
      apiRouter.use('/tables', isAuthenticated, requireAdmin, tablesRoutes);
      logger.info('Tables routes registered successfully');
    } catch (e) {
      logger.error('Error registering tables routes:', e);
      throw e;
    }
    
    try {
      apiRouter.use('/models', isAuthenticated, requireAdmin, modelsRoutes);
      logger.info('Models routes registered successfully');
    } catch (e) {
      logger.error('Error registering models routes:', e);
      throw e;
    }
    
    try {
      apiRouter.use('/conversations', isAuthenticated, requireAdmin, conversationsRoutes);
      logger.info('Conversations routes registered successfully');
    } catch (e) {
      logger.error('Error registering conversations routes:', e);
      throw e;
    }
    
    try {
      apiRouter.use('/users', isAuthenticated, requireAdmin, usersRoutes);
      logger.info('Users routes registered successfully');
    } catch (e) {
      logger.error('Error registering users routes:', e);
      throw e;
    }

    // Debug routes - access without authentication in development mode
    if (process.env.NODE_ENV !== 'production') {
      // apiRouter.use('/debug', schemaDebugRoutes);
      apiRouter.use('/db-health', dbHealthRoutes);
      // Nova Sonic routes without auth in development
      apiRouter.use('/nova-sonic', novaSonicRoutes);
      apiRouter.use('/nova-sonic-status', novaSonicStatusRoutes);
      logger.info('Debug and Nova Sonic routes mounted without authentication in development mode');
    } else {
      // apiRouter.use('/debug', isAuthenticated, requireAdmin, schemaDebugRoutes);
      apiRouter.use('/db-health', isAuthenticated, requireAdmin, dbHealthRoutes);
      apiRouter.use('/nova-sonic', isAuthenticated, requireAdmin, novaSonicRoutes);
      apiRouter.use('/nova-sonic-status', isAuthenticated, requireAdmin, novaSonicStatusRoutes);
    }

    apiRouter.use('/services', isAuthenticated, requireAdmin, servicesRoutes);
    apiRouter.use('/system-status', isAuthenticated, requireAdmin, systemStatusRoutes);
    logger.info('Services, system status, and Nova Sonic routes registered successfully');

    // Register admin UI routes - some routes need authentication, others don't
    // Authentication will be applied selectively in the UI routes file
    uiRouter.use('/', uiRoutes);

    // Add a catch-all route for admin API
    apiRouter.use((req, res) => {
      logger.error(`API endpoint not found: ${req.baseUrl}${req.url}, Method: ${req.method}`);
    
      try {
        const availableRoutes = apiRouter.stack
          .filter((r: any) => r.route && r.route.path)
          .map((r: any) => {
            const methods =
              r.route && typeof r.route === 'object'
                ? Object.keys(r.route.methods || {}).join(',')
                : '';
            return {
              path: r.route?.path || '',
              methods,
            };
          });
    
        logger.info(`Available routes: ${JSON.stringify(availableRoutes)}`);
      } catch (error) {
        logger.error('Error logging available routes:', error);
      }
    
      res.status(404).json({
        status: 'error',
        message: 'Admin API endpoint not found',
        requestedUrl: `${req.baseUrl}${req.url}`,
        method: req.method,
        help: 'Check the URL and method, and make sure you use /api/admin/ not /admin/api/',
      });
    });

    logger.info('Admin routes initialized');
    logger.info(`Admin API router has ${apiRouter.stack.length} layers`);
    logger.info(`Admin UI router has ${uiRouter.stack.length} layers`);
    
    // Log the UI routes for debugging
    uiRouter.stack.forEach((layer: any, index: number) => {
      if (layer.route) {
        logger.info(`UI Route ${index}: ${Object.keys(layer.route.methods).join(',')} ${layer.route.path}`);
      }
    });
    
    return { apiRouter, uiRouter };
  } catch (error) {
    logger.error('Admin initialization error:', error);
    throw error;
  }
}

// Export the initialization function
export { initializeAdmin };
