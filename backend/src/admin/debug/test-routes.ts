/**
 * Debug script to identify which route is causing the path-to-regexp error
 */

import express from 'express';
import { logger } from '../../common/logger';

export async function testRouteImports() {
  const results: { name: string; status: string; error?: any }[] = [];

  // Test each route import individually
  const routesToTest = [
    { name: 'auth.routes', path: '../routes/auth.routes' },
    { name: 'conversations.routes', path: '../routes/conversations.routes' },
    { name: 'db-health.routes', path: '../routes/db-health.routes' },
    { name: 'models.routes', path: '../routes/models.routes' },
    { name: 'schema-debug.routes', path: '../routes/schema-debug.routes' },
    { name: 'services.routes', path: '../routes/services.routes' },
    { name: 'system-status.routes', path: '../routes/system-status.routes' },
    { name: 'tables.routes', path: '../routes/tables.routes' },
    { name: 'ui.routes', path: '../routes/ui.routes' },
  ];

  for (const route of routesToTest) {
    try {
      logger.info(`Testing import of ${route.name}...`);
      const router = await import(route.path);
      
      // Try to create a test router and use the imported routes
      const testRouter = express.Router();
      testRouter.use('/', router.default);
      
      results.push({ name: route.name, status: 'success' });
      logger.info(`✓ ${route.name} imported successfully`);
    } catch (error) {
      results.push({ name: route.name, status: 'failed', error });
      logger.error(`✗ ${route.name} failed:`, error);
    }
  }

  return results;
}

// If running directly
if (require.main === module) {
  testRouteImports().then((results) => {
    console.log('\nRoute Import Test Results:');
    console.log('==========================');
    results.forEach((result) => {
      if (result.status === 'success') {
        console.log(`✓ ${result.name}: SUCCESS`);
      } else {
        console.log(`✗ ${result.name}: FAILED`);
        console.log(`  Error: ${result.error?.message || 'Unknown error'}`);
      }
    });
  });
}
