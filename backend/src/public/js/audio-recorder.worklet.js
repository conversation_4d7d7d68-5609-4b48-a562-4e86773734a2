// Audio recorder worklet for Nova Sonic
// Handles audio recording and downsampling

class AudioRecorderProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    
    // Enhanced buffer management
    this.buffers = [];
    this.isRecording = false;
    this.targetSampleRate = 16000; // Target sample rate for Nova Sonic
    this.sampleRatio = sampleRate / this.targetSampleRate;
    this.needsDownsampling = this.sampleRatio > 1.01; // Allow small rounding errors
    
    // Enhanced filtering for better audio quality
    this.filterCoeff = 0.8;
    this.filterState = 0;
    
    // Audio level monitoring for VAD (Voice Activity Detection)
    this.audioLevel = 0;
    this.levelSmoothingFactor = 0.9;
    this.silenceThreshold = 0.01;
    this.silenceFrames = 0;
    this.maxSilenceFrames = 10; // Report silence after 10 frames
    
    // Performance monitoring
    this.processedFrames = 0;
    this.droppedFrames = 0;
    this.lastReportTime = 0;
    
    // Buffer size management
    this.maxBufferSize = 1024 * 100; // Prevent excessive memory usage
    
    // Process messages from the main thread
    this.port.onmessage = (event) => {
      if (event.data.type === 'start') {
        this.isRecording = true;
        this.buffers = [];
        this.resetMetrics();
      } else if (event.data.type === 'stop') {
        this.isRecording = false;
      } else if (event.data.type === 'getMetrics') {
        this.sendMetrics();
      }
    };
  }
  
  resetMetrics() {
    this.processedFrames = 0;
    this.droppedFrames = 0;
    this.lastReportTime = currentTime;
    this.audioLevel = 0;
    this.silenceFrames = 0;
  }
  
  sendMetrics() {
    const now = currentTime;
    const duration = now - this.lastReportTime;
    
    this.port.postMessage({
      type: 'metrics',
      data: {
        processedFrames: this.processedFrames,
        droppedFrames: this.droppedFrames,
        audioLevel: this.audioLevel,
        isRecording: this.isRecording,
        duration: duration,
        sampleRate: sampleRate,
        targetSampleRate: this.targetSampleRate,
        needsDownsampling: this.needsDownsampling
      }
    });
  }

  // Apply a simple low-pass filter to prevent aliasing when downsampling
  applyLowPassFilter(input) {
    const output = new Float32Array(input.length);
    for (let i = 0; i < input.length; i++) {
      // Simple one-pole low-pass filter
      this.filterState = this.filterState * this.filterCoeff + input[i] * (1 - this.filterCoeff);
      output[i] = this.filterState;
    }
    return output;
  }
  
  // Downsample the audio to the target sample rate
  downsample(input) {
    if (!this.needsDownsampling) {
      return input;
    }
    
    // First apply low-pass filter to prevent aliasing
    const filtered = this.applyLowPassFilter(input);
    
    // Calculate output size
    const outputLength = Math.floor(input.length / this.sampleRatio);
    const output = new Float32Array(outputLength);
    
    // Perform proper downsampling with linear interpolation
    for (let i = 0; i < outputLength; i++) {
      const exactPos = i * this.sampleRatio;
      const intPos = Math.floor(exactPos);
      const fraction = exactPos - intPos;
      
      // Linear interpolation between samples
      if (intPos + 1 < filtered.length) {
        output[i] = filtered[intPos] * (1 - fraction) + filtered[intPos + 1] * fraction;
      } else {
        output[i] = filtered[intPos];
      }
    }
    
    return output;
  }
  
  // Convert Float32Array to Int16Array for PCM encoding
  floatToInt16(input) {
    const output = new Int16Array(input.length);
    for (let i = 0; i < input.length; i++) {
      // Convert to 16-bit PCM
      const s = Math.max(-1, Math.min(1, input[i]));
      output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    return output;
  }

  // Calculate RMS audio level for voice activity detection
  calculateAudioLevel(input) {
    let sum = 0;
    for (let i = 0; i < input.length; i++) {
      sum += input[i] * input[i];
    }
    const rms = Math.sqrt(sum / input.length);
    
    // Smooth the audio level
    this.audioLevel = this.audioLevel * this.levelSmoothingFactor + 
                     rms * (1 - this.levelSmoothingFactor);
    
    return this.audioLevel;
  }

  process(inputs, outputs) {
    // Enhanced processing with metrics and VAD
    this.processedFrames++;
    
    // Get the first input channel
    const input = inputs[0][0];
    
    if (this.isRecording && input && input.length > 0) {
      try {
        // Calculate audio level for VAD
        const audioLevel = this.calculateAudioLevel(input);
        
        // Check for silence
        if (audioLevel < this.silenceThreshold) {
          this.silenceFrames++;
          if (this.silenceFrames === this.maxSilenceFrames) {
            this.port.postMessage({
              type: 'silence-detected',
              audioLevel: audioLevel
            });
          }
        } else {
          if (this.silenceFrames >= this.maxSilenceFrames) {
            this.port.postMessage({
              type: 'voice-detected',
              audioLevel: audioLevel
            });
          }
          this.silenceFrames = 0;
        }
        
        // Downsample if needed
        const downsampled = this.downsample(input);
        
        // Convert to 16-bit PCM
        const pcmData = this.floatToInt16(downsampled);
        
        // Buffer size management
        if (this.buffers.length > this.maxBufferSize) {
          this.droppedFrames++;
          this.buffers = this.buffers.slice(-Math.floor(this.maxBufferSize / 2));
        }
        
        // Send to main thread with enhanced metadata
        this.port.postMessage({
          type: 'audio-data',
          audioData: pcmData.buffer,
          audioLevel: audioLevel,
          timestamp: currentTime,
          frameNumber: this.processedFrames
        }, [pcmData.buffer]); // Transfer buffer ownership for better performance
        
        // Periodic metrics reporting
        if (this.processedFrames % 100 === 0) { // Every 100 frames
          this.sendMetrics();
        }
        
      } catch (error) {
        this.droppedFrames++;
        this.port.postMessage({
          type: 'processing-error',
          error: error.message
        });
      }
    }
    
    // Keep the processor alive
    return true;
  }
}

registerProcessor('audio-recorder-processor', AudioRecorderProcessor);
