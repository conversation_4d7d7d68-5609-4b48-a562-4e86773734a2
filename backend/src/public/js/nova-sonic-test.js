/**
 * Simple Nova Sonic connection test
 * This script tests basic Socket.IO connectivity without audio complexity
 */

console.log('Nova Sonic Connection Test Starting...');

// Test 1: Check if Socket.IO is loaded
if (typeof io === 'undefined') {
  console.error('❌ Socket.IO not loaded!');
} else {
  console.log('✅ Socket.IO loaded');
  if (io.version) {
    console.log('   Version:', io.version);
  }
  if (io.protocol) {
    console.log('   Protocol:', io.protocol);
  }
}

// Test 2: Try to connect to the default namespace first
console.log('\n📡 Testing default namespace connection...');
const defaultSocket = io('/', {
  transports: ['polling', 'websocket'], // Try polling first
  reconnection: false,
  timeout: 5000
});

defaultSocket.on('connect', () => {
  console.log('✅ Connected to default namespace');
  console.log('   Socket ID:', defaultSocket.id);
  defaultSocket.disconnect();
});

defaultSocket.on('connect_error', (error) => {
  console.error('❌ Default namespace connection error:', error.message);
});

// Test 3: Try to connect to Nova Sonic namespace
setTimeout(() => {
  console.log('\n📡 Testing Nova Sonic namespace connection...');
  
  const novaSonicSocket = io('/ws/nova-sonic', {
    transports: ['polling', 'websocket'], // Try polling first
    reconnection: false,
    path: '/socket.io/',
    timeout: 5000
  });

  novaSonicSocket.on('connect', () => {
    console.log('✅ Connected to Nova Sonic namespace!');
    console.log('   Socket ID:', novaSonicSocket.id);
    console.log('   Transport:', novaSonicSocket.io.engine.transport.name);
    
    // Test sending a message
    console.log('\n📤 Testing message send...');
    novaSonicSocket.emit('ping');
    
    // Listen for responses
    novaSonicSocket.on('connected', (data) => {
      console.log('✅ Received "connected" event:', data);
    });
    
    novaSonicSocket.on('pong', () => {
      console.log('✅ Received pong response');
    });
    
    // Disconnect after 5 seconds
    setTimeout(() => {
      console.log('\n🔌 Disconnecting...');
      novaSonicSocket.disconnect();
    }, 5000);
  });

  novaSonicSocket.on('connect_error', (error) => {
    console.error('❌ Nova Sonic connection error:', error.message);
    console.error('   Error type:', error.type);
    console.error('   Error context:', error.context);
  });

  novaSonicSocket.on('disconnect', (reason) => {
    console.log('🔌 Disconnected:', reason);
  });

  // Log all events for debugging
  const originalEmit = novaSonicSocket.onevent;
  novaSonicSocket.onevent = function(packet) {
    console.log('📨 Received event:', packet.data[0], packet.data.slice(1));
    originalEmit.call(this, packet);
  };

}, 1000);

// Test 4: Check endpoints
setTimeout(() => {
  console.log('\n🔍 Checking Nova Sonic endpoints...');
  
  // Check status endpoint
  fetch('/api/admin/nova-sonic/status')
    .then(res => res.json())
    .then(data => {
      console.log('✅ Status endpoint response:', data);
    })
    .catch(err => {
      console.error('❌ Status endpoint error:', err);
    });
    
  // Check debug endpoint
  fetch('/api/admin/nova-sonic/debug')
    .then(res => res.json())
    .then(data => {
      console.log('✅ Debug endpoint response:', data);
    })
    .catch(err => {
      console.error('❌ Debug endpoint error:', err);
    });
}, 2000);
