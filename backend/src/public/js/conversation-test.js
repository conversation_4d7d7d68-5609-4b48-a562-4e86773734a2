/**
 * Conversation Testing Interface
 *
 * This script handles the conversation testing interface, allowing users to:
 * - Create new conversations
 * - Load existing conversations
 * - Send messages with different models
 * - View streaming responses
 */

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const conversationSelect = document.getElementById('conversation-select');
    const modelSelect = document.getElementById('model-select');
    const modelInfo = document.getElementById('model-info');
    const temperatureRange = document.getElementById('temperature-range');
    const temperatureValue = document.getElementById('temperature-value');
    const maxTokens = document.getElementById('max-tokens');
    const memoryCount = document.getElementById('memory-count');
    const promptInput = document.getElementById('prompt-input');
    const tokenCount = document.getElementById('token-count');
    const messageContainer = document.getElementById('message-container');
    const sendBtn = document.getElementById('send-btn');
    const clearBtn = document.getElementById('clear-btn');
    const streamingStatus = document.getElementById('streaming-status');
    const createTestConversationBtn = document.getElementById('create-test-conversation-btn');
    const createDbTestBtn = document.getElementById('create-db-test-btn');
    const createDirectTestBtn = document.getElementById('create-direct-test-btn');
    const createMultiModelTestBtn = document.getElementById('create-multi-model-test-btn');
    const checkSchemaBtn = document.getElementById('check-schema-btn');
    const ensureAdminBtn = document.getElementById('ensure-admin-btn');

    // State variables
    let currentConversationId = null;
    let eventSource = null;

    // Model info data - will be populated from API
    let modelInfoData = {};

    // Initialize the page
    init();

    // Initialize the page
    function init() {
        // Load model information
        loadModelInfo();

        // Load conversations
        loadConversations();

        // Set up event listeners
        setupEventListeners();

        // Update token count
        updateTokenCount();
    }

    // Load model information from API
    async function loadModelInfo() {
        try {
            console.log('Loading model information...');

            // Add a timestamp to prevent caching
            const timestamp = new Date().getTime();
            const url = `/api/admin/models?_=${timestamp}`;

            const response = await fetch(url, {
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch model information: ${response.status}`);
            }

            const data = await response.json();

            if (data && data.status === 'success' && data.data) {
                // Process model data
                data.data.forEach(model => {
                    // Create model info entry
                    modelInfoData[model.id] = {
                        provider: model.provider.charAt(0).toUpperCase() + model.provider.slice(1), // Capitalize provider
                        inputPrice: `$${model.inputPrice.toFixed(5)} / 1K tokens`,
                        outputPrice: `$${model.outputPrice.toFixed(5)} / 1K tokens`,
                        contextWindow: model.tpmLimit ? `${(model.tpmLimit / 1000).toFixed(0)}K tokens` : 'Unknown',
                        description: model.usedIn && model.usedIn.length > 0
                            ? `Used for: ${model.usedIn.join(', ')}`
                            : `${model.type} model from ${model.provider}`
                    };
                });

                console.log('Model information loaded:', modelInfoData);
            } else {
                console.warn('Invalid model data format or empty response');
                console.log('Response data:', data);
            }
        } catch (error) {
            console.error('Error loading model information:', error);
            // Create a fallback entry for each model in the dropdown
            const modelOptions = Array.from(modelSelect.options).slice(1); // Skip the first option (placeholder)

            modelOptions.forEach(option => {
                const modelId = option.value;
                if (modelId && !modelInfoData[modelId]) {
                    // Extract provider from optgroup label if available
                    const optgroup = option.parentElement;
                    const provider = optgroup && optgroup.label ? optgroup.label : 'Unknown';

                    modelInfoData[modelId] = {
                        provider: provider,
                        inputPrice: 'See models.tsv',
                        outputPrice: 'See models.tsv',
                        contextWindow: 'Unknown',
                        description: `${modelId} (No details available)`
                    };
                }
            });
        }
    }

    // Set up event listeners
    function setupEventListeners() {
        // Conversation select change
        conversationSelect.addEventListener('change', function() {
            const selectedConversationId = this.value;
            if (selectedConversationId === 'new') {
                // Create new conversation
                currentConversationId = null;
                clearMessages();
                addSystemMessage('New conversation started. Select a model and send a message to begin.');
            } else if (selectedConversationId === 'create-test') {
                // Trigger the create test conversation button
                createTestConversationBtn.click();
                // Reset the dropdown to the first option
                conversationSelect.selectedIndex = 0;
            } else if (selectedConversationId) {
                // Load existing conversation
                loadConversation(selectedConversationId);
            }
        });

        // Create test conversation button click
        createTestConversationBtn.addEventListener('click', async function() {
            try {
                createTestConversationBtn.disabled = true;
                createTestConversationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

                addSystemMessage('Creating test conversation...');

                const response = await fetch('/api/admin/conversations/create-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Create test response status:', response.status);
                const responseText = await response.text();
                console.log('Create test response text:', responseText);

                try {
                    const data = JSON.parse(responseText);
                    console.log('Create test parsed response:', data);

                    if (data.status === 'success') {
                        addSystemMessage(`Test conversation created with ID: ${data.conversation.id}`);

                        // Reload conversations
                        await loadConversations();

                        // Select the new conversation
                        if (data.conversation && data.conversation.id) {
                            conversationSelect.value = data.conversation.id;
                            loadConversation(data.conversation.id);
                        }
                    } else {
                        addSystemMessage(`Error creating test conversation: ${data.message || 'Unknown error'}`);
                    }
                } catch (parseError) {
                    console.error('Error parsing create test response:', parseError);
                    addSystemMessage(`Error parsing response: ${parseError.message}. Server returned: ${responseText.substring(0, 100)}...`);
                }
            } catch (error) {
                console.error('Error creating test conversation:', error);
                addSystemMessage(`Error creating test conversation: ${error.message}. Please try again.`);
            } finally {
                createTestConversationBtn.disabled = false;
                createTestConversationBtn.innerHTML = '<i class="fas fa-plus"></i> Create Test';
            }
        });

        // Create DB test conversation button click
        createDbTestBtn.addEventListener('click', async function() {
            try {
                createDbTestBtn.disabled = true;
                createDbTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

                addSystemMessage('Creating test conversation directly via database...');

                const response = await fetch('/api/admin/db-health/create-test-conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                console.log('DB test response status:', response.status);
                const responseText = await response.text();
                console.log('DB test response:', responseText);

                try {
                    const data = JSON.parse(responseText);

                    if (data.status === 'success') {
                        addSystemMessage(`DB Test conversation created with ID: ${data.conversation.id}`);
                        addSystemMessage(`Messages created: ${data.messages.length}`);

                        // Reload conversations
                        await loadConversations();

                        // Select the new conversation
                        if (data.conversation && data.conversation.id) {
                            conversationSelect.value = data.conversation.id;
                            loadConversation(data.conversation.id);
                        }
                    } else {
                        addSystemMessage(`Error creating DB test conversation: ${data.message || 'Unknown error'}`);
                        if (data.details) {
                            addSystemMessage(`Error details: ${data.details}`);
                        }
                    }
                } catch (parseError) {
                    console.error('Error parsing DB test response:', parseError);
                    addSystemMessage('Error parsing DB test response. Server may have returned invalid JSON.');
                    addSystemMessage(`Raw response begins with: ${responseText.substring(0, 100)}...`);
                }
            } catch (error) {
                console.error('Error creating DB test conversation:', error);
                addSystemMessage(`Error creating DB test conversation: ${error.message}. Please try again.`);
            } finally {
                createDbTestBtn.disabled = false;
                createDbTestBtn.innerHTML = '<i class="fas fa-database"></i> DB Test';
            }
        });

        // Create direct test conversation button click
        createDirectTestBtn.addEventListener('click', async function() {
            try {
                createDirectTestBtn.disabled = true;
                createDirectTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

                addSystemMessage('Creating test conversation directly via service...');

                const response = await fetch('/api/admin/conversations/create-direct-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Create direct test response status:', response.status);
                const responseText = await response.text();
                console.log('Create direct test response text:', responseText);

                try {
                    const data = JSON.parse(responseText);
                    console.log('Create direct test parsed response:', data);

                    if (data.status === 'success') {
                        addSystemMessage(`Direct test conversation created with ID: ${data.conversation.id}`);
                        if (data.messages && data.messages.length > 0) {
                            addSystemMessage(`Created ${data.messages.length} test messages`);
                        }

                        // Reload conversations
                        await loadConversations();

                        // Select the new conversation
                        if (data.conversation && data.conversation.id) {
                            conversationSelect.value = data.conversation.id;
                            loadConversation(data.conversation.id);
                        }
                    } else {
                        addSystemMessage(`Error creating direct test conversation: ${data.message || 'Unknown error'}`);
                    }
                } catch (parseError) {
                    console.error('Error parsing create direct test response:', parseError);
                    addSystemMessage(`Error parsing response: ${parseError.message}. Server returned: ${responseText.substring(0, 100)}...`);
                }
            } catch (error) {
                console.error('Error creating direct test conversation:', error);
                addSystemMessage(`Error creating direct test conversation: ${error.message}. Please try again.`);
            } finally {
                createDirectTestBtn.disabled = false;
                createDirectTestBtn.innerHTML = '<i class="fas fa-terminal"></i> Direct Test';
            }
        });

        // Ensure admin user button click
        ensureAdminBtn.addEventListener('click', async function() {
            try {
                ensureAdminBtn.disabled = true;
                ensureAdminBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

                addSystemMessage('Ensuring admin user exists in the database...');

                const response = await fetch('/api/admin/db-health/ensure-admin-user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                console.log('Ensure admin response status:', response.status);
                const responseText = await response.text();
                console.log('Ensure admin response:', responseText);

                try {
                    const data = JSON.parse(responseText);

                    if (data.status === 'success') {
                        if (response.status === 201) {
                            addSystemMessage(`Admin user created successfully with ID: ${data.user.id}`);
                        } else {
                            addSystemMessage(`Admin user already exists with ID: ${data.user.id}`);
                        }

                        // Reload conversations after ensuring admin user exists
                        await loadConversations();
                    } else {
                        addSystemMessage(`Error ensuring admin user: ${data.message || 'Unknown error'}`);
                        if (data.details) {
                            addSystemMessage(`Error details: ${data.details}`);
                        }
                    }
                } catch (parseError) {
                    console.error('Error parsing ensure admin response:', parseError);
                    addSystemMessage('Error parsing ensure admin response. Server may have returned invalid JSON.');
                    addSystemMessage(`Raw response begins with: ${responseText.substring(0, 100)}...`);
                }
            } catch (error) {
                console.error('Error ensuring admin user:', error);
                addSystemMessage(`Error ensuring admin user: ${error.message}. Please try again.`);
            } finally {
                ensureAdminBtn.disabled = false;
                ensureAdminBtn.innerHTML = '<i class="fas fa-user-shield"></i> Ensure Admin User';
            }
        });

        // Check schema button click
        checkSchemaBtn.addEventListener('click', async function() {
            try {
                checkSchemaBtn.disabled = true;
                checkSchemaBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

                addSystemMessage('Checking schema compatibility...');

                // First try the health check endpoint to see if we can reach the server
                const timestamp = new Date().getTime();
                const healthUrl = `/api/admin/debug/health?_=${timestamp}`;
                console.log('Checking debug health endpoint:', healthUrl);

                try {
                    const healthResponse = await fetch(healthUrl, {
                        headers: {
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        }
                    });

                    console.log('Health check status:', healthResponse.status);
                    const healthText = await healthResponse.text();
                    console.log('Health check response:', healthText);

                    if (!healthResponse.ok) {
                        addSystemMessage(`Health check failed with status ${healthResponse.status}. Server may be unreachable.`);
                        return;
                    }
                    addSystemMessage('API health check passed. Server is accessible.');
                } catch (healthError) {
                    console.error('Health check error:', healthError);
                    addSystemMessage(`Cannot reach debug API: ${healthError.message}. Server may be down or inaccessible.`);
                    return;
                }

                // Now check database connectivity
                const dbHealthUrl = `/api/admin/db-health/db-health?_=${timestamp}`;
                console.log('Checking DB health endpoint:', dbHealthUrl);

                try {
                    const dbHealthResponse = await fetch(dbHealthUrl, {
                        headers: {
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        }
                    });

                    console.log('DB health check status:', dbHealthResponse.status);
                    const dbHealthText = await dbHealthResponse.text();
                    console.log('DB health check response:', dbHealthText);

                    if (!dbHealthResponse.ok) {
                        addSystemMessage(`Database health check failed with status ${dbHealthResponse.status}. Database may be unreachable.`);
                        try {
                            const dbErrorData = JSON.parse(dbHealthText);
                            if (dbErrorData && dbErrorData.details) {
                                addSystemMessage(`Database error details: ${dbErrorData.details}`);
                            }
                        } catch (e) {
                            // Ignore parsing errors
                        }
                        return;
                    }
                    addSystemMessage('Database health check passed. Database is accessible.');
                } catch (dbHealthError) {
                    console.error('DB health check error:', dbHealthError);
                    addSystemMessage(`Cannot check database health: ${dbHealthError.message}. Database may be inaccessible.`);
                    return;
                }

                // If health check succeeded, try the schema debug endpoint
                const schemaUrl = `/api/admin/debug/schema-debug?_=${timestamp}`;
                console.log('Fetching schema debug URL:', schemaUrl);

                const response = await fetch(schemaUrl, {
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                console.log('Schema check response status:', response.status);

                // Log headers for debugging
                console.log('Response headers:', [...response.headers.entries()]);

                // Get the raw response text
                const responseText = await response.text();
                console.log('Raw response text:', responseText);

                // Try to parse the JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('Schema debug data:', data);

                    // Display summary
                    addSystemMessage(`Schema check successful. Found ${data.conversationCount} conversations in database.`);

                    // Display session info
                    if (data.sessionData) {
                        const sessionInfo = data.sessionData;
                        addSystemMessage(`Session info: User ID = ${sessionInfo.adminUserId} (${sessionInfo.adminUserIdType}), Session ID = ${sessionInfo.sessionId}`);
                    }

                    // Display sample conversation
                    if (data.sampleSanitizedConversation) {
                        const sampleConv = data.sampleSanitizedConversation;
                        addSystemMessage(`Sample conversation: ID=${sampleConv.id}, Title="${sampleConv.title}", User ID=${sampleConv.userId}`);
                    } else {
                        addSystemMessage('No sample conversation available.');
                    }
                } catch (parseError) {
                    console.error('Error parsing JSON:', parseError);
                    addSystemMessage(`Error parsing schema response: ${parseError.message}`);
                    addSystemMessage(`Raw response begins with: ${responseText.substring(0, 100)}...`);
                }
            } catch (error) {
                console.error('Error checking schema:', error);
                addSystemMessage(`Error checking schema: ${error.message}. Please try again.`);
            } finally {
                checkSchemaBtn.disabled = false;
                checkSchemaBtn.innerHTML = '<i class="fas fa-database"></i> Check Schema';
            }
        });

        // Model select change
        modelSelect.addEventListener('change', function() {
            const selectedModel = this.value;
            sendBtn.disabled = !selectedModel || !promptInput.value.trim();

            if (selectedModel && modelInfoData[selectedModel]) {
                const info = modelInfoData[selectedModel];
                modelInfo.innerHTML = `
                    <div><strong>Provider:</strong> ${info.provider}</div>
                    <div><strong>Input Price:</strong> ${info.inputPrice}</div>
                    <div><strong>Output Price:</strong> ${info.outputPrice}</div>
                    <div><strong>Context Window:</strong> ${info.contextWindow}</div>
                    <div><strong>Description:</strong> ${info.description}</div>
                `;
            } else if (selectedModel) {
                // If we don't have info for this model but it's selected, try to fetch it
                modelInfo.innerHTML = '<div class="text-muted">Loading model information...</div>';

                // Attempt to refresh model info
                loadModelInfo().then(() => {
                    // Update the display after fetching
                    if (modelInfoData[selectedModel]) {
                        const info = modelInfoData[selectedModel];
                        modelInfo.innerHTML = `
                            <div><strong>Provider:</strong> ${info.provider}</div>
                            <div><strong>Input Price:</strong> ${info.inputPrice}</div>
                            <div><strong>Output Price:</strong> ${info.outputPrice}</div>
                            <div><strong>Context Window:</strong> ${info.contextWindow}</div>
                            <div><strong>Description:</strong> ${info.description}</div>
                        `;
                    } else {
                        modelInfo.innerHTML = '<div class="text-muted">No information available for this model</div>';
                    }
                });
            } else {
                modelInfo.innerHTML = '';
            }
        });

        // Temperature range change
        temperatureRange.addEventListener('input', function() {
            temperatureValue.textContent = this.value;
        });

        // Prompt input change
        promptInput.addEventListener('input', function() {
            updateTokenCount();
            sendBtn.disabled = !this.value.trim() || !modelSelect.value;
        });

        // Send button click
        sendBtn.addEventListener('click', sendMessage);

        // Clear button click
        clearBtn.addEventListener('click', function() {
            promptInput.value = '';
            updateTokenCount();
            sendBtn.disabled = true;
        });

        // Allow pressing Enter to send message
        promptInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!sendBtn.disabled) {
                    sendMessage();
                }
            }
        });
    }

    // Load conversations
    async function loadConversations() {
        try {
            console.log('Loading conversations...');

            // Log any stored session information in localStorage or cookies
            console.log('localStorage:', { ...localStorage });
            console.log('Cookies:', document.cookie);

            // Log user information if available on the page
            const loggedInElement = document.querySelector('.logged-in-as');
            if (loggedInElement) {
                console.log('Logged in element found:', loggedInElement.textContent);
            } else {
                console.log('No logged-in element found on page');
            }

            // Try to get any admin session info from window
            if (window.admin_user) {
                console.log('Admin user from window:', window.admin_user);
            } else {
                console.log('No admin_user in window object');
            }

            // Show loading state in the dropdown
            // Keep the first option (Create New Conversation) and remove the rest
            while (conversationSelect.options.length > 1) {
                conversationSelect.remove(1);
            }

            // Add a loading option
            const loadingOption = document.createElement('option');
            loadingOption.value = "";
            loadingOption.textContent = "Loading conversations...";
            loadingOption.disabled = true;
            conversationSelect.appendChild(loadingOption);

            // Add a timestamp to prevent caching
            const timestamp = new Date().getTime();
            const url = `/api/admin/conversations?_=${timestamp}`;
            console.log('Fetching URL:', url);

            const response = await fetch(url, {
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            console.log('Response status:', response.status);

            // Handle unauthorized error (not logged in as admin)
            if (response.status === 401) {
                console.warn('Not authenticated as admin. You need to log in first.');

                // Remove the loading option
                conversationSelect.remove(loadingOption);

                // Add a "Login required" option
                const loginOption = document.createElement('option');
                loginOption.value = "";
                loginOption.textContent = "Please log in as admin first";
                loginOption.disabled = true;
                conversationSelect.appendChild(loginOption);

                // Add a "Create Test Conversation" option
                const createTestOption = document.createElement('option');
                createTestOption.value = "create-test";
                createTestOption.textContent = "Create Test Conversation";
                conversationSelect.appendChild(createTestOption);

                // Show a message to the user
                addSystemMessage('You need to log in as an admin to access conversations. You can create a test conversation using the button below.');
                return;
            }

            // Log response headers for debugging
            console.log('Response headers:', [...response.headers.entries()]);

            // Get the raw response text for better error diagnosis
            const responseText = await response.text();
            console.log('Raw response text:', responseText);

            // Attempt to validate the JSON before parsing
            let isValidJson = true;
            try {
                // Try to parse with a more forgiving approach first
                JSON.parse(responseText.trim());
                console.log('Seems to be valid JSON');
            } catch (jsonError) {
                console.error('JSON parse test failed:', jsonError);
                isValidJson = false;

                // Try to pinpoint where the JSON is malformed
                console.log('First 100 chars:', responseText.substring(0, 100));
                console.log('Last 100 chars:', responseText.substring(responseText.length - 100));

                // Check for common issues
                if (responseText.includes('<!DOCTYPE html>')) {
                    console.error('Response contains HTML instead of JSON');
                }
                if (responseText.includes('Error:') || responseText.includes('exception')) {
                    console.error('Response may contain an error message');
                }
            }

            // Check if the response is empty
            if (!responseText || responseText.trim() === '') {
                console.error('Empty response received from server');
                addSystemMessage('Server returned an empty response. The server might be experiencing issues.');
                throw new Error('Empty response from server');
            }

            // Parse the JSON with proper error handling
            let data;
            try {
                // Only try to parse if we think it's valid JSON
                if (isValidJson) {
                    data = JSON.parse(responseText);
                    console.log('Successfully parsed JSON:', data);

                    // Check if the response contains an error message
                    if (data.status === 'error') {
                        console.error('Server returned an error:', data.message);
                        addSystemMessage(`Server error: ${data.message}`);
                        throw new Error(`Server error: ${data.message}`);
                    }
                } else {
                    // Create a fallback data structure
                    console.warn('Using fallback empty data structure due to invalid JSON');
                    data = { status: 'error', message: 'Invalid JSON response', conversations: [] };
                }
            } catch (parseError) {
                console.error('Error parsing JSON:', parseError);
                // Display the raw response for debugging
                console.log('Raw response that failed to parse:', responseText);

                // Create a fallback data structure
                data = { status: 'error', message: 'Invalid JSON response', conversations: [] };
                throw new Error('Invalid JSON response');
            }

            // Remove the loading option
            while (conversationSelect.options.length > 1) {
                conversationSelect.remove(1);
            }

            if (data && data.conversations && data.conversations.length > 0) {
                console.log(`Found ${data.conversations.length} conversations to add to dropdown`);

                // Add conversation options
                data.conversations.forEach(conversation => {
                    console.log(`Adding conversation: ID=${conversation.id}, Title=${conversation.title || 'Untitled'}`);
                    const option = document.createElement('option');
                    option.value = conversation.id;
                    option.textContent = conversation.title || `Conversation ${conversation.id}`;
                    conversationSelect.appendChild(option);
                });
            } else {
                console.warn('No conversations found in the response or invalid response format');
                console.log('Response data structure:', data);

                // Add a "No conversations found" option
                const noConvOption = document.createElement('option');
                noConvOption.value = "";
                noConvOption.textContent = "No conversations found";
                noConvOption.disabled = true;
                conversationSelect.appendChild(noConvOption);
            }

            // Always add a "Create Test Conversation" option
            const createTestOption = document.createElement('option');
            createTestOption.value = "create-test";
            createTestOption.textContent = "Create Test Conversation";
            conversationSelect.appendChild(createTestOption);
        } catch (error) {
            console.error('Error loading conversations:', error);

            // Clear dropdown except first option
            while (conversationSelect.options.length > 1) {
                conversationSelect.remove(1);
            }

            // Add an error option
            const errorOption = document.createElement('option');
            errorOption.value = "";
            errorOption.textContent = "Error loading conversations";
            errorOption.disabled = true;
            conversationSelect.appendChild(errorOption);

            // Add a "Create Test Conversation" option
            const createTestOption = document.createElement('option');
            createTestOption.value = "create-test";
            createTestOption.textContent = "Create Test Conversation";
            conversationSelect.appendChild(createTestOption);

            // Show detailed error message
            const errorMessage = error.message || 'Unknown error';
            addSystemMessage(`Error loading conversations: ${errorMessage}. Please try again or create a test conversation.`);

            // Check if the user is logged in
            const loggedInElement = document.querySelector('.logged-in-as');
            if (loggedInElement) {
                const loggedInText = loggedInElement.textContent;
                console.log('Logged in status:', loggedInText);

                // If logged in but still getting errors, suggest session issues
                if (loggedInText && loggedInText.includes('@')) {
                    addSystemMessage('You appear to be logged in, but there might be an issue with your session. Try logging out and logging back in.');
                }
            } else {
                addSystemMessage('You might need to log in as an admin to access conversations.');
            }
        }
    }

    // Load conversation
    async function loadConversation(conversationId) {
        try {
            console.log(`Loading conversation ${conversationId}...`);
            const response = await fetch(`/api/admin/conversations/${conversationId}`);
            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Conversation data:', data);

            if (data) {
                currentConversationId = conversationId;
                clearMessages();

                if (data.messages && data.messages.length > 0) {
                    // Add messages
                    data.messages.forEach(message => {
                        if (message.role === 'user') {
                            addUserMessage(message.content);
                        } else if (message.role === 'assistant') {
                            addAssistantMessage(message.content, {
                                model: message.model,
                                promptTokens: message.promptTokens,
                                completionTokens: message.completionTokens,
                                totalTokens: message.promptTokens + message.completionTokens,
                                timeTaken: message.durationMs ? (message.durationMs / 1000).toFixed(2) : null,
                                cost: message.cost
                            });
                        } else if (message.role === 'system') {
                            addSystemMessage(message.content);
                        }
                    });
                } else {
                    addSystemMessage('This conversation has no messages yet.');
                }
            }
        } catch (error) {
            console.error('Error loading conversation:', error);
            addSystemMessage('Error loading conversation. Please try again.');
        }
    }

    // Send message
    async function sendMessage() {
        const prompt = promptInput.value.trim();
        const model = modelSelect.value;
        const temperature = parseFloat(temperatureRange.value);
        const maxTokensValue = parseInt(maxTokens.value);

        if (!prompt || !model) {
            return;
        }

        // Add user message
        addUserMessage(prompt);

        // Clear input
        promptInput.value = '';
        updateTokenCount();
        sendBtn.disabled = true;

        // Show streaming status
        streamingStatus.style.display = 'inline-block';

        try {
            if (!currentConversationId) {
                // Create new conversation
                await createConversation(prompt, model, temperature, maxTokensValue);
            } else {
                // Add message to existing conversation
                await addMessageToConversation(currentConversationId, prompt, model, temperature, maxTokensValue);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            addSystemMessage('Error sending message. Please try again.');
            streamingStatus.style.display = 'none';
        }
    }

    // Create new conversation
    async function createConversation(prompt, model, temperature, maxTokensValue) {
        try {
            // Create a placeholder for the assistant message
            const assistantMessageId = 'msg-' + Date.now();
            addAssistantMessage('', {}, assistantMessageId);

            // Start streaming
            startStreaming(null, prompt, model, temperature, maxTokensValue, assistantMessageId);
        } catch (error) {
            console.error('Error creating conversation:', error);
            throw error;
        }
    }

    // Add message to existing conversation
    async function addMessageToConversation(conversationId, prompt, model, temperature, maxTokensValue) {
        try {
            // Create a placeholder for the assistant message
            const assistantMessageId = 'msg-' + Date.now();
            addAssistantMessage('', {}, assistantMessageId);

            // Start streaming
            startStreaming(conversationId, prompt, model, temperature, maxTokensValue, assistantMessageId);
        } catch (error) {
            console.error('Error adding message to conversation:', error);
            throw error;
        }
    }

    // Start streaming
    function startStreaming(conversationId, prompt, model, temperature, maxTokensValue, assistantMessageId) {
        // Close any existing event source
        if (eventSource) {
            eventSource.close();
        }

        // Get memory count (number of previous messages to include)
        // If memoryCount is set to 0, explicitly disable memory
        // If memoryCount is not set or invalid, use a default of 10 to ensure memory works
        let memoryCountValue;
        if (memoryCount && memoryCount.value !== undefined) {
            memoryCountValue = parseInt(memoryCount.value);
            // Log the memory count value for debugging
            console.log(`Memory count from input: ${memoryCountValue}`);
        } else {
            memoryCountValue = 10; // Default to 10 messages if not specified
            console.log(`Using default memory count: ${memoryCountValue}`);
        }

        // Create the URL for the event source
        let url = '/api/admin/conversations/stream?';
        url += `prompt=${encodeURIComponent(prompt)}`;
        url += `&model=${encodeURIComponent(model)}`;
        url += `&temperature=${encodeURIComponent(temperature)}`;
        url += `&maxTokens=${encodeURIComponent(maxTokensValue)}`;
        url += `&memoryCount=${encodeURIComponent(memoryCountValue)}`;

        if (conversationId) {
            url += `&conversationId=${encodeURIComponent(conversationId)}`;
        }

        // Create the event source
        eventSource = new EventSource(url);

        // Initialize variables for streaming
        let fullContent = '';
        let metadata = {};

        // Handle messages
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);

                if (data.type === 'content') {
                    // Update the assistant message with the new content
                    fullContent += data.content;
                    updateAssistantMessage(assistantMessageId, fullContent);
                } else if (data.type === 'metadata') {
                    // Store metadata
                    metadata = data.metadata;
                } else if (data.type === 'conversation_id') {
                    // Update the current conversation ID
                    currentConversationId = data.conversation_id;

                    // Reload conversations
                    loadConversations();
                } else if (data.type === 'done') {
                    // Update the assistant message with the final content and metadata
                    updateAssistantMessage(assistantMessageId, fullContent, metadata);

                    // Close the event source
                    eventSource.close();
                    eventSource = null;

                    // Hide streaming status
                    streamingStatus.style.display = 'none';

                    // Enable send button if there's text in the input
                    sendBtn.disabled = !promptInput.value.trim() || !modelSelect.value;
                } else if (data.type === 'error') {
                    // Handle error message
                    console.error('Streaming error:', data.message);
                    
                    // Remove the empty assistant message
                    const assistantMsg = document.getElementById(assistantMessageId);
                    if (assistantMsg && !fullContent) {
                        assistantMsg.remove();
                    }
                    
                    // Add error message
                    addSystemMessage(`Error: ${data.message}`);
                    
                    // Close the event source
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                    }
                    
                    // Hide streaming status
                    streamingStatus.style.display = 'none';
                    
                    // Enable send button
                    sendBtn.disabled = !promptInput.value.trim() || !modelSelect.value;
                }
            } catch (error) {
                console.error('Error parsing event data:', error);
            }
        };

        // Handle errors
        eventSource.onerror = function(error) {
            console.error('EventSource error:', error);

            // Close the event source
            eventSource.close();
            eventSource = null;

            // Hide streaming status
            streamingStatus.style.display = 'none';

            // Enable send button if there's text in the input
            sendBtn.disabled = !promptInput.value.trim() || !modelSelect.value;

            // Add error message
            addSystemMessage('Error streaming response. Please try again.');
        };
    }

    // Add user message
    function addUserMessage(content) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message message-user';
        messageElement.innerHTML = `
            <div class="message-content">${escapeHtml(content)}</div>
        `;

        messageContainer.appendChild(messageElement);
        scrollToBottom();
    }

    // Add assistant message
    function addAssistantMessage(content, metadata = {}, id = null) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message message-assistant';
        if (id) {
            messageElement.id = id;
        }

        let metadataHtml = '';
        if (metadata.model) {
            metadataHtml = `
                <div class="message-hover-details">
                    Model: ${metadata.model}<br>
                    ${metadata.promptTokens ? `Prompt Tokens: ${metadata.promptTokens}<br>` : ''}
                    ${metadata.completionTokens ? `Completion Tokens: ${metadata.completionTokens}<br>` : ''}
                    ${metadata.totalTokens ? `Total Tokens: ${metadata.totalTokens}<br>` : ''}
                    ${metadata.timeTaken ? `Time: ${metadata.timeTaken}s<br>` : ''}
                    ${metadata.cost ? `Cost: $${metadata.cost.toFixed(6)}` : ''}
                </div>
            `;
        }

        messageElement.innerHTML = `
            <div class="message-content">${escapeHtml(content)}</div>
            ${metadataHtml}
        `;

        messageContainer.appendChild(messageElement);
        scrollToBottom();
    }

    // Update assistant message
    function updateAssistantMessage(id, content, metadata = null) {
        const messageElement = document.getElementById(id);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-content');
            if (contentElement) {
                contentElement.innerHTML = escapeHtml(content);
            }

            if (metadata) {
                let metadataHtml = `
                    <div class="message-hover-details">
                        Model: ${metadata.model}<br>
                        ${metadata.promptTokens ? `Prompt Tokens: ${metadata.promptTokens}<br>` : ''}
                        ${metadata.completionTokens ? `Completion Tokens: ${metadata.completionTokens}<br>` : ''}
                        ${metadata.totalTokens ? `Total Tokens: ${metadata.totalTokens}<br>` : ''}
                        ${metadata.timeTaken ? `Time: ${metadata.timeTaken}s<br>` : ''}
                        ${metadata.cost ? `Cost: $${metadata.cost.toFixed(6)}` : ''}
                    </div>
                `;

                // Add or update metadata
                let metadataElement = messageElement.querySelector('.message-hover-details');
                if (metadataElement) {
                    metadataElement.innerHTML = metadataHtml;
                } else {
                    messageElement.innerHTML += metadataHtml;
                }
            }

            scrollToBottom();
        }
    }

    // Add system message
    function addSystemMessage(content) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message message-system';
        messageElement.innerHTML = `
            <div class="message-content">${escapeHtml(content)}</div>
        `;

        messageContainer.appendChild(messageElement);
        scrollToBottom();
    }

    // Clear messages
    function clearMessages() {
        messageContainer.innerHTML = '';
    }

    // Update token count
    function updateTokenCount() {
        const text = promptInput.value.trim();
        // Simple estimation: ~4 characters per token
        const estimatedTokens = Math.ceil(text.length / 4);
        tokenCount.textContent = `Estimated tokens: ${estimatedTokens}`;
    }

    // Scroll to bottom of message container
    function scrollToBottom() {
        messageContainer.scrollTop = messageContainer.scrollHeight;
    }

    // Escape HTML
    function escapeHtml(text) {
        if (!text) return '';

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Add event listener for the multi-model test button

    // Add event listener for the new button
    createMultiModelTestBtn.addEventListener('click', async function() {
        try {
            createMultiModelTestBtn.disabled = true;
            createMultiModelTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

            addSystemMessage('Creating multi-model test conversation...');

            const response = await fetch('/api/admin/conversations/create-multi-model-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('Create multi-model test response status:', response.status);
            const responseText = await response.text();
            console.log('Create multi-model test response text:', responseText);

            try {
                const data = JSON.parse(responseText);
                console.log('Create multi-model test parsed response:', data);

                if (data.status === 'success') {
                    addSystemMessage(`Multi-model test conversation created with ID: ${data.conversation.id}`);

                    // Reload conversations
                    await loadConversations();

                    // Select the new conversation
                    if (data.conversation && data.conversation.id) {
                        conversationSelect.value = data.conversation.id;
                        loadConversation(data.conversation.id);
                    }
                } else {
                    addSystemMessage(`Error creating multi-model test conversation: ${data.message || 'Unknown error'}`);
                }
            } catch (parseError) {
                console.error('Error parsing create multi-model test response:', parseError);
                addSystemMessage(`Error parsing response: ${parseError.message}. Server returned: ${responseText.substring(0, 100)}...`);
            }
        } catch (error) {
            console.error('Error creating multi-model test conversation:', error);
            addSystemMessage(`Error: ${error.message}`);
        } finally {
            createMultiModelTestBtn.disabled = false;
            createMultiModelTestBtn.innerHTML = '<i class="fas fa-robot"></i> Create Multi-Model Test';
        }
    });
});
