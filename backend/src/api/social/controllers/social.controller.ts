/**
 * Social controller for handling social-related API requests
 */
import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
// import { EnhancedSocialService } from '../../../services/social';
import { ChannelService, MessageService, NotificationService } from '../../../services/social';
import { logger } from '../../../common/logger';
import {
  CreateChannelDto,
  UpdateChannelDto,
  ChannelMemberDto,
  ChannelFilterDto,
  CreateMessageDto,
  UpdateMessageDto,
  MessageReactionDto,
  MessageFilterDto,
  NotificationFilterDto,
} from '../dto';
import { AuthenticatedRequest } from '../../../common/types';

/**
 * Controller for social operations
 */
@injectable()
export class SocialController {
  constructor(
    @inject(TYPES.ChannelService) private channelService: ChannelService,
    @inject(TYPES.MessageService) private messageService: MessageService,
    @inject(TYPES.NotificationService) private notificationService: NotificationService,
  ) {
    // Temporary compatibility layer
    this.socialService = {
      getUserChannels: this.channelService.getUserChannels.bind(this.channelService),
      getPublicChannels: this.channelService.getPublicChannels.bind(this.channelService),
      getChannelById: this.channelService.getChannelById.bind(this.channelService),
      createChannel: this.channelService.createChannel.bind(this.channelService),
      updateChannel: this.channelService.updateChannel.bind(this.channelService),
      deleteChannel: this.channelService.deleteChannel.bind(this.channelService),
      addChannelMember: this.channelService.addChannelMember.bind(this.channelService),
      removeChannelMember: this.channelService.removeChannelMember.bind(this.channelService),
      getChannelMessages: this.messageService.getChannelMessages.bind(this.messageService),
      getMessageById: this.messageService.getMessageById.bind(this.messageService),
      createMessage: this.messageService.createMessage.bind(this.messageService),
      updateMessage: this.messageService.updateMessage.bind(this.messageService),
      deleteMessage: this.messageService.deleteMessage.bind(this.messageService),
      addReaction: this.messageService.addReaction.bind(this.messageService),
      removeReaction: this.messageService.removeReaction.bind(this.messageService),
      getUserNotifications: this.notificationService.getUserNotifications.bind(
        this.notificationService,
      ),
      markNotificationAsRead: this.notificationService.markNotificationAsRead.bind(
        this.notificationService,
      ),
      markAllNotificationsAsRead: this.notificationService.markAllNotificationsAsRead.bind(
        this.notificationService,
      ),
      deleteNotification: this.notificationService.deleteNotification.bind(
        this.notificationService,
      ),
    } as any;
  }

  // Temporary compatibility property
  private socialService: any;

  /**
   * Get channels for the current user
   */
  async getUserChannels(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as ChannelFilterDto;
      const channels = await this.socialService.getUserChannels(req.user.id, {
        skip: filter.skip,
        take: filter.take,
      });

      res.status(200).json(channels);
    } catch (error) {
      logger.error('Error getting user channels:', error);
      throw error;
    }
  }

  /**
   * Get public channels
   */
  async getPublicChannels(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as ChannelFilterDto;
      const channels = await this.socialService.getPublicChannels({
        skip: filter.skip,
        take: filter.take,
      });

      res.status(200).json(channels);
    } catch (error) {
      logger.error('Error getting public channels:', error);
      throw error;
    }
  }

  /**
   * Get a channel by ID
   */
  async getChannelById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const channel = await this.socialService.getChannelById(channelId);
      if (!channel) {
        throw new Error('Channel not found');
      }

      res.status(200).json(channel);
    } catch (error) {
      logger.error('Error getting channel by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new channel
   */
  async createChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelData: CreateChannelDto = req.body;
      const result = await this.socialService.createChannel({
        name: channelData.name,
        description: channelData.description,
        ownerId: req.user.id,
        isPrivate: channelData.isPrivate,
        memberIds: channelData.memberIds,
      });

      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating channel:', error);
      throw error;
    }
  }

  /**
   * Update a channel
   */
  async updateChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const channelData: UpdateChannelDto = req.body;
      const result = await this.socialService.updateChannel(channelId, req.user.id, {
        name: channelData.name,
        description: channelData.description,
        isPrivate: channelData.isPrivate,
      });

      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating channel:', error);
      throw error;
    }
  }

  /**
   * Delete a channel
   */
  async deleteChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const result = await this.socialService.deleteChannel(channelId, req.user.id);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting channel:', error);
      throw error;
    }
  }

  /**
   * Add a member to a channel
   */
  async addChannelMember(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const memberData: ChannelMemberDto = req.body;
      const result = await this.socialService.addChannelMember(
        channelId,
        req.user.id,
        memberData.userId,
      );
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error adding channel member:', error);
      throw error;
    }
  }

  /**
   * Remove a member from a channel
   */
  async removeChannelMember(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const userId = parseInt(req.params.userId, 10);
      if (isNaN(userId)) {
        throw new Error('Invalid user ID');
      }

      const result = await this.socialService.removeChannelMember(channelId, req.user.id, userId);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error removing channel member:', error);
      throw error;
    }
  }

  /**
   * Get messages for a channel
   */
  async getChannelMessages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const filter = req.query as unknown as MessageFilterDto;
      const result = await this.socialService.getChannelMessages(channelId, req.user.id, {
        skip: filter.skip,
        take: filter.take,
        parentOnly: filter.parentOnly,
      });

      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting channel messages:', error);
      throw error;
    }
  }

  /**
   * Get a message by ID
   */
  async getMessageById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        throw new Error('Invalid message ID');
      }

      const result = await this.socialService.getMessageById(messageId, req.user.id);
      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting message by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new message
   */
  async createMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        throw new Error('Invalid channel ID');
      }

      const messageData: CreateMessageDto = req.body;
      const result = await this.socialService.createMessage(channelId, req.user.id, {
        content: messageData.content,
        parentId: messageData.parentId,
        attachments: messageData.attachments,
      });

      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating message:', error);
      throw error;
    }
  }

  /**
   * Update a message
   */
  async updateMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        throw new Error('Invalid message ID');
      }

      const messageData: UpdateMessageDto = req.body;
      const result = await this.socialService.updateMessage(messageId, req.user.id, {
        content: messageData.content,
        attachments: messageData.attachments,
      });

      if ('statusCode' in result) {
        throw new Error(result.detail);
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating message:', error);
      throw error;
    }
  }

  /**
   * Delete a message
   */
  async deleteMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        throw new Error('Invalid message ID');
      }

      const result = await this.socialService.deleteMessage(messageId, req.user.id);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting message:', error);
      throw error;
    }
  }

  /**
   * Add a reaction to a message
   */
  async addReaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        throw new Error('Invalid message ID');
      }

      const reactionData: MessageReactionDto = req.body;
      const result = await this.socialService.addReaction(
        messageId,
        req.user.id,
        reactionData.emoji,
      );
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error adding reaction:', error);
      throw error;
    }
  }

  /**
   * Remove a reaction from a message
   */
  async removeReaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        throw new Error('Invalid message ID');
      }

      const emoji = req.params.emoji;
      if (!emoji) {
        throw new Error('Emoji is required');
      }

      const result = await this.socialService.removeReaction(messageId, req.user.id, emoji);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error removing reaction:', error);
      throw error;
    }
  }

  /**
   * Get notifications for the current user
   */
  async getUserNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as NotificationFilterDto;
      const notifications = await this.socialService.getUserNotifications(req.user.id, {
        skip: filter.skip,
        take: filter.take,
        unreadOnly: filter.unreadOnly,
      });

      res.status(200).json(notifications);
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark a notification as read
   */
  async markNotificationAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const notificationId = parseInt(req.params.id, 10);
      if (isNaN(notificationId)) {
        throw new Error('Invalid notification ID');
      }

      const result = await this.socialService.markNotificationAsRead(notificationId, req.user.id);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const result = await this.socialService.markAllNotificationsAsRead(req.user.id);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const notificationId = parseInt(req.params.id, 10);
      if (isNaN(notificationId)) {
        throw new Error('Invalid notification ID');
      }

      const result = await this.socialService.deleteNotification(notificationId, req.user.id);
      if (typeof result !== 'boolean') {
        throw new Error(result.detail);
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting notification:', error);
      throw error;
    }
  }
}
