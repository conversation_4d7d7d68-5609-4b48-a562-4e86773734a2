import {
  IsString,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsBoolean,
  IsInt,
  IsArray,
  Min,
  Max,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Create channel DTO
 */
export class CreateChannelDto {
  @IsNotEmpty()
  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  memberIds?: number[];
}

/**
 * Update channel DTO
 */
export class UpdateChannelDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

/**
 * Channel member DTO
 */
export class ChannelMemberDto {
  @IsNotEmpty()
  @IsInt()
  userId!: number;
}

/**
 * Channel filter DTO
 */
export class ChannelFilterDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  skip?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  take?: number;
}
