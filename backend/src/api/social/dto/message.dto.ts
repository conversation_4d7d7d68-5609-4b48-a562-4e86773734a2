import { IsString, <PERSON>NotEmpty, <PERSON>O<PERSON>al, <PERSON>Int, IsA<PERSON>y, Min, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Create message DTO
 */
export class CreateMessageDto {
  @IsNotEmpty()
  @IsString()
  content!: string;

  @IsOptional()
  @IsInt()
  parentId?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}

/**
 * Update message DTO
 */
export class UpdateMessageDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}

/**
 * Message reaction DTO
 */
export class MessageReactionDto {
  @IsNotEmpty()
  @IsString()
  emoji!: string;
}

/**
 * Message filter DTO
 */
export class MessageFilterDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  skip?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @<PERSON>(100)
  @Type(() => Number)
  take?: number;

  @IsOptional()
  @Type(() => Boolean)
  parentOnly?: boolean;
}
