import { <PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, Is<PERSON><PERSON>al, IsBoolean, IsInt, Min, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Notification filter DTO
 */
export class NotificationFilterDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  skip?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  take?: number = 20;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  unreadOnly?: boolean = false;
}
