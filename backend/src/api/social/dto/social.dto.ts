import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsInt,
  IsEnum,
  IsArray,
} from 'class-validator';

/**
 * Channel DTOs
 */
export class CreateChannelDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  type?: string = 'TOPIC';
}

export class UpdateChannelDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  type?: string;
}

/**
 * Social Message DTOs
 */
export class CreateMessageDto {
  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsOptional()
  @IsString()
  messageType?: string = 'REGULAR';

  @IsOptional()
  @IsString()
  codeLanguage?: string;
}

export class UpdateMessageDto {
  @IsNotEmpty()
  @IsString()
  content: string = '';
}

/**
 * QA DTOs
 */
export class CreateQuestionDto {
  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsOptional()
  @IsString()
  codeSnippet?: string;
}

export class UpdateQuestionDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsBoolean()
  resolved?: boolean;
}

export class CreateAnswerDto {
  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsOptional()
  @IsString()
  codeSnippet?: string;
}

export class UpdateAnswerDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  codeSnippet?: string;
}
