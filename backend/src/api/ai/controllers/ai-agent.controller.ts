/**
 * AIAgent controller
 */
import { Request, Response } from 'express';
import { Controller, Get, Post, Put, Delete, Middleware, ClassMiddleware } from '@overnightjs/core';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { CreateAIAgentDto, UpdateAIAgentDto, AIAgentActionDto } from '../dto';
import { authMiddleware, roleMiddleware } from '../../../middleware/auth.middleware';
import { validate } from '../../../middleware/validation.middleware';
import { AIAgentRepository } from '../../../db/repositories/ai/ai-agent.repository';
import { AuthenticatedRequest } from '../../../common/types/authenticated-request';

@injectable()
@Controller('api/ai/agents')
@ClassMiddleware([authMiddleware])
export class AIAgentController {
  constructor(@inject(TYPES.AIAgentRepository) private aiAgentRepository: AIAgentRepository) {}

  @Get('')
  async getAllAgents(req: AuthenticatedRequest, res: Response) {
    try {
      if (!req.userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      const userId = req.userId;
      const filters: any = {};

      // Only admins can see all agents, regular users only see their own
      if (req.userRole !== 'ADMIN') {
        filters.userId = userId;
      }

      // Parse query params
      const projectIdParam = req.query.projectId;
      if (projectIdParam && typeof projectIdParam === 'string') {
        filters.projectId = parseInt(projectIdParam, 10);
      }

      if (req.query.isActive !== undefined) {
        filters.isActive = req.query.isActive === 'true';
      }

      const agents = await this.aiAgentRepository.findAll(filters);
      return res.status(200).json(agents);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Get(':id')
  async getAgentById(req: AuthenticatedRequest, res: Response) {
    try {
      const agentId = parseInt(req.params.id, 10);
      const agent = await this.aiAgentRepository.findById(agentId);

      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Only allow access to own agents or admin access
      if (agent.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      return res.status(200).json(agent);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Post('')
  @Middleware([validate(CreateAIAgentDto)])
  async createAgent(req: AuthenticatedRequest, res: Response) {
    try {
      const data = req.body as CreateAIAgentDto;

      // Set user ID if not provided
      if (!data.userId) {
        data.userId = req.userId;
      }

      // Only admins can create agents for other users
      if (data.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Cannot create agent for another user' });
      }

      const agent = await this.aiAgentRepository.create(data);
      return res.status(201).json(agent);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Put(':id')
  @Middleware([validate(UpdateAIAgentDto)])
  async updateAgent(req: AuthenticatedRequest, res: Response) {
    try {
      const agentId = parseInt(req.params.id, 10);
      const data = req.body as UpdateAIAgentDto;

      // Check if agent exists and user has access
      const agent = await this.aiAgentRepository.findById(agentId);
      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Only allow update to own agents or admin access
      if (agent.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Only admins can change userId
      if (data.userId && data.userId !== agent.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Cannot change agent ownership' });
      }

      const updatedAgent = await this.aiAgentRepository.update(agentId, data);
      return res.status(200).json(updatedAgent);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Delete(':id')
  async deleteAgent(req: AuthenticatedRequest, res: Response) {
    try {
      const agentId = parseInt(req.params.id, 10);

      // Check if agent exists and user has access
      const agent = await this.aiAgentRepository.findById(agentId);
      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Only allow delete of own agents or admin access
      if (agent.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      await this.aiAgentRepository.delete(agentId);
      return res.status(204).send();
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Get(':id/actions')
  async getAgentActions(req: AuthenticatedRequest, res: Response) {
    try {
      const agentId = parseInt(req.params.id, 10);

      // Check if agent exists and user has access
      const agent = await this.aiAgentRepository.findById(agentId);
      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Only allow access to own agents' actions or admin access
      if (agent.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      const actions = await this.aiAgentRepository.getAgentActions(agentId);
      return res.status(200).json(actions);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Post(':id/actions')
  @Middleware([validate(AIAgentActionDto)])
  async recordAgentAction(req: AuthenticatedRequest, res: Response) {
    try {
      const agentId = parseInt(req.params.id, 10);
      const data = req.body as AIAgentActionDto;

      // Override agentId from path
      data.agentId = agentId;

      // Check if agent exists and user has access
      const agent = await this.aiAgentRepository.findById(agentId);
      if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
      }

      // Only allow access to own agents or admin access
      if (agent.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Access denied' });
      }

      const action = await this.aiAgentRepository.recordAction(data);
      return res.status(201).json(action);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }
}
