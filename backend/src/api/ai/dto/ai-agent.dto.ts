/**
 * DTOs for AIAgent model
 */
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsArray,
  Min,
  Max,
} from 'class-validator';

export class CreateAIAgentDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  provider: string = '';

  @IsNotEmpty()
  @IsString()
  modelName: string = '';

  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  temperature?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  @IsOptional()
  @IsArray()
  capabilities?: string[];

  @IsOptional()
  @IsNumber()
  @Min(1)
  dailyTokenLimit?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  monthlyTokenLimit?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  costPerInputToken?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  costPerOutputToken?: number;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsOptional()
  @IsNumber()
  projectId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateAIAgentDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  provider?: string;

  @IsOptional()
  @IsString()
  modelName?: string;

  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  temperature?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  @IsOptional()
  @IsArray()
  capabilities?: string[];

  @IsOptional()
  @IsNumber()
  @Min(1)
  dailyTokenLimit?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  monthlyTokenLimit?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  costPerInputToken?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  costPerOutputToken?: number;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsOptional()
  @IsNumber()
  projectId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AIAgentActionDto {
  @IsNotEmpty()
  @IsNumber()
  agentId: number = 0;

  @IsNotEmpty()
  @IsString()
  actionType: string = '';

  @IsOptional()
  @IsString()
  input?: string;

  @IsOptional()
  @IsString()
  output?: string;

  @IsOptional()
  @IsNumber()
  executionTime?: number;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  errorMessage?: string;

  @IsOptional()
  metadata?: any;
}
