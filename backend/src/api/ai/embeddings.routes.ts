/**
 * AI Embeddings API Routes
 * Provides embedding generation endpoints for semantic search
 */

import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import azureService from '../../services/ai/azure.service';
import { logger } from '../../common/logger';
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';

const router = Router();

/**
 * @swagger
 * /api/ai/embeddings:
 *   post:
 *     summary: Generate embeddings for text(s)
 *     description: Generate vector embeddings for one or more texts using Azure OpenAI
 *     tags: [AI, Embeddings]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - texts
 *             properties:
 *               texts:
 *                 oneOf:
 *                   - type: string
 *                     description: Single text to embed
 *                   - type: array
 *                     items:
 *                       type: string
 *                     description: Array of texts to embed
 *               model:
 *                 type: string
 *                 default: text-embedding-3-small
 *                 description: Embedding model to use
 *               deployment:
 *                 type: string
 *                 description: Azure deployment name (optional)
 *             example:
 *               texts: ["Hello world", "How are you?"]
 *               model: "text-embedding-3-small"
 *     responses:
 *       200:
 *         description: Embeddings generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 embeddings:
 *                   type: array
 *                   items:
 *                     type: array
 *                     items:
 *                       type: number
 *                   description: Array of embedding vectors
 *                 usage:
 *                   type: object
 *                   properties:
 *                     promptTokens:
 *                       type: number
 *                     totalTokens:
 *                       type: number
 *                     cost:
 *                       type: number
 *                     durationMs:
 *                       type: number
 *                 model:
 *                   type: string
 *       400:
 *         description: Bad request - validation errors
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/embeddings',
  unifiedAuthMiddleware,
  [
    body('texts')
      .custom((value) => {
        if (typeof value === 'string') return true;
        if (Array.isArray(value) && value.every(item => typeof item === 'string')) return true;
        throw new Error('texts must be a string or array of strings');
      })
      .notEmpty()
      .withMessage('texts is required'),
    body('model')
      .optional()
      .isString()
      .withMessage('model must be a string'),
    body('deployment')
      .optional()
      .isString()
      .withMessage('deployment must be a string'),
  ],
  async (req, res) => {
    try {
      // Check validation results
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { texts, model, deployment } = req.body;

      logger.info(`[Embeddings API] Generating embeddings for ${Array.isArray(texts) ? texts.length : 1} text(s)`);

      // Generate embeddings using Azure service
      const result = await azureService.generateEmbeddings(texts, {
        model,
        deployment
      });

      logger.info(
        `[Embeddings API] Successfully generated ${result.embeddings.length} embeddings. ` +
        `Cost: $${result.usage.cost.toFixed(6)}, Duration: ${result.usage.durationMs}ms`
      );

      res.json({
        success: true,
        embeddings: result.embeddings,
        usage: result.usage,
        model: result.model
      });
    } catch (error) {
      logger.error('[Embeddings API] Error generating embeddings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate embeddings',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/embedding:
 *   post:
 *     summary: Generate embedding for single text
 *     description: Generate vector embedding for a single text using Azure OpenAI
 *     tags: [AI, Embeddings]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text to embed
 *               model:
 *                 type: string
 *                 default: text-embedding-3-small
 *                 description: Embedding model to use
 *               deployment:
 *                 type: string
 *                 description: Azure deployment name (optional)
 *             example:
 *               text: "Hello world"
 *               model: "text-embedding-3-small"
 *     responses:
 *       200:
 *         description: Embedding generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 embedding:
 *                   type: array
 *                   items:
 *                     type: number
 *                   description: Embedding vector
 *                 usage:
 *                   type: object
 *                   properties:
 *                     promptTokens:
 *                       type: number
 *                     totalTokens:
 *                       type: number
 *                     cost:
 *                       type: number
 *                     durationMs:
 *                       type: number
 *                 model:
 *                   type: string
 *       400:
 *         description: Bad request - validation errors
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/embedding',
  unifiedAuthMiddleware,
  [
    body('text')
      .isString()
      .notEmpty()
      .withMessage('text is required and must be a non-empty string'),
    body('model')
      .optional()
      .isString()
      .withMessage('model must be a string'),
    body('deployment')
      .optional()
      .isString()
      .withMessage('deployment must be a string'),
  ],
  async (req, res) => {
    try {
      // Check validation results
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { text, model, deployment } = req.body;

      logger.info(`[Embeddings API] Generating embedding for single text`);

      // Generate embedding using Azure service
      const result = await azureService.generateEmbedding(text, {
        model,
        deployment
      });

      logger.info(
        `[Embeddings API] Successfully generated embedding. ` +
        `Cost: $${result.usage.cost.toFixed(6)}, Duration: ${result.usage.durationMs}ms`
      );

      res.json({
        success: true,
        embedding: result.embedding,
        usage: result.usage,
        model: result.model
      });
    } catch (error) {
      logger.error('[Embeddings API] Error generating embedding:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate embedding',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }
);

/**
 * @swagger
 * /api/ai/embeddings/health:
 *   get:
 *     summary: Health check for embeddings service
 *     description: Check if the embeddings service is available
 *     tags: [AI, Embeddings]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Embeddings service is healthy"
 *                 model:
 *                   type: string
 *                   example: "text-embedding-3-small"
 *       500:
 *         description: Service is unavailable
 */
router.get('/embeddings/health', async (req, res) => {
  try {
    // Try to generate a small test embedding
    const testResult = await azureService.generateEmbedding('test', {
      model: 'text-embedding-3-small'
    });

    res.json({
      success: true,
      message: 'Embeddings service is healthy',
      model: testResult.model,
      embeddingDimension: testResult.embedding.length
    });
  } catch (error) {
    logger.error('[Embeddings API] Health check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Embeddings service is unavailable',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;