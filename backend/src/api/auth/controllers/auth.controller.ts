import { Request, Response } from 'express';

import { logger } from '../../../common/logger';
import { ClerkSdkUser } from '../../../common/types';
import { clerkService } from '../../../services/clerk.service';

/**
 * Controller for auth-related endpoints
 */
export class AuthController {
  /**
   * Get the current user's profile
   */
  async getCurrentUser(req: Request, res: Response): Promise<Response> {
    try {
      const { userId } = req;

      // Validate that userId is present and valid
      if (!userId) {
        logger.error('Auth Controller: Missing userId in request');
        return res.status(401).json({
          success: false,
          message: 'Invalid authentication - missing user ID'
        });
      }

      // Check if this is a development bypass (userId = 1)
      if (process.env.NODE_ENV === 'development' && userId.toString() === '1') {
        logger.info('Auth Controller: Using development bypass user data');

        // Return mock user data for development
        return res.status(200).json({
          success: true,
          data: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            username: 'admin',
            role: 'admin',
            email_verified: true,
            imageUrl: null,
          },
        });
      }

      // Get the user from Clerk for real authentication
      const user: ClerkSdkUser = await clerkService.getUserById(userId.toString());

      return res.status(200).json({
        success: true,
        data: {
          id: user.id,
          email: user.email_addresses[0]?.email_address ?? '',
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          image_url: user.image_url,
          public_metadata: user.public_metadata,
        },
      });
    } catch (error) {
      logger.error('Error fetching current user:', error);
      return res.status(500).json({
        success: false,
        message: 'Error fetching user profile',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}

export const authController = new AuthController();
