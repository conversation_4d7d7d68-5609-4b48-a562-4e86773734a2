import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import { CodeAnalysisService } from '../../services/code-analysis.service';
import { logger } from '../../common/logger';
import { AuthenticatedRequest } from '../../common/types/authenticated-request';

@injectable()
export class CodeAnalysisController {
  constructor(
    @inject(TYPES.CodeAnalysisService) private codeAnalysisService: CodeAnalysisService
  ) {}

  /**
   * POST /api/v1/code-analysis/analyze
   * Trigger code analysis for a project
   */
  async analyzeProject(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { 
        projectId, 
        includeAIAnalysis = true,
        analysisDepth = 'detailed' // 'quick' | 'detailed'
      } = req.body;
      
      if (!projectId) {
        res.status(400).json({
          status: 'error',
          message: 'projectId is required'
        });
        return;
      }
      
      // Set task type header for downstream model selection
      // This triggers the dynamic model selection middleware
      req.headers['x-task-type'] = analysisDepth === 'quick' ? 'chat' : 'code_review';
      
      logger.info(`Starting code analysis for project ${projectId} by user ${req.userId}`);
      
      const report = await this.codeAnalysisService.analyzeProject(
        parseInt(projectId), 
        req.userId, 
        { 
          includeAI: includeAIAnalysis,
          analysisDepth 
        }
      );
      
      res.json({
        status: 'success',
        data: { 
          report,
          modelSelectionInfo: {
            taskType: req.headers['x-task-type'],
            modelUsed: report.aiInsights?.modelUsed || 'none',
            fallbackApplied: report.aiInsights?.fallbackApplied || false
          }
        }
      });
    } catch (error) {
      logger.error('Error analyzing project:', error);
      res.status(500).json({
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to analyze project'
      });
    }
  }

  /**
   * GET /api/v1/code-analysis/health/:projectId
   * Get latest health score for a project
   */
  async getProjectHealth(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const projectId = parseInt(req.params.projectId);
      
      if (isNaN(projectId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid project ID'
        });
        return;
      }
      
      const healthScore = await this.codeAnalysisService.getLatestHealthScore(
        projectId, 
        req.userId
      );
      
      if (!healthScore) {
        res.status(404).json({
          status: 'error',
          message: 'No analysis found for this project'
        });
        return;
      }
      
      res.json({
        status: 'success',
        data: { healthScore }
      });
    } catch (error) {
      logger.error('Error getting project health:', error);
      res.status(500).json({
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to get project health'
      });
    }
  }

  /**
   * GET /api/v1/code-analysis/reports/:id
   * Get specific analysis report
   */
  async getReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const reportId = parseInt(req.params.id);
      
      if (isNaN(reportId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid report ID'
        });
        return;
      }
      
      // Implementation would retrieve report with proper authorization
      res.status(501).json({
        status: 'error',
        message: 'Get specific report not implemented yet'
      });
    } catch (error) {
      logger.error('Error getting report:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get report'
      });
    }
  }

  /**
   * GET /api/v1/code-analysis/reports
   * List user's analysis reports
   */
  async listReports(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { projectId, limit = 10, offset = 0 } = req.query;
      
      // Implementation would list reports with pagination
      res.status(501).json({
        status: 'error',
        message: 'List reports not implemented yet'
      });
    } catch (error) {
      logger.error('Error listing reports:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to list reports'
      });
    }
  }

  /**
   * POST /api/v1/code-analysis/analyze-file
   * Analyze single file in real-time
   */
  async analyzeFile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { filePath, content, projectId } = req.body;
      
      if (!filePath || !content) {
        res.status(400).json({
          status: 'error',
          message: 'filePath and content are required'
        });
        return;
      }
      
      // Set task type for quick analysis
      req.headers['x-task-type'] = 'chat';
      
      // Implementation would analyze single file
      res.status(501).json({
        status: 'error',
        message: 'Single file analysis not implemented yet'
      });
    } catch (error) {
      logger.error('Error analyzing file:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to analyze file'
      });
    }
  }

  /**
   * POST /api/v1/code-analysis/explain/:issueId
   * Get AI explanation for specific issue
   */
  async explainIssue(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { issueId } = req.params;
      const { depth = 'detailed' } = req.body;
      
      if (!issueId) {
        res.status(400).json({
          status: 'error',
          message: 'Issue ID is required'
        });
        return;
      }
      
      // Set task type based on explanation depth requested
      req.headers['x-task-type'] = depth === 'quick' ? 'chat' : 'code_review';
      
      // Implementation would explain specific issue using AI
      res.status(501).json({
        status: 'error',
        message: 'Issue explanation not implemented yet'
      });
    } catch (error) {
      logger.error('Error explaining issue:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to explain issue'
      });
    }
  }

  /**
   * POST /api/v1/code-analysis/suggest-refactoring
   * Get refactoring suggestions for code snippet
   */
  async suggestRefactoring(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { codeSnippet, issues, complexity = 'high' } = req.body;
      
      if (!codeSnippet) {
        res.status(400).json({
          status: 'error',
          message: 'codeSnippet is required'
        });
        return;
      }
      
      // Use code_gen_agentic for refactoring suggestions (iterative improvements)
      req.headers['x-task-type'] = 'code_gen_agentic';
      
      // Implementation would generate refactoring suggestions
      res.status(501).json({
        status: 'error',
        message: 'Refactoring suggestions not implemented yet'
      });
    } catch (error) {
      logger.error('Error generating refactoring suggestions:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to generate refactoring suggestions'
      });
    }
  }

  /**
   * GET /api/v1/code-analysis/trends/:projectId
   * Get health trends over time for project
   */
  async getTrends(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const projectId = parseInt(req.params.projectId);
      const { days = 30 } = req.query;
      
      if (isNaN(projectId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid project ID'
        });
        return;
      }
      
      // Implementation would return trending data
      res.status(501).json({
        status: 'error',
        message: 'Trends analysis not implemented yet'
      });
    } catch (error) {
      logger.error('Error getting trends:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get trends'
      });
    }
  }

  /**
   * GET /api/v1/code-analysis/hotspots/:projectId
   * Get top problematic files in project
   */
  async getHotspots(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const projectId = parseInt(req.params.projectId);
      const { limit = 10 } = req.query;
      
      if (isNaN(projectId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid project ID'
        });
        return;
      }
      
      // Implementation would return hotspot files
      res.status(501).json({
        status: 'error',
        message: 'Hotspots analysis not implemented yet'
      });
    } catch (error) {
      logger.error('Error getting hotspots:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get hotspots'
      });
    }
  }

  /**
   * DELETE /api/v1/code-analysis/reports/:id
   * Delete analysis report
   */
  async deleteReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const reportId = parseInt(req.params.id);
      
      if (isNaN(reportId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid report ID'
        });
        return;
      }
      
      // Implementation would delete report with proper authorization
      res.status(501).json({
        status: 'error',
        message: 'Delete report not implemented yet'
      });
    } catch (error) {
      logger.error('Error deleting report:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to delete report'
      });
    }
  }
}