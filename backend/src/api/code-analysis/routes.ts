import { Router } from 'express';
import { container } from '../../inversify.config';
import { TYPES } from '../../types';
import { CodeAnalysisController } from './code-analysis.controller';
import { clerkAuthMiddleware } from '../../middleware/auth.middleware';
import { logger } from '../../common/logger';

const router = Router();
const codeAnalysisController = container.get<CodeAnalysisController>(TYPES.CodeAnalysisController);

// Apply authentication middleware to all routes
router.use(clerkAuthMiddleware);

/**
 * @swagger
 * /api/v1/code-analysis/analyze:
 *   post:
 *     summary: Trigger code analysis for a project
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               projectId:
 *                 type: integer
 *                 description: ID of the project to analyze
 *               includeAIAnalysis:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include AI-powered analysis
 *               analysisDepth:
 *                 type: string
 *                 enum: [quick, detailed]
 *                 default: detailed
 *                 description: Depth of analysis (affects model selection)
 *     responses:
 *       200:
 *         description: Analysis completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     report:
 *                       $ref: '#/components/schemas/CodeHealthReport'
 *                     modelSelectionInfo:
 *                       $ref: '#/components/schemas/ModelSelectionInfo'
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/analyze', async (req, res) => {
  await codeAnalysisController.analyzeProject(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/health/{projectId}:
 *   get:
 *     summary: Get latest health score for a project
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Health score retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     healthScore:
 *                       $ref: '#/components/schemas/HealthScore'
 *       404:
 *         description: No analysis found
 *       500:
 *         description: Internal server error
 */
router.get('/health/:projectId', async (req, res) => {
  await codeAnalysisController.getProjectHealth(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/reports/{id}:
 *   get:
 *     summary: Get specific analysis report
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Report ID
 *     responses:
 *       200:
 *         description: Report retrieved successfully
 *       404:
 *         description: Report not found
 *       500:
 *         description: Internal server error
 */
router.get('/reports/:id', async (req, res) => {
  await codeAnalysisController.getReport(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/reports:
 *   get:
 *     summary: List user's analysis reports
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: integer
 *         description: Filter by project ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reports to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of reports to skip
 *     responses:
 *       200:
 *         description: Reports listed successfully
 *       500:
 *         description: Internal server error
 */
router.get('/reports', async (req, res) => {
  await codeAnalysisController.listReports(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/analyze-file:
 *   post:
 *     summary: Analyze single file in real-time
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - filePath
 *               - content
 *             properties:
 *               filePath:
 *                 type: string
 *                 description: Path of the file
 *               content:
 *                 type: string
 *                 description: File content to analyze
 *               projectId:
 *                 type: integer
 *                 description: Associated project ID
 *     responses:
 *       200:
 *         description: File analyzed successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/analyze-file', async (req, res) => {
  await codeAnalysisController.analyzeFile(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/explain/{issueId}:
 *   post:
 *     summary: Get AI explanation for specific issue
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: issueId
 *         required: true
 *         schema:
 *           type: string
 *         description: Issue ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               depth:
 *                 type: string
 *                 enum: [quick, detailed]
 *                 default: detailed
 *                 description: Depth of explanation
 *     responses:
 *       200:
 *         description: Explanation generated successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/explain/:issueId', async (req, res) => {
  await codeAnalysisController.explainIssue(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/suggest-refactoring:
 *   post:
 *     summary: Get refactoring suggestions for code snippet
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - codeSnippet
 *             properties:
 *               codeSnippet:
 *                 type: string
 *                 description: Code to refactor
 *               issues:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Known issues with the code
 *               complexity:
 *                 type: string
 *                 enum: [low, medium, high]
 *                 default: high
 *                 description: Complexity level
 *     responses:
 *       200:
 *         description: Refactoring suggestions generated
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post('/suggest-refactoring', async (req, res) => {
  await codeAnalysisController.suggestRefactoring(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/trends/{projectId}:
 *   get:
 *     summary: Get health trends over time for project
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to include
 *     responses:
 *       200:
 *         description: Trends retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/trends/:projectId', async (req, res) => {
  await codeAnalysisController.getTrends(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/hotspots/{projectId}:
 *   get:
 *     summary: Get top problematic files in project
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of hotspots to return
 *     responses:
 *       200:
 *         description: Hotspots retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/hotspots/:projectId', async (req, res) => {
  await codeAnalysisController.getHotspots(req as any, res);
});

/**
 * @swagger
 * /api/v1/code-analysis/reports/{id}:
 *   delete:
 *     summary: Delete analysis report
 *     tags: [Code Analysis]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Report ID
 *     responses:
 *       200:
 *         description: Report deleted successfully
 *       404:
 *         description: Report not found
 *       500:
 *         description: Internal server error
 */
router.delete('/reports/:id', async (req, res) => {
  await codeAnalysisController.deleteReport(req as any, res);
});

// Error handling middleware for this route
router.use((error: any, req: any, res: any, next: any) => {
  logger.error('Code analysis route error:', error);
  res.status(500).json({
    status: 'error',
    message: 'Internal server error in code analysis'
  });
});

export default router;