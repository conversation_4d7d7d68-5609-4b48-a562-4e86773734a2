{"path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search/rag-search.controller.ts", "contentHash": "3a1e1ecadc6fc06ec08b39f44f0394848bafad9ad4f7808c8e1c910d27e4557b", "commit": "bc3df31a2ad7cf06456e71484539b06ca55f315c", "timestamp": "2025-07-15T18:27:27+05:30", "units": [{"unitName": "RAGSearchController", "unitType": "class", "purpose": "This class provides API endpoints for performing RAG (Retrieval-Augmented Generation) based document searches, initiating document processing, and retrieving embedding statistics for projects.", "humanReadableExplanation": "The `RAGSearchController` is an API controller responsible for handling various operations related to a document search and processing system. It's designed to be 'injectable', meaning its dependencies (`VectorService` and `DocumentProcessingService`) are provided at runtime, promoting modularity. \n\nThe `searchDocuments` method is the primary search endpoint. It takes a user's query and a project ID, validates them, and then leverages the `VectorService` to find semantically similar documents. It transforms these raw results into a user-friendly format, including extracted relevant content and optional surrounding context. It also calculates approximate usage metrics like tokens used and estimated cost. \n\nThe `processProjectDocuments` method is an administrative endpoint that triggers the `DocumentProcessingService` to process documents for a given project path and ID. This typically involves tasks like parsing, chunking, and embedding documents into a vector database for future searches. \n\nThe `getEmbeddingStats` method retrieves statistics about the document embeddings for a specific project from the `VectorService`, offering insights into the indexed data. \n\nFinally, `extractRelevantContent` is a private helper method used internally by `searchDocuments`. Its purpose is to intelligently extract the most pertinent sentences from a document's content based on the search query, ensuring that the displayed 'matched content' is concise and directly relevant to what the user searched for. All public methods include robust error handling, returning appropriate HTTP status codes and messages for bad requests or internal server errors.", "dependencies": [{"type": "internal", "name": "VectorService"}, {"type": "internal", "name": "DocumentProcessingService"}, {"type": "external", "name": "Request"}, {"type": "external", "name": "Response"}, {"type": "internal", "name": "BadRequestError"}, {"type": "external", "name": "logger"}, {"type": "external", "name": "@injectable"}, {"type": "external", "name": "@inject"}, {"type": "internal", "name": "TYPES"}], "inputs": [{"name": "searchDocuments", "type": "method", "description": "Handles a RAG-based document search request.", "parameters": [{"name": "req", "type": "Request", "description": "The Express request object, containing `query` (string), `projectId` (string), optional `filters` (object with `documentTypes` and `fileTypes`), optional `limit` (number, default 10), and optional `includeContext` (boolean, default true) in its body."}, {"name": "res", "type": "Response", "description": "The Express response object used to send back the search results or an error."}]}, {"name": "processProjectDocuments", "type": "method", "description": "Initiates the processing of documents for a specific project.", "parameters": [{"name": "req", "type": "Request", "description": "The Express request object, containing `projectId` (string) and `projectPath` (string) in its body."}, {"name": "res", "type": "Response", "description": "The Express response object used to send back the processing status or an error."}]}, {"name": "getEmbeddingStats", "type": "method", "description": "Retrieves embedding statistics for a given project.", "parameters": [{"name": "req", "type": "Request", "description": "The Express request object, containing `projectId` (string) in its body."}, {"name": "res", "type": "Response", "description": "The Express response object used to send back the embedding statistics or an error."}]}], "outputs": {"type": "Promise<void>", "description": "All public methods (`searchDocuments`, `processProjectDocuments`, `getEmbeddingStats`) return a Promise that resolves to `void` after sending an HTTP response. The response body will be a JSON object containing either the requested data (e.g., search results, processing status, embedding stats) or an error message.", "throws": ["BadRequestError: Thrown if required parameters (e.g., query, projectId, projectPath) are missing from the request body.", "Error: Catches and handles other unexpected internal server errors, returning a 500 status code."]}, "visualDiagram": "classDiagram\n    class RAGSearchController {\n        -VectorService vectorService\n        -DocumentProcessingService documentProcessingService\n        +searchDocuments(req: Request, res: Response): Promise<void>\n        +processProjectDocuments(req: Request, res: Response): Promise<void>\n        +getEmbeddingStats(req: Request, res: Response): Promise<void>\n        -extractRelevantContent(content: string, query: string, maxLength: number): string\n    }\n\n    class VectorService {\n        <<interface>>\n        +searchSimilar(query: string, projectId: string, options: object): Promise<SearchResult[]>\n        +getEmbeddingStats(projectId: string): Promise<EmbeddingStats>\n    }\n\n    class DocumentProcessingService {\n        <<interface>>\n        +processAutomatedDocs(projectPath: string, projectId: string): Promise<void>\n    }\n\n    class Request {\n        <<external>>\n    }\n\n    class Response {\n        <<external>>\n    }\n\n    class BadRequestError {\n        <<error>>\n    }\n\n    RAGSearchController --> VectorService : uses\n    RAGSearchController --> DocumentProcessingService : uses\n    RAGSearchController ..> Request : handles\n    RAGSearchController ..> Response : handles\n    RAGSearchController ..> BadRequestError : throws"}]}