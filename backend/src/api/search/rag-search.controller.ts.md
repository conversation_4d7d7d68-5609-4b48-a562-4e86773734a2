# Documentation for `/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search/rag-search.controller.ts`

**Commit:** `bc3df31a2ad7cf06456e71484539b06ca55f315c` | **Last Updated:** `2025-07-15T18:27:27+05:30`

---

## `RAGSearchController` (Class)

**Purpose:** This class provides API endpoints for performing RAG (Retrieval-Augmented Generation) based document searches, initiating document processing, and retrieving embedding statistics for projects.

### Detailed Explanation

The `RAGSearchController` is an API controller responsible for handling various operations related to a document search and processing system. It's designed to be 'injectable', meaning its dependencies (`VectorService` and `DocumentProcessingService`) are provided at runtime, promoting modularity. 

The `searchDocuments` method is the primary search endpoint. It takes a user's query and a project ID, validates them, and then leverages the `VectorService` to find semantically similar documents. It transforms these raw results into a user-friendly format, including extracted relevant content and optional surrounding context. It also calculates approximate usage metrics like tokens used and estimated cost. 

The `processProjectDocuments` method is an administrative endpoint that triggers the `DocumentProcessingService` to process documents for a given project path and ID. This typically involves tasks like parsing, chunking, and embedding documents into a vector database for future searches. 

The `getEmbeddingStats` method retrieves statistics about the document embeddings for a specific project from the `VectorService`, offering insights into the indexed data. 

Finally, `extractRelevantContent` is a private helper method used internally by `searchDocuments`. Its purpose is to intelligently extract the most pertinent sentences from a document's content based on the search query, ensuring that the displayed 'matched content' is concise and directly relevant to what the user searched for. All public methods include robust error handling, returning appropriate HTTP status codes and messages for bad requests or internal server errors.

### Visual Representation

```mermaid
classDiagram
    class RAGSearchController {
        -VectorService vectorService
        -DocumentProcessingService documentProcessingService
        +searchDocuments(req: Request, res: Response): Promise<void>
        +processProjectDocuments(req: Request, res: Response): Promise<void>
        +getEmbeddingStats(req: Request, res: Response): Promise<void>
        -extractRelevantContent(content: string, query: string, maxLength: number): string
    }

    class VectorService {
        <<interface>>
        +searchSimilar(query: string, projectId: string, options: object): Promise<SearchResult[]>
        +getEmbeddingStats(projectId: string): Promise<EmbeddingStats>
    }

    class DocumentProcessingService {
        <<interface>>
        +processAutomatedDocs(projectPath: string, projectId: string): Promise<void>
    }

    class Request {
        <<external>>
    }

    class Response {
        <<external>>
    }

    class BadRequestError {
        <<error>>
    }

    RAGSearchController --> VectorService : uses
    RAGSearchController --> DocumentProcessingService : uses
    RAGSearchController ..> Request : handles
    RAGSearchController ..> Response : handles
    RAGSearchController ..> BadRequestError : throws
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `searchDocuments` | `method` | Handles a RAG-based document search request. |
| `processProjectDocuments` | `method` | Initiates the processing of documents for a specific project. |
| `getEmbeddingStats` | `method` | Retrieves embedding statistics for a given project. |

### Outputs

- **Returns:** `Promise<void>` - All public methods (`searchDocuments`, `processProjectDocuments`, `getEmbeddingStats`) return a Promise that resolves to `void` after sending an HTTP response. The response body will be a JSON object containing either the requested data (e.g., search results, processing status, embedding stats) or an error message.
- **Throws:** `BadRequestError: Thrown if required parameters (e.g., query, projectId, projectPath) are missing from the request body.`, `Error: Catches and handles other unexpected internal server errors, returning a 500 status code.`

### Dependencies

- **VectorService** (internal)
- **DocumentProcessingService** (internal)
- **Request** (external)
- **Response** (external)
- **BadRequestError** (internal)
- **logger** (external)
- **@injectable** (external)
- **@inject** (external)
- **TYPES** (internal)

---

