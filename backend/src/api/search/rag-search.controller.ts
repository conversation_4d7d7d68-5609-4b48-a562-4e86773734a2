import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import { VectorService } from '../../services/vector.service';
import { DocumentProcessingService } from '../../services/document-processing.service';
import { logger } from '../../common/logger';
import { BadRequestError } from '../../common/errors/http.error';

interface RAGSearchRequest {
  query: string;
  projectId: string;
  filters?: {
    fileTypes?: string[];
    dateRange?: { start: string; end: string };
    documentTypes?: ('auto-doc' | 'interview' | 'readme' | 'comment')[];
  };
  limit?: number;
  includeContext?: boolean;
}

interface RAGSearchResponse {
  results: Array<{
    id: string;
    file: string;
    type: 'semantic';
    confidence: number;
    documentType: 'auto-doc' | 'interview' | 'readme' | 'comment';
    matchedContent: string;
    surroundingContext?: string;
    metadata: {
      lastModified?: Date;
      author?: string;
      relatedFiles?: string[];
      [key: string]: any;
    };
  }>;
  usage: {
    tokensUsed: number;
    cost: number;
    durationMs: number;
  };
  totalResults: number;
}

@injectable()
export class RAGSearchController {
  constructor(
    @inject(TYPES.VectorService) private vectorService: VectorService,
    @inject(TYPES.DocumentProcessingService) private documentProcessingService: DocumentProcessingService,
  ) {}

  /**
   * Search documents using RAG (Retrieval-Augmented Generation)
   */
  async searchDocuments(req: Request, res: Response): Promise<void> {
    try {
      const startTime = Date.now();
      const body = req.body as RAGSearchRequest;

      // Validate request
      if (!body.query || !body.projectId) {
        throw new BadRequestError('Query and projectId are required');
      }

      const {
        query,
        projectId,
        filters = {},
        limit = 10,
        includeContext = true,
      } = body;

      logger.info(`RAG search for project ${projectId}: "${query.substring(0, 100)}..."`);

      // Search for similar documents
      const searchResults = await this.vectorService.searchSimilar(query, projectId, {
        limit,
        documentTypes: filters.documentTypes,
        filePattern: filters.fileTypes?.[0], // Simplified for now
      });

      // Transform results to response format
      const results = searchResults.map((result, index) => ({
        id: `rag_${projectId}_${index}`,
        file: result.document.filePath,
        type: 'semantic' as const,
        confidence: Math.round(result.similarity * 100),
        documentType: result.document.documentType,
        matchedContent: this.extractRelevantContent(result.document.content, query),
        surroundingContext: includeContext ? result.document.content : undefined,
        metadata: {
          ...result.document.metadata,
          lastModified: result.document.updatedAt,
        },
      }));

      const durationMs = Date.now() - startTime;

      // Calculate approximate usage (this is a simplified calculation)
      const tokensUsed = Math.ceil((query.length + results.reduce((sum, r) => sum + r.matchedContent.length, 0)) / 4);
      const cost = (tokensUsed / 1000) * 0.00002; // Based on embedding cost

      const response: RAGSearchResponse = {
        results,
        usage: {
          tokensUsed,
          cost,
          durationMs,
        },
        totalResults: results.length,
      };

      logger.info(`RAG search completed in ${durationMs}ms, found ${results.length} results`);

      res.json(response);
    } catch (error) {
      logger.error('Error in RAG search:', error);
      if (error instanceof BadRequestError) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error during RAG search' });
      }
    }
  }

  /**
   * Process documents for a project (admin endpoint)
   */
  async processProjectDocuments(req: Request, res: Response): Promise<void> {
    try {
      const { projectId, projectPath } = req.body;

      if (!projectId || !projectPath) {
        throw new BadRequestError('ProjectId and projectPath are required');
      }

      logger.info(`Processing documents for project ${projectId} at ${projectPath}`);

      // Process automated documentation
      await this.documentProcessingService.processAutomatedDocs(projectPath, projectId);

      res.json({
        success: true,
        message: 'Document processing initiated',
        projectId,
      });
    } catch (error) {
      logger.error('Error processing project documents:', error);
      if (error instanceof BadRequestError) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error during document processing' });
      }
    }
  }

  /**
   * Get embedding statistics for a project
   */
  async getEmbeddingStats(req: Request, res: Response): Promise<void> {
    try {
      const { projectId } = req.body;

      if (!projectId) {
        throw new BadRequestError('ProjectId is required');
      }

      const stats = await this.vectorService.getEmbeddingStats(projectId);

      res.json({
        success: true,
        stats,
      });
    } catch (error) {
      logger.error('Error getting embedding stats:', error);
      if (error instanceof BadRequestError) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error getting stats' });
      }
    }
  }

  /**
   * Extract the most relevant content from a document based on the query
   */
  private extractRelevantContent(content: string, query: string, maxLength: number = 200): string {
    // Simple extraction - find sentences containing query terms
    const queryTerms = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[.!?]+/);
    
    // Score sentences based on query term matches
    const scoredSentences = sentences.map(sentence => {
      const lowerSentence = sentence.toLowerCase();
      const score = queryTerms.reduce((sum, term) => {
        return sum + (lowerSentence.includes(term) ? 1 : 0);
      }, 0);
      return { sentence: sentence.trim(), score };
    });

    // Sort by score and get the best matches
    scoredSentences.sort((a, b) => b.score - a.score);
    
    // Take the top scoring sentences up to maxLength
    let result = '';
    for (const { sentence, score } of scoredSentences) {
      if (score === 0) break;
      if (result.length + sentence.length > maxLength) break;
      result += sentence + '. ';
    }

    return result.trim() || content.substring(0, maxLength) + '...';
  }
}
