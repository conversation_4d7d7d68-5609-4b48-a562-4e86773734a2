/**
 * DTOs for Module and Lesson
 */
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum } from 'class-validator';

// Define LessonType enum since it's not available in Prisma client yet
export enum LessonType {
  VIDEO = 'VIDEO',
  TEXT = 'TEXT',
  QUIZ = 'QUIZ',
  EXERCISE = 'EXERCISE',
  PROJECT = 'PROJECT',
}

export class CreateModuleDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsNotEmpty()
  @IsNumber()
  workshopId: number = 0;
}

export class UpdateModuleDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class CreateLessonDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsEnum(LessonType)
  lessonType?: LessonType;

  @IsOptional()
  @IsNumber()
  durationMinutes?: number;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsNotEmpty()
  @IsNumber()
  moduleId: number = 0;
}

export class UpdateLessonDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsEnum(LessonType)
  lessonType?: LessonType;

  @IsOptional()
  @IsNumber()
  durationMinutes?: number;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class RecordLessonCompletionDto {
  @IsNotEmpty()
  @IsNumber()
  userId: number = 0;

  @IsNotEmpty()
  @IsNumber()
  lessonId: number = 0;

  @IsOptional()
  @IsNumber()
  score?: number;

  @IsOptional()
  @IsString()
  feedback?: string;
}
