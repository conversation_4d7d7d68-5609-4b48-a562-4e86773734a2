/**
 * DTOs for Workshop model
 */
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsArray,
  IsEnum,
  IsDate,
  IsISO8601,
  ValidateNested,
  IsObject,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

// Define WorkshopType enum since it's not available in Prisma client yet
export enum WorkshopType {
  ESSENTIALS = 'ESSENTIALS',
  PRACTITIONER = 'PRACTITIONER',
  VIBECODERS = 'VIBECODERS',
  AGENTIC = 'AGENTIC',
}

export enum LessonType {
  LECTURE = 'LECTURE',
  INTERACTIVE = 'INTERACTIVE',
  LAB = 'LAB',
  QUIZ = 'QUIZ',
  PROJECT = 'PROJECT',
  DISCUSSION = 'DISCUSSION',
}

export class CreateWorkshopDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsNotEmpty()
  @IsString()
  description: string = '';

  @IsNotEmpty()
  @IsEnum(WorkshopType)
  workshopType: WorkshopType = WorkshopType.ESSENTIALS;

  @IsOptional()
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsString()
  syllabusLink?: string;

  @IsOptional()
  @IsString()
  geography?: string;

  @IsOptional()
  @IsString()
  paymentLink?: string;

  @IsOptional()
  @IsNumber()
  cost?: number;

  @IsOptional()
  @IsNumber()
  maxParticipants?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsISO8601()
  enrollmentDeadline?: Date;

  @IsOptional()
  @IsString()
  prerequisites?: string;

  @IsOptional()
  @IsArray()
  instructorIds?: number[];

  @IsOptional()
  @IsString()
  timezone?: string;

  @IsOptional()
  @IsObject()
  schedule?: any;

  @IsOptional()
  @IsBoolean()
  isOnline?: boolean;

  @IsOptional()
  @IsObject()
  venueDetails?: any;

  @IsOptional()
  @IsBoolean()
  featured?: boolean;
}

export class UpdateWorkshopDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(WorkshopType)
  workshopType?: WorkshopType;

  @IsOptional()
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsString()
  syllabusLink?: string;

  @IsOptional()
  @IsString()
  geography?: string;

  @IsOptional()
  @IsString()
  paymentLink?: string;

  @IsOptional()
  @IsNumber()
  cost?: number;

  @IsOptional()
  @IsNumber()
  maxParticipants?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsISO8601()
  enrollmentDeadline?: Date;

  @IsOptional()
  @IsString()
  prerequisites?: string;

  @IsOptional()
  @IsArray()
  instructorIds?: number[];

  @IsOptional()
  @IsString()
  timezone?: string;

  @IsOptional()
  @IsObject()
  schedule?: any;

  @IsOptional()
  @IsBoolean()
  isOnline?: boolean;

  @IsOptional()
  @IsObject()
  venueDetails?: any;

  @IsOptional()
  @IsBoolean()
  featured?: boolean;
}

export class CreateModuleDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class UpdateModuleDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class CreateLessonDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsOptional()
  @IsEnum(LessonType)
  type?: LessonType;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsString()
  codeLanguage?: string;
}

export class UpdateLessonDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsEnum(LessonType)
  type?: LessonType;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsString()
  codeLanguage?: string;
}

export class UpdateProgressDto {
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100)
  completionPercentage: number = 0;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CompleteLessonDto {
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  score?: number;

  @IsOptional()
  @IsString()
  feedback?: string;
}
