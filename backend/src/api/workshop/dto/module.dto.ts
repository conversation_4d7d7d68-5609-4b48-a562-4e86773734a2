import {
  IsBoolean,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsE<PERSON>,
  Min,
  Max,
} from 'class-validator';

export enum LessonType {
  LECTURE = 'LECTURE',
  INTERACTIVE = 'INTERACTIVE',
  LAB = 'LAB',
  QUIZ = 'QUIZ',
  PROJECT = 'PROJECT',
  DISCUSSION = 'DISCUSSION',
}

export class CreateModuleDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class UpdateModuleDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;
}

export class CreateLessonDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsOptional()
  @IsEnum(LessonType)
  type?: LessonType;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsString()
  codeLanguage?: string;
}

export class UpdateLessonDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsEnum(LessonType)
  type?: LessonType;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsString()
  codeLanguage?: string;
}

export class UpdateProgressDto {
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100)
  completionPercentage: number = 0;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CompleteLessonDto {
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  score?: number;

  @IsOptional()
  @IsString()
  feedback?: string;
}
