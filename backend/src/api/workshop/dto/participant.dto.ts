/**
 * DTOs for Workshop Participants
 */
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum } from 'class-validator';

// Define ParticipationStatus enum since it's not available in Prisma client yet
export enum ParticipationStatus {
  REGISTERED = 'REGISTERED',
  PAID = 'PAID',
  CONFIRMED = 'CONFIRMED',
  ATTENDED = 'ATTENDED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export class AddParticipantDto {
  @IsNotEmpty()
  @IsNumber()
  workshopId: number = 0;

  @IsNotEmpty()
  @IsNumber()
  userId: number = 0;

  @IsOptional()
  @IsEnum(ParticipationStatus)
  status?: ParticipationStatus;

  @IsOptional()
  @IsString()
  paymentStatus?: string;

  @IsOptional()
  @IsNumber()
  paymentId?: number;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateParticipantDto {
  @IsOptional()
  @IsEnum(ParticipationStatus)
  status?: ParticipationStatus;

  @IsOptional()
  @IsString()
  paymentStatus?: string;

  @IsOptional()
  @IsNumber()
  paymentId?: number;

  @IsOptional()
  @IsNumber()
  completionPercentage?: number;

  @IsOptional()
  @IsBoolean()
  feedbackSubmitted?: boolean;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class WorkshopMaterialDto {
  @IsNotEmpty()
  @IsNumber()
  workshopId: number = 0;

  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  materialType: string = '';

  @IsOptional()
  @IsString()
  filePath?: string;

  @IsOptional()
  @IsString()
  externalUrl?: string;

  @IsOptional()
  @IsNumber()
  orderIndex?: number;

  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;
}
