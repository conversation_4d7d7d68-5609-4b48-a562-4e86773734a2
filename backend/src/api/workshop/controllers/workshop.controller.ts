/**
 * Workshop controller
 */
import { Request, Response } from 'express';
import { Controller, Get, Post, Put, Delete, Middleware, ClassMiddleware } from '@overnightjs/core';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { CreateWorkshopDto, UpdateWorkshopDto } from '../dto/workshop.dto';
import { AddParticipantDto } from '../dto/participant.dto';
import { WorkshopRepository } from '../../../db/repositories/workshop/workshop.repository';
import { authMiddleware, roleMiddleware } from '../../../middleware/auth.middleware';
import { validateDto } from '../../../middleware/validation/validate-dto.middleware';

interface AuthenticatedRequest extends Request {
  userId?: number;
  userRole?: string;
}

interface Workshop {
  id: number;
  name: string;
  description: string;
  participants?: Array<{
    userId: number;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    workshopId: number;
  }>;
}

@injectable()
@Controller('api/workshops')
@ClassMiddleware([authMiddleware])
export class WorkshopController {
  constructor(@inject(TYPES.WorkshopRepository) private workshopRepository: WorkshopRepository) {}

  @Get('')
  async getAllWorkshops(req: AuthenticatedRequest, res: Response) {
    try {
      const filters: Record<string, boolean> = {};

      // Parse query params
      if (req.query.isActive !== undefined) {
        filters.isActive = req.query.isActive === 'true';
      }

      if (req.query.featured !== undefined) {
        filters.featured = req.query.featured === 'true';
      }

      if (req.query.upcoming !== undefined) {
        filters.upcoming = req.query.upcoming === 'true';
      }

      if (req.query.past !== undefined) {
        filters.past = req.query.past === 'true';
      }

      const workshops = await this.workshopRepository.findAllWorkshops();
      return res.status(200).json(workshops);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Get(':id')
  async getWorkshopById(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      if (isNaN(workshopId)) {
        return res.status(400).json({ error: 'Invalid workshop ID' });
      }

      const workshop = await this.workshopRepository.findWorkshopById(workshopId);

      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      return res.status(200).json(workshop);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Post('')
  @Middleware([roleMiddleware(['ADMIN']), validateDto(CreateWorkshopDto)])
  async createWorkshop(req: AuthenticatedRequest, res: Response) {
    try {
      const data = req.body as CreateWorkshopDto;
      const workshop = await this.workshopRepository.createWorkshop(data);
      return res.status(201).json(workshop);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Put(':id')
  @Middleware([roleMiddleware(['ADMIN']), validateDto(UpdateWorkshopDto)])
  async updateWorkshop(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      if (isNaN(workshopId)) {
        return res.status(400).json({ error: 'Invalid workshop ID' });
      }

      const data = req.body as UpdateWorkshopDto;

      // Check if workshop exists
      const workshop = await this.workshopRepository.findWorkshopById(workshopId);
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      const updatedWorkshop = await this.workshopRepository.updateWorkshop(workshopId, data);
      return res.status(200).json(updatedWorkshop);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Delete(':id')
  @Middleware([roleMiddleware(['ADMIN'])])
  async deleteWorkshop(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      if (isNaN(workshopId)) {
        return res.status(400).json({ error: 'Invalid workshop ID' });
      }

      // Check if workshop exists
      const workshop = await this.workshopRepository.findWorkshopById(workshopId);
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      await this.workshopRepository.deleteWorkshop(workshopId);
      return res.status(204).send();
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  // Participants endpoints
  @Get(':id/participants')
  async getWorkshopParticipants(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      if (isNaN(workshopId)) {
        return res.status(400).json({ error: 'Invalid workshop ID' });
      }

      // Check if workshop exists
      const workshop = (await this.workshopRepository.findWorkshopById(workshopId)) as Workshop;
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      // Only admin or participants can view participant list
      if (req.userRole !== 'ADMIN') {
        const isParticipant = workshop.participants?.some((p) => p.userId === req.userId);
        if (!isParticipant) {
          return res.status(403).json({ error: 'Access denied' });
        }
      }

      const participants = await this.workshopRepository.getParticipants(workshopId);
      return res.status(200).json(participants);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Post(':id/participants')
  @Middleware([roleMiddleware(['ADMIN', 'DEVELOPER']), validateDto(AddParticipantDto)])
  async addParticipant(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      if (isNaN(workshopId)) {
        return res.status(400).json({ error: 'Invalid workshop ID' });
      }

      const data = req.body as AddParticipantDto;

      // Set workshopId from path
      data.workshopId = workshopId;

      // Set userId if not provided
      if (!data.userId) {
        if (!req.userId) {
          return res.status(400).json({ error: 'User ID is required' });
        }
        data.userId = req.userId;
      }

      // Only admins can add other users
      if (data.userId !== req.userId && req.userRole !== 'ADMIN') {
        return res.status(403).json({ error: 'Cannot add another user to workshop' });
      }

      // Check if workshop exists
      const workshop = (await this.workshopRepository.findWorkshopById(workshopId)) as Workshop;
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      // Check if already a participant
      const existingParticipant = workshop.participants?.find((p) => p.userId === data.userId);
      if (existingParticipant) {
        return res.status(400).json({ error: 'User is already a participant' });
      }

      const participant = await this.workshopRepository.addParticipant(workshopId, data.userId);
      return res.status(201).json(participant);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }

  @Delete(':id/participants/:userId')
  @Middleware([roleMiddleware(['ADMIN'])])
  async removeParticipant(req: AuthenticatedRequest, res: Response) {
    try {
      const workshopId = parseInt(req.params.id, 10);
      const userId = parseInt(req.params.userId, 10);

      if (isNaN(workshopId) || isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid workshop ID or user ID' });
      }

      // Check if workshop exists
      const workshop = (await this.workshopRepository.findWorkshopById(workshopId)) as Workshop;
      if (!workshop) {
        return res.status(404).json({ error: 'Workshop not found' });
      }

      // Check if participant exists
      const existingParticipant = workshop.participants?.find((p) => p.userId === userId);
      if (!existingParticipant) {
        return res.status(404).json({ error: 'Participant not found' });
      }

      await this.workshopRepository.removeParticipant(workshopId, userId);
      return res.status(204).send();
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      return res.status(500).json({ error: err.message });
    }
  }
}
