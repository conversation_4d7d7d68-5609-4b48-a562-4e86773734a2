import { Controller, Get, Post, Put, Body, Param, UseGuards } from '@nestjs/common';
import { WorkshopProgressService } from '../../../services/workshop/workshop-progress.service';
import {
  CreateModuleDto,
  UpdateModuleDto,
  CreateLessonDto,
  UpdateLessonDto,
  UpdateProgressDto,
  CompleteLessonDto,
} from '../dto/module.dto';
import { AuthGuard } from '../../../guards/auth.guard';
import { RoleGuard } from '../../../guards/role.guard';
import { Roles } from '../../../decorators/roles.decorator';

@Controller('api/workshops')
@UseGuards(AuthGuard)
export class WorkshopProgressController {
  constructor(private readonly workshopProgressService: WorkshopProgressService) {}

  @Post(':workshopId/modules')
  @UseGuards(RoleGuard)
  @Roles('INSTRUCTOR', 'ADMIN')
  async createModule(@Param('workshopId') workshopId: number, @Body() data: CreateModuleDto) {
    return this.workshopProgressService.createModule(workshopId, data);
  }

  @Put('modules/:moduleId')
  @UseGuards(RoleGuard)
  @Roles('INSTRUCTOR', 'ADMIN')
  async updateModule(@Param('moduleId') moduleId: number, @Body() data: UpdateModuleDto) {
    return this.workshopProgressService.updateModule(moduleId, data);
  }

  @Get(':workshopId/modules')
  async getModules(@Param('workshopId') workshopId: number) {
    return this.workshopProgressService.getModules(workshopId);
  }

  @Post('modules/:moduleId/lessons')
  @UseGuards(RoleGuard)
  @Roles('INSTRUCTOR', 'ADMIN')
  async createLesson(@Param('moduleId') moduleId: number, @Body() data: CreateLessonDto) {
    return this.workshopProgressService.createLesson(moduleId, data);
  }

  @Put('lessons/:lessonId')
  @UseGuards(RoleGuard)
  @Roles('INSTRUCTOR', 'ADMIN')
  async updateLesson(@Param('lessonId') lessonId: number, @Body() data: UpdateLessonDto) {
    return this.workshopProgressService.updateLesson(lessonId, data);
  }

  @Get('modules/:moduleId/lessons')
  async getLessons(@Param('moduleId') moduleId: number) {
    return this.workshopProgressService.getLessons(moduleId);
  }

  @Put(':workshopId/progress/:moduleId')
  async updateProgress(
    @Param('workshopId') workshopId: number,
    @Param('moduleId') moduleId: number,
    @Body() data: UpdateProgressDto,
  ) {
    const userId = 1; // TODO: Get from auth context
    return this.workshopProgressService.updateProgress(workshopId, userId, moduleId, data);
  }

  @Put('lessons/:lessonId/complete')
  async completeLesson(@Param('lessonId') lessonId: number, @Body() data: CompleteLessonDto) {
    const userId = 1; // TODO: Get from auth context
    return this.workshopProgressService.completeLesson(lessonId, userId, data);
  }

  @Get(':workshopId/progress')
  async getProgress(@Param('workshopId') workshopId: number) {
    const userId = 1; // TODO: Get from auth context
    return this.workshopProgressService.getProgress(workshopId, userId);
  }
}
