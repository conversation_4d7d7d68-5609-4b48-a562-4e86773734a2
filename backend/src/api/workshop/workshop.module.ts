import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { WorkshopController } from './controllers/workshop.controller';
import { WorkshopProgressController } from './controllers/workshop-progress.controller';
import { WorkshopService } from '@/services/workshop/workshop.service';
import { WorkshopProgressService } from '@/services/workshop/workshop-progress.service';
import { WorkshopRepository } from '@/db/repositories/workshop/workshop.repository';
import { PrismaService } from '@/db/prisma.service';

@Module({
  controllers: [WorkshopController, WorkshopProgressController],
  providers: [WorkshopService, WorkshopProgressService, WorkshopRepository, PrismaService],
  exports: [WorkshopService, WorkshopProgressService],
})
export class WorkshopModule {}
