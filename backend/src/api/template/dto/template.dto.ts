/**
 * DTOs for template operations
 */

export class CreateTemplateDto {
  name!: string;
  description?: string;
  category!: string;
  version?: string;
  isPublic?: boolean;
  creatorId?: number;
  thumbnailUrl?: string;
  metadata?: any;
}

export class UpdateTemplateDto {
  name?: string;
  description?: string;
  category?: string;
  version?: string;
  isPublic?: boolean;
  thumbnailUrl?: string;
  metadata?: any;
}

export class CreateTemplateFileDto {
  path!: string;
  content!: string;
  isExecutable?: boolean;
  isBinary?: boolean;
}

export class UpdateTemplateFileDto {
  path?: string;
  content?: string;
  isExecutable?: boolean;
  isBinary?: boolean;
}

export class CreateTemplateVariableDto {
  name!: string;
  defaultValue?: string;
  description?: string;
  required?: boolean;
  variableType?: string;
  options?: any;
}

export class UpdateTemplateVariableDto {
  name?: string;
  defaultValue?: string;
  description?: string;
  required?: boolean;
  variableType?: string;
  options?: any;
}

export class CreateTemplateCollectionDto {
  name!: string;
  description?: string;
  isPublic?: boolean;
  creatorId?: number;
}

export class UpdateTemplateCollectionDto {
  name?: string;
  description?: string;
  isPublic?: boolean;
}

export class CreateProjectTemplateDto {
  name!: string;
  description?: string;
  structure!: any;
  defaultBranch?: string;
  creatorId?: number;
  isPublic?: boolean;
}

export class UpdateProjectTemplateDto {
  name?: string;
  description?: string;
  structure?: any;
  defaultBranch?: string;
  isPublic?: boolean;
}

export class TemplateUsageDto {
  templateId!: number;
  userId!: number;
  projectId?: number;
  variableValues?: any;
}
