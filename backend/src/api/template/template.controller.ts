/**
 * Template controller for handling template-related API requests
 */
import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import { TemplateService } from '../../services/template';
import { logger } from '../../common/logger';
import { AuthenticatedRequest } from '../../common/types/authenticated-request';
import {
  CreateTemplateDto,
  UpdateTemplateDto,
  CreateTemplateFileDto,
  UpdateTemplateFileDto,
  CreateTemplateVariableDto,
  UpdateTemplateVariableDto,
  CreateTemplateCollectionDto,
  UpdateTemplateCollectionDto,
  CreateProjectTemplateDto,
  UpdateProjectTemplateDto,
  TemplateUsageDto,
} from './dto';

@injectable()
export class TemplateController {
  constructor(@inject(TYPES.TemplateService) private templateService: TemplateService) {}

  /**
   * Create a new template
   */
  async createTemplate(req: AuthenticatedRequest, res: Response) {
    try {
      const data: CreateTemplateDto = req.body;

      // Set creator ID from authenticated user if not provided
      if (!data.creatorId && req.user) {
        data.creatorId = req.user.id;
      }

      const template = await this.templateService.createTemplate(data);
      return res.status(201).json(template);
    } catch (error) {
      logger.error('Error creating template:', error);
      return res.status(500).json({ error: 'Failed to create template' });
    }
  }

  /**
   * Get a template by ID
   */
  async getTemplateById(req: AuthenticatedRequest, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const template = await this.templateService.getTemplateById(id);
      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }

      return res.status(200).json(template);
    } catch (error) {
      logger.error(`Error getting template:`, error);
      return res.status(500).json({ error: 'Failed to get template' });
    }
  }

  /**
   * Get all templates with optional filters
   */
  async getAllTemplates(req: AuthenticatedRequest, res: Response) {
    try {
      const filters: any = {};

      // Parse query parameters
      if (req.query.creatorId) {
        filters.creatorId = parseInt(req.query.creatorId as string, 10);
      }

      if (req.query.isPublic !== undefined) {
        filters.isPublic = req.query.isPublic === 'true';
      }

      if (req.query.category) {
        filters.category = req.query.category as string;
      }

      const templates = await this.templateService.getAllTemplates(filters);
      return res.status(200).json(templates);
    } catch (error) {
      logger.error('Error getting templates:', error);
      return res.status(500).json({ error: 'Failed to get templates' });
    }
  }

  /**
   * Update a template
   */
  async updateTemplate(req: AuthenticatedRequest, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const data: UpdateTemplateDto = req.body;
      const template = await this.templateService.updateTemplate(id, data);

      return res.status(200).json(template);
    } catch (error) {
      logger.error(`Error updating template:`, error);
      return res.status(500).json({ error: 'Failed to update template' });
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      await this.templateService.deleteTemplate(id);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting template:`, error);
      return res.status(500).json({ error: 'Failed to delete template' });
    }
  }

  /**
   * Add a file to a template
   */
  async addTemplateFile(req: Request, res: Response) {
    try {
      const templateId = parseInt(req.params.templateId, 10);
      if (isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const data: CreateTemplateFileDto = req.body;
      const file = await this.templateService.addTemplateFile(templateId, data);

      return res.status(201).json(file);
    } catch (error) {
      logger.error(`Error adding file to template:`, error);
      return res.status(500).json({ error: 'Failed to add file to template' });
    }
  }

  /**
   * Get all files for a template
   */
  async getTemplateFiles(req: Request, res: Response) {
    try {
      const templateId = parseInt(req.params.templateId, 10);
      if (isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const files = await this.templateService.getTemplateFiles(templateId);
      return res.status(200).json(files);
    } catch (error) {
      logger.error(`Error getting template files:`, error);
      return res.status(500).json({ error: 'Failed to get template files' });
    }
  }

  /**
   * Update a template file
   */
  async updateTemplateFile(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid file ID' });
      }

      const data: UpdateTemplateFileDto = req.body;
      const file = await this.templateService.updateTemplateFile(id, data);

      return res.status(200).json(file);
    } catch (error) {
      logger.error(`Error updating template file:`, error);
      return res.status(500).json({ error: 'Failed to update template file' });
    }
  }

  /**
   * Delete a template file
   */
  async deleteTemplateFile(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid file ID' });
      }

      await this.templateService.deleteTemplateFile(id);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting template file:`, error);
      return res.status(500).json({ error: 'Failed to delete template file' });
    }
  }

  /**
   * Add a variable to a template
   */
  async addTemplateVariable(req: Request, res: Response) {
    try {
      const templateId = parseInt(req.params.templateId, 10);
      if (isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const data: CreateTemplateVariableDto = req.body;
      const variable = await this.templateService.addTemplateVariable(templateId, data);

      return res.status(201).json(variable);
    } catch (error) {
      logger.error(`Error adding variable to template:`, error);
      return res.status(500).json({ error: 'Failed to add variable to template' });
    }
  }

  /**
   * Get all variables for a template
   */
  async getTemplateVariables(req: Request, res: Response) {
    try {
      const templateId = parseInt(req.params.templateId, 10);
      if (isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid template ID' });
      }

      const variables = await this.templateService.getTemplateVariables(templateId);
      return res.status(200).json(variables);
    } catch (error) {
      logger.error(`Error getting template variables:`, error);
      return res.status(500).json({ error: 'Failed to get template variables' });
    }
  }

  /**
   * Update a template variable
   */
  async updateTemplateVariable(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid variable ID' });
      }

      const data: UpdateTemplateVariableDto = req.body;
      const variable = await this.templateService.updateTemplateVariable(id, data);

      return res.status(200).json(variable);
    } catch (error) {
      logger.error(`Error updating template variable:`, error);
      return res.status(500).json({ error: 'Failed to update template variable' });
    }
  }

  /**
   * Delete a template variable
   */
  async deleteTemplateVariable(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid variable ID' });
      }

      await this.templateService.deleteTemplateVariable(id);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting template variable:`, error);
      return res.status(500).json({ error: 'Failed to delete template variable' });
    }
  }

  /**
   * Create a template collection
   */
  async createTemplateCollection(req: AuthenticatedRequest, res: Response) {
    try {
      const data: CreateTemplateCollectionDto = req.body;

      // Set creator ID from authenticated user if not provided
      if (!data.creatorId && req.user) {
        data.creatorId = req.user.id;
      }

      const collection = await this.templateService.createTemplateCollection(data);
      return res.status(201).json(collection);
    } catch (error) {
      logger.error('Error creating template collection:', error);
      return res.status(500).json({ error: 'Failed to create template collection' });
    }
  }

  /**
   * Get a template collection by ID
   */
  async getTemplateCollectionById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID' });
      }

      const collection = await this.templateService.getTemplateCollectionById(id);
      if (!collection) {
        return res.status(404).json({ error: 'Template collection not found' });
      }

      return res.status(200).json(collection);
    } catch (error) {
      logger.error(`Error getting template collection:`, error);
      return res.status(500).json({ error: 'Failed to get template collection' });
    }
  }

  /**
   * Get all template collections with optional filters
   */
  async getAllTemplateCollections(req: Request, res: Response) {
    try {
      const filters: any = {};

      // Parse query parameters
      if (req.query.creatorId) {
        filters.creatorId = parseInt(req.query.creatorId as string, 10);
      }

      if (req.query.isPublic !== undefined) {
        filters.isPublic = req.query.isPublic === 'true';
      }

      const collections = await this.templateService.getAllTemplateCollections(filters);
      return res.status(200).json(collections);
    } catch (error) {
      logger.error('Error getting template collections:', error);
      return res.status(500).json({ error: 'Failed to get template collections' });
    }
  }

  /**
   * Update a template collection
   */
  async updateTemplateCollection(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID' });
      }

      const data: UpdateTemplateCollectionDto = req.body;
      const collection = await this.templateService.updateTemplateCollection(id, data);

      return res.status(200).json(collection);
    } catch (error) {
      logger.error(`Error updating template collection:`, error);
      return res.status(500).json({ error: 'Failed to update template collection' });
    }
  }

  /**
   * Delete a template collection
   */
  async deleteTemplateCollection(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID' });
      }

      await this.templateService.deleteTemplateCollection(id);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting template collection:`, error);
      return res.status(500).json({ error: 'Failed to delete template collection' });
    }
  }

  /**
   * Add a template to a collection
   */
  async addTemplateToCollection(req: Request, res: Response) {
    try {
      const collectionId = parseInt(req.params.collectionId, 10);
      const templateId = parseInt(req.params.templateId, 10);

      if (isNaN(collectionId) || isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid collection or template ID' });
      }

      const result = await this.templateService.addTemplateToCollection(collectionId, templateId);
      return res.status(201).json(result);
    } catch (error) {
      logger.error(`Error adding template to collection:`, error);
      return res.status(500).json({ error: 'Failed to add template to collection' });
    }
  }

  /**
   * Remove a template from a collection
   */
  async removeTemplateFromCollection(req: Request, res: Response) {
    try {
      const collectionId = parseInt(req.params.collectionId, 10);
      const templateId = parseInt(req.params.templateId, 10);

      if (isNaN(collectionId) || isNaN(templateId)) {
        return res.status(400).json({ error: 'Invalid collection or template ID' });
      }

      await this.templateService.removeTemplateFromCollection(collectionId, templateId);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error removing template from collection:`, error);
      return res.status(500).json({ error: 'Failed to remove template from collection' });
    }
  }

  /**
   * Create a project template
   */
  async createProjectTemplate(req: AuthenticatedRequest, res: Response) {
    try {
      const data: CreateProjectTemplateDto = req.body;

      // Set creator ID from authenticated user if not provided
      if (!data.creatorId && req.user) {
        data.creatorId = req.user.id;
      }

      const projectTemplate = await this.templateService.createProjectTemplate(data);
      return res.status(201).json(projectTemplate);
    } catch (error) {
      logger.error('Error creating project template:', error);
      return res.status(500).json({ error: 'Failed to create project template' });
    }
  }

  /**
   * Get a project template by ID
   */
  async getProjectTemplateById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid project template ID' });
      }

      const projectTemplate = await this.templateService.getProjectTemplateById(id);
      if (!projectTemplate) {
        return res.status(404).json({ error: 'Project template not found' });
      }

      return res.status(200).json(projectTemplate);
    } catch (error) {
      logger.error(`Error getting project template:`, error);
      return res.status(500).json({ error: 'Failed to get project template' });
    }
  }

  /**
   * Get all project templates with optional filters
   */
  async getAllProjectTemplates(req: Request, res: Response) {
    try {
      const filters: any = {};

      // Parse query parameters
      if (req.query.creatorId) {
        filters.creatorId = parseInt(req.query.creatorId as string, 10);
      }

      if (req.query.isPublic !== undefined) {
        filters.isPublic = req.query.isPublic === 'true';
      }

      const projectTemplates = await this.templateService.getAllProjectTemplates(filters);
      return res.status(200).json(projectTemplates);
    } catch (error) {
      logger.error('Error getting project templates:', error);
      return res.status(500).json({ error: 'Failed to get project templates' });
    }
  }

  /**
   * Update a project template
   */
  async updateProjectTemplate(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid project template ID' });
      }

      const data: UpdateProjectTemplateDto = req.body;
      const projectTemplate = await this.templateService.updateProjectTemplate(id, data);

      return res.status(200).json(projectTemplate);
    } catch (error) {
      logger.error(`Error updating project template:`, error);
      return res.status(500).json({ error: 'Failed to update project template' });
    }
  }

  /**
   * Delete a project template
   */
  async deleteProjectTemplate(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id, 10);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid project template ID' });
      }

      await this.templateService.deleteProjectTemplate(id);
      return res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting project template:`, error);
      return res.status(500).json({ error: 'Failed to delete project template' });
    }
  }

  /**
   * Record template usage
   */
  async recordTemplateUsage(req: AuthenticatedRequest, res: Response) {
    try {
      const data: TemplateUsageDto = req.body;

      // Set user ID from authenticated user if not provided
      if (!data.userId && req.user) {
        data.userId = req.user.id;
      }

      const usage = await this.templateService.recordTemplateUsage(data);
      return res.status(201).json(usage);
    } catch (error) {
      logger.error(`Error recording template usage:`, error);
      return res.status(500).json({ error: 'Failed to record template usage' });
    }
  }
}
