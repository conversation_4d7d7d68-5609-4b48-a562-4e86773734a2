/**
 * Agent DTOs
 *
 * Data Transfer Objects for agent API endpoints.
 */

import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { AgentStatus } from '../../../generated/prisma';

/**
 * DTO for starting an agent session
 */
export class StartAgentSessionDto {
  @IsNumber()
  userId!: number;

  @IsString()
  taskDescription!: string;

  @IsNumber()
  @IsOptional()
  projectId?: number;
}

/**
 * DTO for file change
 */
export class FileChangeDto {
  @IsEnum(['insert', 'delete', 'replace'])
  type!: 'insert' | 'delete' | 'replace';

  @IsNumber()
  line!: number;

  @IsString()
  content!: string;
}

/**
 * DTO for file edit action
 */
export class FileEditActionDto {
  @IsString()
  path!: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileChangeDto)
  changes!: FileChangeDto[];
}

/**
 * DTO for pipeline stage
 */
export class PipelineStageDto {
  @IsString()
  id!: string;

  @IsString()
  name!: string;

  @IsString()
  description!: string;
}

/**
 * DTO for creating custom pipeline
 */
export class CreateCustomPipelineDto {
  @IsNumber()
  sessionId!: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PipelineStageDto)
  stages!: PipelineStageDto[];
}

/**
 * DTO for agent session response
 */
export class AgentSessionResponseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  user_id!: number;

  @IsString()
  title!: string;

  @IsString()
  key_objective!: string;

  @IsNumber()
  @IsOptional()
  project_id?: number;

  @IsEnum(AgentStatus)
  agent_status!: AgentStatus;

  @IsNumber()
  agent_iteration_count!: number;

  @IsArray()
  @IsString({ each: true })
  agent_allowed_actions!: string[];

  @IsArray()
  agent_commands_executed!: any[];

  @IsString()
  @IsOptional()
  agent_completion_summary?: string;

  @IsString()
  @IsOptional()
  current_stage?: string;

  @IsArray()
  @IsOptional()
  agent_pipeline_stages?: any[];

  @IsOptional()
  currentStage?: PipelineStageDto;

  @IsArray()
  @IsOptional()
  pipelineStages?: PipelineStageDto[];
}

/**
 * DTO for agent message response
 */
export class AgentMessageResponseDto {
  @IsNumber()
  id!: number;

  @IsString()
  role!: string;

  @IsString()
  content!: string;

  @IsString()
  timestamp!: string;

  @IsString()
  type!: string;

  @IsString()
  @IsOptional()
  stage?: string;
}
