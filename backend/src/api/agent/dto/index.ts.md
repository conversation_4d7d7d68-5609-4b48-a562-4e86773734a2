# Documentation for `/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/agent/dto/index.ts`

**Commit:** `bc3df31a2ad7cf06456e71484539b06ca55f315c` | **Last Updated:** `2025-07-15T18:27:27+05:30`

---

## `StartAgentSessionDto` (Class)

**Purpose:** Defines the data structure required to initiate a new agent session.

### Detailed Explanation

This code defines a Data Transfer Object (DTO) named `StartAgentSessionDto`. DTOs are simple objects used to transfer data between different layers or processes of an application. In this case, `StartAgentAgentSessionDto` specifies the exact data expected when a request is made to start an agent session. It includes three properties:
- `userId`: A mandatory number representing the ID of the user initiating the session.
- `taskDescription`: A mandatory string detailing the task the agent needs to perform.
- `projectId`: An optional number representing the ID of the project associated with the session. Its optional nature is indicated by the `?` and the `@IsOptional()` decorator. 

The `@IsNumber()` and `@IsString()` decorators are likely from a validation library (e.g., `class-validator`) and ensure that the incoming data conforms to the specified types, providing robust input validation for API endpoints that consume this DTO.

### Visual Representation

```mermaid
```mermaid
classDiagram
    class StartAgentSessionDto {
        +number userId
        +string taskDescription
        +number? projectId
    }
```
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `userId` | `number` | The unique identifier of the user starting the agent session. This field is mandatory. |
| `taskDescription` | `string` | A detailed description of the task the agent needs to perform. This field is mandatory. |
| `projectId` | `number` | The unique identifier of the project associated with this agent session. This field is optional. |

### Outputs

- **Returns:** `StartAgentSessionDto` - An instance of the StartAgentSessionDto class, populated with the validated input data. This object is typically used by controllers or services to process the request.
- **Throws:** `ValidationErrors (if input data does not conform to specified types or constraints, typically handled by a validation pipeline)`

### Dependencies

- **IsNumber** (external)
- **IsString** (external)
- **IsOptional** (external)

---

## `FileChangeDto` (Class)

**Purpose:** Represents a data transfer object for describing a single change operation on a file.

### Detailed Explanation

The `FileChangeDto` is a Data Transfer Object (DTO) designed to encapsulate the details of a modification made to a file. It is typically used in API requests or responses to convey structured information about file changes, such as those occurring in a code editor or a version control system. The DTO ensures that any data representing a file change adheres to a strict structure and type constraints, which is crucial for reliable communication and data integrity.

It comprises three essential properties:
- `type`: A string indicating the nature of the change. It is strictly validated to be one of 'insert', 'delete', or 'replace', ensuring that only predefined change operations are communicated.
- `line`: A number specifying the line number in the file where the change occurs. This provides the precise location of the modification.
- `content`: A string holding the actual content involved in the change. For 'insert' or 'replace' types, this would be the new text. For 'delete', it might represent the content that was removed, depending on the specific implementation context.

The `@IsEnum`, `@IsNumber`, and `@IsString` decorators are used for validation, typically provided by a library like `class-validator`, to enforce these type and value constraints when an instance of this DTO is received or created.

### Visual Representation

```mermaid
classDiagram
    class FileChangeDto {
        <<DTO>>
        +string type
        +number line
        +string content
    }
    note for FileChangeDto::type "Must be 'insert', 'delete', or 'replace'"
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `type` | `'insert' | 'delete' | 'replace'` | The type of file change operation (insert, delete, or replace). |
| `line` | `number` | The line number in the file where the change occurs. |
| `content` | `string` | The content involved in the file change (e.g., new text for insert/replace). |

### Outputs

- **Returns:** `FileChangeDto` - An object representing a single file modification operation with its type, line number, and associated content.

### Dependencies

- **IsEnum** (external)
- **IsNumber** (external)
- **IsString** (external)

---

## `FileEditActionDto` (Class)

**Purpose:** This DTO defines the structure for representing a file editing action, including the file path and a list of specific changes.

### Detailed Explanation

The `FileEditActionDto` is a Data Transfer Object (DTO) designed to encapsulate all necessary information for a single file editing operation. It contains two key properties:

1.  `path`: A string that specifies the absolute or relative path to the file that is being edited. The `@IsString()` decorator from `class-validator` ensures that any incoming data for this property must be a string, enforcing type integrity.

2.  `changes`: An array of `FileChangeDto` objects. Each `FileChangeDto` object is expected to describe a specific modification within the file (e.g., an insertion, deletion, or replacement at a certain line/column). The `@IsArray()` decorator confirms that this property must be an array. Crucially, `@ValidateNested({ each: true })` instructs the validation system to apply the validation rules defined within each `FileChangeDto` object to every element in this array. Furthermore, `@Type(() => FileChangeDto)` from `class-transformer` is used to ensure that when data is transformed (e.g., from a plain JavaScript object or JSON string), each element in the `changes` array is correctly instantiated as an instance of `FileChangeDto`, enabling proper validation and type-checking for nested objects.

This DTO is typically used in API endpoints to receive structured data from clients when they want to perform a file modification, ensuring that the incoming data adheres to a predefined format and can be reliably processed by the backend.

### Visual Representation

```mermaid
```mermaid
classDiagram
    class FileEditActionDto {
        +string path
        +FileChangeDto[] changes
    }
    class FileChangeDto {
        <<DTO>>
        // ... properties defining a single file change
    }
    FileEditActionDto "1" *-- "0..*" FileChangeDto : contains
```
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `path` | `string` | The path to the file being edited. |
| `changes` | `FileChangeDto[]` | An array of individual changes to be applied to the file. |

### Outputs

- **Returns:** `FileEditActionDto` - An instance of FileEditActionDto representing a structured file edit action, ready for processing.

### Dependencies

- **IsString** (external)
- **IsArray** (external)
- **ValidateNested** (external)
- **Type** (external)
- **FileChangeDto** (internal)

---

## `PipelineStageDto` (Class)

**Purpose:** Defines the data structure for a single stage within a data processing pipeline.

### Detailed Explanation

The `PipelineStageDto` class serves as a Data Transfer Object (DTO) for representing a stage in a pipeline. DTOs are used to define the shape of data that is transferred between different layers of an application, typically between the client and the server, or between service layers. In this case, it ensures that any data representing a pipeline stage adheres to a specific structure, containing an `id`, `name`, and `description`, all of which are expected to be strings. The `@IsString()` decorator, likely from a validation library like `class-validator`, enforces that these properties must be strings when an instance of this DTO is validated, ensuring data integrity.

### Visual Representation

```mermaid
classDiagram
    class PipelineStageDto {
        +string id
        +string name
        +string description
    }
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `id` | `string` | A unique identifier for the pipeline stage. |
| `name` | `string` | The human-readable name of the pipeline stage. |
| `description` | `string` | A brief explanation or description of what the pipeline stage does. |

### Outputs

- **Returns:** `PipelineStageDto` - An object conforming to the defined structure for a pipeline stage, typically used for API request bodies or response payloads.

### Dependencies

- **IsString (from class-validator)** (external)

---

## `CreateCustomPipelineDto` (Class)

**Purpose:** This DTO defines the structure for data required to create a custom pipeline.

### Detailed Explanation

The `CreateCustomPipelineDto` class serves as a Data Transfer Object (DTO) used for validating and structuring the input data when a request is made to create a custom pipeline. It ensures that incoming data conforms to a predefined shape before being processed by the backend. The class has two key properties:

1.  `sessionId`: This property is decorated with `@IsNumber()`, indicating that its value must be a number. It represents the unique identifier for the session to which the custom pipeline will be associated.
2.  `stages`: This property is an array of `PipelineStageDto` objects. It's decorated with `@IsArray()` to ensure it's an array, `@ValidateNested({ each: true })` to recursively validate each item within the array against the `PipelineStageDto` schema, and `@Type(() => PipelineStageDto)` from `class-transformer` to enable proper object instantiation and transformation of plain objects into `PipelineStageDto` instances. This property defines the sequence and details of each stage within the custom pipeline.

This DTO is crucial for maintaining data integrity and providing clear API contracts.

### Visual Representation

```mermaid
classDiagram
  class CreateCustomPipelineDto {
    +number sessionId
    +PipelineStageDto[] stages
  }
  CreateCustomPipelineDto "1" -- "*" PipelineStageDto : contains
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `sessionId` | `number` | The unique identifier of the session for which the custom pipeline is being created. |
| `stages` | `PipelineStageDto[]` | An array of pipeline stages, each conforming to the PipelineStageDto structure, defining the custom pipeline's sequence. |

### Outputs

- **Returns:** `CreateCustomPipelineDto` - An instance of CreateCustomPipelineDto, representing a validated data structure for creating a custom pipeline.

### Dependencies

- **IsNumber** (external)
- **IsArray** (external)
- **ValidateNested** (external)
- **Type** (external)
- **PipelineStageDto** (internal)

---

## `AgentSessionResponseDto` (Class)

**Purpose:** Defines the data structure for responses related to an agent's session, including its status, progress, and associated details.

### Detailed Explanation

This class, `AgentSessionResponseDto`, serves as a Data Transfer Object (DTO) in the backend system. Its primary role is to define the precise structure of data that is sent as a response when information about an agent's session is requested or provided. Each property within the class represents a specific attribute of an agent session, such as its unique identifier (`id`), the user it belongs to (`user_id`), its purpose (`title`, `key_objective`), and its current operational state (`agent_status`, `agent_iteration_count`). It also includes details about the agent's actions (`agent_allowed_actions`, `agent_commands_executed`) and its progression through various stages (`current_stage`, `agent_pipeline_stages`, `currentStage`, `pipelineStages`). The decorators like `@IsNumber()`, `@IsString()`, `@IsEnum()`, `@IsArray()`, and `@IsOptional()` are from a validation library (likely `class-validator`), ensuring that the data conforms to the expected types and constraints when an instance of this DTO is created or validated. Properties marked with `?` and `@IsOptional()` are not mandatory.

### Visual Representation

```mermaid
classDiagram
    class AgentSessionResponseDto {
        +number id
        +number user_id
        +string title
        +string key_objective
        +number? project_id
        +AgentStatus agent_status
        +number agent_iteration_count
        +string[] agent_allowed_actions
        +any[] agent_commands_executed
        +string? agent_completion_summary
        +string? current_stage
        +any[]? agent_pipeline_stages
        +PipelineStageDto? currentStage
        +PipelineStageDto[]? pipelineStages
    }

    AgentSessionResponseDto --o AgentStatus : uses
    AgentSessionResponseDto --o PipelineStageDto : contains
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `id` | `number` | Unique identifier for the agent session. |
| `user_id` | `number` | ID of the user associated with the session. |
| `title` | `string` | Title or name of the agent session. |
| `key_objective` | `string` | The primary objective or goal of the agent session. |
| `project_id` | `number | undefined` | Optional ID of the project related to this agent session. |
| `agent_status` | `AgentStatus` | Current operational status of the agent (e.g., running, completed, failed). |
| `agent_iteration_count` | `number` | Number of iterations or steps the agent has completed. |
| `agent_allowed_actions` | `string[]` | An array of strings representing actions the agent is permitted to perform. |
| `agent_commands_executed` | `any[]` | An array of commands that the agent has executed. |
| `agent_completion_summary` | `string | undefined` | Optional summary provided upon the agent session's completion. |
| `current_stage` | `string | undefined` | Optional string representation of the agent's current operational stage. |
| `agent_pipeline_stages` | `any[] | undefined` | Optional array representing the full pipeline of stages for the agent. |
| `currentStage` | `PipelineStageDto | undefined` | Optional structured DTO representing the agent's current pipeline stage. |
| `pipelineStages` | `PipelineStageDto[] | undefined` | Optional array of structured DTOs representing all pipeline stages for the agent. |

### Outputs

- **Returns:** `AgentSessionResponseDto` - An instance of `AgentSessionResponseDto` representing the structured data for an agent's session, typically used as a response payload in an API.

### Dependencies

- **class-validator** (external)
- **AgentStatus** (internal)
- **PipelineStageDto** (internal)

---

## `AgentMessageResponseDto` (Class)

**Purpose:** Defines the data structure for responses containing agent message details.

### Detailed Explanation

This `AgentMessageResponseDto` class serves as a Data Transfer Object (DTO), specifying the exact format for data related to agent messages when it's sent from a backend service to a client. DTOs are crucial for maintaining consistent data contracts across different parts of an application, ensuring that both the sender and receiver understand the data's structure. Each property within this class represents a specific attribute of an agent message: `id` for unique identification, `role` to denote the sender (e.g., 'user', 'agent'), `content` for the message body, `timestamp` for when it occurred, and `type` for its category. The `stage` property is optional and can be used to indicate the current phase of a multi-step agent process. The decorators like `@IsNumber()` and `@IsString()` are typically from a validation library (e.g., `class-validator`), enforcing type constraints and ensuring data integrity upon creation or transmission of this DTO.

### Visual Representation

```mermaid
classDiagram
    class AgentMessageResponseDto {
        +number id
        +string role
        +string content
        +string timestamp
        +string type
        +string stage$
    }
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `id` | `number` | Unique identifier for the agent message. |
| `role` | `string` | The role of the entity sending the message (e.g., 'user', 'agent'). |
| `content` | `string` | The textual content of the agent message. |
| `timestamp` | `string` | The timestamp when the message was created or sent, typically in ISO 8601 format. |
| `type` | `string` | The category or type of the message (e.g., 'text', 'action'). |
| `stage` | `string` | An optional field indicating the current stage of an agent's operation or interaction. |

### Outputs

- **Returns:** `AgentMessageResponseDto` - An object conforming to the AgentMessageResponseDto structure, representing a single agent message response.

### Dependencies

- **IsNumber** (external)
- **IsString** (external)
- **IsOptional** (external)

---

