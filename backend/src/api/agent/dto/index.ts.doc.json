{"path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/agent/dto/index.ts", "contentHash": "dca3b47554fc1dff0bff8389de97e51a9fcc2a3629eaf7b17d91df12cbeb394f", "commit": "bc3df31a2ad7cf06456e71484539b06ca55f315c", "timestamp": "2025-07-15T18:27:27+05:30", "units": [{"unitName": "StartAgentSessionDto", "unitType": "class", "purpose": "Defines the data structure required to initiate a new agent session.", "humanReadableExplanation": "This code defines a Data Transfer Object (DTO) named `StartAgentSessionDto`. DTOs are simple objects used to transfer data between different layers or processes of an application. In this case, `StartAgentAgentSessionDto` specifies the exact data expected when a request is made to start an agent session. It includes three properties:\n- `userId`: A mandatory number representing the ID of the user initiating the session.\n- `taskDescription`: A mandatory string detailing the task the agent needs to perform.\n- `projectId`: An optional number representing the ID of the project associated with the session. Its optional nature is indicated by the `?` and the `@IsOptional()` decorator. \n\nThe `@IsNumber()` and `@IsString()` decorators are likely from a validation library (e.g., `class-validator`) and ensure that the incoming data conforms to the specified types, providing robust input validation for API endpoints that consume this DTO.", "dependencies": [{"type": "external", "name": "IsNumber"}, {"type": "external", "name": "IsString"}, {"type": "external", "name": "IsOptional"}], "inputs": [{"name": "userId", "type": "number", "description": "The unique identifier of the user starting the agent session. This field is mandatory."}, {"name": "taskDescription", "type": "string", "description": "A detailed description of the task the agent needs to perform. This field is mandatory."}, {"name": "projectId", "type": "number", "description": "The unique identifier of the project associated with this agent session. This field is optional."}], "outputs": {"type": "StartAgentSessionDto", "description": "An instance of the StartAgentSessionDto class, populated with the validated input data. This object is typically used by controllers or services to process the request.", "throws": ["ValidationErrors (if input data does not conform to specified types or constraints, typically handled by a validation pipeline)"]}, "visualDiagram": "```mermaid\nclassDiagram\n    class StartAgentSessionDto {\n        +number userId\n        +string taskDescription\n        +number? projectId\n    }\n```"}, {"unitName": "FileChangeDto", "unitType": "class", "purpose": "Represents a data transfer object for describing a single change operation on a file.", "humanReadableExplanation": "The `FileChangeDto` is a Data Transfer Object (DTO) designed to encapsulate the details of a modification made to a file. It is typically used in API requests or responses to convey structured information about file changes, such as those occurring in a code editor or a version control system. The DTO ensures that any data representing a file change adheres to a strict structure and type constraints, which is crucial for reliable communication and data integrity.\n\nIt comprises three essential properties:\n- `type`: A string indicating the nature of the change. It is strictly validated to be one of 'insert', 'delete', or 'replace', ensuring that only predefined change operations are communicated.\n- `line`: A number specifying the line number in the file where the change occurs. This provides the precise location of the modification.\n- `content`: A string holding the actual content involved in the change. For 'insert' or 'replace' types, this would be the new text. For 'delete', it might represent the content that was removed, depending on the specific implementation context.\n\nThe `@IsEnum`, `@IsNumber`, and `@IsString` decorators are used for validation, typically provided by a library like `class-validator`, to enforce these type and value constraints when an instance of this DTO is received or created.", "dependencies": [{"type": "external", "name": "IsEnum"}, {"type": "external", "name": "IsNumber"}, {"type": "external", "name": "IsString"}], "inputs": [{"name": "type", "type": "'insert' | 'delete' | 'replace'", "description": "The type of file change operation (insert, delete, or replace)."}, {"name": "line", "type": "number", "description": "The line number in the file where the change occurs."}, {"name": "content", "type": "string", "description": "The content involved in the file change (e.g., new text for insert/replace)."}], "outputs": {"type": "FileChangeDto", "description": "An object representing a single file modification operation with its type, line number, and associated content.", "throws": []}, "visualDiagram": "classDiagram\n    class FileChangeDto {\n        <<DTO>>\n        +string type\n        +number line\n        +string content\n    }\n    note for FileChangeDto::type \"Must be 'insert', 'delete', or 'replace'\""}, {"unitName": "FileEditActionDto", "unitType": "class", "purpose": "This DTO defines the structure for representing a file editing action, including the file path and a list of specific changes.", "humanReadableExplanation": "The `FileEditActionDto` is a Data Transfer Object (DTO) designed to encapsulate all necessary information for a single file editing operation. It contains two key properties:\n\n1.  `path`: A string that specifies the absolute or relative path to the file that is being edited. The `@IsString()` decorator from `class-validator` ensures that any incoming data for this property must be a string, enforcing type integrity.\n\n2.  `changes`: An array of `FileChangeDto` objects. Each `FileChangeDto` object is expected to describe a specific modification within the file (e.g., an insertion, deletion, or replacement at a certain line/column). The `@IsArray()` decorator confirms that this property must be an array. Crucially, `@ValidateNested({ each: true })` instructs the validation system to apply the validation rules defined within each `FileChangeDto` object to every element in this array. Furthermore, `@Type(() => FileChangeDto)` from `class-transformer` is used to ensure that when data is transformed (e.g., from a plain JavaScript object or JSON string), each element in the `changes` array is correctly instantiated as an instance of `FileChangeDto`, enabling proper validation and type-checking for nested objects.\n\nThis DTO is typically used in API endpoints to receive structured data from clients when they want to perform a file modification, ensuring that the incoming data adheres to a predefined format and can be reliably processed by the backend.", "dependencies": [{"type": "external", "name": "IsString"}, {"type": "external", "name": "IsArray"}, {"type": "external", "name": "ValidateNested"}, {"type": "external", "name": "Type"}, {"type": "internal", "name": "FileChangeDto"}], "inputs": [{"name": "path", "type": "string", "description": "The path to the file being edited."}, {"name": "changes", "type": "FileChangeDto[]", "description": "An array of individual changes to be applied to the file."}], "outputs": {"type": "FileEditActionDto", "description": "An instance of FileEditActionDto representing a structured file edit action, ready for processing.", "throws": []}, "visualDiagram": "```mermaid\nclassDiagram\n    class FileEditActionDto {\n        +string path\n        +FileChangeDto[] changes\n    }\n    class FileChangeDto {\n        <<DTO>>\n        // ... properties defining a single file change\n    }\n    FileEditActionDto \"1\" *-- \"0..*\" FileChangeDto : contains\n```"}, {"unitName": "PipelineStageDto", "unitType": "class", "purpose": "Defines the data structure for a single stage within a data processing pipeline.", "humanReadableExplanation": "The `PipelineStageDto` class serves as a Data Transfer Object (DTO) for representing a stage in a pipeline. DTOs are used to define the shape of data that is transferred between different layers of an application, typically between the client and the server, or between service layers. In this case, it ensures that any data representing a pipeline stage adheres to a specific structure, containing an `id`, `name`, and `description`, all of which are expected to be strings. The `@IsString()` decorator, likely from a validation library like `class-validator`, enforces that these properties must be strings when an instance of this DTO is validated, ensuring data integrity.", "dependencies": [{"type": "external", "name": "IsString (from class-validator)"}], "inputs": [{"name": "id", "type": "string", "description": "A unique identifier for the pipeline stage."}, {"name": "name", "type": "string", "description": "The human-readable name of the pipeline stage."}, {"name": "description", "type": "string", "description": "A brief explanation or description of what the pipeline stage does."}], "outputs": {"type": "PipelineStageDto", "description": "An object conforming to the defined structure for a pipeline stage, typically used for API request bodies or response payloads.", "throws": []}, "visualDiagram": "classDiagram\n    class PipelineStageDto {\n        +string id\n        +string name\n        +string description\n    }"}, {"unitName": "CreateCustomPipelineDto", "unitType": "class", "purpose": "This DTO defines the structure for data required to create a custom pipeline.", "humanReadableExplanation": "The `CreateCustomPipelineDto` class serves as a Data Transfer Object (DTO) used for validating and structuring the input data when a request is made to create a custom pipeline. It ensures that incoming data conforms to a predefined shape before being processed by the backend. The class has two key properties:\n\n1.  `sessionId`: This property is decorated with `@IsNumber()`, indicating that its value must be a number. It represents the unique identifier for the session to which the custom pipeline will be associated.\n2.  `stages`: This property is an array of `PipelineStageDto` objects. It's decorated with `@IsArray()` to ensure it's an array, `@ValidateNested({ each: true })` to recursively validate each item within the array against the `PipelineStageDto` schema, and `@Type(() => PipelineStageDto)` from `class-transformer` to enable proper object instantiation and transformation of plain objects into `PipelineStageDto` instances. This property defines the sequence and details of each stage within the custom pipeline.\n\nThis DTO is crucial for maintaining data integrity and providing clear API contracts.", "dependencies": [{"type": "external", "name": "IsNumber"}, {"type": "external", "name": "IsArray"}, {"type": "external", "name": "ValidateNested"}, {"type": "external", "name": "Type"}, {"type": "internal", "name": "PipelineStageDto"}], "inputs": [{"name": "sessionId", "type": "number", "description": "The unique identifier of the session for which the custom pipeline is being created."}, {"name": "stages", "type": "PipelineStageDto[]", "description": "An array of pipeline stages, each conforming to the PipelineStageDto structure, defining the custom pipeline's sequence."}], "outputs": {"type": "CreateCustomPipelineDto", "description": "An instance of CreateCustomPipelineDto, representing a validated data structure for creating a custom pipeline.", "throws": []}, "visualDiagram": "classDiagram\n  class CreateCustomPipelineDto {\n    +number sessionId\n    +PipelineStageDto[] stages\n  }\n  CreateCustomPipelineDto \"1\" -- \"*\" PipelineStageDto : contains"}, {"unitName": "AgentSessionResponseDto", "unitType": "class", "purpose": "Defines the data structure for responses related to an agent's session, including its status, progress, and associated details.", "humanReadableExplanation": "This class, `AgentSessionResponseDto`, serves as a Data Transfer Object (DTO) in the backend system. Its primary role is to define the precise structure of data that is sent as a response when information about an agent's session is requested or provided. Each property within the class represents a specific attribute of an agent session, such as its unique identifier (`id`), the user it belongs to (`user_id`), its purpose (`title`, `key_objective`), and its current operational state (`agent_status`, `agent_iteration_count`). It also includes details about the agent's actions (`agent_allowed_actions`, `agent_commands_executed`) and its progression through various stages (`current_stage`, `agent_pipeline_stages`, `currentStage`, `pipelineStages`). The decorators like `@IsNumber()`, `@IsString()`, `@IsEnum()`, `@IsArray()`, and `@IsOptional()` are from a validation library (likely `class-validator`), ensuring that the data conforms to the expected types and constraints when an instance of this DTO is created or validated. Properties marked with `?` and `@IsOptional()` are not mandatory.", "dependencies": [{"type": "external", "name": "class-validator"}, {"type": "internal", "name": "AgentStatus"}, {"type": "internal", "name": "PipelineStageDto"}], "inputs": [{"name": "id", "type": "number", "description": "Unique identifier for the agent session."}, {"name": "user_id", "type": "number", "description": "ID of the user associated with the session."}, {"name": "title", "type": "string", "description": "Title or name of the agent session."}, {"name": "key_objective", "type": "string", "description": "The primary objective or goal of the agent session."}, {"name": "project_id", "type": "number | undefined", "description": "Optional ID of the project related to this agent session."}, {"name": "agent_status", "type": "AgentStatus", "description": "Current operational status of the agent (e.g., running, completed, failed)."}, {"name": "agent_iteration_count", "type": "number", "description": "Number of iterations or steps the agent has completed."}, {"name": "agent_allowed_actions", "type": "string[]", "description": "An array of strings representing actions the agent is permitted to perform."}, {"name": "agent_commands_executed", "type": "any[]", "description": "An array of commands that the agent has executed."}, {"name": "agent_completion_summary", "type": "string | undefined", "description": "Optional summary provided upon the agent session's completion."}, {"name": "current_stage", "type": "string | undefined", "description": "Optional string representation of the agent's current operational stage."}, {"name": "agent_pipeline_stages", "type": "any[] | undefined", "description": "Optional array representing the full pipeline of stages for the agent."}, {"name": "currentStage", "type": "PipelineStageDto | undefined", "description": "Optional structured DTO representing the agent's current pipeline stage."}, {"name": "pipelineStages", "type": "PipelineStageDto[] | undefined", "description": "Optional array of structured DTOs representing all pipeline stages for the agent."}], "outputs": {"type": "AgentSessionResponseDto", "description": "An instance of `AgentSessionResponseDto` representing the structured data for an agent's session, typically used as a response payload in an API.", "throws": []}, "visualDiagram": "classDiagram\n    class AgentSessionResponseDto {\n        +number id\n        +number user_id\n        +string title\n        +string key_objective\n        +number? project_id\n        +AgentStatus agent_status\n        +number agent_iteration_count\n        +string[] agent_allowed_actions\n        +any[] agent_commands_executed\n        +string? agent_completion_summary\n        +string? current_stage\n        +any[]? agent_pipeline_stages\n        +PipelineStageDto? currentStage\n        +PipelineStageDto[]? pipelineStages\n    }\n\n    AgentSessionResponseDto --o AgentStatus : uses\n    AgentSessionResponseDto --o PipelineStageDto : contains"}, {"unitName": "AgentMessageResponseDto", "unitType": "class", "purpose": "Defines the data structure for responses containing agent message details.", "humanReadableExplanation": "This `AgentMessageResponseDto` class serves as a Data Transfer Object (DTO), specifying the exact format for data related to agent messages when it's sent from a backend service to a client. DTOs are crucial for maintaining consistent data contracts across different parts of an application, ensuring that both the sender and receiver understand the data's structure. Each property within this class represents a specific attribute of an agent message: `id` for unique identification, `role` to denote the sender (e.g., 'user', 'agent'), `content` for the message body, `timestamp` for when it occurred, and `type` for its category. The `stage` property is optional and can be used to indicate the current phase of a multi-step agent process. The decorators like `@IsNumber()` and `@IsString()` are typically from a validation library (e.g., `class-validator`), enforcing type constraints and ensuring data integrity upon creation or transmission of this DTO.", "dependencies": [{"type": "external", "name": "IsNumber"}, {"type": "external", "name": "IsString"}, {"type": "external", "name": "IsOptional"}], "inputs": [{"name": "id", "type": "number", "description": "Unique identifier for the agent message."}, {"name": "role", "type": "string", "description": "The role of the entity sending the message (e.g., 'user', 'agent')."}, {"name": "content", "type": "string", "description": "The textual content of the agent message."}, {"name": "timestamp", "type": "string", "description": "The timestamp when the message was created or sent, typically in ISO 8601 format."}, {"name": "type", "type": "string", "description": "The category or type of the message (e.g., 'text', 'action')."}, {"name": "stage", "type": "string", "description": "An optional field indicating the current stage of an agent's operation or interaction."}], "outputs": {"type": "AgentMessageResponseDto", "description": "An object conforming to the AgentMessageResponseDto structure, representing a single agent message response.", "throws": []}, "visualDiagram": "classDiagram\n    class AgentMessageResponseDto {\n        +number id\n        +string role\n        +string content\n        +string timestamp\n        +string type\n        +string stage$\n    }"}]}