import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsInt,
  IsEnum,
  IsObject,
  IsArray,
  IsNumber,
  Min,
  Max,
  IsBoolean,
} from 'class-validator';
import { ConversationCategory, AgentStatus, LastActivityType } from '../../../generated/prisma';

export class CreateMessageDto {
  @IsNotEmpty()
  @IsString()
  role: string = '';

  @IsNotEmpty()
  @IsString()
  content: string = '';
}

export class CreateConversationDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  initialMessage?: string;

  @IsOptional()
  @IsString()
  conversationType?: string = 'chat';

  @IsOptional()
  @IsInt()
  projectId?: number;

  @IsOptional()
  @IsString()
  keyObjective?: string;

  @IsOptional()
  @IsEnum(ConversationCategory)
  category?: ConversationCategory;

  @IsOptional()
  @IsEnum(AgentStatus)
  agentStatus?: AgentStatus;

  @IsOptional()
  @IsObject()
  agentAllowedActions?: Record<string, any>;

  @IsOptional()
  @IsInt()
  parentId?: number;
}

export class UpdateConversationDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsInt()
  projectId?: number;

  @IsOptional()
  @IsString()
  keyObjective?: string;

  @IsOptional()
  @IsEnum(ConversationCategory)
  category?: ConversationCategory;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  userRating?: number;

  @IsOptional()
  @IsString()
  userFeedback?: string;

  @IsOptional()
  @IsBoolean()
  isPinned?: boolean;

  @IsOptional()
  @IsEnum(AgentStatus)
  agentStatus?: AgentStatus;

  @IsOptional()
  @IsObject()
  agentProgress?: Record<string, any>;

  @IsOptional()
  @IsInt()
  agentIterationCount?: number;

  @IsOptional()
  @IsObject()
  agentAllowedActions?: Record<string, any>;

  @IsOptional()
  @IsArray()
  agentCommandsExecuted?: Array<Record<string, any>>;

  @IsOptional()
  @IsString()
  agentCompletionSummary?: string;

  @IsOptional()
  @IsEnum(LastActivityType)
  lastActivityType?: LastActivityType;
}

export class ChatRequestDto {
  @IsNotEmpty()
  @IsArray()
  messages: CreateMessageDto[] = [];

  @IsOptional()
  @IsInt()
  conversationId?: number;

  @IsOptional()
  @IsString()
  model?: string;

  @IsNotEmpty()
  @IsString()
  taskType: string = 'general';

  @IsOptional()
  @IsInt()
  maxTokens?: number = 1000;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  temperature?: number = 0.7;
}
