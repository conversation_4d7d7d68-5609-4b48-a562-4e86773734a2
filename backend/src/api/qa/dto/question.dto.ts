import {
  IsString,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsBoolean,
  IsInt,
  IsArray,
  Min,
  <PERSON>,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Create question DTO
 */
export class CreateQuestionDto {
  @IsNotEmpty()
  @IsString()
  content!: string;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * Update question DTO
 */
export class UpdateQuestionDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  codeSnippet?: string;

  @IsOptional()
  @IsBoolean()
  isResolved?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * Question vote DTO
 */
export class QuestionVoteDto {
  @IsNotEmpty()
  @IsInt()
  @Min(-1)
  @Max(1)
  value!: number;
}

/**
 * Question filter DTO
 */
export class QuestionFilterDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  skip?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  take?: number;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  resolved?: boolean;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  tagIds?: number[];

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  authorId?: number;
}
