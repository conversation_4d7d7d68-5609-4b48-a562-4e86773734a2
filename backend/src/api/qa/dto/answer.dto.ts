import { IsString, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Create answer DTO
 */
export class CreateAnswerDto {
  @IsNotEmpty()
  @IsString()
  content: string = '';

  @IsNotEmpty()
  @IsInt()
  questionId: number = 0;

  @IsOptional()
  @IsString()
  codeSnippet?: string;
}

/**
 * Update answer DTO
 */
export class UpdateAnswerDto {
  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsString()
  codeSnippet?: string;
}

/**
 * Answer vote DTO
 */
export class AnswerVoteDto {
  @IsNotEmpty()
  @IsInt()
  @Min(-1)
  @Max(1)
  value: number = 0;
}

/**
 * Answer filter DTO
 */
export class AnswerFilterDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  skip?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  take?: number = 20;
}
