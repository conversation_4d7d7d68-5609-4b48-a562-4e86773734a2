{"path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/qa/controllers/index.ts", "contentHash": "5cc85445ba2d176482a8e50bf413236e8652f1c11fada090bda6e00c54ee6522", "commit": "bc3df31a2ad7cf06456e71484539b06ca55f315c", "timestamp": "2025-07-15T18:27:27+05:30", "units": [{"unitName": "QAController", "unitType": "class", "purpose": "This class provides a comprehensive set of API endpoints for managing questions, answers, and tags within a Question & Answer (QA) system.", "humanReadableExplanation": "The `QAController` class serves as the primary interface for all Question and Answer related operations in the backend. Each asynchronous method within this class corresponds to a specific API route, handling the lifecycle of questions and answers from creation to deletion, including features like pagination for questions, voting mechanisms, and tag management. It interacts with an underlying `qaService` (presumably a service layer) to perform business logic and data persistence. \n\nCommon patterns observed across methods include:\n1.  **Parameter Parsing**: Extracting IDs from URL parameters (`req.params.id`, `req.params.questionId`) and pagination details from query parameters (`req.query.page`, `req.query.pageSize`).\n2.  **Request Body Handling**: Casting `req.body` to specific Data Transfer Objects (DTOs) like `CreateQuestionDto` or `UpdateAnswerDto` for structured input.\n3.  **User Authentication**: Utilizing a private `getUserId` method to extract the authenticated user's ID from the request, ensuring actions are performed with proper user context and authorization.\n4.  **Service Layer Interaction**: Delegating the core business logic to `qaService` methods (e.g., `qaService.createQuestion`, `qaService.getAnswers`).\n5.  **Response Formatting**: Sending appropriate HTTP status codes (e.g., 200 OK, 201 Created, 204 No Content) and JSON payloads containing the requested data or confirmation messages.\n6.  **Error Handling**: Implementing a consistent `try-catch` block for each method. In case of an error, it logs the error using `logger`, converts the error to a standard `Error` object if necessary, and returns a 500 Internal Server Error response with a generic message and error details. Specific 404 Not Found responses are returned when resources are not found or permissions are insufficient.", "dependencies": [{"type": "external", "name": "Request"}, {"type": "external", "name": "Response"}, {"type": "internal", "name": "qaService"}, {"type": "internal", "name": "logger"}, {"type": "internal", "name": "CreateQuestionDto"}, {"type": "internal", "name": "UpdateQuestionDto"}, {"type": "internal", "name": "CreateAnswerDto"}, {"type": "internal", "name": "UpdateAnswerDto"}, {"type": "internal", "name": "QuestionVoteDto"}, {"type": "internal", "name": "AnswerVoteDto"}], "inputs": [{"name": "req", "type": "Request", "description": "The incoming HTTP request object, containing path parameters (e.g., 'id', 'questionId'), query parameters (e.g., 'page', 'pageSize'), and request body data (e.g., CreateQuestionDto, UpdateQuestionDto, vote DTOs)."}, {"name": "res", "type": "Response", "description": "The HTTP response object used to send back data to the client."}], "outputs": {"type": "Promise<Response>", "description": "An HTTP response object, typically containing JSON data representing questions, answers, tags, or status messages, along with an appropriate HTTP status code (e.g., 200 OK, 201 Created, 204 No Content, 404 Not Found, 500 Internal Server Error).", "throws": ["HTTP 404 Not Found (resource not found or permission denied)", "HTTP 500 Internal Server Error (server-side error)", "Error('User not authenticated') (internal, if user ID is missing from request)"]}, "visualDiagram": "classDiagram\n    class QAController {\n        +getQuestions(req: Request, res: Response): Promise<Response>\n        +createQuestion(req: Request, res: Response): Promise<Response>\n        +getQuestionById(req: Request, res: Response): Promise<Response>\n        +updateQuestion(req: Request, res: Response): Promise<Response>\n        +deleteQuestion(req: Request, res: Response): Promise<Response>\n        +getAnswers(req: Request, res: Response): Promise<Response>\n        +createAnswer(req: Request, res: Response): Promise<Response>\n        +updateAnswer(req: Request, res: Response): Promise<Response>\n        +deleteAnswer(req: Request, res: Response): Promise<Response>\n        +acceptAnswer(req: Request, res: Response): Promise<Response>\n        +voteQuestion(req: Request, res: Response): Promise<Response>\n        +voteAnswer(req: Request, res: Response): Promise<Response>\n        +getTags(req: Request, res: Response): Promise<Response>\n        +getPopularTags(req: Request, res: Response): Promise<Response>\n        -getUserId(req: Request): number\n    }\n    Request --|> QAController : uses\n    Response --|> QAController : uses\n    QAController ..> qaService : interacts with\n    QAController ..> logger : logs errors\n    QAController ..> CreateQuestionDto\n    QAController ..> UpdateQuestionDto\n    QAController ..> CreateAnswerDto\n    QAController ..> UpdateAnswerDto\n    QAController ..> QuestionVoteDto\n    QAController ..> AnswerVoteDto"}]}