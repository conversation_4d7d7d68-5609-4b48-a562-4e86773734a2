/**
 * QA controller for handling QA-related API requests
 */
import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { EnhancedQAService } from '../../../services/qa';
import { logger } from '../../../common/logger';
import {
  CreateQuestionDto,
  UpdateQuestionDto,
  QuestionVoteDto,
  QuestionFilterDto,
  CreateAnswerDto,
  UpdateAnswerDto,
  AnswerVoteDto,
  AnswerFilterDto,
  CreateTagDto,
  UpdateTagDto,
  TagFilterDto,
} from '../dto';
import { AuthenticatedRequest } from '../../../common/types';

/**
 * Controller for QA operations
 */
@injectable()
export class QAController {
  constructor(@inject(TYPES.EnhancedQAService) private qaService: EnhancedQAService) {}

  /**
   * Helper to get userId from an authenticated request
   */
  private getUserId(req: Request): number {
    const authReq = req as AuthenticatedRequest;
    if (authReq.userId === undefined) {
      throw new Error('User ID is undefined');
    }
    return authReq.userId;
  }

  /**
   * Get questions with optional filtering
   */
  async getQuestions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as QuestionFilterDto;
      const questions = await this.qaService.getQuestions({
        skip: filter.skip,
        take: filter.take,
        resolved: filter.resolved,
        authorId: filter.authorId,
        tagIds: filter.tagIds,
      });

      res.status(200).json(questions);
    } catch (error) {
      logger.error('Error getting questions:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get a question by ID
   */
  async getQuestionById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.id, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const question = await this.qaService.getQuestionById(questionId, this.getUserId(req));
      if (!question) {
        res.status(404).json({ error: 'Question not found' });
        return;
      }

      res.status(200).json(question);
    } catch (error) {
      logger.error('Error getting question by ID:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new question
   */
  async createQuestion(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionData: CreateQuestionDto = req.body;
      const result = await this.qaService.createQuestion({
        content: questionData.content,
        authorId: this.getUserId(req),
        codeSnippet: questionData.codeSnippet,
        tags: questionData.tags,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating question:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update a question
   */
  async updateQuestion(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.id, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const questionData: UpdateQuestionDto = req.body;
      const result = await this.qaService.updateQuestion(questionId, this.getUserId(req), {
        content: questionData.content,
        codeSnippet: questionData.codeSnippet,
        isResolved: questionData.isResolved,
        tags: questionData.tags,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating question:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete a question
   */
  async deleteQuestion(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.id, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const result = await this.qaService.deleteQuestion(questionId, this.getUserId(req));
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting question:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Vote on a question
   */
  async voteQuestion(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.id, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const voteData: QuestionVoteDto = req.body;
      const result = await this.qaService.voteQuestion(
        questionId,
        this.getUserId(req),
        voteData.value,
      );
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error voting on question:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get answers for a question
   */
  async getAnswers(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.questionId, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const filter = req.query as unknown as AnswerFilterDto;
      const result = await this.qaService.getAnswersByQuestionId(
        questionId,
        { skip: filter.skip, take: filter.take },
        this.getUserId(req),
      );

      if (Array.isArray(result)) {
        res.status(200).json(result);
      } else {
        res.status(result.statusCode).json({ error: result.detail });
      }
    } catch (error) {
      logger.error('Error getting answers:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new answer
   */
  async createAnswer(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const questionId = parseInt(req.params.questionId, 10);
      if (isNaN(questionId)) {
        res.status(400).json({ error: 'Invalid question ID' });
        return;
      }

      const answerData: CreateAnswerDto = req.body;
      const result = await this.qaService.createAnswer({
        content: answerData.content,
        questionId: questionId,
        authorId: this.getUserId(req),
        codeSnippet: answerData.codeSnippet,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating answer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update an answer
   */
  async updateAnswer(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const answerId = parseInt(req.params.id, 10);
      if (isNaN(answerId)) {
        res.status(400).json({ error: 'Invalid answer ID' });
        return;
      }

      const answerData: UpdateAnswerDto = req.body;
      const result = await this.qaService.updateAnswer(answerId, this.getUserId(req), {
        content: answerData.content,
        codeSnippet: answerData.codeSnippet,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating answer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete an answer
   */
  async deleteAnswer(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const answerId = parseInt(req.params.id, 10);
      if (isNaN(answerId)) {
        res.status(400).json({ error: 'Invalid answer ID' });
        return;
      }

      const result = await this.qaService.deleteAnswer(answerId, this.getUserId(req));
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting answer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Accept an answer
   */
  async acceptAnswer(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const answerId = parseInt(req.params.id, 10);
      if (isNaN(answerId)) {
        res.status(400).json({ error: 'Invalid answer ID' });
        return;
      }

      const result = await this.qaService.acceptAnswer(answerId, this.getUserId(req));
      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error accepting answer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Vote on an answer
   */
  async voteAnswer(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const answerId = parseInt(req.params.id, 10);
      if (isNaN(answerId)) {
        res.status(400).json({ error: 'Invalid answer ID' });
        return;
      }

      const voteData: AnswerVoteDto = req.body;
      const result = await this.qaService.voteAnswer(answerId, this.getUserId(req), voteData.value);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error voting on answer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get tags
   */
  async getTags(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as TagFilterDto;
      const tags = await this.qaService.getTags({
        skip: filter.skip,
        take: filter.take,
      });

      res.status(200).json(tags);
    } catch (error) {
      logger.error('Error getting tags:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get popular tags
   */
  async getPopularTags(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      const tags = await this.qaService.getPopularTags(limit);

      res.status(200).json(tags);
    } catch (error) {
      logger.error('Error getting popular tags:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
