/**
 * QA controllers module exports
 */

import { Request, Response } from 'express';
import { qaService } from '../services/qa.service';
import { logger } from '../../../common/logger';
import {
  CreateQuestionDto,
  UpdateQuestionDto,
  QuestionVoteDto,
  CreateAnswerDto,
  UpdateAnswerDto,
  AnswerVoteDto,
  CreateTagDto,
  UpdateTagDto,
} from '../dto';

/**
 * Controller for QA-related endpoints
 */
export class QAController {
  /**
   * Get all questions with pagination
   */
  async getQuestions(req: Request, res: Response): Promise<Response> {
    try {
      const pageParam = req.query.page;
      const pageSizeParam = req.query.pageSize;
      const page = pageParam && typeof pageParam === 'string' ? parseInt(pageParam, 10) : 1;
      const pageSize =
        pageSizeParam && typeof pageSizeParam === 'string' ? parseInt(pageSizeParam, 10) : 10;
      const result = await qaService.getQuestions(page, pageSize);
      return res.json(result);
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error listing questions:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve questions',
        details: err.message,
      });
    }
  }

  /**
   * Create a new question
   */
  async createQuestion(req: Request, res: Response): Promise<Response> {
    try {
      const userId = this.getUserId(req);
      const questionDto = req.body as CreateQuestionDto;
      const question = await qaService.createQuestion(questionDto, userId);
      return res.status(201).json({ question });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error creating question:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to create question',
        details: err.message,
      });
    }
  }

  /**
   * Get a question by ID
   */
  async getQuestionById(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.id, 10);
      const question = await qaService.getQuestionById(questionId);
      if (!question) {
        return res.status(404).json({
          status: 'error',
          message: `Question with ID ${questionId} not found`,
        });
      }
      return res.json({ question });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error getting question ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve question',
        details: err.message,
      });
    }
  }

  /**
   * Update a question
   */
  async updateQuestion(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const questionDto = req.body as UpdateQuestionDto;
      const question = await qaService.updateQuestion(questionId, questionDto, userId);
      if (!question) {
        return res.status(404).json({
          status: 'error',
          message: `Question with ID ${questionId} not found or you don't have permission to update it`,
        });
      }
      return res.json({ question });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error updating question ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to update question',
        details: err.message,
      });
    }
  }

  /**
   * Delete a question
   */
  async deleteQuestion(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const success = await qaService.deleteQuestion(questionId, userId);
      if (!success) {
        return res.status(404).json({
          status: 'error',
          message: `Question with ID ${questionId} not found or you don't have permission to delete it`,
        });
      }
      return res.status(204).end();
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error deleting question ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to delete question',
        details: err.message,
      });
    }
  }

  /**
   * Get answers for a question
   */
  async getAnswers(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.questionId, 10);
      const answers = await qaService.getAnswers(questionId);
      return res.json({ answers });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error getting answers for question ${req.params.questionId}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve answers',
        details: err.message,
      });
    }
  }

  /**
   * Create an answer
   */
  async createAnswer(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.questionId, 10);
      const userId = this.getUserId(req);
      const answerDto = req.body as CreateAnswerDto;
      const answer = await qaService.createAnswer(questionId, answerDto, userId);
      return res.status(201).json({ answer });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error creating answer:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to create answer',
        details: err.message,
      });
    }
  }

  /**
   * Update an answer
   */
  async updateAnswer(req: Request, res: Response): Promise<Response> {
    try {
      const answerId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const answerDto = req.body as UpdateAnswerDto;
      const answer = await qaService.updateAnswer(answerId, answerDto, userId);
      if (!answer) {
        return res.status(404).json({
          status: 'error',
          message: `Answer with ID ${answerId} not found or you don't have permission to update it`,
        });
      }
      return res.json({ answer });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error updating answer ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to update answer',
        details: err.message,
      });
    }
  }

  /**
   * Delete an answer
   */
  async deleteAnswer(req: Request, res: Response): Promise<Response> {
    try {
      const answerId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const success = await qaService.deleteAnswer(answerId, userId);
      if (!success) {
        return res.status(404).json({
          status: 'error',
          message: `Answer with ID ${answerId} not found or you don't have permission to delete it`,
        });
      }
      return res.status(204).end();
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error deleting answer ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to delete answer',
        details: err.message,
      });
    }
  }

  /**
   * Accept an answer
   */
  async acceptAnswer(req: Request, res: Response): Promise<Response> {
    try {
      const answerId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const answer = await qaService.acceptAnswer(answerId, userId);
      if (!answer) {
        return res.status(404).json({
          status: 'error',
          message: `Answer with ID ${answerId} not found or you don't have permission to accept it`,
        });
      }
      return res.json({ answer });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error accepting answer ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to accept answer',
        details: err.message,
      });
    }
  }

  /**
   * Vote on a question
   */
  async voteQuestion(req: Request, res: Response): Promise<Response> {
    try {
      const questionId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const voteDto = req.body as QuestionVoteDto;
      const question = await qaService.voteQuestion(questionId, voteDto, userId);
      if (!question) {
        return res.status(404).json({
          status: 'error',
          message: `Question with ID ${questionId} not found`,
        });
      }
      return res.json({ question });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error voting on question ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to vote on question',
        details: err.message,
      });
    }
  }

  /**
   * Vote on an answer
   */
  async voteAnswer(req: Request, res: Response): Promise<Response> {
    try {
      const answerId = parseInt(req.params.id, 10);
      const userId = this.getUserId(req);
      const voteDto = req.body as AnswerVoteDto;
      const answer = await qaService.voteAnswer(answerId, voteDto, userId);
      if (!answer) {
        return res.status(404).json({
          status: 'error',
          message: `Answer with ID ${answerId} not found`,
        });
      }
      return res.json({ answer });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Error voting on answer ${req.params.id}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to vote on answer',
        details: err.message,
      });
    }
  }

  /**
   * Get all tags
   */
  async getTags(req: Request, res: Response): Promise<Response> {
    try {
      const tags = await qaService.getTags();
      return res.json({ tags });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error getting tags:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve tags',
        details: err.message,
      });
    }
  }

  /**
   * Get popular tags
   */
  async getPopularTags(req: Request, res: Response): Promise<Response> {
    try {
      const tags = await qaService.getPopularTags();
      return res.json({ tags });
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error getting popular tags:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve popular tags',
        details: err.message,
      });
    }
  }

  /**
   * Get user ID from request
   */
  private getUserId(req: Request): number {
    const authenticatedReq = req as any;
    if (!authenticatedReq.user?.id) {
      throw new Error('User not authenticated');
    }
    return authenticatedReq.user.id;
  }
}

// Create a singleton instance
export const qaController = new QAController();
