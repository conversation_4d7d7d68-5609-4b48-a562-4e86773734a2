import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../../../common/types/authenticated-request';
import { blogService } from '../services/blog.service';
import { logger } from '../../../common/logger';
import { CreateBlogPostDto, UpdateBlogPostDto } from '../dto';

/**
 * Controller for blog-related endpoints
 */
export class BlogController {
  /**
   * Get all blog posts with pagination
   */
  async getAllPosts(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string, 10) || 1;
      const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
      const publishedOnly = req.query.publishedOnly !== 'false';

      const result = await blogService.getAllPosts(page, pageSize, publishedOnly);

      res.json(result);
    } catch (error: any) {
      logger.error('Error listing blog posts:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve blog posts',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Get a blog post by ID
   */
  async getPostById(req: Request, res: Response): Promise<void> {
    try {
      const postId = parseInt(req.params.postId, 10);

      const post = await blogService.getPostById(postId);

      if (!post) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with ID ${postId} not found`,
        });
        return;
      }

      res.json({ post });
    } catch (error: any) {
      logger.error(`Error getting blog post ${req.params.postId}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Get a blog post by slug
   */
  async getPostBySlug(req: Request, res: Response): Promise<void> {
    try {
      const { slug } = req.params;

      const post = await blogService.getPostBySlug(slug);

      if (!post) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with slug "${slug}" not found`,
        });
        return;
      }

      res.json({ post });
    } catch (error: any) {
      logger.error(`Error getting blog post by slug ${req.params.slug}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Create a new blog post
   * Uses CreateBlogPostDto for validation
   */
  async createPost(req: Request, res: Response): Promise<void> {
    try {
      const userId = this.getUserId(req);
      const postDto = req.body as CreateBlogPostDto;

      const post = await blogService.createPost(postDto, userId);

      if (!post) {
        res.status(500).json({
          status: 'error',
          message: 'Failed to create blog post',
        });
        return;
      }

      res.status(201).json({ post });
    } catch (error: any) {
      logger.error('Error creating blog post:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to create blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Update an existing blog post
   * Uses UpdateBlogPostDto for validation
   */
  async updatePost(req: Request, res: Response): Promise<void> {
    try {
      const postId = parseInt(req.params.postId, 10);
      const userId = this.getUserId(req);
      const postDto = req.body as UpdateBlogPostDto;

      const post = await blogService.updatePost(postId, postDto, userId);

      if (!post) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with ID ${postId} not found or you don't have permission to update it`,
        });
        return;
      }

      res.json({ post });
    } catch (error: any) {
      logger.error(`Error updating blog post ${req.params.postId}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to update blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Delete a blog post
   */
  async deletePost(req: Request, res: Response): Promise<void> {
    try {
      const postId = parseInt(req.params.postId, 10);
      const userId = this.getUserId(req);

      const success = await blogService.deletePost(postId, userId);

      if (!success) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with ID ${postId} not found or you don't have permission to delete it`,
        });
        return;
      }

      res.status(204).end();
    } catch (error: any) {
      logger.error(`Error deleting blog post ${req.params.postId}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to delete blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Publish a blog post
   */
  async publishPost(req: Request, res: Response): Promise<void> {
    try {
      const postId = parseInt(req.params.postId, 10);
      const userId = this.getUserId(req);

      const post = await blogService.publishPost(postId, userId);

      if (!post) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with ID ${postId} not found or you don't have permission to publish it`,
        });
        return;
      }

      res.json({ post });
    } catch (error: any) {
      logger.error(`Error publishing blog post ${req.params.postId}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to publish blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Unpublish a blog post
   */
  async unpublishPost(req: Request, res: Response): Promise<void> {
    try {
      const postId = parseInt(req.params.postId, 10);
      const userId = this.getUserId(req);

      const post = await blogService.unpublishPost(postId, userId);

      if (!post) {
        res.status(404).json({
          status: 'error',
          message: `Blog post with ID ${postId} not found or you don't have permission to unpublish it`,
        });
        return;
      }

      res.json({ post });
    } catch (error: any) {
      logger.error(`Error unpublishing blog post ${req.params.postId}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to unpublish blog post',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Get blog posts by tag
   */
  async getPostsByTag(req: Request, res: Response): Promise<void> {
    try {
      const { tag } = req.params;
      const page = parseInt(req.query.page as string, 10) || 1;
      const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
      const publishedOnly = req.query.publishedOnly !== 'false';

      const result = await blogService.getPostsByTag(tag, page, pageSize, publishedOnly);

      res.json(result);
    } catch (error: any) {
      logger.error(`Error getting blog posts for tag ${req.params.tag}:`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve blog posts',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Get all available tags
   */
  async getAllTags(req: Request, res: Response): Promise<void> {
    try {
      const tags = await blogService.getAllTags();
      res.json({ tags });
    } catch (error: any) {
      logger.error('Error getting blog tags:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve blog tags',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Helper to get userId from an authenticated request
   */
  private getUserId(req: Request): number {
    const authReq = req as AuthenticatedRequest;
    return authReq.userId || 0;
  }
}

// Export a singleton instance
export const blogController = new BlogController();
export default blogController;
