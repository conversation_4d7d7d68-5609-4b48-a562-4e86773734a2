import { PrismaClient } from '../../../generated/prisma';
import { logger } from '../../../common/logger';
import {
  BlogPostCreate,
  BlogPostUpdate,
  BlogPost,
  BlogPostWithAuthor,
  Tag,
} from '../../../common/types/blog';

/**
 * Repository for blog post operations
 */
export class BlogRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Create a new blog post
   * @param data Blog post data
   * @param authorId Author ID
   */
  async createPost(data: BlogPostCreate, authorId: number): Promise<BlogPost> {
    try {
      const { contentPath, ...restData } = data;
      const result = await this.prisma.blog_posts.create({
        data: {
          ...restData,
          content: contentPath, // Map contentPath to content field in Prisma
          author_id: authorId,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      // Transform the result to match the BlogPost interface
      return {
        id: result.id,
        title: result.title,
        slug: result.slug,
        contentPath: result.content,
        authorId: result.author_id,
        publishedAt: result.published_at || undefined,
        createdAt: result.created_at,
        updatedAt: result.updated_at,
      };
    } catch (error) {
      logger.error('Error creating blog post:', error);
      throw error;
    }
  }

  /**
   * Update an existing blog post
   * @param id Blog post ID
   * @param data Updated blog post data
   */
  async updatePost(id: number, data: BlogPostUpdate): Promise<BlogPost | null> {
    try {
      const updateData: any = { ...data, updated_at: new Date() };

      // Map contentPath to content if provided
      if (data.contentPath !== undefined) {
        updateData.content = data.contentPath;
        delete updateData.contentPath;
      }

      const result = await this.prisma.blog_posts.update({
        where: { id },
        data: updateData,
      });

      // Transform the result to match the BlogPost interface
      return result
        ? {
            id: result.id,
            title: result.title,
            slug: result.slug,
            contentPath: result.content,
            authorId: result.author_id,
            publishedAt: result.published_at || undefined,
            createdAt: result.created_at,
            updatedAt: result.updated_at,
          }
        : null;
    } catch (error) {
      logger.error(`Error updating blog post ${id}:`, error);
      return null;
    }
  }

  /**
   * Get a blog post by ID
   * @param id Blog post ID
   */
  async getPostById(id: number): Promise<BlogPost | null> {
    try {
      const result = await this.prisma.blog_posts.findUnique({
        where: { id },
      });

      // Transform the result to match the BlogPost interface
      return result
        ? {
            id: result.id,
            title: result.title,
            slug: result.slug,
            contentPath: result.content,
            authorId: result.author_id,
            publishedAt: result.published_at || undefined,
            createdAt: result.created_at,
            updatedAt: result.updated_at,
          }
        : null;
    } catch (error) {
      logger.error(`Error getting blog post ${id}:`, error);
      return null;
    }
  }

  /**
   * Get a blog post by slug
   * @param slug Blog post slug
   */
  async getPostBySlug(slug: string): Promise<BlogPost | null> {
    try {
      const result = await this.prisma.blog_posts.findUnique({
        where: { slug },
      });

      // Transform the result to match the BlogPost interface
      return result
        ? {
            id: result.id,
            title: result.title,
            slug: result.slug,
            contentPath: result.content,
            authorId: result.author_id,
            publishedAt: result.published_at || undefined,
            createdAt: result.created_at,
            updatedAt: result.updated_at,
          }
        : null;
    } catch (error) {
      logger.error(`Error getting blog post by slug ${slug}:`, error);
      return null;
    }
  }

  /**
   * Get all blog posts with pagination
   * @param skip Number of records to skip
   * @param take Number of records to take
   * @param publishedOnly Only include published posts
   */
  async getAllPosts(skip = 0, take = 10, publishedOnly = true): Promise<BlogPost[]> {
    try {
      const where = publishedOnly ? { published: true } : {};

      const results = await this.prisma.blog_posts.findMany({
        where,
        skip,
        take,
        orderBy: { published_at: 'desc' },
      });

      // Transform the results to match the BlogPost interface
      return results.map((post) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        contentPath: post.content,
        authorId: post.author_id,
        publishedAt: post.published_at || undefined,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
      }));
    } catch (error) {
      logger.error('Error getting blog posts:', error);
      return [];
    }
  }

  /**
   * Delete a blog post
   * @param id Blog post ID
   */
  async deletePost(id: number): Promise<boolean> {
    try {
      await this.prisma.blog_posts.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      logger.error(`Error deleting blog post ${id}:`, error);
      return false;
    }
  }

  /**
   * Count total blog posts
   * @param publishedOnly Only count published posts
   */
  async countPosts(publishedOnly = true): Promise<number> {
    try {
      const where = publishedOnly ? { published: true } : {};

      return await this.prisma.blog_posts.count({ where });
    } catch (error) {
      logger.error('Error counting blog posts:', error);
      return 0;
    }
  }

  /**
   * Get blog posts by tag with pagination
   * @param tag Tag name
   * @param skip Number of records to skip
   * @param take Number of records to take
   * @param publishedOnly Only include published posts
   */
  async getPostsByTag(
    tag: string,
    skip = 0,
    take = 10,
    publishedOnly = true,
  ): Promise<BlogPostWithAuthor[]> {
    try {
      const posts = await this.prisma.blog_posts.findMany({
        where: {
          blog_post_tags: {
            some: {
              tags: {
                name: tag,
              },
            },
          },
          published: publishedOnly,
        },
        include: {
          blog_post_tags: {
            include: {
              tags: true,
            },
          },
          users: true,
        },
        skip,
        take,
        orderBy: { published_at: 'desc' },
      });

      return posts.map((post) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        contentPath: post.content,
        authorId: post.author_id,
        authorName: post.users
          ? `${post.users.first_name || ''} ${post.users.last_name || ''}`.trim() ||
            post.users.username ||
            'Unknown'
          : 'Unknown',
        authorBio: post.users?.bio || '',
        authorProfileImage: post.users?.profile_image_url || undefined,
        publishedAt: post.published_at || undefined,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
        tags: post.blog_post_tags.map((tag) => ({
          id: tag.tags.id,
          name: tag.tags.name,
          slug: tag.tags.name.toLowerCase().replace(/\s+/g, '-'),
          createdAt: new Date(),
        })),
      }));
    } catch (error) {
      logger.error(`Error getting blog posts for tag ${tag}:`, error);
      return [];
    }
  }

  /**
   * Count blog posts by tag
   * @param tag Tag name
   * @param publishedOnly Only count published posts
   */
  async countPostsByTag(tag: string, publishedOnly = true): Promise<number> {
    try {
      return await this.prisma.blog_posts.count({
        where: {
          blog_post_tags: {
            some: {
              tags: {
                name: tag,
              },
            },
          },
          published: publishedOnly,
        },
      });
    } catch (error) {
      logger.error(`Error counting blog posts for tag ${tag}:`, error);
      return 0;
    }
  }

  /**
   * Get all available tags
   */
  async getAllTags(): Promise<Tag[]> {
    try {
      const tags = await this.prisma.tags.findMany({
        orderBy: { name: 'asc' },
      });

      return tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        slug: tag.name.toLowerCase().replace(/\s+/g, '-'),
        createdAt: new Date(),
      }));
    } catch (error) {
      logger.error('Error getting blog tags:', error);
      return [];
    }
  }
}

// Export a singleton instance
export const blogRepository = new BlogRepository();
export default blogRepository;
