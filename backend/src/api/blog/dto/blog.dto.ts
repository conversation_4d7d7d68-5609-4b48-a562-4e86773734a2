import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsInt,
  IsISO8601,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTOs for Tag
 */
export class TagDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsNotEmpty()
  @IsString()
  slug: string = '';
}

/**
 * DTOs for BlogPost
 */
export class CreateBlogPostDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsNotEmpty()
  @IsString()
  slug: string = '';

  @IsNotEmpty()
  @IsString()
  contentPath: string = '';

  @IsOptional()
  @IsString()
  featuredImagePath?: string;

  @IsOptional()
  @IsInt()
  estimatedReadingTime?: number;
}

export class UpdateBlogPostDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  contentPath?: string;

  @IsOptional()
  @IsString()
  featuredImagePath?: string;

  @IsOptional()
  @IsInt()
  estimatedReadingTime?: number;

  @IsOptional()
  @IsISO8601()
  publishedAt?: Date;
}
