import {
  Blog<PERSON>ost<PERSON><PERSON>,
  BlogPostUpdate,
  BlogPost,
  BlogPostWithAuthor,
  Tag,
} from '../../../common/types/blog';
import { blogRepository } from '../repositories/blog.repository';
import { logger } from '../../../common/logger';

/**
 * Service for blog-related operations
 */
export class BlogService {
  /**
   * Create a new blog post
   * @param data Blog post data
   * @param authorId Author ID
   */
  async createPost(data: BlogPostCreate, authorId: number): Promise<BlogPost | null> {
    try {
      return await blogRepository.createPost(data, authorId);
    } catch (error) {
      logger.error('Error in createPost service:', error);
      return null;
    }
  }

  /**
   * Update an existing blog post
   * @param id Blog post ID
   * @param data Updated blog post data
   * @param authorId Author ID for permission check
   */
  async updatePost(id: number, data: BlogPostUpdate, authorId: number): Promise<BlogPost | null> {
    try {
      // Check ownership
      const post = await blogRepository.getPostById(id);
      if (!post) {
        return null;
      }

      if (post.authorId !== authorId) {
        logger.warn(`User ${authorId} attempted to update post ${id} but is not the author`);
        return null;
      }

      return await blogRepository.updatePost(id, data);
    } catch (error) {
      logger.error(`Error in updatePost service for post ${id}:`, error);
      return null;
    }
  }

  /**
   * Get a blog post by ID with author information
   * @param id Blog post ID
   */
  async getPostById(id: number): Promise<BlogPostWithAuthor | null> {
    try {
      const post = await blogRepository.getPostById(id);
      if (!post) {
        return null;
      }

      // Fetch author information
      // For now, we'll just use dummy author info
      return {
        ...post,
        authorName: 'Author Name', // In a real implementation, fetch from user repository
        authorBio: 'Author biography',
        authorProfileImage: undefined,
        tags: [],
      };
    } catch (error) {
      logger.error(`Error in getPostById service for post ${id}:`, error);
      return null;
    }
  }

  /**
   * Get a blog post by slug with author information
   * @param slug Blog post slug
   */
  async getPostBySlug(slug: string): Promise<BlogPostWithAuthor | null> {
    try {
      const post = await blogRepository.getPostBySlug(slug);
      if (!post) {
        return null;
      }

      // Fetch author information
      // For now, we'll just use dummy author info
      return {
        ...post,
        authorName: 'Author Name', // In a real implementation, fetch from user repository
        authorBio: 'Author biography',
        authorProfileImage: undefined,
        tags: [],
      };
    } catch (error) {
      logger.error(`Error in getPostBySlug service for slug ${slug}:`, error);
      return null;
    }
  }

  /**
   * Get all blog posts with pagination
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @param publishedOnly Only include published posts
   */
  async getAllPosts(
    page = 1,
    pageSize = 10,
    publishedOnly = true,
  ): Promise<{
    posts: BlogPost[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * pageSize;
      const [posts, total] = await Promise.all([
        blogRepository.getAllPosts(skip, pageSize, publishedOnly),
        blogRepository.countPosts(publishedOnly),
      ]);

      return {
        posts,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      logger.error('Error in getAllPosts service:', error);
      return {
        posts: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      };
    }
  }

  /**
   * Delete a blog post
   * @param id Blog post ID
   * @param authorId Author ID for permission check
   */
  async deletePost(id: number, authorId: number): Promise<boolean> {
    try {
      // Check ownership
      const post = await blogRepository.getPostById(id);
      if (!post) {
        return false;
      }

      if (post.authorId !== authorId) {
        logger.warn(`User ${authorId} attempted to delete post ${id} but is not the author`);
        return false;
      }

      return await blogRepository.deletePost(id);
    } catch (error) {
      logger.error(`Error in deletePost service for post ${id}:`, error);
      return false;
    }
  }

  /**
   * Publish a blog post
   * @param id Blog post ID
   * @param authorId Author ID for permission check
   */
  async publishPost(id: number, authorId: number): Promise<BlogPost | null> {
    try {
      // Check ownership
      const post = await blogRepository.getPostById(id);
      if (!post) {
        return null;
      }

      if (post.authorId !== authorId) {
        logger.warn(`User ${authorId} attempted to publish post ${id} but is not the author`);
        return null;
      }

      return await blogRepository.updatePost(id, {
        publishedAt: new Date(),
      });
    } catch (error) {
      logger.error(`Error in publishPost service for post ${id}:`, error);
      return null;
    }
  }

  /**
   * Unpublish a blog post
   * @param id Blog post ID
   * @param authorId Author ID for permission check
   */
  async unpublishPost(id: number, authorId: number): Promise<BlogPost | null> {
    try {
      // Check ownership
      const post = await blogRepository.getPostById(id);
      if (!post) {
        return null;
      }

      if (post.authorId !== authorId) {
        logger.warn(`User ${authorId} attempted to unpublish post ${id} but is not the author`);
        return null;
      }

      // Use undefined instead of null to match the type
      return await blogRepository.updatePost(id, {
        publishedAt: undefined,
      });
    } catch (error) {
      logger.error(`Error in unpublishPost service for post ${id}:`, error);
      return null;
    }
  }

  /**
   * Get blog posts by tag with pagination
   * @param tag Tag slug
   * @param page Page number
   * @param pageSize Number of items per page
   * @param publishedOnly Only include published posts
   */
  async getPostsByTag(
    tag: string,
    page: number,
    pageSize: number,
    publishedOnly: boolean,
  ): Promise<{
    posts: BlogPostWithAuthor[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * pageSize;
      const [posts, total] = await Promise.all([
        blogRepository.getPostsByTag(tag, skip, pageSize, publishedOnly),
        blogRepository.countPostsByTag(tag, publishedOnly),
      ]);

      return {
        posts,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      logger.error(`Error in getPostsByTag service for tag ${tag}:`, error);
      return {
        posts: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      };
    }
  }

  /**
   * Get all available tags
   */
  async getAllTags(): Promise<Tag[]> {
    try {
      return await blogRepository.getAllTags();
    } catch (error) {
      logger.error('Error in getAllTags service:', error);
      return [];
    }
  }
}

// Export a singleton instance
export const blogService = new BlogService();
export default blogService;
