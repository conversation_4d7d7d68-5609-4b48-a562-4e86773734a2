import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsEnum,
  IsBoolean,
  IsDate,
  IsObject,
  IsArray,
  IsInt,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Subscription status enum
 */
export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  EXPIRED = 'expired',
  PAST_DUE = 'past_due',
  UNPAID = 'unpaid',
  TRIALING = 'trialing',
}

/**
 * Subscription event type enum
 */
export enum SubscriptionEventType {
  CREATED = 'created',
  RENEWED = 'renewed',
  CANCELED = 'canceled',
  PAYMENT_FAILED = 'payment_failed',
  PAYMENT_SUCCEEDED = 'payment_succeeded',
  TRIAL_STARTED = 'trial_started',
  TRIAL_ENDED = 'trial_ended',
  PLAN_CHANGED = 'plan_changed',
}

/**
 * Create subscription DTO
 */
export class CreateSubscriptionDto {
  @IsNotEmpty()
  @IsInt()
  userId!: number;

  @IsNotEmpty()
  @IsString()
  planName!: string;

  @IsNotEmpty()
  @IsString()
  planId!: string;

  @IsOptional()
  @IsString()
  status?: string = SubscriptionStatus.ACTIVE;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  currentPeriodStart!: Date;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  currentPeriodEnd!: Date;

  @IsOptional()
  @IsBoolean()
  cancelAtPeriodEnd?: boolean = false;

  @IsNotEmpty()
  @IsString()
  provider!: string;

  @IsOptional()
  @IsString()
  providerSubscriptionId?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Update subscription DTO
 */
export class UpdateSubscriptionDto {
  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  currentPeriodStart?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  currentPeriodEnd?: Date;

  @IsOptional()
  @IsBoolean()
  cancelAtPeriodEnd?: boolean;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  canceledAt?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endedAt?: Date;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Create subscription payment DTO
 */
export class CreateSubscriptionPaymentDto {
  @IsNotEmpty()
  @IsInt()
  subscriptionId!: number;

  @IsNotEmpty()
  @IsInt()
  paymentId!: number;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  billingPeriodStart!: Date;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  billingPeriodEnd!: Date;
}

/**
 * Cancel subscription DTO
 */
export class CancelSubscriptionDto {
  @IsOptional()
  @IsBoolean()
  cancelImmediately?: boolean = false;

  @IsOptional()
  @IsString()
  reason?: string;
}
