import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsInt,
  IsArray,
  IsObject,
  IsUrl,
} from 'class-validator';
import { ProjectType, ProjectMotivationType } from '../../../generated/prisma';

export class CreateProjectDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsUrl()
  repositoryUrl?: string;

  @IsOptional()
  @IsString()
  mainBranch?: string = 'main';

  @IsOptional()
  @IsString()
  localPath?: string;

  @IsOptional()
  @IsString()
  language?: string;

  @IsOptional()
  @IsString()
  framework?: string;

  @IsOptional()
  @IsObject()
  techStack?: Record<string, any>;

  @IsOptional()
  @IsEnum(ProjectType)
  projectType?: ProjectType;

  @IsOptional()
  @IsEnum(ProjectMotivationType)
  projectMotivation?: ProjectMotivationType;

  @IsOptional()
  @IsInt()
  templateId?: number;

  @IsOptional()
  @IsString()
  domain?: string;

  @IsOptional()
  @IsArray()
  targetAudience?: string[];

  @IsOptional()
  @IsObject()
  constraints?: Record<string, any>;

  @IsOptional()
  @IsString()
  stage?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}

export class UpdateProjectDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsUrl()
  repositoryUrl?: string;

  @IsOptional()
  @IsString()
  mainBranch?: string;

  @IsOptional()
  @IsString()
  localPath?: string;

  @IsOptional()
  @IsString()
  language?: string;

  @IsOptional()
  @IsString()
  framework?: string;

  @IsOptional()
  @IsObject()
  techStack?: Record<string, any>;

  @IsOptional()
  @IsEnum(ProjectType)
  projectType?: ProjectType;

  @IsOptional()
  @IsEnum(ProjectMotivationType)
  projectMotivation?: ProjectMotivationType;

  @IsOptional()
  @IsInt()
  templateId?: number;

  @IsOptional()
  @IsString()
  domain?: string;

  @IsOptional()
  @IsArray()
  targetAudience?: string[];

  @IsOptional()
  @IsObject()
  constraints?: Record<string, any>;

  @IsOptional()
  @IsString()
  stage?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

// Object DTOs
export class CreateObjectiveDto {
  @IsNotEmpty()
  @IsString()
  title: string = '';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsInt()
  priority?: number;

  @IsOptional()
  @IsString()
  status?: string;
}

export class CreateTechStackDto {
  @IsOptional()
  @IsString()
  category?: string;

  @IsNotEmpty()
  @IsString()
  technology: string = '';

  @IsOptional()
  @IsString()
  version?: string;
}

export class CreateSlideDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsInt()
  order?: number;
}

export class CreateTestDto {
  @IsNotEmpty()
  @IsString()
  name: string = '';

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  code?: string;
}
