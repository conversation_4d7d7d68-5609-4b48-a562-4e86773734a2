import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../../../common/types/authenticated-request';
import { projectService } from '../../../services/project.service';
import { logger } from '../../../common/logger';
import {
  CreateProjectDto,
  UpdateProjectDto,
  CreateObjectiveDto,
} from '../dto';

/**
 * Project controller handling all project-related endpoints
 */
export class ProjectController {
  /**
   * Get a user's projects with optional filters
   */
  async getUserProjects(req: Request, res: Response) {
    try {
      const activeOnly = req.query.activeOnly !== 'false';
      const sortByUpdated = req.query.sortByUpdated !== 'false';
      const userId = this.getUserId(req);

      const projects = await projectService.getUserProjects(userId, activeOnly, sortByUpdated);

      res.json({ projects });
    } catch (error: any) {
      logger.error('Error listing projects:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to list projects',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Get a specific project by ID
   */
  async getProjectById(req: Request, res: Response) {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const userId = this.getUserId(req);

      const project = await projectService.getById(projectId);

      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: `Project with ID ${projectId} not found`,
        });
      }

      // Check project ownership
      if (project.user_id !== userId && project.user_id !== undefined) {
        return res.status(403).json({
          status: 'error',
          message: 'You do not have permission to access this project',
        });
      }

      return res.json({ project });
    } catch (error: any) {
      logger.error(`Error getting project ${req.params.projectId}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to get project',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Create a new project
   * Uses CreateProjectDto for validation
   */
  async createProject(req: Request, res: Response) {
    try {
      const userId = this.getUserId(req);
      const projectDto = req.body as CreateProjectDto;

      // Create project with user ID
      const projectData = {
        ...projectDto,
        userId,
      };

      const project = await projectService.createProject(projectData);

      if (!project) {
        return res.status(500).json({
          status: 'error',
          message: 'Failed to create project',
        });
      }

      return res.status(201).json({ project });
    } catch (error: any) {
      logger.error('Error creating project:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to create project',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Update an existing project
   * Uses UpdateProjectDto for validation
   */
  async updateProject(req: Request, res: Response) {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const userId = this.getUserId(req);
      const projectDto = req.body as UpdateProjectDto;

      // Check project ownership
      const project = await projectService.getById(projectId);
      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: `Project with ID ${projectId} not found`,
        });
      }

      if (project.user_id !== userId && project.user_id !== undefined) {
        return res.status(403).json({
          status: 'error',
          message: 'You do not have permission to update this project',
        });
      }

      const updatedProject = await projectService.updateProject(projectId, projectDto);

      return res.json({ project: updatedProject });
    } catch (error: any) {
      logger.error(`Error updating project ${req.params.projectId}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to update project',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Delete a project
   */
  async deleteProject(req: Request, res: Response) {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const userId = this.getUserId(req);

      // Check project ownership
      const project = await projectService.getById(projectId);
      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: `Project with ID ${projectId} not found`,
        });
      }

      if (project.user_id !== userId && project.user_id !== undefined) {
        return res.status(403).json({
          status: 'error',
          message: 'You do not have permission to delete this project',
        });
      }

      const success = await projectService.deleteProject(projectId);

      if (!success) {
        return res.status(500).json({
          status: 'error',
          message: 'Failed to delete project',
        });
      }

      return res.status(204).end();
    } catch (error: any) {
      logger.error(`Error deleting project ${req.params.projectId}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to delete project',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Add an objective to a project
   * Uses CreateObjectiveDto for validation
   */
  async addObjective(req: Request, res: Response) {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const userId = this.getUserId(req);
      const objectiveDto = req.body as CreateObjectiveDto;

      // Check project ownership
      const project = await projectService.getById(projectId);
      if (!project) {
        return res.status(404).json({
          status: 'error',
          message: `Project with ID ${projectId} not found`,
        });
      }

      if (project.user_id !== userId && project.user_id !== undefined) {
        return res.status(403).json({
          status: 'error',
          message: 'You do not have permission to update this project',
        });
      }

      const objective = await projectService.addObjective(projectId, objectiveDto);

      return res.status(201).json({ objective });
    } catch (error: any) {
      logger.error(`Error adding objective to project ${req.params.projectId}:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to add objective',
        details: error?.message || 'Unknown error',
      });
    }
  }

  /**
   * Helper to get userId from an authenticated request
   */
  private getUserId(req: Request): number {
    const authReq = req as AuthenticatedRequest;
    return authReq.userId;
  }
}

// Export a singleton instance
export const projectController = new ProjectController();
export default projectController;
