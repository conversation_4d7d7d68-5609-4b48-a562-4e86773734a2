/**
 * This file tests edge cases for the TypeScript documentation parser.
 */

// 1. A class with no explicit constructor
export class SimpleLogger {
  log(message: string) {
    console.log(message);
  }
}

// 2. A function that returns another function (higher-order function)
export function createMultiplier(factor: number): (value: number) => number {
  return (value: number) => value * factor;
}

// 3. An arrow function assigned to a const
export const subtract = (a: number, b: number): number => {
  return a - b;
};

// 4. A class with a private constructor (Singleton pattern)
export class SettingsManager {
  private static instance: SettingsManager;
  private constructor() {}

  public static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager();
    }
    return SettingsManager.instance;
  }
}

// 5. A function with a generic constraint
export function getProperty<T, K extends keyof T>(obj: T, key: K) {
  return obj[key];
}

// 6. An empty class
export class EmptyClass {}

// 7. A file with no functions or classes, just constants
export const PI = 3.14159;
export const E = 2.71828;
