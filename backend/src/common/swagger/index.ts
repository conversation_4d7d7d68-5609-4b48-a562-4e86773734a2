import swaggerJsdoc from 'swagger-jsdoc';
import { version } from '../../../package.json';

// Import swagger docs to be included
import '../../docs';

/**
 * Swagger options for API documentation
 */
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'KAPI API Documentation',
      version,
      description: 'API documentation for KAPI backend services',
      license: {
        name: 'Private',
        url: 'https://kapihq.com',
      },
      contact: {
        name: 'KAPI Support',
        url: 'https://kapihq.com/support',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: '/api',
        description: 'API Server',
      },
    ],
  },
  apis: [
    './src/routes/**/*.ts',
    './src/api/**/*.ts',
    './src/docs/**/*.ts',
    './src/common/types/**/*.ts',
  ],
};

export const specs = swaggerJsdoc(options);
