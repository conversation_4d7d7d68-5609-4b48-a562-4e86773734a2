/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - id
 *         - email
 *         - clerkId
 *         - role
 *       properties:
 *         id:
 *           type: integer
 *           description: User ID
 *         email:
 *           type: string
 *           format: email
 *           description: User email
 *         username:
 *           type: string
 *           description: Optional username
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         clerkId:
 *           type: string
 *           description: Clerk authentication ID
 *         role:
 *           type: string
 *           enum: [FREE, DEVELOPER, ADMIN]
 *           description: User role
 *         isActive:
 *           type: boolean
 *           description: Whether the user is active
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           description: URL to profile image
 *         eloRating:
 *           type: integer
 *           description: User's ELO rating
 *         karma:
 *           type: integer
 *           description: User's karma points
 *         onboardingCompleted:
 *           type: boolean
 *           description: Whether onboarding has been completed
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when account was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when account was last updated
 *         lastLogin:
 *           type: string
 *           format: date-time
 *           description: Timestamp of last login
 *
 *     UserCreate:
 *       type: object
 *       required:
 *         - email
 *         - clerkId
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User email
 *         username:
 *           type: string
 *           description: Optional username
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         clerkId:
 *           type: string
 *           description: Clerk authentication ID
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           description: URL to profile image
 *
 *     UserUpdate:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User email
 *         username:
 *           type: string
 *           description: Optional username
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           description: URL to profile image
 *         role:
 *           type: string
 *           enum: [FREE, DEVELOPER, ADMIN]
 *           description: User role
 *
 *     UserProfile:
 *       type: object
 *       required:
 *         - id
 *         - email
 *       properties:
 *         id:
 *           type: integer
 *           description: User ID
 *         email:
 *           type: string
 *           format: email
 *           description: User email
 *         username:
 *           type: string
 *           description: Optional username
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           description: URL to profile image
 *         preferredIde:
 *           type: string
 *           description: User's preferred IDE
 *         learningStyle:
 *           type: string
 *           description: User's learning style
 *         developerStrengths:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of user's developer strengths
 *         preferredAiModels:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of user's preferred AI models
 *         additionalInfo:
 *           type: string
 *           description: Additional information about the user
 *         eloRating:
 *           type: integer
 *           description: User's ELO rating
 *         onboardingCompleted:
 *           type: boolean
 *           description: Whether onboarding has been completed
 *
 *     Error:
 *       type: object
 *       required:
 *         - status
 *         - message
 *       properties:
 *         status:
 *           type: string
 *           description: Error status
 *         message:
 *           type: string
 *           description: Error message
 *         code:
 *           type: string
 *           description: Error code
 *         details:
 *           type: object
 *           description: Additional error details
 */

export {}; // This makes the file a module
