/**
 * @swagger
 * components:
 *   schemas:
 *     Message:
 *       type: object
 *       required:
 *         - id
 *         - conversationId
 *         - role
 *         - content
 *       properties:
 *         id:
 *           type: integer
 *           description: Message ID
 *         conversationId:
 *           type: integer
 *           description: ID of the conversation this message belongs to
 *         role:
 *           type: string
 *           description: Role of the sender (user, assistant, system)
 *         content:
 *           type: string
 *           description: Message content
 *         model:
 *           type: string
 *           description: AI model used to generate this message
 *         promptTokens:
 *           type: integer
 *           description: Number of tokens in the prompt
 *         completionTokens:
 *           type: integer
 *           description: Number of tokens in the completion
 *         cost:
 *           type: number
 *           format: float
 *           description: Cost in USD for this message/completion
 *         durationMs:
 *           type: integer
 *           description: Processing time in milliseconds
 *         userFeedback:
 *           type: string
 *           description: User feedback (thumbs_up, thumbs_down)
 *         projectId:
 *           type: integer
 *           description: ID of the associated project
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when message was created
 *
 *     MessageCreate:
 *       type: object
 *       required:
 *         - role
 *         - content
 *       properties:
 *         role:
 *           type: string
 *           description: Role of the sender (user, assistant, system)
 *         content:
 *           type: string
 *           description: Message content
 *
 *     Conversation:
 *       type: object
 *       required:
 *         - id
 *         - userId
 *       properties:
 *         id:
 *           type: integer
 *           description: Conversation ID
 *         userId:
 *           type: integer
 *           description: ID of the user who owns this conversation
 *         title:
 *           type: string
 *           description: Conversation title
 *         status:
 *           type: string
 *           enum: [active, archived]
 *           description: Conversation status
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when conversation was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when conversation was last updated
 *         metaData:
 *           type: object
 *           description: Additional metadata
 *         settings:
 *           type: object
 *           description: Conversation settings
 *         projectId:
 *           type: integer
 *           description: ID of the associated project
 *         keyObjective:
 *           type: string
 *           description: The main goal/purpose of this conversation
 *         category:
 *           type: string
 *           enum: [chat, agent, code_assist, debugging, planning, learning, other]
 *           description: Conversation category
 *         userRating:
 *           type: integer
 *           description: User rating (1-5 stars)
 *         userFeedback:
 *           type: string
 *           description: Detailed user feedback
 *         isPinned:
 *           type: boolean
 *           description: Whether the conversation is pinned
 *         agentStatus:
 *           type: string
 *           enum: [initializing, running, paused, completed, failed, waiting, cancelled]
 *           description: Agent status
 *         agentProgress:
 *           type: object
 *           description: Structured progress data
 *         agentIterationCount:
 *           type: integer
 *           description: Number of iterations the agent has gone through
 *         agentAllowedActions:
 *           type: object
 *           description: List of actions the agent is allowed to perform
 *         agentCommandsExecuted:
 *           type: array
 *           items:
 *             type: object
 *           description: List of commands executed by the agent
 *         agentCompletionSummary:
 *           type: string
 *           description: Summary of what the agent accomplished
 *         lastActivityType:
 *           type: string
 *           enum: [thinking, command, file_edit, complete, error, user_input]
 *           description: Type of the last activity
 *         parentId:
 *           type: integer
 *           description: ID of the parent conversation
 *         messages:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Message'
 *           description: Messages in this conversation
 *
 *     ConversationCreate:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Conversation title
 *         initialMessage:
 *           type: string
 *           description: Optional initial message to start the conversation
 *         conversationType:
 *           type: string
 *           default: chat
 *           description: Type of conversation (chat, code_assistance, etc.)
 *         projectId:
 *           type: integer
 *           description: ID of the associated project
 *         keyObjective:
 *           type: string
 *           description: The main goal/purpose of this conversation
 *         category:
 *           type: string
 *           enum: [chat, agent, code_assist, debugging, planning, learning, other]
 *           description: Conversation category
 *         agentStatus:
 *           type: string
 *           enum: [initializing, running, paused, completed, failed, waiting, cancelled]
 *           description: Initial agent status
 *         agentAllowedActions:
 *           type: object
 *           description: List of actions the agent is allowed to perform
 *         parentId:
 *           type: integer
 *           description: ID of the parent conversation
 *
 *     ChatRequest:
 *       type: object
 *       required:
 *         - messages
 *       properties:
 *         messages:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/MessageCreate'
 *           description: List of messages in the conversation
 *         conversationId:
 *           type: integer
 *           description: ID of the existing conversation (if any)
 *         model:
 *           type: string
 *           description: Specific model to use (optional)
 *         taskType:
 *           type: string
 *           default: general
 *           description: Type of task (code_planning, code_generation, general)
 *         maxTokens:
 *           type: integer
 *           default: 1000
 *           description: Maximum number of tokens for the response
 *         temperature:
 *           type: number
 *           format: float
 *           default: 0.7
 *           description: Temperature for response generation (0.0 to 1.0)
 *
 *     ChatResponse:
 *       type: object
 *       required:
 *         - status
 *         - model
 *         - processingTime
 *       properties:
 *         status:
 *           type: string
 *           enum: [success, error]
 *           description: Status of the request
 *         message:
 *           $ref: '#/components/schemas/Message'
 *         error:
 *           type: string
 *           description: Error message if status is error
 *         model:
 *           type: string
 *           description: Model used for the response
 *         requestedModel:
 *           type: string
 *           description: Model that was requested (may differ from model used)
 *         processingTime:
 *           type: number
 *           format: float
 *           description: Time taken to process the request (seconds)
 *         durationMs:
 *           type: integer
 *           description: Processing time in milliseconds
 *         promptTokens:
 *           type: integer
 *           description: Number of tokens in the prompt
 *         completionTokens:
 *           type: integer
 *           description: Number of tokens in the completion
 *         cost:
 *           type: number
 *           format: float
 *           description: Cost in USD for this completion
 *         totalTokens:
 *           type: integer
 *           description: Total number of tokens (prompt + completion)
 *         estimatedCost:
 *           type: number
 *           format: float
 *           description: Estimated cost (deprecated, use cost instead)
 *         conversationId:
 *           type: integer
 *           description: ID of the conversation
 */

export {}; // This makes the file a module
