/**
 * @swagger
 * components:
 *   schemas:
 *     Project:
 *       type: object
 *       required:
 *         - id
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: Project ID
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         userId:
 *           type: integer
 *           description: ID of the user who owns the project
 *         repositoryUrl:
 *           type: string
 *           description: URL to the Git repository
 *         mainBranch:
 *           type: string
 *           description: Name of the main branch
 *         localPath:
 *           type: string
 *           description: Local file system path (for development)
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Primary framework
 *         techStack:
 *           type: object
 *           description: Detailed tech stack as JSON
 *         projectType:
 *           type: string
 *           enum: [WEB_APPLICATION, MOBILE_APPLICATION, LIBRARY, CLI_TOOL, API, OTHER]
 *           description: Project type
 *         projectMotivation:
 *           type: string
 *           enum: [learner, contributor, builder]
 *           description: Project motivation
 *         templateId:
 *           type: integer
 *           description: ID of the template used to create this project
 *         domain:
 *           type: string
 *           description: Project domain (e.g., E-commerce, Healthcare)
 *         targetAudience:
 *           type: array
 *           items:
 *             type: string
 *           description: List of target audience descriptions
 *         constraints:
 *           type: object
 *           description: Project constraints as JSON
 *         stage:
 *           type: string
 *           description: Project stage (e.g., Ideation, Development)
 *         isPublic:
 *           type: boolean
 *           description: Whether the project is public
 *         isActive:
 *           type: boolean
 *           description: Whether the project is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when project was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when project was last updated
 *
 *     ProjectCreate:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         repositoryUrl:
 *           type: string
 *           description: URL to the Git repository
 *         mainBranch:
 *           type: string
 *           description: Name of the main branch
 *         localPath:
 *           type: string
 *           description: Local file system path (for development)
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Primary framework
 *         techStack:
 *           type: object
 *           description: Detailed tech stack as JSON
 *         projectType:
 *           type: string
 *           enum: [WEB_APPLICATION, MOBILE_APPLICATION, LIBRARY, CLI_TOOL, API, OTHER]
 *           description: Project type
 *         projectMotivation:
 *           type: string
 *           enum: [learner, contributor, builder]
 *           description: Project motivation
 *         templateId:
 *           type: integer
 *           description: ID of the template used to create this project
 *         domain:
 *           type: string
 *           description: Project domain (e.g., E-commerce, Healthcare)
 *         targetAudience:
 *           type: array
 *           items:
 *             type: string
 *           description: List of target audience descriptions
 *         constraints:
 *           type: object
 *           description: Project constraints as JSON
 *         stage:
 *           type: string
 *           description: Project stage (e.g., Ideation, Development)
 *         isPublic:
 *           type: boolean
 *           description: Whether the project is public
 *         isActive:
 *           type: boolean
 *           description: Whether the project is active
 *
 *     ProjectUpdate:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         repositoryUrl:
 *           type: string
 *           description: URL to the Git repository
 *         mainBranch:
 *           type: string
 *           description: Name of the main branch
 *         localPath:
 *           type: string
 *           description: Local file system path (for development)
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Primary framework
 *         techStack:
 *           type: object
 *           description: Detailed tech stack as JSON
 *         projectType:
 *           type: string
 *           enum: [WEB_APPLICATION, MOBILE_APPLICATION, LIBRARY, CLI_TOOL, API, OTHER]
 *           description: Project type
 *         projectMotivation:
 *           type: string
 *           enum: [learner, contributor, builder]
 *           description: Project motivation
 *         templateId:
 *           type: integer
 *           description: ID of the template used to create this project
 *         domain:
 *           type: string
 *           description: Project domain (e.g., E-commerce, Healthcare)
 *         targetAudience:
 *           type: array
 *           items:
 *             type: string
 *           description: List of target audience descriptions
 *         constraints:
 *           type: object
 *           description: Project constraints as JSON
 *         stage:
 *           type: string
 *           description: Project stage (e.g., Ideation, Development)
 *         isPublic:
 *           type: boolean
 *           description: Whether the project is public
 *         isActive:
 *           type: boolean
 *           description: Whether the project is active
 *
 *     ProjectSummary:
 *       type: object
 *       required:
 *         - id
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: Project ID
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         projectType:
 *           type: string
 *           description: Project type
 *         projectMotivation:
 *           type: string
 *           description: Project motivation
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Primary framework
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when project was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when project was last updated
 *         isActive:
 *           type: boolean
 *           description: Whether the project is active
 *
 *     Objective:
 *       type: object
 *       required:
 *         - id
 *         - projectId
 *         - title
 *       properties:
 *         id:
 *           type: integer
 *           description: Objective ID
 *         projectId:
 *           type: integer
 *           description: ID of the associated project
 *         title:
 *           type: string
 *           description: Objective title
 *         description:
 *           type: string
 *           description: Objective description
 *         priority:
 *           type: integer
 *           description: Objective priority
 *         status:
 *           type: string
 *           description: Objective status
 */

export {}; // This makes the file a module
