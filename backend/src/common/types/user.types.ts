/**
 * User-related type definitions
 */
import { UserRole } from '../../generated/prisma';

// Base User Type
export interface UserBase {
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  profileImageUrl?: string;
}

// Type for creating a new user
export interface UserCreate extends UserBase {
  email: string;
  clerkId: string;
}

// Type for updating user information
export interface UserUpdate extends UserBase {
  role?: UserRole;
}

// Type for user profile updates
export interface UserProfileUpdate {
  firstName?: string;
  lastName?: string;
  bio?: string;
  profileImageUrl?: string;
  preferredIde?: string;
  learningStyle?: string;
  developerStrengths?: string[];
  preferredAiModels?: string[];
  additionalInfo?: string;
}

// Type for user profile response
export interface UserProfile {
  id: number;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  profileImageUrl?: string;
  preferredIde?: string;
  learningStyle?: string;
  developerStrengths?: string[];
  preferredAiModels?: string[];
  additionalInfo?: string;
  eloRating: number;
  onboardingCompleted: boolean;
}

// Type for user response
export interface UserResponse {
  id: number;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  profileImageUrl?: string;
  role: UserRole;
  isActive: boolean;
  eloRating: number;
  karma: number;
  onboardingCompleted: boolean;
  createdAt: Date;
  updatedAt?: Date;
  lastLogin?: Date;
  developerStrengths?: string[];
  preferredAiModels?: string[];
}
