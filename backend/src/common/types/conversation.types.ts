/**
 * Conversation-related type definitions
 */
import { ConversationCategory, AgentStatus, LastActivityType } from '../../generated/prisma';

// Base Message Type
export interface MessageBase {
  role: string;
  content: string;
}

// Type for creating a new message
export interface MessageCreate extends MessageBase {}

// Type for message response
export interface MessageResponse extends MessageBase {
  id?: number;
  conversationId: number;
  createdAt?: Date;
  model?: string;

  // Token usage
  promptTokens?: number;
  completionTokens?: number;

  // Performance and cost metrics
  cost?: number;
  durationMs?: number;

  // User feedback
  userFeedback?: string;

  // Project reference
  projectId?: number;

  // Legacy field - for backward compatibility
  tokens?: number;
}

// Base Conversation Type
export interface ConversationBase {
  title?: string;
}

// Type for creating a new conversation
export interface ConversationCreate extends ConversationBase {
  initialMessage?: string;
  conversationType?: string;

  // Project association
  projectId?: number;

  // Conversation purpose and categorization
  keyObjective?: string;
  category?: ConversationCategory;

  // Agent-specific fields
  agentStatus?: AgentStatus;
  agentAllowedActions?: Record<string, any>;

  // Integration fields
  parentId?: number;
}

// Type for updating an existing conversation
export interface ConversationUpdate {
  title?: string;
  status?: string;

  // Project association
  projectId?: number;

  // Conversation purpose and categorization
  keyObjective?: string;
  category?: ConversationCategory;

  // User experience fields
  userRating?: number;
  userFeedback?: string;
  isPinned?: boolean;

  // Agent-specific fields
  agentStatus?: AgentStatus;
  agentProgress?: Record<string, any>;
  agentIterationCount?: number;
  agentAllowedActions?: Record<string, any>;
  agentCommandsExecuted?: Array<Record<string, any>>;
  agentCompletionSummary?: string;
  lastActivityType?: LastActivityType;
}

// Type for conversation response
export interface ConversationResponse extends ConversationBase {
  id: number;
  userId: number;
  createdAt: Date;
  updatedAt?: Date;
  conversationType: string;
  status: string;
  messageCount?: number;
  lastMessageAt?: Date;
  messages?: MessageResponse[];

  // Project association
  projectId?: number;

  // Conversation purpose and categorization
  keyObjective?: string;
  category?: string;

  // User experience fields
  userRating?: number;
  userFeedback?: string;
  isPinned?: boolean;

  // Agent-specific fields
  agentStatus?: string;
  agentProgress?: Record<string, any>;
  agentIterationCount?: number;
  agentAllowedActions?: Record<string, any>;
  agentCommandsExecuted?: Array<Record<string, any>>;
  agentCompletionSummary?: string;
  lastActivityType?: string;

  // Integration fields
  parentId?: number;
}

// Type for listing multiple conversations
export interface ConversationList {
  conversations: ConversationResponse[];
  total: number;
  skip: number;
  limit: number;
}

// Type for chat request
export interface ChatRequest {
  messages: MessageBase[];
  conversationId?: number;
  model?: string;
  taskType: string;
  maxTokens: number;
  temperature: number;
}

// Type for chat response
export interface ChatResponse {
  status: string;
  message?: MessageResponse;
  error?: string;
  model: string;
  requestedModel?: string;

  // Performance metrics
  processingTime: number;
  durationMs?: number;

  // Token usage
  promptTokens?: number;
  completionTokens?: number;

  // Cost information
  cost?: number;

  // Legacy fields - for backward compatibility
  totalTokens?: number;
  estimatedCost?: number;

  conversationId?: number;
}
