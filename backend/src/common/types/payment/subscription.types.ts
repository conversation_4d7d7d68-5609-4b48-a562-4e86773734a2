/**
 * Subscription-related type definitions
 */

// Subscription types
export interface SubscriptionBase {
  userId: number;
  planName: string;
  planId: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  provider: string;
  providerSubscriptionId?: string;
  metadata?: Record<string, any>;
}

export interface SubscriptionCreate extends SubscriptionBase {}

export interface SubscriptionUpdate {
  status?: string;
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
  canceledAt?: Date;
  endedAt?: Date;
  metadata?: Record<string, any>;
}

export interface SubscriptionResponse extends SubscriptionBase {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  canceledAt?: Date;
  endedAt?: Date;
  payments?: SubscriptionPaymentResponse[];
}

// Subscription Payment types
export interface SubscriptionPaymentBase {
  subscriptionId: number;
  paymentId: number;
  billingPeriodStart: Date;
  billingPeriodEnd: Date;
}

export interface SubscriptionPaymentCreate extends SubscriptionPaymentBase {}

export interface SubscriptionPaymentResponse extends SubscriptionPaymentBase {
  id: number;
  createdAt: Date;
  payment?: any; // This would be the payment response type
}

// Subscription Event types
export interface SubscriptionEventBase {
  subscriptionId: number;
  eventType: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface SubscriptionEventCreate extends SubscriptionEventBase {}

export interface SubscriptionEventResponse extends SubscriptionEventBase {
  id: number;
  createdAt: Date;
}
