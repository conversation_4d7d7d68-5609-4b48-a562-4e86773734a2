/**
 * Payment-related type definitions
 */

// Payment types
export interface PaymentBase {
  userId: number;
  amount: number;
  currency: string;
  paymentMethod: string;
  paymentProvider: string;
  providerPaymentId?: string;
  status: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface PaymentCreate extends PaymentBase {}

export interface PaymentUpdate {
  status?: string;
  providerPaymentId?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse extends PaymentBase {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  refunds?: RefundResponse[];
}

// Refund types
export interface RefundBase {
  paymentId: number;
  amount: number;
  reason?: string;
  status: string;
  providerRefundId?: string;
  refundedByUserId?: number;
}

export interface RefundCreate extends RefundBase {}

export interface RefundUpdate {
  status?: string;
  providerRefundId?: string;
  completedAt?: Date;
}

export interface RefundResponse extends RefundBase {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}
