/**
 * Project-related type definitions
 */
import { ProjectType, ProjectMotivationType } from '../../generated/prisma';

// Base Project Type
export interface ProjectBase {
  name: string;
  description?: string;
  repositoryUrl?: string;
  mainBranch?: string;
  localPath?: string;
  language?: string;
  framework?: string;
  techStack?: Record<string, any>;
  projectType?: ProjectType;
  projectMotivation?: ProjectMotivationType;
  templateId?: number;
  domain?: string;
  targetAudience?: string[];
  constraints?: Record<string, any>;
  stage?: string;
  isPublic?: boolean;
  isActive?: boolean;
}

// Type for creating a new project
export interface ProjectCreate extends ProjectBase {}

// Type for updating a project
export interface ProjectUpdate {
  name?: string;
  description?: string;
  repositoryUrl?: string;
  mainBranch?: string;
  localPath?: string;
  language?: string;
  framework?: string;
  techStack?: Record<string, any>;
  projectType?: ProjectType;
  projectMotivation?: ProjectMotivationType;
  templateId?: number;
  domain?: string;
  targetAudience?: string[];
  constraints?: Record<string, any>;
  stage?: string;
  isPublic?: boolean;
  isActive?: boolean;
}

// Type for project response
export interface ProjectResponse extends ProjectBase {
  id: number;
  userId?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Type for project summary (used in list views)
export interface ProjectSummary {
  id: number;
  name: string;
  description?: string;
  projectType?: string;
  projectMotivation?: string;
  language?: string;
  framework?: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Project Component Types - Objectives
export interface ObjectiveBase {
  title: string;
  description?: string;
  priority?: number;
  status?: string;
}

export interface ObjectiveCreate extends ObjectiveBase {}

export interface ObjectiveResponse extends ObjectiveBase {
  id: number;
  projectId: number;
}

// Project Component Types - Tech Stack
export interface TechStackBase {
  category?: string;
  technology: string;
  version?: string;
}

export interface TechStackCreate extends TechStackBase {}

export interface TechStackResponse extends TechStackBase {
  id: number;
  projectId: number;
}

// Project Component Types - Slides
export interface SlideBase {
  title?: string;
  content?: string;
  order?: number;
}

export interface SlideCreate extends SlideBase {}

export interface SlideResponse extends SlideBase {
  id: number;
  projectId: number;
}

// Project Component Types - Tests
export interface TestBase {
  name: string;
  type?: string; // Unit, Integration, E2E
  status?: string;
  code?: string;
}

export interface TestCreate extends TestBase {}

export interface TestResponse extends TestBase {
  id: number;
  projectId: number;
}
