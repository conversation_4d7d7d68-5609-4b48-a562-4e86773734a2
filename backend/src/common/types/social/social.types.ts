/**
 * Social-related type definitions
 */

// Base User Type for Social
export interface SocialUserBase {
  id: number;
  username: string;
  email?: string;
  avatarUrl?: string;
}

// Channel types
export interface ChannelBase {
  id: number;
  name: string;
  description?: string;
  isPrivate: boolean;
  ownerId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChannelCreate {
  name: string;
  description?: string;
  isPrivate?: boolean;
  memberIds?: number[];
}

export interface ChannelUpdate {
  name?: string;
  description?: string;
  isPrivate?: boolean;
}

export interface ChannelMember {
  userId: number;
  channelId: number;
  joinedAt: Date;
  user: SocialUserBase;
}

export interface ChannelResponse extends ChannelBase {
  owner: SocialUserBase;
  members?: ChannelMember[];
  messageCount?: number;
  memberCount?: number;
}

// Social Message types
export interface SocialMessageBase {
  id: number;
  content: string;
  channelId: number;
  authorId: number;
  parentId?: number;
  attachments?: string[];
  isEdited: boolean;
  timestamp: Date;
  editedAt?: Date;
}

export interface SocialMessageCreate {
  content: string;
  parentId?: number;
  attachments?: string[];
}

export interface SocialMessageUpdate {
  content?: string;
  attachments?: string[];
}

export interface MessageReaction {
  id: number;
  messageId: number;
  userId: number;
  emoji: string;
  timestamp: Date;
  user: SocialUserBase;
}

export interface SocialMessageResponse extends SocialMessageBase {
  author: SocialUserBase;
  reactions?: MessageReaction[];
  replyCount?: number;
  parent?: {
    id: number;
    content: string;
    author: SocialUserBase;
  };
  replies?: SocialMessageResponse[];
}

// Activity types
export interface ActivityResponse {
  id: number;
  userId: number;
  activityType: string;
  content: string;
  timestamp: Date;
  relatedId?: number;
  channelId?: number;
  user?: SocialUserBase;
}

// Notification types
export interface NotificationBase {
  id: number;
  userId: number;
  type: string;
  content: string;
  sourceId?: number;
  sourceType?: string;
  actorId?: number;
  timestamp: Date;
  isRead: boolean;
  readAt?: Date;
}

export interface NotificationResponse extends NotificationBase {
  actor?: SocialUserBase;
}

// List response types
export interface ChannelList {
  channels: ChannelResponse[];
  total: number;
}

export interface MessageList {
  messages: SocialMessageResponse[];
  total: number;
}

export interface NotificationList {
  notifications: NotificationResponse[];
  total: number;
  unreadCount: number;
}

// QA types
export interface QuestionBase {
  content: string;
  codeSnippet?: string;
}

export interface QuestionCreate extends QuestionBase {}

export interface QuestionUpdate {
  content?: string;
  codeSnippet?: string;
  resolved?: boolean;
}

export interface QuestionResponse extends QuestionBase {
  id: number;
  userId: number;
  createdAt: Date;
  updatedAt?: Date;
  resolved: boolean;
  acceptedAnswerId?: number;
  user?: SocialUserBase;
}

export interface AnswerBase {
  content: string;
  codeSnippet?: string;
}

export interface AnswerCreate extends AnswerBase {}

export interface AnswerUpdate {
  content?: string;
  codeSnippet?: string;
}

export interface AnswerResponse extends AnswerBase {
  id: number;
  questionId: number;
  userId: number;
  createdAt: Date;
  updatedAt?: Date;
  isAccepted: boolean;
  user?: SocialUserBase;
}
