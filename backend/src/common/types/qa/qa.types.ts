/**
 * QA-related type definitions
 */

// User type for QA responses
export interface QAUserBase {
  id: number;
  username: string;
  email?: string;
  karma?: number;
  avatarUrl?: string;
}

// Tag types
export interface TagBase {
  id: number;
  name: string;
  description?: string;
}

export interface TagCreate {
  name: string;
  description?: string;
}

export interface TagUpdate {
  name?: string;
  description?: string;
}

export interface TagResponse extends TagBase {
  questionCount?: number;
}

// Question types
export interface QuestionBase {
  id: number;
  content: string;
  authorId: number;
  codeSnippet?: string;
  isResolved: boolean;
  timestamp: Date;
}

export interface QuestionCreate {
  content: string;
  codeSnippet?: string;
  tags?: string[];
}

export interface QuestionUpdate {
  content?: string;
  codeSnippet?: string;
  isResolved?: boolean;
  tags?: string[];
}

export interface QuestionResponse extends QuestionBase {
  author: QAUserBase;
  tags: TagBase[];
  answerCount?: number;
  voteCount?: number;
  userVote?: number;
  answers?: AnswerResponse[];
}

// Answer types
export interface AnswerBase {
  id: number;
  content: string;
  questionId: number;
  authorId: number;
  codeSnippet?: string;
  isAccepted: boolean;
  timestamp: Date;
}

export interface AnswerCreate {
  content: string;
  questionId: number;
  codeSnippet?: string;
}

export interface AnswerUpdate {
  content?: string;
  codeSnippet?: string;
}

export interface AnswerResponse extends AnswerBase {
  author: QAUserBase;
  voteCount?: number;
  userVote?: number;
}

// Vote types
export interface VoteBase {
  value: number;
}

export interface QuestionVote extends VoteBase {
  questionId: number;
}

export interface AnswerVote extends VoteBase {
  answerId: number;
}
