/**
 * Blog-related type definitions
 */

// Tag types
export interface TagBase {
  name: string;
  slug: string;
}

export interface TagCreate extends TagBase {}

export interface Tag extends TagBase {
  id: number;
  createdAt: Date;
}

// Base blog post types
export interface BlogPostBase {
  title: string;
  slug: string;
  contentPath: string;
  featuredImagePath?: string;
  estimatedReadingTime?: number;
}

// Type for creating a new blog post
export interface BlogPostCreate extends BlogPostBase {}

// Type for updating an existing blog post
export interface BlogPostUpdate {
  title?: string;
  slug?: string;
  contentPath?: string;
  featuredImagePath?: string;
  estimatedReadingTime?: number;
  publishedAt?: Date;
}

// Type for blog post response
export interface BlogPost extends BlogPostBase {
  id: number;
  authorId: number;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Type for blog post with author information
export interface BlogPostWithAuthor extends BlogPost {
  authorName: string;
  authorBio?: string;
  authorProfileImage?: string;
  tags?: Tag[];
}

// Type for content with reading time
export interface BlogPostContent {
  content: string;
  estimatedReadingTime?: number;
}
