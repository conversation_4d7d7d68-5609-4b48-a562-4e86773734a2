import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

/**
 * Configure CORS with secure settings
 */
export const configureCors = () => {
  const allowedOrigins = process.env.CORS_ORIGINS?.split(',') || [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:5173',
    'http://localhost:5174'
  ];

  // Add production origins if defined
  if (process.env.NODE_ENV === 'production') {
    const prodOrigins = [
      'https://app.kapihq.com',
      'https://kapihq.com',
      'https://admin.kapihq.com',
      'https://modernaipro.com'
    ];
    allowedOrigins.push(...prodOrigins);
  }

  return cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps or Postman)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With',
      'Cache-Control',
      'Accept',
      'Origin',
      'X-CSRF-Token'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
    maxAge: 86400 // 24 hours
  });
};

/**
 * Configure Helmet for security headers
 */
export const configureHelmet = () => {
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'", 
          "'unsafe-inline'",
          "https://cdn.jsdelivr.net",
          "https://cdnjs.cloudflare.com"
        ],
        scriptSrc: [
          "'self'", 
          "'unsafe-inline'", 
          "'unsafe-eval'", // Required for Clerk
          "https://code.jquery.com",
          "https://cdn.jsdelivr.net",
          "https://js.clerk.com",
          "https://*.clerk.accounts.dev",
          "blob:" // Required for Clerk workers
        ],
        workerSrc: [
          "'self'",
          "blob:", // Required for Clerk web workers
          "https://js.clerk.com"
        ],
        imgSrc: ["'self'", "data:", "https:", "blob:"],
        connectSrc: [
          "'self'", 
          "wss:", 
          "https:",
          "https://api.clerk.com",
          "https://*.clerk.accounts.dev",
          "https://clerk.com"
        ],
        fontSrc: [
          "'self'", 
          "https:", 
          "data:",
          "https://cdnjs.cloudflare.com"
        ],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: [
          "'self'",
          "https://*.clerk.accounts.dev",
          "https://js.clerk.com"
        ],
      },
    },
    crossOriginEmbedderPolicy: process.env.NODE_ENV === 'production',
  });
};

/**
 * Rate limiting configuration
 */
export const createRateLimiter = (options?: {
  windowMs?: number;
  max?: number;
  message?: string;
}) => {
  return rateLimit({
    windowMs: options?.windowMs || 15 * 60 * 1000, // 15 minutes default
    max: options?.max || 100, // Limit each IP to 100 requests per windowMs
    message: options?.message || 'Too many requests from this IP, please try again later.',
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        error: {
          message: options?.message || 'Too many requests from this IP, please try again later.',
          statusCode: 429,
          retryAfter: (req as any).rateLimit?.resetTime
        }
      });
    }
  });
};

/**
 * Strict rate limiter for sensitive endpoints
 */
export const strictRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Only 5 requests per window
  message: 'Too many attempts, please try again later.'
});

/**
 * API rate limiter
 */
export const apiRateLimiter = process.env.NODE_ENV === 'development'
  ? (req: Request, res: Response, next: NextFunction) => next() // Skip rate limiting in development
  : createRateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // 100 requests per window
    });

/**
 * Auth endpoint rate limiter (more lenient for login flows)
 */
export const authRateLimiter = process.env.NODE_ENV === 'development' 
  ? (req: Request, res: Response, next: NextFunction) => next() // Skip rate limiting in development
  : createRateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 50, // 50 requests per window for auth endpoints
      message: 'Too many authentication attempts, please try again later.'
    });

/**
 * AI endpoint rate limiter (more restrictive due to cost)
 */
export const aiRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute
  message: 'AI rate limit exceeded. Please wait before making more requests.'
});

/**
 * Input sanitization middleware
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Recursively clean an object
  const clean = (obj: any): any => {
    if (typeof obj !== 'object' || obj === null) {
      if (typeof obj === 'string') {
        // Remove any potential script tags or SQL injection attempts
        return obj
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/[<>]/g, '') // Remove < and > to prevent HTML injection
          .trim();
      }
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(clean);
    }

    const cleaned: any = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cleaned[key] = clean(obj[key]);
      }
    }
    return cleaned;
  };

  // Sanitize body, query, and params
  if (req.body) req.body = clean(req.body);
  if (req.query) {
    // For read-only query objects, we need to define the property descriptor
    const cleanedQuery = clean(req.query);
    Object.defineProperty(req, 'query', {
      value: cleanedQuery,
      writable: false,
      enumerable: true,
      configurable: true
    });
  }
  if (req.params) req.params = clean(req.params);

  next();
};

/**
 * Security headers middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Enable XSS filter in browsers
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Permissions policy - allow microphone for admin pages
  if (req.path.startsWith('/admin/nova-sonic')) {
    // Allow microphone for Nova Sonic admin pages
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(self), camera=()');
  } else {
    // Deny microphone for other pages
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  }
  
  next();
};
