import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

/**
 * Validation error handler
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        statusCode: 400,
        details: errors.array()
      }
    });
  }
  next();
};

/**
 * Common validation chains
 */
export const validators = {
  // ID parameter validation
  idParam: param('id')
    .isInt({ min: 1 })
    .withMessage('ID must be a positive integer'),

  // Pagination validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],

  // User registration
  userRegistration: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Invalid email address'),
    body('username')
      .optional()
      .isAlphanumeric()
      .isLength({ min: 3, max: 30 })
      .withMessage('Username must be 3-30 alphanumeric characters'),
    body('firstName')
      .optional()
      .isAlpha()
      .isLength({ max: 50 })
      .withMessage('First name must be alphabetic and less than 50 characters'),
    body('lastName')
      .optional()
      .isAlpha()
      .isLength({ max: 50 })
      .withMessage('Last name must be alphabetic and less than 50 characters')
  ],

  // Conversation creation
  createConversation: [
    body('title')
      .optional()
      .isString()
      .isLength({ max: 200 })
      .withMessage('Title must be less than 200 characters'),
    body('projectId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Project ID must be a positive integer'),
    body('agentId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Agent ID must be a positive integer')
  ],

  // Message creation
  createMessage: [
    body('content')
      .isString()
      .isLength({ min: 1, max: 50000 })
      .withMessage('Message content must be between 1 and 50000 characters'),
    body('role')
      .isIn(['user', 'assistant', 'system'])
      .withMessage('Role must be user, assistant, or system'),
    body('conversationId')
      .isInt({ min: 1 })
      .withMessage('Conversation ID must be a positive integer')
  ],

  // Project creation
  createProject: [
    body('name')
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage('Project name must be between 1 and 100 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('language')
      .optional()
      .isString()
      .isLength({ max: 50 })
      .withMessage('Language must be less than 50 characters'),
    body('framework')
      .optional()
      .isString()
      .isLength({ max: 50 })
      .withMessage('Framework must be less than 50 characters')
  ],

  // AI request validation
  aiRequest: [
    body('prompt')
      .isString()
      .isLength({ min: 1, max: 10000 })
      .withMessage('Prompt must be between 1 and 10000 characters'),
    body('model')
      .optional()
      .isString()
      .withMessage('Model must be a string'),
    body('temperature')
      .optional()
      .isFloat({ min: 0, max: 2 })
      .withMessage('Temperature must be between 0 and 2'),
    body('maxTokens')
      .optional()
      .isInt({ min: 1, max: 100000 })
      .withMessage('Max tokens must be between 1 and 100000')
  ],

  // File path validation (security critical)
  filePath: [
    body('path')
      .isString()
      .custom((value) => {
        // Prevent path traversal attacks
        if (value.includes('..') || value.includes('~')) {
          throw new Error('Invalid file path');
        }
        // Only allow certain file extensions
        const allowedExtensions = ['.ts', '.tsx', '.js', '.jsx', '.json', '.md', '.txt', '.css', '.scss', '.html'];
        const hasValidExtension = allowedExtensions.some(ext => value.endsWith(ext));
        if (!hasValidExtension) {
          throw new Error('File type not allowed');
        }
        return true;
      })
      .withMessage('Invalid or unsafe file path')
  ],

  // Search query validation
  searchQuery: [
    query('q')
      .isString()
      .isLength({ min: 1, max: 200 })
      .withMessage('Search query must be between 1 and 200 characters')
      .trim()
      .escape() // Escape HTML entities
  ]
};

/**
 * Create a validation chain for a specific endpoint
 */
export const validate = (validations: any[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    // Check for errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          statusCode: 400,
          details: errors.array()
        }
      });
    }
    
    next();
  };
};
