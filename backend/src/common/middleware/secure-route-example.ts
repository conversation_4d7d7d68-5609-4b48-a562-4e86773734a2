/**
 * Example of secure route implementation with validation
 */
import { Router } from 'express';
import { body } from 'express-validator';
import { validate } from '../../middleware/validation.middleware';
// import { aiRateLimiter, strictRateLimiter } from '../../middleware/security.middleware';
// import { createError } from '../../middleware/error.middleware';

const router = Router();

/**
 * Example: Create conversation with validation
 */
router.post(
  '/conversations',
  // validate(validators.createConversation), // Example validation
  async (req, res, next) => {
    try {
      // Input is already validated and sanitized
      const { title, projectId, agentId } = req.body;
      
      // Your business logic here
      // ...
      
      res.json({ success: true, data: { /* ... */ } });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * Example: AI endpoint with strict rate limiting
 */
router.post(
  '/ai/generate',
  // aiRateLimiter, // AI-specific rate limiting
  // validate(validators.aiRequest), // Example validation
  async (req, res, next) => {
    try {
      const { prompt, model, temperature, maxTokens } = req.body;
      
      // Your AI logic here
      // ...
      
      res.json({ success: true, data: { /* ... */ } });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * Example: File operation with security validation
 */
router.post(
  '/files/read',
  // validate(validators.filePath), // Example validation
  async (req, res, next) => {
    try {
      const { path } = req.body;
      
      // Path is already validated to prevent traversal attacks
      // Your file reading logic here
      // ...
      
      res.json({ success: true, data: { /* ... */ } });
    } catch (error) {
      next(error);
    }
  }
);

/**
 * Example: Login endpoint with strict rate limiting
 */
router.post(
  '/auth/login',
  // strictRateLimiter, // Only 5 attempts per 15 minutes
  validate([
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 })
  ]),
  async (req, res, next) => {
    try {
      const { email, password } = req.body;
      
      // Your auth logic here
      // ...
      
      res.json({ success: true, data: { /* ... */ } });
    } catch (error) {
      // Use operational errors for expected cases
      if (error instanceof Error && error.message === 'Invalid credentials') {
        return res.status(401).json({ error: 'Invalid email or password' });
      }
      next(error);
    }
  }
);

export default router;
