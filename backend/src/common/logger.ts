/**
 * Application logger
 */

// Simple logger for development
// In production, you might want to use a more robust logging library like winston
class Logger {
  debug(message: string, ...args: any[]) {
    // Only show debug logs if explicitly enabled
    if (process.env.LOG_LEVEL === 'debug') {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]) {
    console.info(`[INFO] ${message}`, ...args);
  }

  warn(message: string, ...args: any[]) {
    console.warn(`[WARN] ${message}`, ...args);
  }

  error(message: string, ...args: any[]) {
    console.error(`[ERROR] ${message}`, ...args);
  }
}

export const logger = new Logger();
