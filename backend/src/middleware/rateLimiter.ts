import rateLimit, { Options as RateLimitOptions } from 'express-rate-limit';

import { env } from '@/config/env.validation';
import { ApiError } from '@/utils/asyncHandler';

/**
 * Create a rate limiter with custom configuration
 */
export const createRateLimiter = (options?: Partial<RateLimitOptions>) => {
  return rateLimit({
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    max: env.RATE_LIMIT_MAX_REQUESTS,
    standardHeaders: true,
    legacyHeaders: false,
    handler: () => {
      throw new ApiError(429, 'Too many requests, please try again later');
    },
    ...options,
  });
};

/**
 * Default API rate limiter
 */
export const apiRateLimiter = createRateLimiter();

/**
 * Strict rate limiter for sensitive endpoints
 */
export const strictRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per window
});

/**
 * Auth endpoints rate limiter
 */
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 requests per window
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * File upload rate limiter
 */
export const uploadRateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 uploads per hour
});
