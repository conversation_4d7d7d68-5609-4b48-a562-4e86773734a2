import { plainToInstance } from 'class-transformer';
import { validate as classValidate } from 'class-validator';
import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

/**
 * Middleware to validate request using express-validator
 */
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      errors: errors.array(),
    });
    return;
  }
  next();
};

/**
 * Middleware factory to validate request body against a DTO class
 * @param type The DTO class to validate against
 * @param skipMissingProperties Whether to skip validation of missing properties (default: false)
 */
export const validate = (type: any, skipMissingProperties = false) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const instance = plainToInstance(type, req.body);
    const errors = await classValidate(instance, { skipMissingProperties });

    if (errors.length > 0) {
      const formattedErrors = errors.map((error) => ({
        property: error.property,
        constraints: error.constraints,
      }));

      res.status(400).json({
        success: false,
        errors: formattedErrors,
      });
      return;
    }

    next();
  };
};
