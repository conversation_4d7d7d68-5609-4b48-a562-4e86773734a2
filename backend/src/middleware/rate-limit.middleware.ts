/**
 * Dynamic Rate Limiting Middleware
 *
 * This middleware implements rate limiting for LLM model calls based on:
 * 1. Requests per minute (RPM)
 * 2. Tokens per minute (TPM)
 * 3. Maximum concurrent requests
 *
 * It's designed to ensure all LLM calls go through the conversation service
 * and are properly rate limited according to model configurations.
 */

import { Request, Response, NextFunction } from 'express';

import config from '../../config/config';
import { logger } from '../common/logger';
import { prisma } from '../db/client';

// Semaphore implementation for concurrency control
class Semaphore {
  private permits: number;
  private waiting: Array<(value: unknown) => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(timeoutMs: number = 5000): Promise<boolean> {
    if (this.permits > 0) {
      this.permits -= 1;
      return true;
    }

    // If no permits available, wait with timeout
    return new Promise<boolean>((resolve) => {
      const timeout = setTimeout(() => {
        // Remove this waiter from the queue
        const index = this.waiting.indexOf(resolve as unknown as (value: unknown) => void);
        if (index !== -1) {
          this.waiting.splice(index, 1);
        }
        resolve(false); // Timeout occurred
      }, timeoutMs);

      // Add to waiting queue
      this.waiting.push((value: unknown) => {
        clearTimeout(timeout);
        resolve(value as boolean);
      });
    });
  }

  release(): void {
    if (this.waiting.length > 0) {
      // Wake up the next waiter
      const resolve = this.waiting.shift()!;
      resolve(true);
    } else {
      this.permits += 1;
    }
  }
}

// Load model configurations
function loadModelConfigs() {
  const modelConfigs: Record<
    string,
    {
      rpmLimit: number;
      tpmLimit: number;
      maxConcurrentRequests: number;
    }
  > = {};

  try {
    const models = config.models || [];

    for (const model of models) {
      try {
        modelConfigs[model.model_id] = {
          rpmLimit: parseInt(model.rpm_limit, 10) || 0,
          tpmLimit: parseInt(model.tpm_limit, 10) || 0,
          maxConcurrentRequests: parseInt(model.max_concurrent_requests, 10) || 1,
        };
      } catch (error) {
        logger.error(`Error loading config for model ${model.model_id}:`, error);
        modelConfigs[model.model_id] = {
          rpmLimit: 0,
          tpmLimit: 0,
          maxConcurrentRequests: 1,
        };
      }
    }

    return modelConfigs;
  } catch (error) {
    logger.error('Error loading model configurations:', error);
    return {};
  }
}

// Create model configurations and semaphores
const modelConfigs = loadModelConfigs();
const modelSemaphores: Record<string, Semaphore> = {};

// Initialize semaphores for each model
for (const [modelId, config] of Object.entries(modelConfigs)) {
  modelSemaphores[modelId] = new Semaphore(config.maxConcurrentRequests);
}

/**
 * Middleware to implement dynamic rate limiting for LLM models
 */
export const dynamicRateLimitMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // Skip for non-LLM endpoints
  const path = req.path;
  if (
    path.includes('/health') ||
    path.includes('/docs') ||
    path.includes('/api-docs') ||
    path.includes('/admin')
  ) {
    return next();
  }

  // Get model ID and task type from headers
  const modelId = req.headers['x-model-id'] as string;
  const taskType = req.headers['x-task-type'] as string;

  // If no model ID or task type, skip rate limiting
  if (!modelId || !taskType) {
    return next();
  }

  // Get model configuration
  const modelConfig = modelConfigs[modelId];
  if (!modelConfig) {
    return res.status(400).json({
      status: 'error',
      message: `Model '${modelId}' not found in configuration`,
    });
  }

  try {
    // Check RPM limit
    if (modelConfig.rpmLimit > 0) {
      const oneMinuteAgo = new Date(Date.now() - 60 * 1000);

      const rpmCount = await prisma.model_usage.count({
        where: {
          model_name: modelId,
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
      });

      if (rpmCount >= modelConfig.rpmLimit) {
        return res.status(429).json({
          status: 'error',
          message: `Model '${modelId}' RPM limit exceeded`,
        });
      }
    }

    // Check TPM limit
    if (modelConfig.tpmLimit > 0) {
      const oneMinuteAgo = new Date(Date.now() - 60 * 1000);

      const tpmUsage = await prisma.model_usage.aggregate({
        where: {
          model_name: modelId,
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
        _sum: {
          total_tokens: true,
        },
      });

      const totalTokens = tpmUsage._sum?.total_tokens || 0;

      if (totalTokens >= modelConfig.tpmLimit) {
        return res.status(429).json({
          status: 'error',
          message: `Model '${modelId}' TPM limit exceeded`,
        });
      }
    }

    // Check concurrency limit
    const semaphore = modelSemaphores[modelId];
    if (!semaphore) {
      return res.status(500).json({
        status: 'error',
        message: `No semaphore for model '${modelId}'`,
      });
    }

    // Try to acquire a permit with 5 second timeout
    const acquired = await semaphore.acquire(5000);
    if (!acquired) {
      return res.status(429).json({
        status: 'error',
        message: `Model '${modelId}' at concurrency limit`,
      });
    }

    // Add a response listener to release the semaphore when done
    res.on('finish', () => {
      semaphore.release();
    });

    // Continue to the next middleware/route handler
    next();
  } catch (error) {
    logger.error('Error in rate limit middleware:', error);

    // Make sure to release the semaphore in case of error
    if (modelSemaphores[modelId]) {
      modelSemaphores[modelId].release();
    }

    return res.status(500).json({
      status: 'error',
      message: 'Internal server error during rate limiting',
    });
  }
};
