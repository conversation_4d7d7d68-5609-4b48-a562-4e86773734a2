import { Request, Response, NextFunction } from 'express';

import MemoryModule from '../services/memory/memory.module';

/**
 * Middleware to add memory context to request
 */
export const memoryContextMiddleware = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const memoryModule = MemoryModule.getInstance();
    // Add memory services to request object for use in routes
    (req as any).memoryServices = {
      contextManager: memoryModule.contextManager,
      userContext: memoryModule.userContext,
      slideBuilder: memoryModule.slideBuilder,
      documentationSummarizer: memoryModule.documentationSummarizer,
      aiContextIntegration: memoryModule.aiContextIntegration,
    };

    next();
  } catch (error) {
    console.error('Error initializing memory middleware:', error);
    next(error);
  }
};

/**
 * Middleware to enhance AI service with context
 */
export const enhanceAiServiceMiddleware = (req: Request, _res: Response, next: NextFunction): void => {
  try {
    // If AI service is available in request, enhance it with context
    if ((req as any).aiService) {
      const memoryModule = MemoryModule.getInstance();
      const enhancedService = memoryModule.aiContextIntegration.enhanceAiService(
        (req as any).aiService,
      );
      (req as any).aiService = enhancedService;
    }

    next();
  } catch (error) {
    console.error('Error enhancing AI service:', error);
    next(error);
  }
};
