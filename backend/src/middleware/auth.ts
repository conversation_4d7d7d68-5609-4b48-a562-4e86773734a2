import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import { JwtUser } from '../common/types';

export const authenticateJwt = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    res.status(401).json({ message: 'No authorization header' });
    return;
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    req.user = decoded as JwtUser;
    next();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (_err) {
    res.status(401).json({ message: 'Invalid token' });
    return;
  }
};
