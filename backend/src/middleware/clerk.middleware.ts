import axios from 'axios';
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import jwkToPem from 'jwk-to-pem';

import { AuthenticatedRequest } from '../common/types/authenticated-request';

// Define types for JWK (JSON Web Key)
interface JwkRsa {
  kid: string;
  kty: 'RSA';
  alg: string;
  use: string;
  n: string;
  e: string;
  [key: string]: any;
}

interface JwksResponse {
  keys: Array<JwkRsa>;
}

// Cache for JWKS
const jwksCache: {
  keys: JwksResponse | null;
  lastUpdated: number;
  expiry: number;
} = {
  keys: null,
  lastUpdated: 0,
  expiry: 3600, // Cache for 1 hour
};

/**
 * Fetch JWKS from Clerk
 * @returns The JSON Web Key Set
 */
const getJwks = async (): Promise<JwksResponse> => {
  // Check if we have a cached JWKS that's still valid
  const currentTime = Math.floor(Date.now() / 1000);
  if (jwksCache.keys !== null && currentTime - jwksCache.lastUpdated < jwksCache.expiry) {
    return jwksCache.keys;
  }

  try {
    const jwksUrl = process.env.CLERK_JWKS_URL;
    if (!jwksUrl) {
      throw new Error('CLERK_JWKS_URL environment variable is not set');
    }

    const response = await axios.get<JwksResponse>(jwksUrl);

    // Ensure all keys have the correct kty set to "RSA"
    const processedKeys = response.data.keys.map((key) => ({
      ...key,
      kty: 'RSA' as const,
    }));

    // Update the cache with processed keys
    jwksCache.keys = {
      ...response.data,
      keys: processedKeys,
    };

    jwksCache.lastUpdated = currentTime;

    return jwksCache.keys;
  } catch (error) {
    console.error('Error fetching JWKS from Clerk:', error);

    // If we have a cached JWKS, return it even if it's expired
    if (jwksCache.keys !== null) {
      console.warn('Using expired JWKS cache');
      return jwksCache.keys;
    }

    throw error;
  }
};

/**
 * Find a key in the JWKS by its key ID
 * @param jwks The JWKS
 * @param kid The key ID to find
 * @returns The key if found, undefined otherwise
 */
const findKeyByKid = (jwks: JwksResponse, kid: string): JwkRsa | undefined => {
  return jwks.keys.find((key) => key.kid === kid);
};

/**
 * Verify a Clerk JWT token
 * @param token The JWT token to verify
 * @returns The decoded token payload if valid, null otherwise
 */
export const verifyClerkToken = async (token: string): Promise<jwt.JwtPayload | null> => {
  try {
    // Get the unverified header to extract the key ID
    const unverifiedHeader = jwt.decode(token, { complete: true })?.header as { kid?: string };
    const kid = unverifiedHeader?.kid;

    if (!kid) {
      console.error('No kid in token header');
      return null;
    }

    // Get the JWKS
    const jwks = await getJwks();

    // Find the key
    const key = findKeyByKid(jwks, kid);
    if (!key) {
      console.error(`No key found for kid: ${kid}`);
      return null;
    }

    // Convert the JWK to a PEM format that the jsonwebtoken library can use
    const publicKey = jwkToPem(key);

    // Verify the token
    try {
      const payload = jwt.verify(token, publicKey, {
        algorithms: ['RS256'],
        issuer: process.env.CLERK_FRONTEND_API?.replace(/\/$/, ''),
        clockTolerance: 60, // Allow 1 minute of clock skew
      }) as jwt.JwtPayload;

      return payload;
    } catch (error) {
      console.error('JWT verification error:', error);

      // In development mode, optionally fall back to unverified payload (if explicitly enabled)
      if (process.env.NODE_ENV === 'development' && process.env.ALLOW_UNVERIFIED_JWT === 'true') {
        console.warn('SECURITY WARNING: Using unverified JWT payload in development');
        return jwt.decode(token) as jwt.JwtPayload;
      }

      return null;
    }
  } catch (error) {
    console.error('Error verifying Clerk token:', error);
    return null;
  }
};

/**
 * Extract the Clerk user ID from a token
 * @param token The JWT token
 * @returns The Clerk user ID if found, null otherwise
 */
export const getClerkUserId = async (token: string): Promise<string | null> => {
  const payload = await verifyClerkToken(token);
  if (payload) {
    return payload.sub || null;
  }

  // Fallback: try to extract the user ID without verification (if explicitly enabled)
  if (process.env.NODE_ENV === 'development' && process.env.ALLOW_UNVERIFIED_JWT === 'true') {
    try {
      console.warn('SECURITY WARNING: Extracting user ID from unverified JWT');
      const unverifiedPayload = jwt.decode(token) as jwt.JwtPayload;
      return unverifiedPayload?.sub || null;
    } catch (error) {
      console.error('Error extracting user ID from token:', error);
      return null;
    }
  }

  return null;
};

/**
 * Middleware to authenticate requests using Clerk JWT tokens
 */
export const clerkAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({
      success: false,
      message: 'Authentication token is missing',
    });
    return;
  }

  const token = authHeader.split(' ')[1];

  // Development bypass for IDE testing (requires proper token)
  if (process.env.NODE_ENV === 'development' && process.env.DEV_IDE_TOKEN && token === process.env.DEV_IDE_TOKEN) {
    console.log('Clerk middleware: Using development bypass token for IDE');
    (req as AuthenticatedRequest).userId = parseInt(process.env.DEV_USER_ID || '1', 10);
    next();
    return;
  }

  getClerkUserId(token)
    .then((userId) => {
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired token',
        });
        return;
      }

      // Set the user ID in request for use in route handlers
      // Convert string userId to number for consistency
      (req as AuthenticatedRequest).userId = parseInt(userId, 10);
      next();
    })
    .catch((error) => {
      next(error);
    });
};

/**
 * Optional authentication middleware - doesn't fail if no token is provided
 * Useful for endpoints that can work with or without authentication
 */
export const optionalClerkAuthMiddleware = (req: Request, _res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    next();
    return;
  }

  const token = authHeader.split(' ')[1];

  getClerkUserId(token)
    .then((userId) => {
      if (userId) {
        // Convert string userId to number for consistency
        (req as AuthenticatedRequest).userId = parseInt(userId, 10);
      }
      next();
    })
    .catch(() => {
      // Just continue without authentication in case of error
      next();
    });
};
