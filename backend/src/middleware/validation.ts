import { Request, Response, NextFunction } from 'express';
import { validationResult, ValidationChain, body, param, query, header, cookie, check } from 'express-validator';

import { ApiError } from '@/utils/asyncHandler';

/**
 * Middleware to handle validation results from express-validator
 */
export const handleValidationErrors = (req: Request, _res: Response, next: NextFunction): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map((error) => {
      if ('param' in error && error.type === 'field') {
        return {
          field: error.path,
          message: error.msg,
          value: (error as any).value,
          location: (error as any).location,
        };
      }
      return {
        message: error.msg,
      };
    });

    throw new ApiError(400, 'Validation failed');
  }

  next();
};

/**
 * Create a validation middleware chain
 */
export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map((validation) => validation.run(req)));

    // Check for errors
    handleValidationErrors(req, res, next);
  };
};

/**
 * Re-export common validation chains for convenience
 */
export { body, param, query, header, cookie, check };

/**
 * Custom validation helpers
 */
export const isValidObjectId = (value: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(value);
};

export const isValidUUID = (value: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
};

/**
 * Common validation schemas
 */
export const commonValidations = {
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
    query('sort')
      .optional()
      .isString()
      .trim()
      .isIn(['asc', 'desc', 'ASC', 'DESC'])
      .withMessage('Sort must be either asc or desc'),
    query('sortBy')
      .optional()
      .isString()
      .trim()
      .notEmpty()
      .withMessage('Sort by field cannot be empty'),
  ],

  idParam: [
    param('id')
      .notEmpty()
      .withMessage('ID is required')
      .isInt({ min: 1 })
      .withMessage('ID must be a positive integer')
      .toInt(),
  ],

  emailValidation: [
    body('email')
      .trim()
      .notEmpty()
      .withMessage('Email is required')
      .isEmail()
      .withMessage('Invalid email format')
      .normalizeEmail(),
  ],

  passwordValidation: [
    body('password')
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage(
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      ),
  ],
};
