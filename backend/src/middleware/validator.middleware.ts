/**
 * Request validation middleware using express-validator
 */
import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

import { logger } from '../common/logger';

/**
 * Middleware that validates request using express-validator
 *
 * This middleware should be used after validation chains from express-validator.
 * It will check if there are any validation errors and return a 400 response with
 * error details if any are found.
 *
 * @example
 * // Usage with express-validator
 * router.post('/endpoint', [
 *   body('email').isEmail(),
 *   body('password').isLength({ min: 6 })
 * ], validateRequest, (req, res) => {
 *   // Handle valid request
 * });
 */
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    logger.debug(`Validation errors in request: ${req.method} ${req.path}`, {
      errors: errors.array(),
    });

    res.status(400).json({
      status: 'error',
      message: 'Validation error',
      errors: errors.array(),
    });
    return;
  }

  next();
};
