import { NextFunction, Request, Response } from 'express';
import next from 'next';

import config from '../../config/config';

let nextApp: ReturnType<typeof next> | null = null;
let handle: ((req: Request, res: Response) => Promise<void>) | null = null;

export async function initNextApp() {
  if (!nextApp) {
    const dev = process.env.NODE_ENV !== 'production';

    // Pass Clerk environment variables explicitly
    const env = {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: config.auth.clerkPublishableKey || '',
      CLERK_SECRET_KEY: config.auth.clerkSecretKey || '',
      NEXT_PUBLIC_CLERK_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '/signin',
      NEXT_PUBLIC_CLERK_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '/signup',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL:
        process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '/kapi',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL:
        process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '/kapi',
    };

    console.log('Next.js environment variables:', {
      CLERK_PUBLISHABLE_KEY_SET: !!config.auth.clerkPublishableKey,
      CLERK_SECRET_KEY_SET: !!config.auth.clerkSecretKey,
    });

    nextApp = next({
      dev,
      dir: './src/next',
      conf: {
        env, // Pass environment variables directly
      },
    });

    await nextApp.prepare();
    handle = nextApp.getRequestHandler();
  }
}

export function nextMiddleware(req: Request, res: Response, next: NextFunction): void | Promise<void> {
  if (!handle) {
    initNextApp()
      .then(() => {
        if (handle) {
          handle(req, res);
          return;
        }
        next();
      })
      .catch((error) => {
        console.error('Next.js middleware error:', error);
        next(error);
      });
    return;
  }

  handle(req, res);
}
