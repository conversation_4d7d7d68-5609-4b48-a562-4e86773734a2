import { Request, Response, NextFunction } from 'express';

import { AuthenticatedRequest } from '../common/types/authenticated-request';

import { clerkAuthMiddleware } from './clerk.middleware';

// Re-export clerkAuthMiddleware
export { clerkAuthMiddleware };

/**
 * Middleware to check if user is authenticated
 * Uses the modern clerkAuthMiddleware internally
 */
export const isAuthenticated = (req: Request, res: Response, next: NextFunction): void => {
  // If running in test mode, skip authentication
  if (process.env.NODE_ENV === 'test') {
    (req as any).user = { id: 1, role: 'ADMIN' };
    (req as AuthenticatedRequest).userId = 1;
    (req as AuthenticatedRequest).userRole = 'ADMIN';
    next();
    return;
  }

  // Use our custom Clerk authentication middleware
  clerkAuthMiddleware(req, res, next);
};

/**
 * Middleware to check if user is authenticated and set userId and userRole
 * Note: clerkAuthMiddleware already sets userId, so this just adds userRole
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  clerkAuthMiddleware(req, res, () => {
    // clerkAuthMiddleware already sets userId
    // Add userRole for backwards compatibility
    (req as AuthenticatedRequest).userRole = (req as any).user?.role || 'FREE';
    next();
  });
};

/**
 * Middleware to check if user has required role
 */
export const roleMiddleware = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.userRole || !roles.includes(authReq.userRole)) {
      res.status(403).json({ error: 'Access denied: insufficient permissions' });
      return;
    }
    next();
  };
};
