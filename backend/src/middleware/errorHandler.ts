import { Request, Response, NextFunction } from 'express';

import { env } from '@/config/env.validation';
import { logger } from '@/config/logger';
import { ApiError } from '@/utils/asyncHandler';

/**
 * Global error handler middleware
 */
export const errorHandler = (
  err: Error | ApiError,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  // Default to 500 server error
  let statusCode = 500;
  let message = 'Internal Server Error';
  let stack: string | undefined;

  // Handle ApiError instances
  if (err instanceof ApiError) {
    statusCode = err.statusCode;
    message = err.message;
  } else if (err instanceof Error) {
    message = err.message;
  }

  // Log error details
  logger.error({
    message: err.message,
    statusCode,
    path: req.path,
    method: req.method,
    ip: req.ip,
    stack: err.stack,
    body: req.body,
    query: req.query,
    params: req.params,
  });

  // Include stack trace in development
  if (env.NODE_ENV === 'development') {
    stack = err.stack;
  }

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      message,
      statusCode,
      ...(stack && { stack }),
      ...(env.NODE_ENV === 'development' && {
        details: err,
        request: {
          method: req.method,
          path: req.path,
          query: req.query,
          body: req.body,
        },
      }),
    },
    timestamp: new Date().toISOString(),
  });
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction): void => {
  const error = new ApiError(404, `Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Validation error handler for express-validator
 */
export const validationErrorHandler = (errors: any[]): void => {
  // Format errors for logging/debugging if needed
  const formattedErrors = errors.map((error) => ({
    field: error.param || error.path,
    message: error.msg,
    value: error.value,
  }));

  // Log formatted errors for debugging
  logger.error('Validation errors:', formattedErrors);

  throw new ApiError(400, 'Validation failed', true);
};

/**
 * Async route wrapper with automatic error handling
 */
export const asyncRoute = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Handle unhandled promise rejections
 */
export const handleUnhandledRejections = () => {
  process.on('unhandledRejection', (reason: Error | any, _promise: Promise<any>) => {
    logger.error('Unhandled Promise Rejection:', {
      reason: reason?.message || reason,
      stack: reason?.stack,
    });

    // In production, you might want to gracefully shutdown
    if (env.NODE_ENV === 'production') {
      logger.error('Shutting down due to unhandled promise rejection...');
      process.exit(1);
    }
  });
};

/**
 * Handle uncaught exceptions
 */
export const handleUncaughtExceptions = () => {
  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception:', {
      message: error.message,
      stack: error.stack,
    });

    // Always exit on uncaught exceptions
    logger.error('Shutting down due to uncaught exception...');
    process.exit(1);
  });
};
