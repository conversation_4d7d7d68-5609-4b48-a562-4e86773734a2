import { Request, Response, NextFunction } from 'express';

import { HttpError } from '../common/errors/http.error';

export const errorMiddleware = (
  error: HttpError,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  const status = error.status || 500;
  const message = error.message || 'Something went wrong';

  console.error(`[${req.method}] ${req.path} >> StatusCode:: ${status}, Message:: ${message}`);

  res.status(status).json({ success: false, message });
};
