import { Request, Response, NextFunction } from 'express';
import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';

/**
 * Middleware to validate request body against a DTO class
 * @param dtoClass - The DTO class to validate against
 * @param skipMissingProperties - Whether to skip validation of missing properties (default: false)
 */
export function validateDto<T extends object>(
  dtoClass: new () => T,
  skipMissingProperties = false,
): (req: Request, res: Response, next: NextFunction) => Promise<void> {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const dtoObj = plainToInstance(dtoClass, req.body);
      const errors = await validate(dtoObj, { skipMissingProperties });

      if (errors.length > 0) {
        const formattedErrors = formatErrors(errors);
        res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: formattedErrors,
        });
        return;
      }

      // Add the validated object to the request for controller use
      req.body = dtoObj;
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Format validation errors for better readability
 */
function formatErrors(
  errors: ValidationError[],
): Array<{ property: string; message: string; value?: any }> {
  return errors.map((error) => {
    const constraints = error.constraints
      ? Object.values(error.constraints).join(', ')
      : 'Invalid value';

    return {
      property: error.property,
      message: constraints,
      value: error.value,
    };
  });
}
