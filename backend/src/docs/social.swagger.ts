/**
 * Swagger documentation for Social DTOs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateChannelDto:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: Channel name
 *         description:
 *           type: string
 *           description: Channel description
 *         type:
 *           type: string
 *           default: "TOPIC"
 *           description: Channel type
 *       example:
 *         name: "general"
 *         description: "General discussion channel"
 *         type: "TOPIC"
 *
 *     UpdateChannelDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Channel name
 *         description:
 *           type: string
 *           description: Channel description
 *         type:
 *           type: string
 *           description: Channel type
 *       example:
 *         name: "general-discussion"
 *         description: "Updated general discussion channel"
 *
 *     CreateMessageDto:
 *       type: object
 *       required:
 *         - content
 *       properties:
 *         content:
 *           type: string
 *           description: Message content
 *         messageType:
 *           type: string
 *           default: "REGULAR"
 *           description: Message type
 *         codeLanguage:
 *           type: string
 *           description: Code language (for code snippets)
 *       example:
 *         content: "Hello everyone!"
 *         messageType: "REGULAR"
 *
 *     UpdateMessageDto:
 *       type: object
 *       required:
 *         - content
 *       properties:
 *         content:
 *           type: string
 *           description: Message content
 *       example:
 *         content: "Updated message content"
 *
 *     CreateQuestionDto:
 *       type: object
 *       required:
 *         - content
 *       properties:
 *         content:
 *           type: string
 *           description: Question content
 *         codeSnippet:
 *           type: string
 *           description: Related code snippet
 *       example:
 *         content: "How do I implement a binary search tree in TypeScript?"
 *         codeSnippet: "class Node { ... }"
 *
 *     UpdateQuestionDto:
 *       type: object
 *       properties:
 *         content:
 *           type: string
 *           description: Question content
 *         codeSnippet:
 *           type: string
 *           description: Related code snippet
 *         resolved:
 *           type: boolean
 *           description: Whether the question is resolved
 *       example:
 *         content: "Updated question"
 *         resolved: true
 *
 *     CreateAnswerDto:
 *       type: object
 *       required:
 *         - content
 *       properties:
 *         content:
 *           type: string
 *           description: Answer content
 *         codeSnippet:
 *           type: string
 *           description: Related code snippet
 *       example:
 *         content: "Here's how you can implement a binary search tree in TypeScript"
 *         codeSnippet: "class BST { ... }"
 *
 *     UpdateAnswerDto:
 *       type: object
 *       properties:
 *         content:
 *           type: string
 *           description: Answer content
 *         codeSnippet:
 *           type: string
 *           description: Related code snippet
 *       example:
 *         content: "Updated answer with more detail"
 *         codeSnippet: "class BinarySearchTree { ... }"
 */
