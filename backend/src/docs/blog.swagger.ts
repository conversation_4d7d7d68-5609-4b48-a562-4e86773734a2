/**
 * Swagger documentation for Blog DTOs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     TagDto:
 *       type: object
 *       required:
 *         - name
 *         - slug
 *       properties:
 *         name:
 *           type: string
 *           description: Tag name
 *         slug:
 *           type: string
 *           description: URL-friendly slug
 *       example:
 *         name: "TypeScript"
 *         slug: "typescript"
 *
 *     CreateBlogPostDto:
 *       type: object
 *       required:
 *         - title
 *         - slug
 *         - contentPath
 *       properties:
 *         title:
 *           type: string
 *           description: Blog post title
 *         slug:
 *           type: string
 *           description: URL-friendly slug
 *         contentPath:
 *           type: string
 *           description: Path to content file
 *         featuredImagePath:
 *           type: string
 *           description: Path to featured image
 *         estimatedReadingTime:
 *           type: integer
 *           description: Estimated reading time in minutes
 *       example:
 *         title: "Getting Started with TypeScript"
 *         slug: "getting-started-with-typescript"
 *         contentPath: "/content/blog/typescript-intro.md"
 *         featuredImagePath: "/images/typescript.jpg"
 *         estimatedReadingTime: 5
 *
 *     UpdateBlogPostDto:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Blog post title
 *         slug:
 *           type: string
 *           description: URL-friendly slug
 *         contentPath:
 *           type: string
 *           description: Path to content file
 *         featuredImagePath:
 *           type: string
 *           description: Path to featured image
 *         estimatedReadingTime:
 *           type: integer
 *           description: Estimated reading time in minutes
 *         publishedAt:
 *           type: string
 *           format: date-time
 *           description: Publication date and time
 *       example:
 *         title: "Updated: Getting Started with TypeScript"
 *         estimatedReadingTime: 7
 *         publishedAt: "2023-05-15T10:30:00Z"
 */
