/**
 * Swagger documentation for User DTOs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateUserDto:
 *       type: object
 *       required:
 *         - email
 *         - clerkId
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         clerkId:
 *           type: string
 *           description: User ID from Clerk authentication service
 *         username:
 *           type: string
 *           description: Username (optional)
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           format: uri
 *           description: URL to profile image
 *       example:
 *         email: "<EMAIL>"
 *         clerkId: "user_1234567890"
 *         username: "codemaster"
 *         firstName: "John"
 *         lastName: "Doe"
 *
 *     UpdateUserDto:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         username:
 *           type: string
 *           description: Username
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           format: uri
 *           description: URL to profile image
 *         role:
 *           type: string
 *           enum: [FREE, DEVELOPER, ADMIN]
 *           description: User role
 *       example:
 *         firstName: "Jane"
 *         lastName: "Smith"
 *         bio: "Software engineer passionate about AI"
 *
 *     UpdateUserProfileDto:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *           description: First name
 *         lastName:
 *           type: string
 *           description: Last name
 *         bio:
 *           type: string
 *           description: User biography
 *         profileImageUrl:
 *           type: string
 *           format: uri
 *           description: URL to profile image
 *         preferredIde:
 *           type: string
 *           description: Preferred IDE
 *         learningStyle:
 *           type: string
 *           description: Learning style preference
 *         developerStrengths:
 *           type: array
 *           items:
 *             type: string
 *           description: Developer strengths/skills
 *         preferredAiModels:
 *           type: array
 *           items:
 *             type: string
 *           description: Preferred AI models
 *         additionalInfo:
 *           type: string
 *           description: Additional user information
 *       example:
 *         firstName: "Jane"
 *         lastName: "Smith"
 *         bio: "Software engineer passionate about AI"
 *         preferredIde: "VSCode"
 *         learningStyle: "visual"
 *         developerStrengths: ["JavaScript", "React", "Node.js"]
 *         preferredAiModels: ["Claude", "GPT-4"]
 */
