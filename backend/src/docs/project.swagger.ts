/**
 * Swagger documentation for Project DTOs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateProjectDto:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         repositoryUrl:
 *           type: string
 *           format: uri
 *           description: URL to the project's repository
 *         mainBranch:
 *           type: string
 *           default: main
 *           description: Main branch name
 *         localPath:
 *           type: string
 *           description: Local file system path
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Framework used
 *         projectType:
 *           type: string
 *           enum: [WEB_APPLICATION, MOBILE_APPLICATION, LIBRARY, CLI_TOOL, API, OTHER]
 *           description: Type of project
 *         projectMotivation:
 *           type: string
 *           enum: [learner, contributor, builder]
 *           description: Motivation for the project
 *         templateId:
 *           type: integer
 *           description: Template ID if project is based on a template
 *         domain:
 *           type: string
 *           description: Business domain
 *         isPublic:
 *           type: boolean
 *           default: false
 *           description: Whether the project is public
 *       example:
 *         name: "My Awesome Project"
 *         description: "A project that does amazing things"
 *         language: "TypeScript"
 *         framework: "React"
 *         projectType: "WEB_APPLICATION"
 *         projectMotivation: "builder"
 *
 *     UpdateProjectDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Project name
 *         description:
 *           type: string
 *           description: Project description
 *         repositoryUrl:
 *           type: string
 *           format: uri
 *           description: URL to the project's repository
 *         mainBranch:
 *           type: string
 *           description: Main branch name
 *         localPath:
 *           type: string
 *           description: Local file system path
 *         language:
 *           type: string
 *           description: Primary programming language
 *         framework:
 *           type: string
 *           description: Framework used
 *         projectType:
 *           type: string
 *           enum: [WEB_APPLICATION, MOBILE_APPLICATION, LIBRARY, CLI_TOOL, API, OTHER]
 *           description: Type of project
 *         projectMotivation:
 *           type: string
 *           enum: [learner, contributor, builder]
 *           description: Motivation for the project
 *         templateId:
 *           type: integer
 *           description: Template ID if project is based on a template
 *         domain:
 *           type: string
 *           description: Business domain
 *         isPublic:
 *           type: boolean
 *           description: Whether the project is public
 *         isActive:
 *           type: boolean
 *           description: Whether the project is active
 *       example:
 *         name: "Updated Project Name"
 *         description: "Updated description"
 *         language: "JavaScript"
 *
 *     CreateObjectiveDto:
 *       type: object
 *       required:
 *         - title
 *       properties:
 *         title:
 *           type: string
 *           description: Objective title
 *         description:
 *           type: string
 *           description: Objective description
 *         priority:
 *           type: integer
 *           description: Priority level (1-5)
 *         status:
 *           type: string
 *           description: Current status
 *       example:
 *         title: "Implement user authentication"
 *         description: "Add login, registration, and password reset functionality"
 *         priority: 1
 *         status: "TODO"
 *
 *     CreateTechStackDto:
 *       type: object
 *       required:
 *         - technology
 *       properties:
 *         category:
 *           type: string
 *           description: Category (frontend, backend, etc.)
 *         technology:
 *           type: string
 *           description: Technology name
 *         version:
 *           type: string
 *           description: Version
 *       example:
 *         category: "frontend"
 *         technology: "React"
 *         version: "18.2.0"
 *
 *     CreateSlideDto:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Slide title
 *         content:
 *           type: string
 *           description: Slide content
 *         order:
 *           type: integer
 *           description: Display order
 *       example:
 *         title: "Project Overview"
 *         content: "# Project Overview\n\nThis project aims to solve..."
 *         order: 1
 *
 *     CreateTestDto:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: Test name
 *         type:
 *           type: string
 *           description: Test type (unit, integration, e2e)
 *         status:
 *           type: string
 *           description: Test status
 *         code:
 *           type: string
 *           description: Test code
 *       example:
 *         name: "User Authentication Test"
 *         type: "unit"
 *         status: "passing"
 *         code: "test('login works', () => { ... });"
 */
