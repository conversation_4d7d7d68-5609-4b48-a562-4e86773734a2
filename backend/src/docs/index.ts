/**
 * Swagger documentation file index
 * Includes all swagger documentation from other files
 */

// Import all swagger docs
import './user.swagger';
import './project.swagger';
import './conversation.swagger';
import './blog.swagger';
import './social.swagger';

/**
 * @swagger
 * tags:
 *   - name: Users
 *     description: User management
 *   - name: Projects
 *     description: Project management
 *   - name: Conversations
 *     description: AI conversations
 *   - name: Blog
 *     description: Blog management
 *   - name: Social
 *     description: Social features
 *
 * components:
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */
