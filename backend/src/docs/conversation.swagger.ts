/**
 * Swagger documentation for Conversation DTOs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateMessageDto:
 *       type: object
 *       required:
 *         - role
 *         - content
 *       properties:
 *         role:
 *           type: string
 *           description: Role of the message sender (e.g., user, assistant, system)
 *         content:
 *           type: string
 *           description: Message content
 *       example:
 *         role: "user"
 *         content: "How can I implement a REST API in Node.js?"
 *
 *     CreateConversationDto:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Conversation title
 *         initialMessage:
 *           type: string
 *           description: Initial message to start the conversation
 *         conversationType:
 *           type: string
 *           default: "chat"
 *           description: Type of conversation
 *         projectId:
 *           type: integer
 *           description: Associated project ID
 *         keyObjective:
 *           type: string
 *           description: Main goal of the conversation
 *         category:
 *           type: string
 *           enum: [chat, agent, code_assist, debugging, planning, learning, other]
 *           description: Conversation category
 *         agentStatus:
 *           type: string
 *           enum: [initializing, running, paused, completed, failed, waiting, cancelled]
 *           description: Initial agent status
 *         agentAllowedActions:
 *           type: object
 *           description: Actions the agent is allowed to perform
 *         parentId:
 *           type: integer
 *           description: Parent conversation ID for threaded conversations
 *       example:
 *         title: "API Design Discussion"
 *         initialMessage: "I need help designing a REST API for my e-commerce application"
 *         conversationType: "chat"
 *         category: "planning"
 *
 *     UpdateConversationDto:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Conversation title
 *         status:
 *           type: string
 *           description: Conversation status (active, archived)
 *         projectId:
 *           type: integer
 *           description: Associated project ID
 *         keyObjective:
 *           type: string
 *           description: Main goal of the conversation
 *         category:
 *           type: string
 *           enum: [chat, agent, code_assist, debugging, planning, learning, other]
 *           description: Conversation category
 *         userRating:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: User rating (1-5 stars)
 *         userFeedback:
 *           type: string
 *           description: Detailed user feedback
 *         isPinned:
 *           type: boolean
 *           description: Whether the conversation is pinned
 *         agentStatus:
 *           type: string
 *           enum: [initializing, running, paused, completed, failed, waiting, cancelled]
 *           description: Agent status
 *         agentProgress:
 *           type: object
 *           description: Structured progress data
 *         agentIterationCount:
 *           type: integer
 *           description: Number of agent iterations
 *         agentAllowedActions:
 *           type: object
 *           description: Actions the agent is allowed to perform
 *         agentCommandsExecuted:
 *           type: array
 *           description: Commands executed by the agent
 *         agentCompletionSummary:
 *           type: string
 *           description: Summary of agent accomplishments
 *         lastActivityType:
 *           type: string
 *           enum: [thinking, command, file_edit, complete, error, user_input]
 *           description: Type of last activity
 *       example:
 *         title: "Updated API Discussion"
 *         status: "active"
 *         userRating: 5
 *         isPinned: true
 *
 *     ChatRequestDto:
 *       type: object
 *       required:
 *         - messages
 *         - taskType
 *       properties:
 *         messages:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/CreateMessageDto'
 *           description: Conversation message history
 *         conversationId:
 *           type: integer
 *           description: Existing conversation ID (optional)
 *         model:
 *           type: string
 *           description: Specific model to use
 *         taskType:
 *           type: string
 *           default: "general"
 *           description: Type of task
 *         maxTokens:
 *           type: integer
 *           default: 1000
 *           description: Maximum tokens for response
 *         temperature:
 *           type: number
 *           default: 0.7
 *           minimum: 0
 *           maximum: 1
 *           description: Temperature for response generation
 *       example:
 *         messages:
 *           - role: "user"
 *             content: "How can I implement authentication in my Node.js app?"
 *         taskType: "code_assist"
 *         maxTokens: 2000
 *         temperature: 0.7
 */
