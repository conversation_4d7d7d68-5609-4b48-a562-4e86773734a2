declare module 'jwk-to-pem' {
  interface RsaJwk {
    kty: 'RSA';
    n: string;
    e: string;
    kid?: string;
    use?: string;
    alg?: string;
    [key: string]: any;
  }

  interface EcJwk {
    kty: 'EC';
    crv: string;
    x: string;
    y: string;
    kid?: string;
    use?: string;
    alg?: string;
    [key: string]: any;
  }

  type Jwk = RsaJwk | EcJwk;

  function jwkToPem(jwk: Jwk, options?: { private?: boolean }): string;
  export = jwkToPem;
}
