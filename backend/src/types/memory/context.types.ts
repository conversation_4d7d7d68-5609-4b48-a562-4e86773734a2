// Core types for memory architecture
import { ContextType, ContextFormat, SlideDeckType } from '../../generated/prisma';

/**
 * Token budget allocation for context blocks
 */
export interface TokenBudget {
  total: number;
  static: number;
  dynamic: number;
  allocated: {
    [key in ContextType]?: number;
  };
  remaining: number;
}

/**
 * Interface for context providers
 */
export interface ContextProvider {
  getContext(projectId: number, options?: ContextProviderOptions): Promise<ContextBlock>;
  getPriority(): number;
  getContextType(): ContextType;
  getMaxTokens(): number;
}

/**
 * Options for context providers
 */
export interface ContextProviderOptions {
  tokenLimit?: number;
  includeInactive?: boolean;
  userId?: number;
  conversationId?: number;
}

/**
 * A block of context content
 */
export interface ContextBlock {
  type: ContextType;
  content: string;
  format: ContextFormat;
  tokenCount: number;
  metadata?: Record<string, any>;
}

/**
 * Assembled context with multiple blocks
 */
export interface AssembledContext {
  blocks: ContextBlock[];
  totalTokens: number;
  strategy: string;
  timestamp: Date;
}

/**
 * Result of updating a context
 */
export interface ContextUpdateResult {
  success: boolean;
  previousVersion?: number;
  newVersion?: number;
  error?: string;
}

/**
 * Options for slide deck operations
 */
export interface SlideDeckOptions {
  includeSlideDeck?: boolean;
  includeSlides?: boolean;
}

/**
 * Data for creating a slide deck
 */
export interface SlideDeckData {
  projectId: number;
  title: string;
  description?: string;
  type: SlideDeckType;
}

/**
 * Data for slide operations
 */
export interface SlideData {
  slideDeckId: number;
  title?: string;
  content: string;
  order: number;
  format: ContextFormat;
  tokenCount?: number;
}

/**
 * Options for summary operations
 */
export interface SummaryOptions {
  recursive?: boolean;
  depth?: number;
  includeStale?: boolean;
}

/**
 * File summary data
 */
export interface FileSummaryData {
  projectId: number;
  filePath: string;
  summary: string;
  tokenCount?: number;
  gitHash?: string;
}

/**
 * Token usage tracking
 */
export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

/**
 * Context selection strategy
 */
export interface ContextSelectionStrategy {
  name: string;
  selectContext(availableContexts: ContextBlock[], tokenBudget: TokenBudget): ContextBlock[];
}

/**
 * Chat context data
 */
export interface ChatContextData {
  conversationId: number;
  contextSnapshot: Record<string, any>;
  contextSelectionRationale?: string;
  tokenUsage?: number;
}

/**
 * User context preference data
 */
export interface UserContextPreferenceData {
  userId: number;
  contextType: ContextType;
  isPinned?: boolean;
  isDisabled?: boolean;
  priority?: number;
}
