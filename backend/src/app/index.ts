import compression from 'compression';
import cors from 'cors';
import express, { Express, Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import swaggerUi from 'swagger-ui-express';

import { logger } from '../common/logger';
import { specs } from '../common/swagger';
import { databaseService, database } from '../db';
import routes from '../routes';

class App {
  public app: Express;

  constructor() {
    this.app = express();
    this.configureMiddleware();
    this.configureRoutes();
    this.configureErrorHandling();
  }

  /**
   * Configure middleware
   */
  private configureMiddleware(): void {
    // Body parsers
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Security
    this.app.use(cors());
    this.app.use(
      helmet({
        contentSecurityPolicy: process.env.NODE_ENV === 'production' ? undefined : false,
      }),
    );

    // Compression
    this.app.use(compression());

    // Logging middleware for each request
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      logger.info(`${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * Configure API routes
   */
  private configureRoutes(): void {
    // Health check endpoint
    this.app.get('/health', async (req: Request, res: Response) => {
      const dbCheck = await database.healthCheck();
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: dbCheck ? 'connected' : 'disconnected',
      });
    });

    // API documentation
    this.app.use(
      '/api-docs',
      swaggerUi.serve,
      swaggerUi.setup(specs, {
        explorer: true,
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'KAPI API Documentation',
      }),
    );

    // API routes
    this.app.use('/api', routes);

    // Not found handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({ status: 'error', message: 'Resource not found' });
    });
  }

  /**
   * Configure error handling
   */
  private configureErrorHandling(): void {
    this.app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error(`Error: ${err.message}`);
      logger.error(err.stack || 'No stack trace available');

      res.status(500).json({
        status: 'error',
        message: 'Internal server error',
        ...(process.env.NODE_ENV !== 'production' && { details: err.message, stack: err.stack }),
      });
    });
  }

  /**
   * Initialize the application
   */
  public async init(): Promise<void> {
    try {
      // Initialize database connection
      await databaseService.init();

      logger.info('Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application:', error);
      throw error;
    }
  }

  /**
   * Gracefully shutdown the application
   */
  public async shutdown(): Promise<void> {
    try {
      // Close database connection
      await databaseService.shutdown();

      logger.info('Application shutdown successfully');
    } catch (error) {
      logger.error('Error during application shutdown:', error);
      throw error;
    }
  }
}

export default App;
