import { Router, Request, Response, NextFunction } from 'express';
import { body, param, query } from 'express-validator';

import { clerkAuthMiddleware } from '../middleware/clerk.middleware';
import { memoryContextMiddleware } from '../middleware/memory.middleware';
import { validateRequest } from '../middleware/validation.middleware';

const router = Router();

// Apply memory middleware to all routes
router.use(memoryContextMiddleware);
router.use(clerkAuthMiddleware);

/**
 * Get context for a project
 */
router.get(
  '/context/:projectId',
  [
    param('projectId').isInt().toInt(),
    query('includeTypes').optional().isString(),
    validateRequest,
  ],
  async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const includeTypes = req.query.includeTypes
        ? (req.query.includeTypes as string).split(',')
        : undefined;

      const contextManager = (req as any).memoryServices.contextManager;
      const context = await contextManager.assembleContext(projectId, {
        userId: (req as any).user.id,
        forceIncludeTypes: includeTypes,
      });

      res.json({
        success: true,
        data: context,
      });
    } catch (error) {
      console.error('Error getting context:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

/**
 * Get context debug info
 */
router.get(
  '/context/:projectId/debug',
  [param('projectId').isInt().toInt(), validateRequest],
  async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const aiContextIntegration = (req as any).memoryServices.aiContextIntegration;

      const debugInfo = await aiContextIntegration.getContextDebugInfo(projectId, {
        userId: (req as any).user.id,
      });

      res.json({
        success: true,
        data: debugInfo,
      });
    } catch (error) {
      console.error('Error getting context debug info:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

/**
 * Update business context
 */
router.put(
  '/context/:projectId/business',
  [
    param('projectId').isInt().toInt(),
    body('slideDeckId').isInt().toInt(),
    body('slides').isArray(),
    validateRequest,
  ],
  async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const { slideDeckId, slides } = req.body;

      const businessContext = (req as any).memoryServices.userContext;
      const updatedContext = await businessContext.updateBusinessContext(
        projectId,
        slideDeckId,
        slides,
      );

      res.json({
        success: true,
        data: updatedContext,
      });
    } catch (error) {
      console.error('Error updating business context:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

/**
 * Get business context slides
 */
router.get(
  '/context/:projectId/business/slides',
  [param('projectId').isInt().toInt(), validateRequest],
  async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId, 10);
      const businessContext = (req as any).memoryServices.userContext;

      const slides = await businessContext.getBusinessContextSlides(projectId);

      res.json({
        success: true,
        data: slides,
      });
    } catch (error) {
      console.error('Error getting business context slides:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

export default router;
