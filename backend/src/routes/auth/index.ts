import { Router } from 'express';
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';
import { authController } from '../../api/auth/controllers';
import { authRateLimiter } from '../../common/middleware/security.middleware';

const router = Router();

/**
 * Get the current user's profile
 * Endpoint: GET /api/auth/me
 */
router.get('/me', authRateLimiter, unifiedAuthMiddleware, (req, res) => authController.getCurrentUser(req, res));

export default router;
