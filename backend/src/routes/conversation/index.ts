/**
 * Unified Conversation Router
 *
 * This router consolidates both conversation and conversation task routes
 * to reduce middleware duplication and simplify the architecture.
 */

import { Router, Request, Response, RequestHandler } from 'express';
import { conversationService } from '../../services';
import { ConversationStatus } from '../../generated/prisma';
import { ConversationUpdate } from '../../common/types/conversation.types';
import { AuthenticatedRequest } from '../../common/types/authenticated-request';
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';
import { dynamicRateLimitMiddleware } from '../../middleware/rate-limit.middleware';
import { logger } from '../../common/logger';
import fs from 'fs/promises';
import path from 'path';
import yaml from 'js-yaml';

const router = Router();

// Apply middleware at the router level to avoid duplication
router.use(unifiedAuthMiddleware as any);

/**
 * Helper function to safely get userId as a number
 */
function getUserId(req: Request): number {
  const authReq = req as AuthenticatedRequest;
  return authReq.userId;
}

// Define task request types
interface TaskRequest {
  prompt: string;
  conversationId: number;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

interface CodeGenRequest extends TaskRequest {
  language: string;
  context?: string;
}

interface MockupRequest extends TaskRequest {
  description?: string;
  style?: string;
  components?: string[];
  width?: number;
  height?: number;
}

interface TestCaseRequest extends TaskRequest {
  code: string;
  framework: string;
  coverageLevel?: string;
}

interface SlideRequest extends TaskRequest {
  topic: string;
  numSlides?: number;
  format?: string;
}

// Load task prompts from config
let TASK_PROMPTS: any = {};
(async () => {
  try {
    const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
    const fileContent = await fs.readFile(configPath, 'utf8');
    TASK_PROMPTS = yaml.load(fileContent) || {};
  } catch (error) {
    logger.error('Error loading task prompts:', error);
    TASK_PROMPTS = {};
  }
})();

// ===== CONVERSATION ROUTES =====

/**
 * @swagger
 * /conversations:
 *   get:
 *     summary: List conversations
 *     description: Get a list of conversations for the authenticated user
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of items to skip
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of items to return
 *     responses:
 *       200:
 *         description: List of conversations
 *       500:
 *         description: Server error
 */
router.get('/', (async (req: Request, res: Response) => {
  try {
    const skip = parseInt(req.query.skip as string, 10) || 0;
    const limit = Math.min(parseInt(req.query.limit as string, 10) || 100, 100);
    const userId = getUserId(req);

    const result = await conversationService.getUserConversations(userId, undefined, skip, limit);

    res.json({
      conversations: result.conversations,
      total: result.total,
      skip,
      limit,
    });
  } catch (error: any) {
    logger.error('Error listing conversations:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to list conversations',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/{conversationId}:
 *   get:
 *     summary: Get conversation
 *     description: Get a conversation by ID
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *       - in: query
 *         name: includeMessages
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include messages in the response
 *     responses:
 *       200:
 *         description: Conversation details
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get('/:conversationId', (async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.conversationId, 10);
    const includeMessages = req.query.includeMessages === 'true';
    const userId = getUserId(req);

    // Get the conversation
    const conversation = await conversationService.getConversation(conversationId, userId);

    if (!conversation) {
      return res.status(404).json({
        status: 'error',
        message: `Conversation with ID ${conversationId} not found`,
      });
    }

    // Get messages if requested
    if (includeMessages) {
      const messages = await conversationService.getMessages(conversationId);

      return res.json({
        ...conversation,
        messages,
      });
    }

    return res.json(conversation);
  } catch (error: any) {
    logger.error('Error getting conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get conversation',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/{conversationId}:
 *   put:
 *     summary: Update conversation
 *     description: Update a conversation's details
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, archived, deleted]
 *     responses:
 *       200:
 *         description: Conversation updated successfully
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.put('/:conversationId', (async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.conversationId, 10);
    const updateData: ConversationUpdate = req.body;
    const userId = getUserId(req);

    // Check if conversation exists and belongs to user
    const conversation = await conversationService.getConversation(conversationId, userId);

    if (!conversation) {
      return res.status(404).json({
        status: 'error',
        message: `Conversation with ID ${conversationId} not found`,
      });
    }

    // Update the conversation
    const updatedConversation = await conversationService.updateConversation(
      conversationId,
      updateData,
    );

    res.json(updatedConversation);
  } catch (error: any) {
    logger.error('Error updating conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update conversation',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/{conversationId}:
 *   delete:
 *     summary: Delete conversation
 *     description: Delete a conversation and all its messages
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *     responses:
 *       204:
 *         description: Conversation deleted successfully
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.delete('/:conversationId', (async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.conversationId, 10);
    const userId = getUserId(req);

    // Check if conversation exists and belongs to user
    const conversation = await conversationService.getConversation(conversationId, userId);

    if (!conversation) {
      return res.status(404).json({
        status: 'error',
        message: `Conversation with ID ${conversationId} not found`,
      });
    }

    // Delete the conversation
    await conversationService.deleteConversation(conversationId);

    res.status(204).send();
  } catch (error: any) {
    logger.error('Error deleting conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete conversation',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/{conversationId}/messages:
 *   get:
 *     summary: Get conversation messages
 *     description: Get messages for a conversation
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of items to skip
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of items to return
 *     responses:
 *       200:
 *         description: List of messages
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get('/:conversationId/messages', (async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.conversationId, 10);
    const skip = parseInt(req.query.skip as string, 10) || 0;
    const limit = parseInt(req.query.limit as string, 10) || 100;
    const userId = getUserId(req);

    // Check if conversation exists and belongs to user
    const conversation = await conversationService.getConversation(conversationId, userId);

    if (!conversation) {
      return res.status(404).json({
        status: 'error',
        message: `Conversation with ID ${conversationId} not found`,
      });
    }

    // Get messages
    const messages = await conversationService.getMessages(conversationId, skip, limit);

    res.json(messages);
  } catch (error: any) {
    logger.error('Error getting conversation messages:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get conversation messages',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

// Apply rate limiting middleware only to routes that make LLM calls
router.use(dynamicRateLimitMiddleware as any);

/**
 * @swagger
 * /conversations:
 *   post:
 *     summary: Create conversation
 *     description: Create a new conversation, optionally with an initial message
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               initialMessage:
 *                 type: string
 *                 description: Optional initial message to start the conversation
 *               title:
 *                 type: string
 *                 description: Optional title for the conversation
 *               model:
 *                 type: string
 *                 description: Optional model ID to use
 *               taskType:
 *                 type: string
 *                 default: general
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       201:
 *         description: Conversation created successfully
 *       500:
 *         description: Server error
 */
router.post('/', (async (req: Request, res: Response) => {
  try {
    const userId = getUserId(req);
    const {
      initialMessage,
      model,
      taskType = 'general',
      maxTokens = 1000,
      temperature = 0.7,
      title,
      ...restData
    } = req.body;

    // If no initial message, just create a conversation with a temporary title
    if (!initialMessage) {
      const conversation = await conversationService.createConversation(userId, {
        title: title || 'New Conversation',
        ...restData,
      });

      return res.status(201).json({
        id: conversation.id,
        userId: conversation.user_id,
        title: conversation.title,
        status: conversation.status,
        createdAt: conversation.created_at,
        updatedAt: conversation.updated_at,
      });
    }

    // If initial message is provided, create conversation with message and get response
    const result = await conversationService.createConversationWithMessage(userId, initialMessage, {
      modelId: model,
      taskType,
      maxTokens,
      temperature,
      settings: restData,
    });

    res.status(201).json(result);
  } catch (error: any) {
    logger.error('Error creating conversation:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create conversation',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/invoke-model:
 *   post:
 *     summary: Invoke model directly
 *     description: Send a prompt to a model without creating a persistent conversation
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               modelId:
 *                 type: string
 *                 description: Optional specific model to use
 *               taskType:
 *                 type: string
 *                 default: general
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       400:
 *         description: Bad request - missing required parameters
 *       500:
 *         description: Server error
 */
router.post('/invoke-model', (async (req: Request, res: Response) => {
  try {
    const { prompt, modelId, taskType = 'general', maxTokens = 1000, temperature = 0.7 } = req.body;
    const userId = getUserId(req);

    // Validate required parameters
    if (!prompt) {
      return res.status(400).json({
        status: 'error',
        message: 'prompt is required',
      });
    }

    // Create a temporary conversation with the prompt and get a response
    const result = await conversationService.createConversationWithMessage(userId, prompt, {
      modelId,
      taskType,
      maxTokens,
      temperature,
      settings: { temporary: true },
    });

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during model invocation',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error invoking model:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to invoke model',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/invoke-for-task:
 *   post:
 *     summary: Invoke model for specific task
 *     description: Send a prompt to the best model for a specific task type
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - taskType
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               taskType:
 *                 type: string
 *                 description: The type of task (chat, code_generation, etc.)
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       400:
 *         description: Bad request - missing required parameters
 *       404:
 *         description: No suitable model found for task
 *       500:
 *         description: Server error
 */
router.post('/invoke-for-task', (async (req: Request, res: Response) => {
  try {
    const { prompt, taskType, maxTokens = 1000, temperature = 0.7 } = req.body;
    const userId = getUserId(req);

    // Validate required parameters
    if (!prompt) {
      return res.status(400).json({
        status: 'error',
        message: 'prompt is required',
      });
    }

    if (!taskType) {
      return res.status(400).json({
        status: 'error',
        message: 'taskType is required',
      });
    }

    // Create a temporary conversation with the prompt and get a response
    // The conversation service will automatically select the best model for the task
    const result = await conversationService.createConversationWithMessage(userId, prompt, {
      taskType,
      maxTokens,
      temperature,
      settings: { temporary: true },
    });

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during task invocation',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error invoking model for task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to invoke model for task',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

// ===== TASK-SPECIFIC ROUTES =====

/**
 * @swagger
 * /conversations/tasks/chat:
 *   post:
 *     summary: General chat task
 *     description: General chat interaction with the LLM
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post('/tasks/chat', (async (req: Request, res: Response) => {
  try {
    const {
      prompt,
      conversationId,
      model,
      maxTokens = 1000,
      temperature = 0.7,
    } = req.body as TaskRequest;
    const userId = getUserId(req);

    const result = await conversationService.addUserMessageAndGetResponse(conversationId, prompt, {
      modelId: model,
      taskType: 'chat',
      maxTokens,
      temperature,
      userId: parseInt(userId.toString(), 10),
    });

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during chat',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error in chat task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process chat task',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/tasks/code-planning:
 *   post:
 *     summary: Code planning task
 *     description: Generate code architecture and planning
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 2000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post('/tasks/code-planning', (async (req: Request, res: Response) => {
  try {
    const {
      prompt,
      conversationId,
      model,
      maxTokens = 2000,
      temperature = 0.7,
    } = req.body as TaskRequest;
    const userId = getUserId(req);

    // Get the system prompt from the config
    const systemPrompt =
      TASK_PROMPTS.code_planning?.system ||
      'You are an expert software architect. Your task is to create detailed plans and architecture for software systems.';

    // Add system prompt to conversation
    await conversationService.addMessage(conversationId, 'system', systemPrompt);

    const result = await conversationService.addUserMessageAndGetResponse(conversationId, prompt, {
      modelId: model,
      taskType: 'code_planning',
      maxTokens,
      temperature,
      userId: parseInt(userId.toString(), 10),
    });

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during code planning',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error in code planning task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process code planning task',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/tasks/code-generation:
 *   post:
 *     summary: Code generation task
 *     description: Generate code based on requirements
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - language
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               language:
 *                 type: string
 *                 description: Programming language to generate
 *               context:
 *                 type: string
 *                 description: Additional context for code generation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 2000
 *               temperature:
 *                 type: number
 *                 default: 0.3
 *     responses:
 *       200:
 *         description: Model response
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post('/tasks/code-generation', (async (req: Request, res: Response) => {
  try {
    const {
      prompt,
      conversationId,
      language,
      context,
      model,
      maxTokens = 2000,
      temperature = 0.3,
    } = req.body as CodeGenRequest;
    const userId = getUserId(req);

    // Get the system prompt from the config
    const systemPrompt =
      TASK_PROMPTS.code_generation?.system ||
      'You are an expert software developer. Your task is to generate high-quality, well-documented code based on requirements.';

    // Enhance the prompt with language and context information
    let enhancedPrompt = `Generate ${language} code for: ${prompt}`;
    if (context) {
      enhancedPrompt += `\n\nAdditional context: ${context}`;
    }

    // Add system prompt to conversation
    await conversationService.addMessage(conversationId, 'system', systemPrompt);

    const result = await conversationService.addUserMessageAndGetResponse(
      conversationId,
      enhancedPrompt,
      {
        modelId: model,
        taskType: 'code_generation',
        maxTokens,
        temperature,
        userId: parseInt(userId.toString(), 10),
      },
    );

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during code generation',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error in code generation task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process code generation task',
      details: error?.message || 'Unknown error',
    });
  }
}) as RequestHandler);

/**
 * @swagger
 * /conversations/{conversationId}/stream:
 *   post:
 *     summary: Stream AI response
 *     description: Stream AI response using Server-Sent Events (SSE)
 *     tags: [Conversations]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Conversation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *               taskType:
 *                 type: string
 *                 default: chat
 *               memoryCount:
 *                 type: integer
 *                 description: Number of previous messages to include in context
 *     responses:
 *       200:
 *         description: SSE stream of AI response
 *         content:
 *           text/event-stream:
 *             schema:
 *               type: string
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.post('/:conversationId/stream', (async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.conversationId, 10);
    const { prompt, model, maxTokens = 1000, temperature = 0.7, taskType = 'chat', memoryCount } = req.body;
    const userId = getUserId(req);

    // Validate conversation exists and belongs to user
    const conversation = await conversationService.getConversation(conversationId, userId);
    if (!conversation) {
      return res.status(404).json({
        status: 'error',
        message: `Conversation with ID ${conversationId} not found`,
      });
    }

    // Set up SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // Disable Nginx buffering
    
    // Flush headers immediately
    res.flushHeaders();

    // Add user message to conversation
    await conversationService.addMessage(conversationId, 'user', prompt);

    // Get the stream from conversation service
    const stream = await conversationService.streamResponse(conversationId, prompt, {
      modelId: model,
      taskType,
      maxTokens,
      temperature,
      memoryCount,
    });

    let fullContent = '';
    let usage: any = null;
    let modelUsed = model;
    let classifiedTaskType = taskType; // Default to request taskType
    let routedBy = 'default-routing';

    try {
      // Process the stream
      for await (const chunk of stream) {
        if (chunk.content) {
          fullContent += chunk.content;
          // Send content chunk to client
          res.write(`data: ${JSON.stringify({ type: 'content', content: chunk.content })}\n\n`);
        }

        if (chunk.done && chunk.usage) {
          usage = chunk.usage;
          modelUsed = chunk.model || modelUsed;
          
          // Use the classified task type from the stream if available
          if (chunk.taskType) {
            classifiedTaskType = chunk.taskType;
          }
          if (chunk.routedBy) {
            routedBy = chunk.routedBy;
          }
          
          // Send completion event with usage data and task classification info
          res.write(`data: ${JSON.stringify({ 
            type: 'done', 
            taskType: classifiedTaskType, // Use the intelligently classified task type
            routedBy: routedBy, // Indicate the routing method used
            usage: {
              promptTokens: usage.prompt_tokens,
              completionTokens: usage.completion_tokens,
              totalTokens: usage.total_tokens,
              cost: usage.cost,
              durationMs: usage.duration_ms
            },
            model: modelUsed
          })}\n\n`);
        }
      }

      // Save the complete assistant message with token usage
      if (fullContent) {
        await conversationService.addMessage(conversationId, 'assistant', fullContent, {
          model: modelUsed,
          promptTokens: usage?.prompt_tokens,
          completionTokens: usage?.completion_tokens,
          cost: usage?.cost,
          durationMs: usage?.duration_ms,
        });
      }

      // Send final close event
      res.write(`data: ${JSON.stringify({ type: 'close' })}\n\n`);
      res.end();
    } catch (error) {
      logger.error('Error during streaming:', error);
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })}\n\n`);
      res.end();
    }
  } catch (error: any) {
    logger.error('Error setting up stream:', error);
    // If headers haven't been sent, send JSON error
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'Failed to start streaming',
        details: error?.message || 'Unknown error',
      });
    } else {
      // If streaming has started, send error event
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        error: error?.message || 'Unknown error' 
      })}\n\n`);
      res.end();
    }
  }
}) as RequestHandler);

// Export the router
export default router;
