import { Router, Request, Response } from 'express';
import { clerkService } from '../../services/clerk.service';

// Define interfaces for Clerk webhook event types
interface ClerkWebhookEvent {
  type: string;
  data: any;
  object: string;
}

const router = Router();

/**
 * Handle Clerk webhook events
 * Endpoint: POST /api/webhooks/clerk
 */
router.post('/clerk', async (req: Request, res: Response) => {
  try {
    // Get the webhook signature data from the request headers
    const svixId = req.headers['svix-id'] as string;
    const svixTimestamp = req.headers['svix-timestamp'] as string;
    const svixSignature = req.headers['svix-signature'] as string;

    // Get the raw body from the request
    // The rawBodyMiddleware should be applied to this route in app.ts
    const rawBody = (req as any).rawBody || JSON.stringify(req.body);

    // Verify the webhook signature
    const isValid = clerkService.verifyWebhookSignature(
      svixId,
      svixTimestamp,
      svixSignature,
      rawBody,
    );

    if (!isValid) {
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }

    // Process the webhook event
    const event = req.body as ClerkWebhookEvent;
    console.log(`Received Clerk webhook event: ${event.type}`);

    const { type, data } = event;

    switch (type) {
      case 'user.created':
        // Handle user creation event
        console.log('User created:', data.id);
        // Create a user in your database with data.id as the clerk_id
        // Include relevant user data from the webhook
        break;

      case 'user.updated':
        // Handle user update event
        console.log('User updated:', data.id);
        // Update the user in your database
        break;

      case 'user.deleted':
        // Handle user deletion event
        console.log('User deleted:', data.id);
        // Delete or deactivate the user in your database
        break;

      case 'session.created':
        // Handle session creation event
        console.log('Session created for user:', data.user_id);
        // You might want to log this for analytics
        break;

      case 'session.revoked':
        // Handle session revocation event
        console.log('Session revoked for user:', data.user_id);
        // You might want to invalidate any internal sessions as well
        break;

      case 'organization.created':
        // Handle organization creation event
        console.log('Organization created:', data.id);
        // Create an organization in your database
        break;

      case 'organization.updated':
        // Handle organization update event
        console.log('Organization updated:', data.id);
        // Update the organization in your database
        break;

      case 'organization.deleted':
        // Handle organization deletion event
        console.log('Organization deleted:', data.id);
        // Delete or deactivate the organization in your database
        break;

      case 'organizationMembership.created':
        // Handle organization membership creation event
        console.log(
          'Organization membership created:',
          data.organization_id,
          data.public_user_data.user_id,
        );
        // Add the user to the organization in your database
        break;

      default:
        console.log('Unhandled webhook event type:', type);
    }

    // Acknowledge the webhook event
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error processing Clerk webhook:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
