import { Router, Request, Response } from 'express';
import { userRepository } from '../../db/repositories/user.repository';
import { UserCreate, UserUpdate } from '../../common/types';
// import { userOnboardingService } from '../../services/user-onboarding.service';
import { userService } from '../../services/user.service';
import { body, validationResult } from 'express-validator';
import { logger } from '../../common/logger';
import { clerkAuthMiddleware } from '../../middleware/clerk.middleware';

const router = Router();

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get all users
 *     description: Retrieve a list of all users. Requires admin privileges.
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip for pagination
 *       - in: query
 *         name: take
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of records to take for pagination
 *     responses:
 *       200:
 *         description: A list of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 total:
 *                   type: integer
 *                   description: Total number of users
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin privileges
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const skip = parseInt(req.query.skip as string, 10) || 0;
    const take = parseInt(req.query.take as string, 10) || 20;

    const users = await userRepository.findAll({ skip, take });
    const total = await userRepository.count();

    res.json({ users, total });
  } catch (error: any) {
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve users',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Get user by ID
 *     description: Retrieve a user by their ID
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    const user = await userRepository.findById(id);

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: `User with ID ${id} not found`,
      });
    }

    return res.json(user);
  } catch (error: any) {
    return res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve user',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /users:
 *   post:
 *     summary: Create a new user
 *     description: Create a new user in the system
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserCreate'
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       400:
 *         description: Bad request - invalid data
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const userData: UserCreate = req.body;

    const user = await userRepository.create(userData);

    res.status(201).json(user);
  } catch (error: any) {
    res.status(500).json({
      status: 'error',
      message: 'Failed to create user',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /users/{id}:
 *   patch:
 *     summary: Update a user
 *     description: Update an existing user's information
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserUpdate'
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);
    const updateData: UserUpdate = req.body;

    const updatedUser = await userRepository.update(id, updateData);

    if (!updatedUser) {
      return res.status(404).json({
        status: 'error',
        message: `User with ID ${id} not found`,
      });
    }

    return res.json(updatedUser);
  } catch (error: any) {
    return res.status(500).json({
      status: 'error',
      message: 'Failed to update user',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /users/{id}/profile:
 *   get:
 *     summary: Get user profile
 *     description: Retrieve a user's profile information
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User profile details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserProfile'
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id/profile', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    const user = await userRepository.findById(id);

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: `User with ID ${id} not found`,
      });
    }

    // Map user to profile response
    const profile = {
      email: user.email,
      username: user.username || undefined,
      first_name: user.first_name || undefined,
      last_name: user.last_name || undefined,
      bio: user.bio || undefined,
      profile_image_url: user.profile_image_url || undefined,
      preferred_ide: user.preferred_ide || undefined,
      learning_style: user.learning_style || undefined,
      developer_strengths: (user.developer_strengths as string[]) || [],
      preferred_ai_models: (user.preferred_ai_models as string[]) || [],
      additional_info: user.additional_info || undefined,
      elo_rating: user.elo_rating,
      onboarding_completed: user.onboarding_completed,
    };

    return res.json(profile);
  } catch (error: any) {
    return res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve user profile',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /users/start-onboarding:
 *   post:
 *     summary: Start a new onboarding conversation
 *     description: Creates a new conversation specifically for user onboarding
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Onboarding conversation started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 conversation_id:
 *                   type: integer
 *                 message:
 *                   type: string
 *                 first_question:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
/* DISABLED FOR DEPLOYMENT
router.post('/start-onboarding', clerkAuthMiddleware, async (req: Request, res: Response) => {
  try {
    // Get user ID from authenticated user (set by the clerkAuthMiddleware)
    const userId = (req as any).userId || parseInt(req.query.userId as string, 10);

    if (!userId) {
      return res.status(401).json({
        status: 'error',
        message: 'User not authenticated',
      });
    }

    const result = await userOnboardingService.startOnboarding(userId);

    if (!result) {
      return res.status(500).json({
        status: 'error',
        message: 'Failed to start onboarding conversation',
      });
    }

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error(`Error starting onboarding conversation:`, error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to start onboarding conversation',
      details: error?.message || 'Unknown error',
    });
  }
});
*/

/**
 * @swagger
 * /users/complete-onboarding:
 *   post:
 *     summary: Process onboarding conversation and update user profile
 *     description: Processes an onboarding conversation and updates the user's profile with extracted information
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - conversation_id
 *             properties:
 *               conversation_id:
 *                 type: integer
 *                 description: ID of the onboarding conversation
 *     responses:
 *       200:
 *         description: Onboarding completed successfully
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post(
  '/complete-onboarding',
  clerkAuthMiddleware,
  [body('conversation_id').isInt().withMessage('Conversation ID must be an integer')],
  async (req: Request, res: Response) => {
    try {
      // Validate request body
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation error',
          errors: errors.array(),
        });
      }

      // Get user ID from authenticated user (set by the clerkAuthMiddleware)
      const userId = (req as any).userId || parseInt(req.query.userId as string, 10);

      if (!userId) {
        return res.status(401).json({
          status: 'error',
          message: 'User not authenticated',
        });
      }

      const { conversation_id } = req.body;

      // const success = await userOnboardingService.completeOnboarding(userId, conversation_id);
    const success = false; // Disabled for deployment

      if (!success) {
        return res.status(500).json({
          status: 'error',
          message: 'Failed to complete onboarding',
        });
      }

      const updatedUser = await userService.getById(userId);
      return res.status(200).json(updatedUser);
    } catch (error: any) {
      logger.error(`Error completing onboarding:`, error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to complete onboarding',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

/**
 * @swagger
 * /users/onboarding:
 *   post:
 *     summary: Mark onboarding as completed
 *     description: Marks the user's onboarding process as completed
 *     tags: [Users]
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Onboarding marked as completed
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/onboarding', clerkAuthMiddleware, async (req: Request, res: Response) => {
  try {
    // Get user ID from authenticated user (set by the clerkAuthMiddleware)
    const userId = (req as any).userId || parseInt(req.query.userId as string, 10);

    if (!userId) {
      return res.status(401).json({
        status: 'error',
        message: 'User not authenticated',
      });
    }

    const success = await userService.completeOnboarding(userId);

    if (!success) {
      return res.status(500).json({
        status: 'error',
        message: 'Failed to complete onboarding',
      });
    }

    const updatedUser = await userService.getById(userId);
    return res.status(200).json(updatedUser);
  } catch (error: any) {
    logger.error(`Error marking onboarding as completed:`, error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to complete onboarding',
      details: error?.message || 'Unknown error',
    });
  }
});

export default router;
