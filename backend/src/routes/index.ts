import { Router } from 'express';

import agentRoutes from '../api/agent/routes';
import codeAnalysisRoutes from '../api/code-analysis/routes';
import embeddingsRoutes from '../api/ai/embeddings.routes';
import multimodalRoutes from '../api/ai/multimodal.routes';

import audioRoutes from './audio.routes';
import authRoutes from './auth';
import blogRoutes from './blog';
import unifiedConversationRoutes from './conversation';
import conversationTasksRoutes from './conversation-tasks';
import documentationRoutes from './documentation.routes';
import healthRoutes from './health.routes';
import memoryRoutes from './memory.routes';
import paymentRoutes from './payment.routes';
import projectRoutes from './projects';
import qaRoutes from './qa.routes';
import templateRoutes from './template.routes';
import userRoutes from './users';
import webhookRoutes from './webhooks';
import * as socialRoutesModule from './social.routes';
import socketTestRoutes from './socket-test.routes';
import toolsRoutes from './tools.routes';
import taskRouterRoutes from './task-router';
import onboardingRoutes from './onboarding.routes';

const socialRoutes = socialRoutesModule.default;
import { initializeAdmin } from '../admin/admin';

const router = Router();

// Register routes
router.use('/users', userRoutes);
router.use('/auth', authRoutes);
router.use('/webhooks', webhookRoutes);
router.use('/conversations', unifiedConversationRoutes);
router.use('/tasks', conversationTasksRoutes); // Add conversation tasks routes
router.use('/documentation', documentationRoutes); // Add documentation routes
router.use('/projects', projectRoutes);
router.use('/memory', memoryRoutes);
router.use('/audio', audioRoutes); // Add audio routes (TTS/STT)
router.use('/blog', blogRoutes); // Add blog routes
router.use('/api/v1/templates', templateRoutes); // Add template routes
router.use('/api/v1/payment', paymentRoutes); // Add payment routes
router.use('/api/v1/qa', qaRoutes); // Add QA routes
router.use('/api/v1/social', socialRoutes); // Add social routes
router.use('/api/v1/agent', agentRoutes); // Add agent routes
router.use('/api/v1/code-analysis', codeAnalysisRoutes); // Add code analysis routes
router.use('/api/ai', embeddingsRoutes); // Add AI embeddings routes
router.use('/api/ai', multimodalRoutes); // Add AI multimodal routes
router.use('/api/tools', toolsRoutes); // Add Nova Sonic tools routes
router.use('/task-router', taskRouterRoutes); // Add intelligent task routing
router.use('/onboarding', onboardingRoutes); // Add onboarding profile routes
router.use('/health', healthRoutes); // Add health check endpoint
router.use('/socket-test', socketTestRoutes); // Add socket test endpoint

// Initialize and register admin routes
(async () => {
  try {
    console.log('[DEBUG] Starting admin initialization...');
    const { apiRouter, uiRouter } = await initializeAdmin();
    console.log('[DEBUG] Admin initialization completed successfully');

    console.log('[DEBUG] Mounting admin API routes at /admin');
    router.use('/admin', apiRouter);
    console.log('[DEBUG] Admin API routes mounted successfully');

    // Export the UI router to be mounted at the app level
    (router as any).uiRouter = uiRouter;
  } catch (error) {
    console.error('[ERROR] Failed to initialize admin routes:', error);
    // Log the stack trace to help identify where the error is coming from
    if (error instanceof Error) {
      console.error('[ERROR] Stack trace:', error.stack);
    }
  }
})();

export default router;
