/**
 * QA routes
 */
import { Router, Request, Response, NextFunction, RequestHandler } from 'express';

import { qaController } from '../api/qa/controllers';
import {
  CreateQuestionDto,
  UpdateQuestionDto,
  QuestionVoteDto,
  CreateAnswerDto,
  UpdateAnswerDto,
  AnswerVoteDto,
} from '../api/qa/dto';
import { AuthenticatedRequest } from '../common/types';
import { isAuthenticated } from '../middleware/auth.middleware';
import { validateDto } from '../middleware/validation/validate-dto.middleware';

const router = Router();

// Helper function to wrap controller methods
const wrapController = (
  handler: (req: AuthenticatedRequest, res: Response) => Promise<any>,
): RequestHandler => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await handler(req as AuthenticatedRequest, res);
    } catch (error) {
      next(error);
    }
  };
};

// Question routes
router.get(
  '/questions',
  isAuthenticated,
  wrapController(qaController.getQuestions.bind(qaController)),
);
router.post(
  '/questions',
  isAuthenticated,
  validateDto(CreateQuestionDto),
  wrapController(qaController.createQuestion.bind(qaController)),
);
router.get(
  '/questions/:id',
  isAuthenticated,
  wrapController(qaController.getQuestionById.bind(qaController)),
);
router.put(
  '/questions/:id',
  isAuthenticated,
  validateDto(UpdateQuestionDto),
  wrapController(qaController.updateQuestion.bind(qaController)),
);
router.delete(
  '/questions/:id',
  isAuthenticated,
  wrapController(qaController.deleteQuestion.bind(qaController)),
);

// Answer routes
router.get(
  '/questions/:questionId/answers',
  isAuthenticated,
  wrapController(qaController.getAnswers.bind(qaController)),
);
router.post(
  '/questions/:questionId/answers',
  isAuthenticated,
  validateDto(CreateAnswerDto),
  wrapController(qaController.createAnswer.bind(qaController)),
);
router.put(
  '/answers/:id',
  isAuthenticated,
  validateDto(UpdateAnswerDto),
  wrapController(qaController.updateAnswer.bind(qaController)),
);
router.delete(
  '/answers/:id',
  isAuthenticated,
  wrapController(qaController.deleteAnswer.bind(qaController)),
);
router.post(
  '/answers/:id/accept',
  isAuthenticated,
  wrapController(qaController.acceptAnswer.bind(qaController)),
);

// Vote routes
router.post(
  '/questions/:id/vote',
  isAuthenticated,
  validateDto(QuestionVoteDto),
  wrapController(qaController.voteQuestion.bind(qaController)),
);
router.post(
  '/answers/:id/vote',
  isAuthenticated,
  validateDto(AnswerVoteDto),
  wrapController(qaController.voteAnswer.bind(qaController)),
);

// Tag routes
router.get('/tags', isAuthenticated, wrapController(qaController.getTags.bind(qaController)));
router.get(
  '/tags/popular',
  isAuthenticated,
  wrapController(qaController.getPopularTags.bind(qaController)),
);

export default router;
