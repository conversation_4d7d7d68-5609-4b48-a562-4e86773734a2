import { Router, Request, Response } from 'express';

const router: Router = Router();

/**
 * GET /api/socket-test
 * Simple endpoint to test Socket.IO status without admin authentication
 */
router.get('/socket-test', (req: Request, res: Response) => {
  try {
    const io = (req.app as any).io;
    const novaSonicHandler = (req.app as any).novaSonicHandler;
    
    if (!io) {
      return res.status(503).json({ 
        error: 'Socket.IO not initialized',
        help: 'Make sure the server has started properly'
      });
    }

    // Get all namespaces
    const namespaces = [];
    if (io._nsps && typeof io._nsps.forEach === 'function') {
      io._nsps.forEach((namespace: any, name: string) => {
        namespaces.push({
          name: name,
          sockets: namespace.sockets?.size || 0,
          hasMiddleware: namespace._fns?.length > 0
        });
      });
    } else {
      // Try to get the default namespace at least
      const defaultNs = io.of('/');
      if (defaultNs) {
        namespaces.push({
          name: '/',
          sockets: defaultNs.sockets?.size || 0,
          hasMiddleware: false
        });
      }
      
      // Try to get Nova Sonic namespace
      const novaSonicNs = io.of('/ws/nova-sonic');
      if (novaSonicNs) {
        namespaces.push({
          name: '/ws/nova-sonic',
          sockets: novaSonicNs.sockets?.size || 0,
          hasMiddleware: false
        });
      }
    }

    const novaSonicNamespace = io.of('/ws/nova-sonic');
    
    const status = {
      socketIO: {
        initialized: true,
        engine: {
          clientsCount: io.engine.clientsCount,
          transports: io.engine.opts.transports,
          cors: io.engine.opts.cors
        },
        namespaces: namespaces
      },
      novaSonic: {
        namespace: {
          exists: !!novaSonicNamespace,
          name: novaSonicNamespace?.name,
          sockets: novaSonicNamespace?.sockets?.size || 0,
          adapter: novaSonicNamespace?.adapter?.constructor?.name
        },
        handler: {
          exists: !!novaSonicHandler,
          hasBedrockClient: !!novaSonicHandler?.getBedrockClient,
          activeSessions: novaSonicHandler?.getActiveWebSocketSessions ? 
            novaSonicHandler.getActiveWebSocketSessions().size : 0
        }
      },
      endpoints: {
        websocket: `ws://localhost:${process.env.PORT || 3000}/socket.io/?EIO=4&transport=websocket&ns=/ws/nova-sonic`,
        polling: `http://localhost:${process.env.PORT || 3000}/socket.io/?EIO=4&transport=polling&ns=/ws/nova-sonic`,
        test: `http://localhost:${process.env.PORT || 3000}/admin/nova-sonic/test`,
        connectionTest: `http://localhost:${process.env.PORT || 3000}/admin/nova-sonic/connection-test`
      }
    };

    res.json(status);
  } catch (error) {
    console.error('Error in socket-test endpoint:', error);
    res.status(500).json({
      error: 'Failed to get Socket.IO status',
      message: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;
