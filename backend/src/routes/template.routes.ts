/**
 * Template routes for the API
 */
import express from 'express';
import { Request, Response } from 'express';

import { TemplateController } from '../api/template';
import { AuthenticatedRequest } from '../common/types/authenticated-request';
import { container } from '../inversify.config';
import { authMiddleware as authenticateJWT } from '../middleware/auth.middleware';
import { TYPES } from '../types';

const router = express.Router();
const templateController = container.get<TemplateController>(TYPES.TemplateController);

// Apply authentication middleware to all template routes
router.use(authenticateJWT);

// Helper function to safely cast request to AuthenticatedRequest
const handleRequest = (handler: (req: AuthenticatedRequest, res: Response) => void) => {
  return (req: Request, res: Response) => {
    // Type assertion - we trust that the auth middleware has added these properties
    // This is a workaround for TypeScript's type checking
    const authReq = req as any as AuthenticatedRequest;
    handler(authReq, res);
  };
};

// Template routes
router.post(
  '/templates',
  handleRequest((req, res) => templateController.createTemplate(req, res)),
);
router.get(
  '/templates',
  handleRequest((req, res) => templateController.getAllTemplates(req, res)),
);
router.get(
  '/templates/:id',
  handleRequest((req, res) => templateController.getTemplateById(req, res)),
);
router.put(
  '/templates/:id',
  handleRequest((req, res) => templateController.updateTemplate(req, res)),
);
router.delete(
  '/templates/:id',
  handleRequest((req, res) => templateController.deleteTemplate(req, res)),
);

// Template file routes
router.post(
  '/templates/:templateId/files',
  handleRequest((req, res) => templateController.addTemplateFile(req, res)),
);
router.get(
  '/templates/:templateId/files',
  handleRequest((req, res) => templateController.getTemplateFiles(req, res)),
);
router.put(
  '/template-files/:id',
  handleRequest((req, res) => templateController.updateTemplateFile(req, res)),
);
router.delete(
  '/template-files/:id',
  handleRequest((req, res) => templateController.deleteTemplateFile(req, res)),
);

// Template variable routes
router.post(
  '/templates/:templateId/variables',
  handleRequest((req, res) => templateController.addTemplateVariable(req, res)),
);
router.get(
  '/templates/:templateId/variables',
  handleRequest((req, res) => templateController.getTemplateVariables(req, res)),
);
router.put(
  '/template-variables/:id',
  handleRequest((req, res) => templateController.updateTemplateVariable(req, res)),
);
router.delete(
  '/template-variables/:id',
  handleRequest((req, res) => templateController.deleteTemplateVariable(req, res)),
);

// Template collection routes
router.post(
  '/template-collections',
  handleRequest((req, res) => templateController.createTemplateCollection(req, res)),
);
router.get(
  '/template-collections',
  handleRequest((req, res) => templateController.getAllTemplateCollections(req, res)),
);
router.get(
  '/template-collections/:id',
  handleRequest((req, res) => templateController.getTemplateCollectionById(req, res)),
);
router.put(
  '/template-collections/:id',
  handleRequest((req, res) => templateController.updateTemplateCollection(req, res)),
);
router.delete(
  '/template-collections/:id',
  handleRequest((req, res) => templateController.deleteTemplateCollection(req, res)),
);

// Template collection membership routes
router.post(
  '/template-collections/:collectionId/templates/:templateId',
  handleRequest((req, res) => templateController.addTemplateToCollection(req, res)),
);
router.delete(
  '/template-collections/:collectionId/templates/:templateId',
  handleRequest((req, res) => templateController.removeTemplateFromCollection(req, res)),
);

// Project template routes
router.post(
  '/project-templates',
  handleRequest((req, res) => templateController.createProjectTemplate(req, res)),
);
router.get(
  '/project-templates',
  handleRequest((req, res) => templateController.getAllProjectTemplates(req, res)),
);
router.get(
  '/project-templates/:id',
  handleRequest((req, res) => templateController.getProjectTemplateById(req, res)),
);
router.put(
  '/project-templates/:id',
  handleRequest((req, res) => templateController.updateProjectTemplate(req, res)),
);
router.delete(
  '/project-templates/:id',
  handleRequest((req, res) => templateController.deleteProjectTemplate(req, res)),
);

// Template usage routes
router.post(
  '/template-usage',
  handleRequest((req, res) => templateController.recordTemplateUsage(req, res)),
);

export default router;
