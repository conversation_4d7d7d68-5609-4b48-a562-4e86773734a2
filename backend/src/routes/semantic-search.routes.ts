// eslint-disable-next-line import/namespace
/**
 * Semantic Search Routes
 * API endpoints for performing semantic searches against documentation.
 * These routes enable natural language queries against the codebase
 * using vector embeddings stored in ChromaDB.
 */

import type { Request, Response } from 'express';
import { Router } from 'express';

import { logger } from '../common/logger';
import { chromaDBService } from '../services/chroma-db.service';
import { documentationIndexerService } from '../services/documentation-indexer.service';

interface SemanticSearchRequestBody {
  query: string;
  projectPath?: string;
  limit?: number;
  filters?: Record<string, unknown>;
}

interface SearchResult {
  id: string;
  distance: number;
  metadata: {
    sourceFile?: string;
    filePath?: string;
    line?: number;
    column?: number;
    [key: string]: unknown;
  };
  content: string;
}

interface IndexRequestBody {
  projectPath: string;
  filePath?: string;
  force?: boolean;
}

// Create router
const router = Router();

/**
 * @swagger
 * /api/search/semantic:
 *   post:
 *     summary: Perform semantic search on documentation
 *     description: Search documentation using natural language queries
 *     tags:
 *       - search
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 description: Natural language search query
 *               projectPath:
 *                 type: string
 *                 description: Path to project (optional)
 *               limit:
 *                 type: number
 *                 description: Maximum number of results
 *                 default: 10
 *               filters:
 *                 type: object
 *                 description: Optional metadata filters
 *     responses:
 *       200:
 *         description: Search results
 *       500:
 *         description: Server error
 */
router.post('/semantic', async (req: Request, res: Response) => {
  try {
    const { query, projectPath, limit = 10, filters = {} } = req.body as SemanticSearchRequestBody;
    // Input validation
    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Query is required and must be a string',
        searchType: 'semantic',
      });
    }
    logger.info(
      `Semantic search request: "${query.substring(0, 100)}${query.length > 100 ? '...' : ''}"`,
    );
    // Add project path to filters if provided
    const searchFilters = { ...filters };
    if (projectPath) {
      searchFilters.sourceFile = { $like: `${projectPath}%` };
    }
    // Perform search
    const results = (await chromaDBService.semanticSearch(
      query,
      limit,
      searchFilters,
    )) as SearchResult[];
    // Transform results to match expected format in UI
    const formattedResults = results.map((result, index) => {
      // Extract file path from metadata
      const filePath = result.metadata.sourceFile || result.metadata.filePath || '';
      // Extract line number if available
      const line = result.metadata.line || 1;
      const column = result.metadata.column || 1;
      // Generate preview with highlighted match
      // For semantic search, we can't directly highlight matches as with text search
      // Instead we'll return a relevant snippet from the content
      const contentLines = result.content.split('\n');
      const previewLines = contentLines.slice(0, Math.min(3, contentLines.length));
      const preview = previewLines.join('\n');
      return {
        id: `semantic-${index}-${result.id}`,
        file: filePath,
        line: line,
        column: column,
        match: preview,
        preview: result.content.substring(0, 300) + (result.content.length > 300 ? '...' : ''),
        matchStart: 0,
        matchEnd: preview.length,
        score: result.distance,
        documentId: result.id,
        metadata: result.metadata,
      };
    });
    res.json({
      success: true,
      results: formattedResults,
      searchType: 'semantic',
    });
  } catch (error: unknown) {
    logger.error('Semantic search error:', error);
    const message = error instanceof Error ? error.message : 'Semantic search failed';
    res.status(500).json({
      success: false,
      error: message,
      searchType: 'semantic',
    });
  }
});

/**
 * @swagger
 * /api/search/index:
 *   post:
 *     summary: Index documentation for semantic search
 *     description: Process and index project documentation in ChromaDB
 *     tags:
 *       - search
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectPath
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to project root
 *               filePath:
 *                 type: string
 *                 description: Specific file to index (optional)
 *               force:
 *                 type: boolean
 *                 description: Force reindexing
 *                 default: false
 *     responses:
 *       200:
 *         description: Indexing successful
 *       500:
 *         description: Server error
 */
router.post('/index', async (req: Request, res: Response) => {
  try {
    const { projectPath, filePath, force = false } = req.body as IndexRequestBody;
    // Input validation
    if (!projectPath) {
      return res.status(400).json({
        success: false,
        error: 'Project path is required',
        searchType: 'index',
      });
    }
    logger.info(`Documentation indexing request for ${filePath || projectPath}`);
    // Perform indexing
    if (filePath) {
      await documentationIndexerService.indexDocumentationFile(filePath);
      res.json({
        success: true,
        message: `Documentation file indexed: ${filePath}`,
        searchType: 'index',
      });
    } else {
      // This will be an async process for large projects
      // Respond immediately and continue indexing in background
      res.json({
        success: true,
        message: `Indexing documentation for project: ${projectPath}`,
        searchType: 'index',
      });
      documentationIndexerService.indexProjectDocumentation(projectPath, force).catch((err) => {
        logger.error(`Background indexing failed for ${projectPath}:`, err);
      });
    }
  } catch (error: unknown) {
    logger.error('Documentation indexing error:', error);
    const message = error instanceof Error ? error.message : 'Documentation indexing failed';
    res.status(500).json({
      success: false,
      error: message,
      searchType: 'index',
    });
  }
});

/**
 * @swagger
 * /api/search/health:
 *   get:
 *     summary: Check semantic search health
 *     description: Check if ChromaDB service is available and healthy
 *     tags:
 *       - search
 *     responses:
 *       200:
 *         description: Health status
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const isHealthy = await chromaDBService.healthCheck();
    res.json({
      success: true,
      healthy: isHealthy,
    });
  } catch (error: any) {
    logger.error('Health check error:', error);
    res.status(500).json({
      success: false,
      healthy: false,
      error: error.message,
    });
  }
});

export const semanticSearchRoutes = router;
