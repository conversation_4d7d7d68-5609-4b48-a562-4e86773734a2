import { Router } from 'express';
import { clerkAuthMiddleware } from '../../middleware/clerk.middleware';
import { projectController } from '../../api/projects/controllers';
import { validateDto } from '../../middleware/validation/validate-dto.middleware';
import { CreateProjectDto, UpdateProjectDto, CreateObjectiveDto } from '../../api/projects/dto';

const router = Router();

/**
 * @swagger
 * /projects:
 *   get:
 *     summary: List projects
 *     description: Lists all projects for the current user with optional filters.
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: activeOnly
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Filter to active projects only
 *       - in: query
 *         name: sortByUpdated
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Sort by last updated time
 *     responses:
 *       200:
 *         description: A list of user projects
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', clerkAuthMiddleware, (req, res) => projectController.getUserProjects(req, res));

/**
 * @swagger
 * /projects/{projectId}:
 *   get:
 *     summary: Get project by ID
 *     description: Retrieve a project by its ID
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Project details
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.get('/:projectId', clerkAuthMiddleware, (req, res) =>
  projectController.getProjectById(req, res),
);

/**
 * @swagger
 * /projects:
 *   post:
 *     summary: Create new project
 *     description: Create a new project for the current user
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProjectDto'
 *     responses:
 *       201:
 *         description: Project created successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/', clerkAuthMiddleware, validateDto(CreateProjectDto), (req, res) =>
  projectController.createProject(req, res),
);

/**
 * @swagger
 * /projects/{projectId}:
 *   put:
 *     summary: Update project
 *     description: Update an existing project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProjectDto'
 *     responses:
 *       200:
 *         description: Project updated successfully
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.put('/:projectId', clerkAuthMiddleware, validateDto(UpdateProjectDto), (req, res) =>
  projectController.updateProject(req, res),
);

/**
 * @swagger
 * /projects/{projectId}:
 *   delete:
 *     summary: Delete project
 *     description: Delete a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *     responses:
 *       204:
 *         description: Project deleted successfully
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.delete('/:projectId', clerkAuthMiddleware, (req, res) =>
  projectController.deleteProject(req, res),
);

/**
 * @swagger
 * /projects/{projectId}/objectives:
 *   post:
 *     summary: Add project objective
 *     description: Add an objective to a project
 *     tags: [Projects]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateObjectiveDto'
 *     responses:
 *       201:
 *         description: Objective added successfully
 *       404:
 *         description: Project not found
 *       500:
 *         description: Server error
 */
router.post(
  '/:projectId/objectives',
  clerkAuthMiddleware,
  validateDto(CreateObjectiveDto),
  (req, res) => projectController.addObjective(req, res),
);

export default router;
