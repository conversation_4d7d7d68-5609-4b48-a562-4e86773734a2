import { Router } from 'express';
import { generateDocumentation, generateDocumentationSelective } from '../services/documentation.service';
import { chromaDBService } from '../services/chroma-db.service';
import { DocumentationIndexerService } from '../services/documentation-indexer.service';

const router = Router();
const documentationIndexer = new DocumentationIndexerService();

/**
 * @swagger
 * /documentation/generate:
 *   post:
 *     summary: Generate documentation for selected files or entire project
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: The absolute path to the project directory
 *               selectedFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of specific file paths to document
 *               directoryPath:
 *                 type: string
 *                 description: Path to directory for batch documentation
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, critical]
 *                 description: Documentation priority level
 *               includeSubdirectories:
 *                 type: boolean
 *                 description: Include subdirectories when documenting a directory
 *     responses:
 *       202:
 *         description: Documentation generation started
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Error generating documentation
 */
router.post('/generate', async (req, res) => {
  try {
    const {
      projectPath,
      selectedFiles,
      directoryPath,
      priority = 'normal',
      includeSubdirectories = true
    } = req.body;

    if (!projectPath) {
      return res.status(400).json({ error: 'projectPath is required' });
    }

    // Validate that at least one target is specified
    if (!selectedFiles && !directoryPath) {
      return res.status(400).json({
        error: 'Either selectedFiles or directoryPath must be specified'
      });
    }

    // We run this in the background and immediately return a response
    generateDocumentationSelective({
      projectPath,
      selectedFiles,
      directoryPath,
      priority,
      includeSubdirectories
    }).catch(error => {
        console.error('Error during background documentation generation:', error);
    });

    res.status(202).json({
      message: 'Documentation generation started in the background',
      targets: {
        selectedFiles: selectedFiles?.length || 0,
        directoryPath: directoryPath || null,
        priority,
        includeSubdirectories
      }
    });
  } catch (error) {
    console.error('Error starting documentation generation:', error);
    res.status(500).json({ error: 'Error starting documentation generation', details: error.message });
  }
});

/**
 * @swagger
 * /documentation/search:
 *   post:
 *     summary: Search documentation using semantic search
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *                 description: Natural language search query
 *               projectPath:
 *                 type: string
 *                 description: Project path to filter results (optional)
 *               limit:
 *                 type: integer
 *                 description: Maximum number of results (default 10)
 *               threshold:
 *                 type: number
 *                 description: Minimum similarity threshold (default 0.7)
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       content:
 *                         type: string
 *                       metadata:
 *                         type: object
 *                       similarity:
 *                         type: number
 *       400:
 *         description: Query is required
 *       500:
 *         description: Search failed
 */
router.post('/search', async (req, res) => {
  try {
    const { query, projectPath, limit = 10, threshold = 0.7 } = req.body;

    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return res.status(400).json({ error: 'Query is required and must be a non-empty string' });
    }

    // Build filters based on projectPath if provided
    const filters: Record<string, any> = {};
    if (projectPath) {
      // Filter by project path in metadata using ChromaDB's where syntax
      filters.sourceFile = projectPath;
    }

    // Perform semantic search
    const searchResults = await chromaDBService.semanticSearch(query, limit, filters);

    // Transform results to include similarity score
    const results = searchResults.map((result: any) => ({
      id: result.id,
      content: result.content,
      metadata: result.metadata,
      similarity: 1 - result.distance // Convert distance to similarity
    })).filter((result: any) => result.similarity >= threshold);

    res.json({
      query,
      results,
      total: results.length,
      limit,
      threshold
    });
  } catch (error) {
    console.error('Error performing semantic search:', error);
    res.status(500).json({ error: 'Search failed', details: error.message });
  }
});

/**
 * @swagger
 * /documentation/index:
 *   post:
 *     summary: Index documentation files for semantic search
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project to index
 *               force:
 *                 type: boolean
 *                 description: Force reindexing of all documents
 *     responses:
 *       202:
 *         description: Indexing started
 *       400:
 *         description: projectPath is required
 *       500:
 *         description: Indexing failed
 */
router.post('/index', async (req, res) => {
  try {
    const { projectPath, force = false } = req.body;

    if (!projectPath) {
      return res.status(400).json({ error: 'projectPath is required' });
    }

    // Run indexing in background
    documentationIndexer.indexProjectDocumentation(projectPath, force).catch(error => {
      console.error('Error during background documentation indexing:', error);
    });

    res.status(202).json({
      message: 'Documentation indexing started in the background',
      projectPath,
      force
    });
  } catch (error) {
    console.error('Error starting documentation indexing:', error);
    res.status(500).json({ error: 'Indexing failed', details: error.message });
  }
});

/**
 * @swagger
 * /documentation/health:
 *   get:
 *     summary: Check health of documentation search service
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: Service is healthy
 *       503:
 *         description: Service is unhealthy
 */
/**
 * @swagger
 * /documentation/files:
 *   post:
 *     summary: Get file information for documentation selection
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               directoryPath:
 *                 type: string
 *                 description: Path to directory to scan
 *               includeSubdirectories:
 *                 type: boolean
 *                 description: Include subdirectories in scan
 *     responses:
 *       200:
 *         description: File information retrieved
 *       400:
 *         description: directoryPath is required
 *       500:
 *         description: Error scanning directory
 */
router.post('/files', async (req, res) => {
  try {
    const { directoryPath, includeSubdirectories = true } = req.body;

    if (!directoryPath) {
      return res.status(400).json({ error: 'directoryPath is required' });
    }

    // Import the function here to avoid circular dependencies
    const { getFilesFromDirectory, getFileImportance } = require('../services/documentation.service');

    const files = getFilesFromDirectory(directoryPath, includeSubdirectories);

    const fileInfo = files.map(filePath => ({
      path: filePath,
      name: require('path').basename(filePath),
      relativePath: require('path').relative(directoryPath, filePath),
      importance: getFileImportance(filePath),
      size: require('fs').statSync(filePath).size,
      lastModified: require('fs').statSync(filePath).mtime
    }));

    // Sort by importance and name
    fileInfo.sort((a, b) => {
      const importanceOrder = { critical: 4, high: 3, normal: 2, low: 1 };
      const importanceDiff = importanceOrder[b.importance] - importanceOrder[a.importance];

      if (importanceDiff !== 0) return importanceDiff;

      return a.name.localeCompare(b.name);
    });

    res.json({
      directory: directoryPath,
      includeSubdirectories,
      totalFiles: fileInfo.length,
      files: fileInfo,
      summary: {
        critical: fileInfo.filter(f => f.importance === 'critical').length,
        high: fileInfo.filter(f => f.importance === 'high').length,
        normal: fileInfo.filter(f => f.importance === 'normal').length,
        low: fileInfo.filter(f => f.importance === 'low').length
      }
    });
  } catch (error) {
    console.error('Error scanning directory:', error);
    res.status(500).json({ error: 'Error scanning directory', details: error.message });
  }
});

/**
 * @swagger
 * /documentation/commit/{commitHash}:
 *   post:
 *     summary: Index documentation for a specific commit
 *     tags: [Documentation]
 *     parameters:
 *       - in: path
 *         name: commitHash
 *         required: true
 *         schema:
 *           type: string
 *         description: Git commit hash
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project
 *     responses:
 *       202:
 *         description: Commit documentation indexing started
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Indexing failed
 */
router.post('/commit/:commitHash', async (req, res) => {
  try {
    const { commitHash } = req.params;
    const { projectPath } = req.body;

    if (!commitHash || !projectPath) {
      return res.status(400).json({
        error: 'commitHash and projectPath are required'
      });
    }

    // Run commit indexing in background
    documentationIndexer.indexCommitDocumentation(commitHash, projectPath).catch(error => {
      console.error('Error during background commit documentation indexing:', error);
    });

    res.status(202).json({
      message: 'Commit documentation indexing started in the background',
      commitHash,
      projectPath
    });
  } catch (error) {
    console.error('Error starting commit documentation indexing:', error);
    res.status(500).json({
      error: 'Commit indexing failed',
      details: error.message
    });
  }
});

/**
 * @swagger
 * /documentation/changes:
 *   post:
 *     summary: Get documentation changes between commits
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fromCommit:
 *                 type: string
 *                 description: Starting commit hash
 *               toCommit:
 *                 type: string
 *                 description: Ending commit hash (defaults to HEAD)
 *               projectPath:
 *                 type: string
 *                 description: Path to the project
 *     responses:
 *       200:
 *         description: Documentation changes retrieved
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Failed to get changes
 */
router.post('/changes', async (req, res) => {
  try {
    const { fromCommit, toCommit = 'HEAD', projectPath } = req.body;

    if (!fromCommit || !projectPath) {
      return res.status(400).json({
        error: 'fromCommit and projectPath are required'
      });
    }

    const changes = await documentationIndexer.getDocumentationChanges(
      fromCommit,
      toCommit,
      projectPath
    );

    res.json({
      fromCommit,
      toCommit,
      projectPath,
      changes,
      total: changes.length
    });
  } catch (error) {
    console.error('Error getting documentation changes:', error);
    res.status(500).json({
      error: 'Failed to get documentation changes',
      details: error.message
    });
  }
});

router.get('/health', async (req, res) => {
  try {
    const isHealthy = await chromaDBService.healthCheck();

    if (isHealthy) {
      res.json({
        status: 'healthy',
        service: 'documentation-search',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        service: 'documentation-search',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      service: 'documentation-search',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
