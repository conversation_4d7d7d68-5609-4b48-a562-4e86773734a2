/**
 * Payment routes
 */
import { Router, Request, Response, NextFunction, RequestHandler } from 'express';

import { PaymentController } from '../api/payment/controllers';
import {
  CreatePaymentDto,
  CreateRefundDto,
  CreateSubscriptionDto,
  CancelSubscriptionDto,
} from '../api/payment/dto';
import { AuthenticatedRequest } from '../common/types/authenticated-request';
import { container } from '../inversify.config';
import { roleMiddleware } from '../middleware/auth.middleware';
import { clerkAuthMiddleware } from '../middleware/clerk.middleware';
import { validate } from '../middleware/validation.middleware';
import { TYPES } from '../types';

const router = Router();
const paymentController = container.get<PaymentController>(TYPES.PaymentController);

/**
 * Type-safe wrapper for route handlers that ensures proper error handling
 * and type safety for authenticated requests
 */
function createHandler(
  fn: (req: AuthenticatedRequest, res: Response) => Promise<void>,
): RequestHandler {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req as AuthenticatedRequest, res).catch(next);
    return undefined;
  };
}

// Payment routes
router.get(
  '/payments',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.getUserPayments(req, res);
  }),
);

router.get(
  '/payments/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.getPaymentById(req, res);
  }),
);

router.post(
  '/payments',
  clerkAuthMiddleware,
  validate(CreatePaymentDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.createPayment(req, res);
  }),
);

router.patch(
  '/payments/:id/status',
  clerkAuthMiddleware,
  roleMiddleware(['ADMIN']),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.updatePaymentStatus(req, res);
  }),
);

router.post(
  '/payments/refund',
  clerkAuthMiddleware,
  roleMiddleware(['ADMIN']),
  validate(CreateRefundDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.createRefund(req, res);
  }),
);

// Subscription routes
router.get(
  '/subscriptions',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.getUserSubscriptions(req, res);
  }),
);

router.get(
  '/subscriptions/active',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.getUserActiveSubscription(req, res);
  }),
);

router.post(
  '/subscriptions',
  clerkAuthMiddleware,
  validate(CreateSubscriptionDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.createSubscription(req, res);
  }),
);

router.post(
  '/subscriptions/:id/cancel',
  clerkAuthMiddleware,
  validate(CancelSubscriptionDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await paymentController.cancelSubscription(req, res);
  }),
);

export default router;
