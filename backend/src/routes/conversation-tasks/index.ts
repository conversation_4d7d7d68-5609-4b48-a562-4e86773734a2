import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { AuthenticatedRequest } from '../../common/types/authenticated-request';
import { clerkAuthMiddleware } from '../../middleware/clerk.middleware';
import { dynamicRateLimitMiddleware } from '../../middleware/rate-limit.middleware';
import { unifiedConversationService } from '../../services/conversation/unified-conversation.service';
import { ChatResponse } from '../../common/types/conversation.types';
import { logger } from '../../common/logger';
import fs from 'fs/promises';
import path from 'path';
import yaml from 'js-yaml';

// Define task request types
interface TaskRequest {
  prompt: string;
  conversationId: number;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

interface CodeGenRequest extends TaskRequest {
  language: string;
  context?: string;
}

interface MockupRequest extends TaskRequest {
  description: string;
  style: string;
  components: string[];
  width: number;
  height: number;
}

interface TestCaseRequest extends TaskRequest {
  code: string;
  framework: string;
  coverageLevel?: string;
}

interface SlideRequest extends TaskRequest {
  topic: string;
  numSlides: number;
  format: string;
}

interface MultimodalRequest extends TaskRequest {
  images: Array<{
    url: string;
    description?: string;
  }>;
  instructions: string;
  outputFormat?: 'description' | 'code' | 'both';
  designStyle?: string;
  targetPlatform?: 'web' | 'mobile' | 'desktop';
}

// Load prompts from YAML file
let TASK_PROMPTS: any = {};

// Load prompts from config
async function loadPrompts() {
  try {
    // Use process.cwd() to get the project root, then navigate to config
    const promptsPath = path.join(process.cwd(), 'config/prompts.yaml');
    const fileContents = await fs.readFile(promptsPath, 'utf8');
    const config = yaml.load(fileContents) as any;

    TASK_PROMPTS = config?.conversation_tasks || {};
    logger.info('Task prompts loaded successfully');
  } catch (error) {
    logger.error('Error loading task prompts:', error);

    // Default prompts if config can't be loaded
    TASK_PROMPTS = {
      code_planning: {
        system:
          'You are an expert software architect and planner. Your task is to help plan code implementation, design systems, and provide detailed technical guidance.',
      },
      code_generation: {
        system:
          'You are an expert software developer. Your task is to generate high-quality, well-documented code based on requirements.',
      },
      mockup_generation: {
        system: 'You are an expert UI/UX designer specializing in creating SVG-based mockups.',
      },
    };
  }
}

// Helper function to process task response
function processTaskResponse(result: any): any {
  return {
    status: result.status || 'error',
    content: result.content || '',
    conversationId: result.conversationId || 0,
    model: result.model || '',
    tokens: {
      prompt: result.promptTokens || 0,
      completion: result.completionTokens || 0,
      total: result.totalTokens || 0,
    },
    processingTime: result.processingTime || 0,
    error: result.error,
  };
}

const router = Router();

// Load prompts when the router is initialized
loadPrompts();

/**
 * @swagger
 * /tasks/chat:
 *   post:
 *     summary: General chat task
 *     description: General chat interaction with the LLM
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The prompt to send to the model
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 1000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post(
  '/chat',
  clerkAuthMiddleware,
  dynamicRateLimitMiddleware,
  async (req: Request, res: Response) => {
    try {
      const {
        prompt,
        conversationId,
        model,
        maxTokens = 1000,
        temperature = 0.7,
      } = req.body as TaskRequest;
      const userId = (req as any).userId;

      const result = await unifiedConversationService.addUserMessageAndGetResponse(
        conversationId,
        prompt,
        {
          model,
          taskType: 'chat',
          maxTokens,
          temperature,
        },
      );

      // Check for error status in the response
      if (result.status === 'error') {
        return res.status(422).json({
          status: 'error',
          message: result.error || 'An unknown error occurred during chat',
        });
      }

      res.json(result);
    } catch (error: any) {
      logger.error('Error in chat task:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to process chat task',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

/**
 * @swagger
 * /tasks/code-planning:
 *   post:
 *     summary: Code planning task
 *     description: Get help with code architecture and planning
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The planning request
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 2000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post(
  '/code-planning',
  clerkAuthMiddleware,
  dynamicRateLimitMiddleware,
  async (req: Request, res: Response) => {
    try {
      const {
        prompt,
        conversationId,
        model,
        maxTokens = 2000,
        temperature = 0.7,
      } = req.body as TaskRequest;
      const userId = (req as any).userId;

      // Get the system prompt from the config
      const systemPrompt =
        TASK_PROMPTS.code_planning?.system ||
        'You are an expert software architect and planner. Your task is to help plan code implementation, design systems, and provide detailed technical guidance.';

      // Use the conversation service to get a response with system prompt
      const result = await unifiedConversationService.addUserMessageAndGetResponse(
        conversationId,
        `${systemPrompt}\n\nUser: ${prompt}`,
        {
          model,
          taskType: 'code_planning',
          maxTokens,
          temperature,
        },
      );

      // Check for error status in the response
      if (result.status === 'error') {
        return res.status(422).json({
          status: 'error',
          message: result.error || 'An unknown error occurred during code planning',
        });
      }

      res.json(result);
    } catch (error: any) {
      logger.error('Error in code planning task:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to process code planning task',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

/**
 * @swagger
 * /tasks/code-generation:
 *   post:
 *     summary: Code generation task
 *     description: Generate code based on a prompt and language
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - language
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: The code generation request
 *               language:
 *                 type: string
 *                 description: Programming language for code generation
 *               context:
 *                 type: string
 *                 description: Additional context for code generation
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 2000
 *               temperature:
 *                 type: number
 *                 default: 0.3
 *     responses:
 *       200:
 *         description: Model response with generated code
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post(
  '/code-generation',
  clerkAuthMiddleware,
  dynamicRateLimitMiddleware,
  async (req: Request, res: Response) => {
    try {
      const {
        prompt,
        conversationId,
        language,
        context,
        model,
        maxTokens = 2000,
        temperature = 0.3,
      } = req.body as CodeGenRequest;
      const userId = (req as any).userId;

      // Get the system prompt from the config
      const systemPrompt =
        TASK_PROMPTS.code_generation?.system ||
        'You are an expert software developer. Your task is to generate high-quality, well-documented code based on requirements.';

      // Enhance the prompt with language and context information
      let enhancedPrompt = `Generate ${language} code for: ${prompt}`;
      if (context) {
        enhancedPrompt += `\n\nAdditional context: ${context}`;
      }

      // Use the conversation service to get a response with system prompt
      const result = await unifiedConversationService.addUserMessageAndGetResponse(
        conversationId,
        `${systemPrompt}\n\nUser: ${enhancedPrompt}`,
        {
          model,
          taskType: 'code_generation',
          maxTokens,
          temperature,
        },
      );

      // Check for error status in the response
      if (result.status === 'error') {
        return res.status(422).json({
          status: 'error',
          message: result.error || 'An unknown error occurred during code generation',
        });
      }

      res.json(result);
    } catch (error: any) {
      logger.error('Error in code generation task:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to process code generation task',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

/**
 * @swagger
 * /tasks/svg:
 *   post:
 *     summary: SVG mockup generation
 *     description: Generate SVG-based UI mockups
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - description
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Additional instructions
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               description:
 *                 type: string
 *                 description: Description of the UI to generate
 *               style:
 *                 type: string
 *                 default: modern
 *                 description: Style of the mockup
 *               components:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Specific components to include
 *               width:
 *                 type: integer
 *                 default: 800
 *                 description: Width of the SVG in pixels
 *               height:
 *                 type: integer
 *                 default: 600
 *                 description: Height of the SVG in pixels
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 4000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response with SVG code
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post(
  '/svg',
  clerkAuthMiddleware,
  dynamicRateLimitMiddleware,
  async (req: Request, res: Response) => {
    try {
      const {
        prompt,
        conversationId,
        description,
        style = 'modern',
        components = [],
        width = 800,
        height = 600,
        model,
        maxTokens = 4000,
        temperature = 0.7,
      } = req.body as MockupRequest;
      const userId = (req as any).userId;

      // Get the system prompt from the config
      const systemPrompt =
        TASK_PROMPTS.mockup_generation?.system ||
        'You are an expert UI/UX designer specializing in creating SVG-based mockups.';

      // Get the user prompt template from the config
      const userTemplate =
        TASK_PROMPTS.mockup_generation?.user_template ||
        'Please create an SVG mockup with the following specifications: Description: {description}, Style: {style}, Components: {components}, Dimensions: {width}x{height}';

      // Format the user prompt with the provided parameters
      const componentsStr = components.length > 0 ? components.join(', ') : 'standard UI elements';
      const userPrompt = userTemplate
        .replace('{description}', description)
        .replace('{style}', style)
        .replace('{components}', componentsStr)
        .replace('{width}', width.toString())
        .replace('{height}', height.toString());

      // Add user prompt
      const finalPrompt = userPrompt + (prompt ? `\n\nAdditional instructions: ${prompt}` : '');

      // Use the conversation service to get a response with system prompt
      const result = await unifiedConversationService.addUserMessageAndGetResponse(
        conversationId,
        `${systemPrompt}\n\nUser: ${finalPrompt}`,
        {
          model,
          taskType: 'svg_mockup',
          maxTokens,
          temperature,
        },
      );

      // Check for error status in the response
      if (result.status === 'error') {
        return res.status(422).json({
          status: 'error',
          message: result.error || 'An unknown error occurred during SVG generation',
        });
      }

      res.json(result);
    } catch (error: any) {
      logger.error('Error in SVG mockup generation task:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to process SVG mockup generation task',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

/**
 * @swagger
 * /tasks/test-cases:
 *   post:
 *     summary: Test case generation
 *     description: Generate test cases for provided code
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - code
 *               - framework
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Additional requirements for test cases
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               code:
 *                 type: string
 *                 description: Code to generate tests for
 *               framework:
 *                 type: string
 *                 description: Test framework to use
 *               coverageLevel:
 *                 type: string
 *                 default: high
 *                 description: Desired test coverage level
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 3000
 *               temperature:
 *                 type: number
 *                 default: 0.3
 *     responses:
 *       200:
 *         description: Model response with test cases
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post('/test-cases', clerkAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const {
      prompt,
      conversationId,
      code,
      framework,
      coverageLevel = 'high',
      model,
      maxTokens = 3000,
      temperature = 0.3,
    } = req.body as TestCaseRequest;
    const userId = (req as any).userId;

    // Create system prompt
    const systemPrompt = `You are an expert software tester specializing in ${framework}. Your task is to create comprehensive test cases with ${coverageLevel} coverage.`;

    // Enhance the prompt with test-specific instructions
    const enhancedPrompt = `Generate ${framework} test cases for the following code with ${coverageLevel} coverage level:

\`\`\`
${code}
\`\`\`

Additional requirements: ${prompt}`;

    // Use the conversation service to get a response with system prompt
    const result = await unifiedConversationService.addUserMessageAndGetResponse(
      conversationId,
      `${systemPrompt}\n\nUser: ${enhancedPrompt}`,
      {
        model,
        taskType: 'test_cases',
        maxTokens,
        temperature,
      },
    );

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during test case generation',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error in test case generation task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process test case generation task',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /tasks/slides:
 *   post:
 *     summary: Slide generation
 *     description: Generate presentation slides
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - topic
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Additional requirements for slides
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               topic:
 *                 type: string
 *                 description: Topic for the slides
 *               numSlides:
 *                 type: integer
 *                 default: 5
 *                 description: Number of slides to generate
 *               format:
 *                 type: string
 *                 default: markdown
 *                 description: Format for the slides
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 4000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response with slide content
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post('/slides', clerkAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const {
      prompt,
      conversationId,
      topic,
      numSlides = 5,
      format = 'markdown',
      model,
      maxTokens = 4000,
      temperature = 0.7,
    } = req.body as SlideRequest;
    const userId = (req as any).userId;

    // Create system prompt
    const systemPrompt =
      'You are an expert presentation designer specializing in creating clear, concise, and visually appealing presentations.';

    // Enhance the prompt with slide-specific instructions
    const enhancedPrompt = `Generate ${numSlides} presentation slides in ${format} format on the topic: ${topic}

Additional requirements: ${prompt}`;

    // Use the conversation service to get a response with system prompt
    const result = await unifiedConversationService.addUserMessageAndGetResponse(
      conversationId,
      `${systemPrompt}\n\nUser: ${enhancedPrompt}`,
      {
        model,
        taskType: 'slides',
        maxTokens,
        temperature,
      },
    );

    // Check for error status in the response
    if (result.status === 'error') {
      return res.status(422).json({
        status: 'error',
        message: result.error || 'An unknown error occurred during slide generation',
      });
    }

    res.json(result);
  } catch (error: any) {
    logger.error('Error in slide generation task:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process slide generation task',
      details: error?.message || 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /tasks/multimodal:
 *   post:
 *     summary: Multimodal AI task for mockup analysis
 *     description: Analyze and enhance UI/UX mockups using multimodal AI with conversation context
 *     tags: [ConversationTasks]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prompt
 *               - conversationId
 *               - images
 *               - instructions
 *             properties:
 *               prompt:
 *                 type: string
 *                 description: Additional context and instructions
 *               conversationId:
 *                 type: integer
 *                 description: ID of an existing conversation
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - url
 *                   properties:
 *                     url:
 *                       type: string
 *                       format: uri
 *                       description: URL of the mockup image
 *                     description:
 *                       type: string
 *                       description: Optional description of the mockup
 *                 description: Array of mockup images to analyze
 *               instructions:
 *                 type: string
 *                 description: Specific instructions for improving the mockups
 *               outputFormat:
 *                 type: string
 *                 enum: [description, code, both]
 *                 default: both
 *                 description: Format of the output
 *               designStyle:
 *                 type: string
 *                 default: modern
 *                 description: Design style preference
 *               targetPlatform:
 *                 type: string
 *                 enum: [web, mobile, desktop]
 *                 default: web
 *                 description: Target platform for the design
 *               model:
 *                 type: string
 *                 description: Optional specific model to use
 *               maxTokens:
 *                 type: integer
 *                 default: 8000
 *               temperature:
 *                 type: number
 *                 default: 0.7
 *     responses:
 *       200:
 *         description: Model response with mockup analysis and improvements
 *       422:
 *         description: Processing error
 *       500:
 *         description: Server error
 */
router.post(
  '/multimodal',
  clerkAuthMiddleware,
  dynamicRateLimitMiddleware,
  async (req: Request, res: Response) => {
    try {
      const {
        prompt,
        conversationId,
        images,
        instructions,
        outputFormat = 'both',
        designStyle = 'modern',
        targetPlatform = 'web',
        model,
        maxTokens = 8000,
        temperature = 0.7,
      } = req.body as MultimodalRequest;
      const userId = (req as any).userId;

      // Create system prompt for multimodal mockup analysis
      const systemPrompt = `You are an expert UI/UX designer and developer specializing in mockup analysis and enhancement. 

Your task is to analyze provided mockup images within the context of an ongoing conversation and provide:
1. Detailed analysis of the current design
2. Specific improvement recommendations
3. Updated mockup description with precise component specifications
4. Implementation guidance when requested

Design Context:
- Style: ${designStyle}
- Platform: ${targetPlatform}
- Output Format: ${outputFormat}

Consider the conversation history and any previous design decisions when making recommendations.
Provide responses that build upon the existing conversation context.`;

      // Enhance the prompt with conversation context
      const contextualPrompt = `Based on our conversation, please analyze the provided mockup images and ${instructions}

Additional context: ${prompt}

Please provide structured recommendations that align with our discussion and build upon any previous design decisions.`;

      // Use the conversation service to get a response with multimodal task type
      const result = await unifiedConversationService.addUserMessageAndGetResponse(
        conversationId,
        contextualPrompt,
        {
          model,
          taskType: 'multimodal',
          maxTokens,
          temperature,
          // Pass multimodal-specific options
          multimodalOptions: {
            images,
            instructions,
            outputFormat,
            designStyle,
            targetPlatform,
            systemPrompt,
          },
        },
      );

      // Check for error status in the response
      if (result.status === 'error') {
        return res.status(422).json({
          status: 'error',
          message: result.error || 'An unknown error occurred during multimodal analysis',
        });
      }

      res.json(result);
    } catch (error: any) {
      logger.error('Error in multimodal task:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to process multimodal task',
        details: error?.message || 'Unknown error',
      });
    }
  },
);

export default router;
