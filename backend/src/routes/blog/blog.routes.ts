import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { clerkAuthMiddleware } from '../../middleware/clerk.middleware';
import { blogController } from '../../api/blog/controllers/blog.controller';
import { validateDto } from '../../middleware/validation/validate-dto.middleware';
import { CreateBlogPostDto, UpdateBlogPostDto } from '../../api/blog/dto/blog.dto';
import { wrapController } from '../../utils/controller-wrapper';

const router = Router();

/**
 * @swagger
 * /blog:
 *   get:
 *     summary: List blog posts
 *     description: Get paginated list of blog posts
 *     tags: [Blog]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: publishedOnly
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Only include published posts
 *     responses:
 *       200:
 *         description: Paginated list of blog posts
 *       500:
 *         description: Server error

*/
router.get('/', wrapController(blogController.getAllPosts.bind(blogController)));

/**
 * @swagger
 * /blog/{postId}:
 *   get:
 *     summary: Get blog post by ID
 *     description: Retrieve blog post details by ID
 *     tags: [Blog]
 *     parameters:
 *       - in: path
 *         name: postId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Blog post ID
 *     responses:
 *       200:
 *         description: Blog post details
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.get('/:postId', wrapController(blogController.getPostById.bind(blogController)));

/**
 * @swagger
 * /blog/slug/{slug}:
 *   get:
 *     summary: Get blog post by slug
 *     description: Retrieve blog post details by slug
 *     tags: [Blog]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Blog post slug
 *     responses:
 *       200:
 *         description: Blog post details
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.get('/slug/:slug', wrapController(blogController.getPostBySlug.bind(blogController)));

/**
 * @swagger
 * /blog/tag/{tag}:
 *   get:
 *     summary: Get blog posts by tag
 *     description: Retrieve blog posts by tag
 *     tags: [Blog]
 *     parameters:
 *       - in: path
 *         name: tag
 *         required: true
 *         schema:
 *           type: string
 *         description: Blog post tag
 *     responses:
 *       200:
 *         description: List of blog posts
 *       404:
 *         description: Blog posts not found
 *       500:
 *         description: Server error
 */
router.get('/tag/:tag', wrapController(blogController.getPostsByTag.bind(blogController)));

/**
 * @swagger
 * /blog/tags:
 *   get:
 *     summary: Get all blog post tags
 *     description: Retrieve a list of all blog post tags
 *     tags: [Blog]
 *     responses:
 *       200:
 *         description: List of blog post tags
 *       500:
 *         description: Server error
 */
router.get('/tags', wrapController(blogController.getAllTags.bind(blogController)));

/**
 * @swagger
 * /blog:
 *   post:
 *     summary: Create blog post
 *     description: Create a new blog post
 *     tags: [Blog]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBlogPostDto'
 *     responses:
 *       201:
 *         description: Blog post created successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post(
  '/',
  clerkAuthMiddleware as RequestHandler,
  validateDto(CreateBlogPostDto),
  wrapController(blogController.createPost.bind(blogController)),
);

/**
 * @swagger
 * /blog/{postId}:
 *   put:
 *     summary: Update blog post
 *     description: Update an existing blog post
 *     tags: [Blog]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: postId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Blog post ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateBlogPostDto'
 *     responses:
 *       200:
 *         description: Blog post updated successfully
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.put(
  '/:postId',
  clerkAuthMiddleware as RequestHandler,
  validateDto(UpdateBlogPostDto),
  wrapController(blogController.updatePost.bind(blogController)),
);

/**
 * @swagger
 * /blog/{postId}:
 *   delete:
 *     summary: Delete blog post
 *     description: Delete a blog post
 *     tags: [Blog]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: postId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Blog post ID
 *     responses:
 *       204:
 *         description: Blog post deleted successfully
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.delete(
  '/:postId',
  clerkAuthMiddleware as RequestHandler,
  wrapController(blogController.deletePost.bind(blogController)),
);

/**
 * @swagger
 * /blog/{postId}/publish:
 *   post:
 *     summary: Publish blog post
 *     description: Publish a blog post
 *     tags: [Blog]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: postId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Blog post ID
 *     responses:
 *       200:
 *         description: Blog post published successfully
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.post(
  '/:postId/publish',
  clerkAuthMiddleware as RequestHandler,
  wrapController(blogController.publishPost.bind(blogController)),
);

/**
 * @swagger
 * /blog/{postId}/unpublish:
 *   post:
 *     summary: Unpublish blog post
 *     description: Unpublish a blog post
 *     tags: [Blog]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: postId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Blog post ID
 *     responses:
 *       200:
 *         description: Blog post unpublished successfully
 *       404:
 *         description: Blog post not found
 *       500:
 *         description: Server error
 */
router.post(
  '/:postId/unpublish',
  clerkAuthMiddleware as RequestHandler,
  wrapController(blogController.unpublishPost.bind(blogController)),
);

export default router;
