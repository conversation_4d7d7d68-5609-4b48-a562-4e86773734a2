/**
 * Social routes
 */
import { Router, Request, Response, NextFunction, RequestHandler } from 'express';

import { SocialController } from '../api/social/controllers';
import {
  CreateChannelDto,
  UpdateChannelDto,
  ChannelMemberDto,
  CreateMessageDto,
  UpdateMessageDto,
  MessageReactionDto,
} from '../api/social/dto';
import { AuthenticatedRequest } from '../common/types/authenticated-request';
import { container } from '../inversify.config';
import { clerkAuthMiddleware } from '../middleware/clerk.middleware';
import { validate } from '../middleware/validation.middleware';
import { TYPES } from '../types';

const router = Router();
const socialController = container.get<SocialController>(TYPES.SocialController);

/**
 * Type-safe wrapper for route handlers that ensures proper error handling
 * and type safety for authenticated requests
 */
function createHandler(
  fn: (req: AuthenticatedRequest, res: Response) => Promise<void>,
): RequestHandler {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req as AuthenticatedRequest, res).catch(next);
    return undefined;
  };
}

// Channel routes
router.get(
  '/channels',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getUserChannels(req, res);
  }),
);

router.get(
  '/channels/public',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getPublicChannels(req, res);
  }),
);

router.get(
  '/channels/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getChannelById(req, res);
  }),
);

router.post(
  '/channels',
  clerkAuthMiddleware,
  validate(CreateChannelDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.createChannel(req, res);
  }),
);

router.put(
  '/channels/:id',
  clerkAuthMiddleware,
  validate(UpdateChannelDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.updateChannel(req, res);
  }),
);

router.delete(
  '/channels/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.deleteChannel(req, res);
  }),
);

router.post(
  '/channels/:id/members',
  clerkAuthMiddleware,
  validate(ChannelMemberDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.addChannelMember(req, res);
  }),
);

router.delete(
  '/channels/:id/members/:userId',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.removeChannelMember(req, res);
  }),
);

// Message routes
router.get(
  '/channels/:id/messages',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getChannelMessages(req, res);
  }),
);

router.get(
  '/messages/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getMessageById(req, res);
  }),
);

router.post(
  '/channels/:id/messages',
  clerkAuthMiddleware,
  validate(CreateMessageDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.createMessage(req, res);
  }),
);

router.put(
  '/messages/:id',
  clerkAuthMiddleware,
  validate(UpdateMessageDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.updateMessage(req, res);
  }),
);

router.delete(
  '/messages/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.deleteMessage(req, res);
  }),
);

router.post(
  '/messages/:id/reactions',
  clerkAuthMiddleware,
  validate(MessageReactionDto),
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.addReaction(req, res);
  }),
);

router.delete(
  '/messages/:id/reactions/:emoji',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.removeReaction(req, res);
  }),
);

// Notification routes
router.get(
  '/notifications',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.getUserNotifications(req, res);
  }),
);

router.post(
  '/notifications/:id/read',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.markNotificationAsRead(req, res);
  }),
);

router.post(
  '/notifications/read-all',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.markAllNotificationsAsRead(req, res);
  }),
);

router.delete(
  '/notifications/:id',
  clerkAuthMiddleware,
  createHandler(async (req: AuthenticatedRequest, res: Response) => {
    await socialController.deleteNotification(req, res);
  }),
);

export default router;
