/**
 * Onboarding Routes
 * 
 * API endpoints for onboarding profile extraction and management
 */

import { Router, Request, Response } from 'express';
import { onboardingProfileService } from '../services/onboarding-profile.service';
import { logger } from '../common/logger';
import { unifiedAuthMiddleware } from '../middleware/unified-auth';

const router = Router();

/**
 * @swagger
 * /onboarding/extract-profile:
 *   post:
 *     summary: Extract user profile from conversation messages
 *     description: Analyzes conversation messages and extracts user profile information
 *     tags: [Onboarding]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               messages:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     role:
 *                       type: string
 *                       enum: [user, assistant]
 *                     content:
 *                       type: string
 *               projectDir:
 *                 type: string
 *                 description: Optional project directory path
 *     responses:
 *       200:
 *         description: Profile extracted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 profile:
 *                   type: object
 *                 summary:
 *                   type: object
 *                 confidence:
 *                   type: number
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.post('/extract-profile', unifiedAuthMiddleware, async (req: Request, res: Response) => {
  try {
    logger.info('🎯 [ONBOARDING-ROUTE] Profile extraction request received');
    const { messages, projectDir } = req.body;

    logger.info(`🎯 [ONBOARDING-ROUTE] Messages count: ${messages?.length}, ProjectDir: ${projectDir || 'default'}`);

    if (!messages || !Array.isArray(messages)) {
      logger.warn('🎯 [ONBOARDING-ROUTE] Invalid messages array');
      return res.status(400).json({
        status: 'error',
        message: 'Messages array is required'
      });
    }

    logger.info('🎯 [ONBOARDING-ROUTE] Calling profile service...');
    
    const result = await onboardingProfileService.processOnboardingConversation(
      messages,
      projectDir
    );

    logger.info('🎯 [ONBOARDING-ROUTE] Profile extraction completed successfully');

    res.json({
      status: 'success',
      ...result
    });

  } catch (error) {
    logger.error('Error extracting profile:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to extract profile',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /onboarding/profile:
 *   get:
 *     summary: Get current user profile
 *     description: Retrieves the user profile from .kapi/user_profile.yaml
 *     tags: [Onboarding]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: projectDir
 *         schema:
 *           type: string
 *         description: Project directory path
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *       404:
 *         description: Profile not found
 *       500:
 *         description: Server error
 */
router.get('/profile', unifiedAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { projectDir } = req.query;

    const profile = await onboardingProfileService.loadProfile(
      projectDir as string
    );

    if (!profile) {
      return res.status(404).json({
        status: 'error',
        message: 'Profile not found'
      });
    }

    res.json({
      status: 'success',
      profile
    });

  } catch (error) {
    logger.error('Error loading profile:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to load profile',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /onboarding/profile:
 *   put:
 *     summary: Update user profile
 *     description: Updates the user profile in .kapi/user_profile.yaml
 *     tags: [Onboarding]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               profile:
 *                 type: object
 *               projectDir:
 *                 type: string
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.put('/profile', unifiedAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { profile, projectDir } = req.body;

    if (!profile) {
      return res.status(400).json({
        status: 'error',
        message: 'Profile data is required'
      });
    }

    await onboardingProfileService.saveProfile(profile, projectDir);

    res.json({
      status: 'success',
      message: 'Profile updated successfully'
    });

  } catch (error) {
    logger.error('Error updating profile:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update profile',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;