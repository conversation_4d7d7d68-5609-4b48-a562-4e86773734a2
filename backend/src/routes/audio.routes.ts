import { Router } from 'express';
import AudioService from '../services/audio.service';
import { asyncHand<PERSON> } from '../utils/asyncHandler';

const router = Router();
const audioService = new AudioService();

/**
 * @swagger
 * /api/audio/tts:
 *   post:
 *     summary: Convert text to speech using Google TTS
 *     tags: [Audio]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text to convert to speech
 *                 example: "Hello, this is a test message"
 *               voice:
 *                 type: string
 *                 description: Voice to use for synthesis
 *                 default: alloy
 *                 enum: [standard-a, standard-b, standard-c, standard-d, standard-e, standard-f, neural2-a, neural2-c, neural2-d, neural2-e, neural2-f, male, female, alloy, echo, fable, onyx, nova, shimmer]
 *     responses:
 *       200:
 *         description: Audio generated successfully
 *         content:
 *           audio/mpeg:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: TTS generation failed
 */
router.post('/tts', asyncHandler(async (req, res) => {
  const { text, voice = 'alloy' } = req.body;

  if (!text || typeof text !== 'string') {
    return res.status(400).json({
      error: 'Text is required and must be a string'
    });
  }

  if (text.length > 4000) {
    return res.status(400).json({
      error: 'Text too long. Maximum 4000 characters allowed.'
    });
  }

  try {
    const audioBuffer = await audioService.textToSpeech(text, voice);
    
    res.set({
      'Content-Type': 'audio/mpeg',
      'Content-Length': audioBuffer.length.toString(),
      'Cache-Control': 'public, max-age=300' // 5 minute cache
    });
    
    res.send(audioBuffer);
  } catch (error) {
    console.error('TTS generation error:', error);
    res.status(500).json({
      error: 'Failed to generate speech',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * @swagger
 * /api/audio/voices:
 *   get:
 *     summary: Get available TTS voices
 *     tags: [Audio]
 *     responses:
 *       200:
 *         description: List of available voices
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 properties:
 *                   languageCode:
 *                     type: string
 *                   name:
 *                     type: string
 *                   ssmlGender:
 *                     type: string
 */
router.get('/voices', asyncHandler(async (req, res) => {
  const voices = audioService.getAvailableVoices();
  res.json(voices);
}));

/**
 * @swagger
 * /api/audio/stt:
 *   post:
 *     summary: Convert speech to text using Azure Whisper
 *     tags: [Audio]
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - audio
 *             properties:
 *               audio:
 *                 type: string
 *                 format: binary
 *                 description: Audio file (mp3, wav, m4a)
 *     responses:
 *       200:
 *         description: Transcription successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 text:
 *                   type: string
 *                   description: Transcribed text
 *       400:
 *         description: Invalid audio file
 *       500:
 *         description: Transcription failed
 */
router.post('/stt', asyncHandler(async (req, res) => {
  if (!(req as any).file) {
    return res.status(400).json({
      error: 'Audio file is required'
    });
  }

  try {
    const transcribedText = await audioService.transcribeAudioBytes(
      (req as any).file.buffer,
      (req as any).file.originalname
    );
    
    res.json({ text: transcribedText });
  } catch (error) {
    console.error('STT transcription error:', error);
    res.status(500).json({
      error: 'Failed to transcribe audio',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

export default router;