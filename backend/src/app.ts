import path from 'path';

import compression from 'compression';
import cookieParser from 'cookie-parser';
import express, { Request, Response, NextFunction, Application, Router } from 'express';
import session from 'express-session';
import nunjucks from 'nunjucks';
import swaggerUi from 'swagger-ui-express';

import { logger } from './common/logger';
import { secureErrorHandler, notFoundHandler } from './common/middleware/error.middleware';
import {
  apiRateLimiter,
  configureCors,
  configureHelmet,
  sanitizeInput,
  securityHeaders,
} from './common/middleware/security.middleware';
import { specs } from './common/swagger';
import { initNextApp, nextMiddleware } from './middleware/next.middleware';
import routes from './routes';

// Documentation and Search routes are imported later, after admin routes are mounted
const app: Application = express();

// Initialize Next.js
initNextApp().catch((err) => {
  console.error('Failed to initialize Next.js:', err);
});

// Add Next.js middleware
app.use('/web', nextMiddleware);

// Configure Nunjucks
const nunjucksEnv = nunjucks.configure(path.join(__dirname, 'views'), {
  autoescape: true,
  express: app,
  noCache: process.env.NODE_ENV !== 'production',
});

// Add a custom filter to handle template paths
nunjucksEnv.addFilter('template', (str: string) => str);

app.set('view engine', 'html');

// Raw body middleware for webhook verification
const rawBodyMiddleware = (req: Request, _res: Response, next: NextFunction) => {
  // This middleware is specifically for webhooks that need the raw body for signature verification.
  // It should only be applied to specific webhook routes.
  req.rawBody = '';
  req.on('data', (chunk) => {
    req.rawBody += chunk;
  });
  req.on('end', () => {
    next();
  });
};

// Apply raw body middleware only to webhook routes
app.use('/api/webhooks', rawBodyMiddleware);

// Security Middlewares
app.use(configureHelmet());
app.use(configureCors());
app.use(securityHeaders);

// General middlewares
app.use(
  compression({
    filter: (req: Request, res: Response) => {
      // Don't compress SSE streams
      if (res.getHeader('Content-Type') === 'text/event-stream') {
        return false;
      }
      // Don't compress if skipCompression is set
      if (res.locals && res.locals.skipCompression) {
        return false;
      }
      // Default compression filter
      return compression.filter(req, res);
    },
  }),
);

// Regular JSON parsing with default limit
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Input sanitization
app.use(sanitizeInput);

// Rate limiting for API routes (exclude auth routes)
app.use('/api', apiRateLimiter);

// Add global session middleware
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'kapi-global-secret',
    resave: true,
    saveUninitialized: true,
    name: 'kapi_session',
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      httpOnly: true,
      path: '/',
      sameSite: 'lax',
    },
  }),
);

// Log session info for debugging
app.use((req, res, next) => {
  logger.debug(`Session ID: ${req.sessionID}`);
  logger.debug(`Session data (sample): ${JSON.stringify(req.session || {}).substring(0, 100)}...`);
  next();
});

// Serve static files
app.use('/static', express.static(path.join(__dirname, 'public')));
app.use('/js', express.static(path.join(__dirname, 'public/js')));
app.use('/nova-sonic', express.static(path.join(__dirname, 'public/nova-sonic')));

// Serve favicon.ico from root public directory
app.get('/favicon.ico', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/favicon.ico'));
});

// Health check endpoints
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'API is healthy',
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API documentation
app.use(
  '/api-docs',
  swaggerUi.serve,
  swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'KAPI API Documentation',
  }),
);

// API Routes
app.use('/api', routes);

// Asynchronously mount routes that depend on external initializations (like the admin UI)
const mountDynamicRoutes = async (expressApp: Application) => {
  // The admin UI router is attached dynamically. We wait for it to be available.
  // This is a workaround for the async initialization in routes/index.ts
  await new Promise<void>((resolve) => {
    const interval = setInterval(() => {
      const extendedRouter = routes as Router & { uiRouter?: Router };
      if (extendedRouter.uiRouter) {
        expressApp.use('/admin', extendedRouter.uiRouter);
        logger.info('Admin UI routes mounted at /admin');
        clearInterval(interval);
        resolve();
      }
    }, 100);

    setTimeout(() => {
      clearInterval(interval);
      const extendedRouter = routes as Router & { uiRouter?: Router };
      if (!extendedRouter.uiRouter) {
        logger.error('Failed to mount admin UI routes after 5 seconds');
      }
      resolve(); // Resolve anyway to not block server start
    }, 5000);
  });

  // Documentation and Search routes can be loaded now
  const documentationRoutes = await import('./routes/documentation.routes');
  expressApp.use('/api/documentation', documentationRoutes.default);

  const { semanticSearchRoutes } = await import('./routes/semantic-search.routes');
  expressApp.use('/api/search', semanticSearchRoutes);

  // Default route
  expressApp.get('/', (_req: Request, res: Response) => {
    res.json({ message: 'KAPI API Server', status: 'running', version: '1.0.0' });
  });

  // Final handlers must be last
  expressApp.use(notFoundHandler);
  expressApp.use(secureErrorHandler);
};

mountDynamicRoutes(app).catch((err) => {
  logger.error('Failed to mount dynamic routes:', err);
});

export default app;
