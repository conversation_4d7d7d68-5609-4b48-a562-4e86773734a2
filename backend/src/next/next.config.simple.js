/** @type {import('next').NextConfig} */

// Load environment variables from the parent directory
require('dotenv').config({ path: '../../.env.local' });

const nextConfig = {
    reactStrictMode: true,
    output: 'standalone',
    // Ensure static files are properly served
    assetPrefix: process.env.NODE_ENV === 'production' ? undefined : '',
    // Simplified webpack config for Linux compatibility
    webpack: (config, { dev }) => {
      // Ensure consistent path resolution across platforms
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': require('path').resolve(__dirname),
      };
      
      return config;
    },
    // Explicitly pass environment variables to Next.js
    env: {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
      NEXT_PUBLIC_CLERK_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '/signin',
      NEXT_PUBLIC_CLERK_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '/signup',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '/kapi',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '/kapi',
    },
    // This enables relative API URLs to work properly
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:3000/api/:path*',
        },
        {
          source: '/health',
          destination: 'http://localhost:3000/health',
        },
      ];
    },
  };
  
  module.exports = nextConfig;