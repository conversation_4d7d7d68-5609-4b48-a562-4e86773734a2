<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  <rect width="1200" height="630" fill="url(#bg-gradient)" />
  
  <!-- Grid pattern for background -->
  <defs>
    <pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)" />

  <!-- Main Title -->
  <text x="600" y="100" font-family="Arial" font-size="48" fill="white" text-anchor="middle" font-weight="bold">
    7 Best Practices for Vibe Coding
  </text>
  <text x="600" y="150" font-family="Arial" font-size="24" fill="rgba(255,255,255,0.8)" text-anchor="middle">
    Bringing software discipline to AI-assisted development
  </text>
  
  <!-- Divider -->
  <line x1="300" y1="180" x2="900" y2="180" stroke="rgba(255,255,255,0.6)" stroke-width="2" />
  
  <!-- Best Practices Icons and Text -->
  <!-- Practice 1 -->
  <circle cx="200" cy="250" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="200" y="265" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">1</text>
  <text x="270" y="240" font-family="Arial" font-size="24" fill="white" font-weight="bold">Start with docs</text>
  <text x="270" y="270" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Write requirements before code</text>
  
  <!-- Practice 2 -->
  <circle cx="200" cy="350" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="200" y="365" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">2</text>
  <text x="270" y="340" font-family="Arial" font-size="24" fill="white" font-weight="bold">Test first</text>
  <text x="270" y="370" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Define behavior with tests</text>
  
  <!-- Practice 3 -->
  <circle cx="200" cy="450" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="200" y="465" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">3</text>
  <text x="270" y="440" font-family="Arial" font-size="24" fill="white" font-weight="bold">Use templates</text>
  <text x="270" y="470" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Start with proven patterns</text>
  
  <!-- Practice 4 -->
  <circle cx="650" cy="250" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="650" y="265" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">4</text>
  <text x="720" y="240" font-family="Arial" font-size="24" fill="white" font-weight="bold">Precise prompts</text>
  <text x="720" y="270" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Be specific in your instructions</text>
  
  <!-- Practice 5 -->
  <circle cx="650" cy="350" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="650" y="365" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">5</text>
  <text x="720" y="340" font-family="Arial" font-size="24" fill="white" font-weight="bold">Verify output</text>
  <text x="720" y="370" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Always review generated code</text>
  
  <!-- Practice 6 -->
  <circle cx="650" cy="450" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="650" y="465" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">6</text>
  <text x="720" y="440" font-family="Arial" font-size="24" fill="white" font-weight="bold">Refactor regularly</text>
  <text x="720" y="470" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Cleanup at each iteration</text>
  
  <!-- Practice 7 -->
  <circle cx="425" cy="550" r="40" fill="rgba(255,255,255,0.2)" />
  <text x="425" y="565" font-family="Arial" font-size="32" fill="white" text-anchor="middle" font-weight="bold">7</text>
  <text x="495" y="540" font-family="Arial" font-size="24" fill="white" font-weight="bold">Measure constantly</text>
  <text x="495" y="570" font-family="Arial" font-size="18" fill="rgba(255,255,255,0.8)">Track metrics to improve</text>
  
  <!-- KAPI Logo or Brand -->
  <text x="1100" y="600" font-family="Arial" font-size="20" fill="white" text-anchor="end" font-weight="bold">
    KAPI
  </text>
</svg>