<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="500" viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="traditionalGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8787;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="backwardsBuildGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4d96ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6eb6ff;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000033" />
    </filter>
  </defs>

  <!-- Background -->
  <rect width="800" height="500" fill="#f8fafc" rx="10" ry="10" />
  
  <!-- Title -->
  <text x="400" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#334155">Backwards-Build Approach</text>
  
  <!-- Traditional Flow (Left) -->
  <g transform="translate(140, 100)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#334155">Traditional Approach</text>
    
    <!-- Flow diagram -->
    <rect x="-100" y="20" width="200" height="60" rx="5" ry="5" fill="url(#traditionalGradient)" filter="url(#shadow)" />
    <text x="0" y="55" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">1. Write Code</text>
    
    <line x1="0" y1="80" x2="0" y2="100" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="100" width="200" height="60" rx="5" ry="5" fill="url(#traditionalGradient)" opacity="0.9" filter="url(#shadow)" />
    <text x="0" y="135" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">2. Write Tests</text>
    
    <line x1="0" y1="160" x2="0" y2="180" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="180" width="200" height="60" rx="5" ry="5" fill="url(#traditionalGradient)" opacity="0.8" filter="url(#shadow)" />
    <text x="0" y="215" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">3. Document</text>
    
    <line x1="0" y1="240" x2="0" y2="260" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="260" width="200" height="60" rx="5" ry="5" fill="url(#traditionalGradient)" opacity="0.7" filter="url(#shadow)" />
    <text x="0" y="295" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">4. Define Business Case</text>
    
    <!-- Problems -->
    <g transform="translate(-120, 350)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• Rework</text>
      <text x="0" y="25" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• Misalignment</text>
      <text x="0" y="50" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• Technical Debt</text>
    </g>
  </g>
  
  <!-- Backwards-Build Flow (Right) -->
  <g transform="translate(580, 100)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#334155">Backwards-Build</text>
    
    <!-- Flow diagram -->
    <rect x="-100" y="260" width="200" height="60" rx="5" ry="5" fill="url(#backwardsBuildGradient)" filter="url(#shadow)" />
    <text x="0" y="295" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">4. Write Code</text>
    
    <line x1="0" y1="240" x2="0" y2="260" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="180" width="200" height="60" rx="5" ry="5" fill="url(#backwardsBuildGradient)" opacity="0.9" filter="url(#shadow)" />
    <text x="0" y="215" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">3. Write Tests</text>
    
    <line x1="0" y1="160" x2="0" y2="180" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="100" width="200" height="60" rx="5" ry="5" fill="url(#backwardsBuildGradient)" opacity="0.8" filter="url(#shadow)" />
    <text x="0" y="135" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">2. Create Documentation</text>
    
    <line x1="0" y1="80" x2="0" y2="100" stroke="#cbd5e1" stroke-width="3" stroke-dasharray="5,5" />
    
    <rect x="-100" y="20" width="200" height="60" rx="5" ry="5" fill="url(#backwardsBuildGradient)" opacity="0.7" filter="url(#shadow)" />
    <text x="0" y="55" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">1. Business Case Slides</text>
    
    <!-- Benefits -->
    <g transform="translate(-100, 350)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• 40% Less Rework</text>
      <text x="0" y="25" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• 60-80% Token Savings</text>
      <text x="0" y="50" font-family="Arial, sans-serif" font-size="14" fill="#64748b">• 90% Test Coverage</text>
    </g>
  </g>
  
  <!-- Comparison Arrows -->
  <g transform="translate(400, 250)">
    <polygon points="0,0 -60,-15 -60,15" fill="#cbd5e1" />
    <polygon points="0,0 60,-15 60,15" fill="#cbd5e1" />
    <text x="0" y="35" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#64748b">VS</text>
  </g>
  
  <!-- Bottom Note -->
  <text x="400" y="480" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#334155">KAPI's Backwards-Build Approach</text>
</svg>