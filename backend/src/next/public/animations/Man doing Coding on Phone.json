{"v": "5.12.2", "fr": 29.97, "ip": 0, "op": 120, "w": 600, "h": 600, "nm": "Comp 1", "assets": [], "layers": [{"ind": 1, "ty": 4, "nm": "Layer 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [95]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 49, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [95]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 101, "s": [0]}, {"t": 106, "s": [95]}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [351.559, 183.079, 0], "l": 2}, "a": {"a": 0, "k": [51.559, -116.921, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.272, -3.701], [-3.042, -0.295], [-0.272, 3.701], [3.042, 0.295]], "o": [[-0.272, 3.701], [3.042, 0.295], [0.272, -3.701], [-3.042, -0.295]], "v": [[64.705, -119.554], [69.72, -112.319], [75.721, -118.485], [70.706, -125.721]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.272, -3.701], [-3.042, -0.295], [-0.272, 3.701], [3.042, 0.295]], "o": [[-0.272, 3.701], [3.042, 0.295], [0.272, -3.701], [-3.042, -0.295]], "v": [[27.398, -115.357], [32.413, -108.121], [38.414, -114.288], [33.399, -121.523]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2"}], "ip": 0, "op": 120, "st": 0, "ct": 1}, {"ind": 2, "ty": 4, "nm": "Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [11]}, {"t": 119, "s": [0]}]}, "p": {"a": 0, "k": [488.087, 266.528, 0], "l": 2}, "a": {"a": 0, "k": [188.087, -33.472, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.18, -2.675], [2.675, -0.18], [0.18, 2.675], [-2.675, 0.18]], "o": [[0.18, 2.675], [-2.675, 0.18], [-0.18, -2.675], [2.675, -0.18]], "v": [[6.532, 55.024], [2.013, 60.194], [-3.156, 55.676], [1.362, 50.506]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.18, -2.675], [2.675, -0.18], [0.18, 2.675], [-2.675, 0.18]], "o": [[0.18, 2.675], [-2.675, 0.18], [-0.18, -2.675], [2.675, -0.18]], "v": [[3.812, 43.529], [-0.706, 48.699], [-5.876, 44.181], [-1.358, 39.011]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.325, -1.756]], "o": [[7.41, 4.182], [0, 0]], "v": [[5.133, 107.02], [28.921, 115.998]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-3.255, -1.469]], "o": [[3.255, 1.469], [0, 0]], "v": [[44.575, 79.213], [54.339, 83.621]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.892, -1.102]], "o": [[10.793, 5.112], [0, 0]], "v": [[-1.276, 94.076], [33.103, 103.492]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-9.896, -2.249]], "o": [[9.896, 2.249], [0, 0]], "v": [[-2.649, 82.394], [27.039, 89.143]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[172.243, 143.992], [186.117, 150.632]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.014, 1.004], [0, 0]], "o": [[0, 0], [-0.993, -0.148], [0, 0], [0, 0]], "v": [[170.404, 124.567], [157.904, 122.704], [156.193, 120.692], [157.563, 20.763]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-9.904, -17.701], [-2.969, -20.065], [0.909, -21.587], [2.161, -0.178], [5.877, -0.219], [10.327, 1.279], [6.952, 1.654], [3.475, 1.037], [1.719, 0.574], [-0.085, 2.776], [-0.296, 4.248], [-0.512, 8.502], [-0.453, 7.531], [0.412, 3.416], [0.017, 2.435]], "o": [[19.379, 5.989], [9.904, 17.701], [3.16, 21.352], [-0.098, 2.338], [-5.887, 0.484], [-10.398, 0.388], [-7.092, -0.878], [-3.528, -0.839], [-1.737, -0.518], [-3.186, -1.063], [0.13, -4.256], [0.593, -8.496], [0.453, -7.531], [0.206, -3.427], [-0.284, -2.352], [0, 0]], "v": [[183.406, -35.109], [228.3, 3.765], [245.911, 61.655], [249.746, 126.478], [245.884, 129.551], [228.161, 131.111], [197.007, 129.768], [175.919, 125.968], [165.412, 123.153], [160.224, 121.524], [157.647, 114.334], [158.319, 101.578], [160.226, 76.098], [161.586, 53.506], [162.078, 42.885], [161.027, 36.08]], "c": false}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-2.934, -1.607], [-1.511, 0.567], [-0.625, 1.273], [-1.677, 3.417], [0.433, 1.639], [1.6, 0.913], [3.16, 1.531], [1.15, -0.215], [0.769, -0.972], [0.971, -5.443], [-2.417, -1.324]], "o": [[1.415, 0.775], [1.328, -0.498], [1.677, -3.417], [0.747, -1.522], [-0.47, -1.782], [-3.049, -1.741], [-1.053, -0.51], [-1.218, 0.228], [-3.426, 4.332], [-0.544, 3.047], [2.934, 1.607]], "v": [[84.445, 143.109], [89.019, 144.115], [91.714, 140.96], [96.743, 130.708], [97.824, 125.856], [94.032, 122.046], [84.715, 117.136], [81.364, 116.33], [78.46, 118.517], [71.658, 133.522], [75.643, 138.288]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-3.716, -2.035], [-1.914, 0.718], [-0.791, 1.613], [-2.124, 4.328], [0.548, 2.076], [2.027, 1.157], [4.002, 1.939], [1.456, -0.273], [0.974, -1.231], [1.23, -6.894], [-3.061, -1.677]], "o": [[1.792, 0.982], [1.682, -0.631], [2.124, -4.328], [0.946, -1.928], [-0.596, -2.257], [-3.862, -2.205], [-1.333, -0.646], [-1.543, 0.289], [-4.339, 5.486], [-0.689, 3.86], [3.716, 2.035]], "v": [[84.361, 146.314], [90.155, 147.587], [93.567, 143.591], [99.938, 130.606], [101.306, 124.46], [96.504, 119.634], [84.703, 113.416], [80.458, 112.395], [76.78, 115.165], [68.165, 134.17], [73.212, 140.207]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-5.014, -2.047], [6.319, -9.642], [2.986, -2.087], [0.633, -0.326], [3.975, 2.571], [0.297, 0.208], [-0.781, 1.058], [-2.611, 6.903], [-0.85, 4.074], [-0.752, 1.019]], "o": [[-1.681, 11.412], [-1.998, 3.046], [-0.573, 0.395], [-3.975, -2.581], [-0.307, -0.198], [1.266, -1.473], [4.371, -5.953], [1.483, -3.896], [0.267, -1.276], [5.014, 2.047]], "v": [[98.689, 113.93], [86.505, 146.01], [79.267, 154.099], [77.457, 155.187], [65.521, 147.453], [64.621, 146.85], [68.231, 142.885], [78.763, 123.492], [82.263, 111.507], [83.638, 107.779]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.826, -15.714], [15.763, -4.658], [13.904, 5.33], [25.059, 16.228], [1.968, 1.266], [3.975, 2.571], [0.297, 0.208], [5.113, 2.838], [6.536, 3.086], [7.043, 3.326], [1.646, 1.478], [-2.571, 2.057], [-1.051, -0.297], [-0.694, 4.613], [-2.089, 0.253], [1.157, 2.65], [-0.62, 1.279], [-0.99, -0.124], [0.752, 2.176], [-1.612, 1.642], [-3.6, -0.109], [-4.687, -0.722], [-5.645, -1.753], [-2.827, -0.749], [-0.881, 1.929], [0.076, 1.072], [0.183, 2.587], [-2.706, -2.276], [-2.831, -3.021], [-5.024, -10.215], [-0.227, -0.089], [-5.014, -2.047], [-24.515, -10.018], [0.178, 8.326], [-7.753, -0.91], [-14.814, -1.74]], "o": [[-4.826, 15.714], [-14.27, 4.232], [-27.936, -10.71], [-1.968, -1.276], [-3.975, -2.581], [-0.307, -0.198], [-4.895, -3.283], [-6.258, -3.481], [-7.043, -3.326], [-2.001, -0.945], [-1.874, -1.683], [0.853, -0.682], [-3.398, -1.518], [0.436, -2.894], [-1.734, -2.109], [-0.583, -1.335], [0.359, -0.74], [-2.294, -0.287], [-0.761, -2.176], [2.917, -2.986], [4.737, 0.139], [5.846, 0.9], [2.798, 0.869], [1.819, 0.482], [0.447, -0.978], [-0.183, -2.587], [2.889, 2.032], [3.168, 2.665], [7.842, 8.376], [0.227, 0.089], [5.014, 2.047], [24.515, 10.018], [-0.168, -8.317], [7.773, -0.771], [14.804, 1.731], [0.494, 16.426]], "v": [[235.226, 174.747], [203.493, 209.853], [160.03, 204.216], [83.351, 159.004], [77.457, 155.187], [65.521, 147.453], [64.621, 146.85], [50.223, 136.882], [30.438, 127.76], [9.31, 117.784], [3.631, 114.396], [2.153, 107.459], [5.245, 106.833], [-5.606, 97.179], [-1.092, 94.331], [-6.394, 88.352], [-6.584, 84.806], [-3.504, 81.761], [-8.607, 77.637], [-7.183, 71.229], [2.973, 69.469], [17.124, 70.734], [34.358, 74.86], [42.742, 77.652], [47.925, 75.948], [48.229, 72.774], [47.68, 65.011], [56.146, 71.313], [65.155, 79.852], [82.965, 107.502], [83.638, 107.779], [98.689, 113.93], [172.243, 143.992], [171.146, 119.517], [194.493, 120.565], [239.113, 125.826]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.415, 1.173], [-0.278, 0.664], [-1.595, 0.18], [0, 0], [-0.229, -0.98], [0, 0], [0.456, -0.484], [0, 0], [0.388, 1.515], [0, 0], [0.984, -0.111], [3.006, -0.339], [6.021, -0.68], [2.736, -0.309], [1.055, -0.097], [2.002, -0.791], [0.173, 0.12], [-0.061, 0.248]], "o": [[0.24, -0.678], [0.532, -1.269], [0.006, -0.001], [1, -0.113], [0, 0], [0.151, 0.648], [0, 0], [-1.073, 1.138], [0, 0], [-0.246, -0.959], [-3.006, 0.339], [-6.021, 0.68], [-2.736, 0.309], [-1.052, 0.119], [-2.129, 0.196], [-0.196, 0.077], [-0.21, -0.146], [0.296, -1.209]], "v": [[-15.369, 29.375], [-14.591, 27.362], [-11.83, 23.325], [34.474, 18.098], [36.629, 19.619], [49.304, 73.868], [48.815, 75.681], [46.64, 77.989], [43.273, 77.12], [30.914, 28.826], [28.769, 27.346], [19.751, 28.364], [1.689, 30.403], [-6.518, 31.33], [-9.678, 31.686], [-15.718, 33.629], [-16.322, 33.66], [-16.436, 32.951]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.296, 1.147], [1.996, 7.821], [0.985, -0.111], [0, 0], [-0.317, -1.173], [0, 0], [-0.991, 0.139], [0, 0]], "o": [[-2.272, -8.807], [-0.245, -0.96], [0, 0], [-1.208, 0.136], [0, 0], [0.261, 0.966], [0, 0], [1.173, -0.165]], "v": [[43.61, 78.473], [30.912, 28.821], [28.768, 27.346], [-14.042, 32.179], [-15.734, 34.669], [-2.023, 85.356], [0.169, 86.803], [41.962, 80.923]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15"}], "ip": 0, "op": 120, "st": 0, "ct": 1}, {"ind": 3, "ty": 4, "nm": "Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 300, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.315, 0.811], [0, 0]], "o": [[0, 0], [0, 0], [-0.315, -0.811], [0, 0]], "v": [[62.802, -44.91], [42.549, -21.441], [71.275, 4.093], [67.993, -23.419]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-1.162, 1.588]], "o": [[1.162, -1.588], [0, 0]], "v": [[115.975, -48.937], [119.461, -53.702]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.038, 0.149], [-0.028, 0.044], [-0.494, -0.023], [-1.361, -0.063], [-1.679, -0.078], [-0.371, -0.371], [0, 0], [0.263, -0.761], [0, 0], [0.965, 1.447], [0, 0], [-0.529, 0.713], [-3.534, 4.84], [-4.472, 6.45], [-1.273, 2.02], [-0.18, 0.688], [0.189, 1.153], [0.243, 1.485]], "o": [[0.013, -0.05], [0.241, -0.375], [1.361, 0.063], [1.679, 0.078], [0.524, 0.024], [0, 0], [0.57, 0.57], [0, 0], [-0.569, 1.644], [0, 0], [-0.493, -0.739], [3.571, -4.813], [4.628, -6.338], [1.36, -1.962], [0.38, -0.603], [0.295, -1.127], [-0.243, -1.485], [-0.025, -0.156]], "v": [[114.566, -57.626], [114.627, -57.768], [118.049, -57.704], [122.133, -57.514], [127.169, -57.279], [128.559, -56.665], [137.622, -47.603], [138.122, -45.427], [112.427, 28.849], [108.688, 29.328], [86.15, -4.478], [86.206, -6.891], [96.864, -21.371], [110.58, -40.51], [114.572, -46.46], [115.865, -48.603], [115.307, -52.699], [114.577, -57.153]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.515, -1.982], [2.973, -0.772], [0.515, 1.982], [-2.973, 0.772]], "o": [[0.515, 1.982], [-2.973, 0.772], [-0.515, -1.982], [2.973, -0.772]], "v": [[38.73, -96.545], [34.278, -91.559], [27.962, -93.75], [32.414, -98.737]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.929, 0.741, 0.804, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.515, -1.982], [2.973, -0.772], [0.515, 1.982], [-2.973, 0.772]], "o": [[0.515, 1.982], [-2.973, 0.772], [-0.515, -1.982], [2.973, -0.772]], "v": [[90.126, -105.737], [85.673, -100.75], [79.358, -102.942], [83.81, -107.929]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.929, 0.741, 0.804, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-1.473, 1.673], [0.27, 2.213]], "o": [[2.229, -0.013], [1.473, -1.673], [0, 0]], "v": [[64.433, -85.607], [70.341, -88.298], [72.261, -94.5]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-3.376, 5.704]], "o": [[3.376, -5.704], [0, 0]], "v": [[93.858, -107.131], [103.987, -124.243]], "c": false}}, "nm": "Path 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [4.821, -9.957], [-5.351, 3.07], [-5.493, -2.808]], "o": [[6.807, 8.72], [0.348, -6.159], [5.351, -3.07], [0, 0]], "v": [[91.177, -206.1], [94.508, -174.778], [104.542, -189.21], [122.291, -189.639]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-6.901, -5.75], [2.062, -17.199], [7.413, -2.375], [2.391, -2.71], [0.654, -1.927], [-0.815, -0.822], [-0.739, 11.11], [-1.617, -0.384], [7.353, -13.062], [-2.422, 4.422], [1.643, 0.239], [1.056, -0.393], [1.914, -1.955], [0.128, -4.63], [0.246, 2.77], [-0.365, 1.373], [-0.119, 1.04], [2.724, -3.252], [1.485, -5.723], [-1.667, 6.246], [-5.679, 3.089], [2.754, -5.797], [-0.195, 1.313], [-0.738, 1.8], [-2.87, 2.792], [-4.095, 0.96], [-4.267, -0.785], [3.945, 2.731], [-5.905, -0.807], [-3.52, -4.81], [-8.147, 0.162], [-2.404, -7.786]], "o": [[14.235, 11.86], [-4.231, -6.705], [-3.45, 1.105], [-1.346, 1.526], [-0.316, 0.932], [-7.254, -7.315], [0.072, -1.084], [-12.919, -3.078], [-1.531, -5.294], [0.652, -1.19], [-1.143, -0.166], [-2.562, 0.953], [-3.24, 3.31], [-1.738, -2.179], [-0.125, -1.406], [0.277, -1.043], [-4.769, -0.147], [-3.793, 4.529], [-3.384, -5.508], [1.667, -6.246], [-6.391, -0.592], [0.518, -1.09], [0.285, -1.924], [1.541, -3.76], [2.894, -2.816], [4.238, -0.994], [-2.714, -3.868], [4.682, -3.689], [5.905, 0.807], [2.093, -7.875], [8.147, -0.162], [7.995, -4.094]], "v": [[120.329, -177.996], [135.228, -122.54], [114.887, -129.456], [105.405, -122.899], [102.362, -117.649], [101.623, -111.962], [86.833, -139.592], [89.356, -147.998], [52.553, -138.271], [55.51, -151.579], [54.972, -153.143], [50.994, -152.137], [44.159, -147.721], [38.858, -135.225], [35.842, -142.886], [36.101, -147.202], [37.302, -150.212], [25.355, -140.985], [17.261, -125.375], [14.535, -144.043], [26.202, -158.869], [10.487, -149.924], [10.666, -155.057], [12.215, -160.669], [19.021, -170.029], [28.413, -175.458], [41.351, -175.606], [28.393, -185.067], [45.186, -189.638], [60.136, -180.728], [76.713, -195.154], [95.18, -181.81]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-6.901, -5.75], [2.062, -17.199], [8.008, -6.256], [0.606, -4.958], [1.979, 7.16], [-4.122, 5.966], [7.353, -13.062], [-2.422, 4.422], [1.643, 0.239], [1.056, -0.393], [1.914, -1.955], [0.128, -4.63], [0.246, 2.77], [-0.365, 1.373], [-0.119, 1.04], [2.724, -3.252], [1.485, -5.723], [-1.667, 6.246], [-5.679, 3.089], [2.754, -5.797], [-0.195, 1.313], [-0.738, 1.8], [-2.87, 2.792], [-4.095, 0.96], [-4.267, -0.785], [3.945, 2.731], [-5.905, -0.807], [-3.52, -4.81], [-8.147, 0.162], [-2.404, -7.786]], "o": [[14.235, 11.86], [-5.456, -8.647], [-3.936, 3.074], [-6.416, -4.174], [-1.724, -6.238], [-12.919, -3.078], [-1.531, -5.294], [0.652, -1.19], [-1.143, -0.166], [-2.562, 0.953], [-3.24, 3.31], [-1.738, -2.179], [-0.125, -1.406], [0.277, -1.043], [-4.769, -0.147], [-3.793, 4.529], [-3.384, -5.508], [1.667, -6.246], [-6.391, -0.592], [0.518, -1.09], [0.285, -1.924], [1.541, -3.76], [2.894, -2.816], [4.238, -0.994], [-2.714, -3.868], [4.682, -3.689], [5.905, 0.807], [2.093, -7.875], [8.147, -0.162], [7.995, -4.094]], "v": [[120.329, -177.996], [135.228, -122.54], [109.38, -126.553], [101.047, -112.633], [89.078, -129.944], [89.356, -147.998], [52.553, -138.271], [55.51, -151.579], [54.972, -153.143], [50.994, -152.137], [44.159, -147.721], [38.858, -135.225], [35.842, -142.886], [36.101, -147.202], [37.302, -150.212], [25.355, -140.985], [17.261, -125.375], [14.535, -144.043], [26.202, -158.869], [10.487, -149.924], [10.666, -155.057], [12.215, -160.669], [19.021, -170.029], [28.413, -175.458], [41.351, -175.606], [28.393, -185.067], [45.186, -189.638], [60.136, -180.728], [76.713, -195.154], [95.18, -181.81]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-1.44, -1.661], [-2.198, 0.024]], "o": [[-0.336, 2.172], [1.44, 1.661], [0, 0]], "v": [[118.663, -120.449], [120.43, -114.308], [126.257, -111.685]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-2.945, 0.646], [-2.448, -1.759]], "o": [[1.488, -2.622], [2.945, -0.646], [0, 0]], "v": [[112.216, -116.309], [119.285, -121.521], [127.885, -119.745]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.654, 0.291], [-4.01, -1.213], [-2.072, -1.638], [-0.759, -1.354], [2.376, -4.178], [7.033, 4.974]], "o": [[0.472, -0.42], [3.904, -1.738], [2.527, 0.764], [1.195, 0.945], [2.454, 4.379], [-3.928, 6.906], [0, 0]], "v": [[110.181, -128.683], [111.864, -129.755], [124.952, -130.19], [132.179, -126.497], [135.113, -122.998], [135.331, -108.453], [112.626, -101.589]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-4.612, -3.844]], "o": [[5.375, -2.674], [0, 0]], "v": [[24.259, -124.424], [41.168, -122.445]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-2.994, 0.909], [-2.924, -1.114]], "o": [[1.812, -2.551], [2.994, -0.909], [0, 0]], "v": [[61.181, -128.04], [68.663, -133.425], [77.875, -133.105]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 15"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[51.746, -124.63], [47.871, -99.828], [60.21, -100.503]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 16"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-1.849, -33.807], [-27.045, 1.479], [-5.031, 20.266], [43.371, -2.372]], "o": [[1.849, 33.807], [22.07, -1.207], [8.222, -33.12], [-27.045, 1.479]], "v": [[23.401, -111.092], [75.719, -52.558], [116.539, -91.101], [69.023, -174.983]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 17"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.411, -2.158], [5.022, -0.412], [4.412, -0.194], [-0.197, -1.476], [-8.629, 6.763], [-3.05, 10.531], [1.226, -1.629], [1.685, -1.8]], "o": [[-4.527, 2.22], [-4.673, 0.384], [-0.327, 0.014], [9.84, -4.835], [8.629, -6.763], [-0.354, 1.961], [-1.509, 2.004], [-3.354, 3.584]], "v": [[90.88, -56.908], [76.371, -52.95], [60.348, -55.799], [64.648, -36.126], [93.037, -52.609], [112.069, -78.775], [106.99, -72.027], [102.641, -65.642]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 18"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-2.584, -24.79]], "o": [[2.584, 24.79], [0, 0]], "v": [[-2.64, 116.701], [5.111, 191.072]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 19"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.543, 1.328], [0, 0]], "o": [[0, 0], [-0.508, 1.971], [0, 0], [0, 0]], "v": [[42.549, -21.441], [35.035, 7.741], [38.404, 9.835], [58.262, -7.264]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 20"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.564, 0.141]], "o": [[-1.392, -4.915], [0, 0], [0, 0]], "v": [[64.441, -33.23], [61.816, -43.078], [62.679, -43.307]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 21"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-2.237, -0.423], [0, 0], [0, 0], [0, 0], [0, 0], [0.987, -0.986], [0.793, 2.431], [0.018, 0.035], [0.581, 2.096], [0, 0], [0, 0], [0, 0], [0, 0], [-1.709, -9.988], [-0.035, -0.158]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.299, 0.3], [0, -0.018], [-0.546, -1.691], [0, 0], [0, 0], [0, 0], [0, 0], [1.867, 9.971], [0.035, 0.159], [2.396, 0.194]], "v": [[122.645, -48.927], [117.184, -41.387], [97.665, -14.522], [96.52, -11.58], [74.923, 43.824], [67.999, -23.418], [66.238, -27.188], [66.202, -27.276], [64.458, -33.16], [64.441, -33.23], [62.679, -43.307], [60.142, -57.735], [110.208, -80.301], [115.599, -50.354], [115.687, -49.86]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 22"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [3.055, 52.376], [0.772, 13.238], [-2.489, 11.189], [-16.251, 5.934], [-36.657, -6.802], [-9.404, -16.654], [0.349, -17.173], [9.633, -67.93]], "o": [[-3.055, -52.376], [-0.772, -13.238], [-0.668, -11.455], [4.164, -18.718], [34.742, -12.685], [17.521, 3.251], [8.676, 15.365], [-1.394, 68.595], [0, 0]], "v": [[5.722, 235.917], [-3.443, 78.791], [-5.759, 39.078], [-7.534, 1.241], [34.058, -34.625], [143.045, -46.423], [199.655, -21.044], [208.191, 29.694], [191.629, 234.73]], "c": false}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 23"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.599, -5.962], [2.18, -13.9], [4.346, -27.719], [-17.333, 1.092], [-3.261, 2.305], [0.259, 7.969], [1.136, 34.986], [-2.729, 9.355], [-2.484, 3.889], [-0.983, 1.418], [-0.673, 0.777], [-0.768, 0.19], [1.954, -0.901], [2.126, -0.981], [5.558, -3.97], [4.125, -5.076]], "o": [[-5.623, 12.897], [-4.346, 27.719], [15.73, 7.363], [3.986, -0.251], [6.511, -4.602], [-1.136, -34.986], [-0.307, -9.46], [1.292, -4.43], [0.929, -1.454], [0.585, -0.844], [0.475, -0.548], [-2.094, 0.519], [-2.126, 0.981], [-6.165, 2.844], [-5.328, 3.806], [-4.099, 5.044]], "v": [[-15.952, 8.088], [-25.984, 48.962], [-39.023, 132.12], [11.634, 141.728], [23.041, 138.622], [29.678, 116.707], [26.27, 11.75], [26.976, -15.467], [32.676, -28.032], [35.482, -32.378], [37.341, -34.847], [39.048, -36.428], [32.774, -34.533], [26.396, -31.591], [8.397, -21.979], [-5.791, -8.488]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 24"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-1.587, 36.353], [-14.946, -2.203], [-2.557, -32.646]], "o": [[1.587, -36.353], [14.946, 2.203], [-2.983, 32.601], [0, 0]], "v": [[-30.834, 237.67], [-26.074, 128.611], [18.765, 135.219], [18.123, 233.378]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 25"}], "ip": 0, "op": 120, "st": 0, "ct": 1}, {"ind": 4, "ty": 4, "nm": "Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 45, "s": [-7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110, "s": [-7]}, {"t": 119, "s": [0]}]}, "p": {"a": 0, "k": [296.314, 320.788, 0], "l": 2}, "a": {"a": 0, "k": [-3.686, 20.788, 0], "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 45, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 110, "s": [110, 110, 100]}, {"t": 119, "s": [0, 0, 100]}], "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.647, -0.537], [0, 0], [0.537, 1.647], [0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0]], "o": [[0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0], [1.647, -0.537], [0, 0], [0.537, 1.647]], "v": [[-17.849, -112.228], [-55.781, -99.869], [-59.735, -101.88], [-63.843, -114.488], [-61.832, -118.441], [-23.899, -130.8], [-19.945, -128.789], [-15.838, -116.182]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.647, -0.537], [0, 0], [0.537, 1.647], [0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0]], "o": [[0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0], [1.647, -0.537], [0, 0], [0.537, 1.647]], "v": [[-71.876, -94.626], [-138.511, -72.916], [-142.465, -74.927], [-146.573, -87.534], [-144.562, -91.488], [-77.927, -113.198], [-73.973, -111.187], [-69.866, -98.58]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.647, -0.537], [0, 0], [0.537, 1.647], [0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0]], "o": [[0, 0], [-1.647, 0.537], [0, 0], [-0.537, -1.647], [0, 0], [1.647, -0.537], [0, 0], [0.537, 1.647]], "v": [[-152.918, -68.222], [-177.343, -60.264], [-181.297, -62.275], [-185.405, -74.882], [-183.394, -78.836], [-158.968, -86.794], [-155.014, -84.784], [-150.907, -72.176]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.633, -0.579], [0, 0], [0.579, 1.633], [0, 0], [-1.633, 0.579], [0, 0], [-0.579, -1.633], [0, 0]], "o": [[0, 0], [-1.633, 0.579], [0, 0], [-0.579, -1.633], [0, 0], [1.633, -0.579], [0, 0], [0.579, 1.633]], "v": [[-58.036, -149.479], [-89.227, -138.425], [-93.232, -140.333], [-96.194, -148.691], [-94.285, -152.696], [-63.093, -163.75], [-59.089, -161.841], [-56.127, -153.483]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.633, -0.579], [0, 0], [0.579, 1.633], [0, 0], [-1.633, 0.579], [0, 0], [-0.579, -1.633], [0, 0]], "o": [[0, 0], [-1.633, 0.579], [0, 0], [-0.579, -1.633], [0, 0], [1.633, -0.579], [0, 0], [0.579, 1.633]], "v": [[-100.829, -134.262], [-130.593, -123.714], [-134.598, -125.622], [-138.066, -135.408], [-136.157, -139.412], [-106.392, -149.961], [-102.388, -148.052], [-98.92, -138.267]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-168.51, -133.054], [-148.017, -125.547], [-161.599, -101.617]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[-0.562, -0.614], [0, 0]], "v": [[-178.952, -143.623], [-177.67, -89.058]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-193.174, -123.313], [-203.897, -101.177], [-185.097, -95.303]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -4.445], [4.445, 0], [0, 4.445], [-4.445, 0]], "o": [[0, 4.445], [-4.445, 0], [0, -4.445], [4.445, 0]], "v": [[-153.792, -182.658], [-161.84, -174.61], [-169.888, -182.658], [-161.84, -190.706]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 9"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -4.445], [4.445, 0], [0, 4.445], [-4.445, 0]], "o": [[0, 4.445], [-4.445, 0], [0, -4.445], [4.445, 0]], "v": [[-180.954, -173.604], [-189.002, -165.556], [-197.05, -173.604], [-189.002, -181.652]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 10"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -4.445], [4.445, 0], [0, 4.445], [-4.445, 0]], "o": [[0, 4.445], [-4.445, 0], [0, -4.445], [4.445, 0]], "v": [[-208.116, -164.55], [-216.164, -156.502], [-224.212, -164.55], [-216.164, -172.598]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0.431, 0.357, 0.902, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 11"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-220.188, -144.43], [-39.109, -202.778]], "c": false}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 12"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.775, -1.851], [0, 0], [0, 0], [1.871, 2.133], [0, 0], [0, 0], [1.851, 5.775], [0, 0], [-5.774, 1.851], [0, 0], [-1.831, -5.774], [0, 0]], "o": [[0, 0], [0, 0], [0.362, 2.837], [0, 0], [0, 0], [-5.774, 1.831], [0, 0], [-1.831, -5.775], [0, 0], [5.775, -1.851], [0, 0], [1.831, 5.774]], "v": [[5.618, -81.193], [-31.001, -69.483], [-25.769, -27.614], [-30.799, -25.341], [-61.019, -59.886], [-186.487, -19.787], [-200.289, -26.91], [-243.446, -161.954], [-236.324, -175.757], [-44.219, -237.143], [-30.437, -230.04], [12.74, -94.995]], "c": true}}, "nm": "Path 1"}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.953}, "lc": 2, "lj": 2, "nm": "Stroke 1"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 13"}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.775, -1.851], [0, 0], [-0.607, -4.859], [-0.65, -5.204], [-0.222, -2.162], [-0.888, -1.273], [-0.788, 0.349], [1.833, 2.089], [0, 0], [0, 0], [1.851, 5.775], [0, 0], [-5.774, 1.851], [0, 0], [-1.831, -5.774], [0, 0]], "o": [[0, 0], [0.607, 4.859], [0.65, 5.204], [0.269, 2.157], [0.154, 1.507], [0.369, 0.529], [-2.111, 0.937], [0, 0], [0, 0], [-5.774, 1.831], [0, 0], [-1.831, -5.775], [0, 0], [5.775, -1.851], [0, 0], [1.831, 5.774]], "v": [[-0.418, -79.181], [-37.036, -67.471], [-35.215, -52.896], [-33.265, -37.285], [-32.457, -30.809], [-31.023, -26.716], [-27.655, -23.859], [-36.835, -23.329], [-67.055, -57.874], [-192.523, -17.776], [-206.325, -24.898], [-249.482, -159.942], [-242.36, -173.745], [-50.255, -235.131], [-36.473, -228.028], [6.704, -92.983]], "c": true}}, "nm": "Path 1"}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Fill 1"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 14"}], "ip": 0, "op": 120, "st": 0, "ct": 1}], "markers": []}