<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="500" viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="costGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ff4757;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b81;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="savingsGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e90ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#70a1ff;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000033" />
    </filter>
  </defs>

  <!-- Background -->
  <rect width="800" height="500" fill="#f8f9fa" rx="10" ry="10" />
  
  <!-- Title -->
  <text x="400" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#2f3542">Token Cost Optimization with KAPI</text>
  
  <!-- Cost Reduction Bar Chart -->
  <g transform="translate(50, 100)">
    <!-- Strategy 1 -->
    <rect x="0" y="0" width="700" height="60" fill="#f1f2f6" rx="5" ry="5" />
    <rect x="250" y="10" width="400" height="40" fill="url(#costGradient)" rx="5" ry="5" opacity="0.3" />
    <rect x="250" y="10" width="180" height="40" fill="url(#savingsGradient)" rx="5" ry="5" filter="url(#shadow)" />
    <text x="20" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2f3542">Template-First Development</text>
    <text x="660" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="end" fill="#1e90ff">45%</text>
    
    <!-- Strategy 2 -->
    <rect x="0" y="70" width="700" height="60" fill="#f1f2f6" rx="5" ry="5" />
    <rect x="250" y="80" width="400" height="40" fill="url(#costGradient)" rx="5" ry="5" opacity="0.3" />
    <rect x="250" y="80" width="140" height="40" fill="url(#savingsGradient)" rx="5" ry="5" filter="url(#shadow)" />
    <text x="20" y="105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2f3542">Intelligent Caching</text>
    <text x="660" y="105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="end" fill="#1e90ff">35%</text>
    
    <!-- Strategy 3 -->
    <rect x="0" y="140" width="700" height="60" fill="#f1f2f6" rx="5" ry="5" />
    <rect x="250" y="150" width="400" height="40" fill="url(#costGradient)" rx="5" ry="5" opacity="0.3" />
    <rect x="250" y="150" width="120" height="40" fill="url(#savingsGradient)" rx="5" ry="5" filter="url(#shadow)" />
    <text x="20" y="175" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2f3542">Adaptive Model Selection</text>
    <text x="660" y="175" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="end" fill="#1e90ff">30%</text>
    
    <!-- Strategy 4 -->
    <rect x="0" y="210" width="700" height="60" fill="#f1f2f6" rx="5" ry="5" />
    <rect x="250" y="220" width="400" height="40" fill="url(#costGradient)" rx="5" ry="5" opacity="0.3" />
    <rect x="250" y="220" width="130" height="40" fill="url(#savingsGradient)" rx="5" ry="5" filter="url(#shadow)" />
    <text x="20" y="245" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2f3542">Structured Prompting</text>
    <text x="660" y="245" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="end" fill="#1e90ff">32.5%</text>
    
    <!-- Strategy 5 -->
    <rect x="0" y="280" width="700" height="60" fill="#f1f2f6" rx="5" ry="5" />
    <rect x="250" y="290" width="400" height="40" fill="url(#costGradient)" rx="5" ry="5" opacity="0.3" />
    <rect x="250" y="290" width="80" height="40" fill="url(#savingsGradient)" rx="5" ry="5" filter="url(#shadow)" />
    <text x="20" y="315" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2f3542">Continuous Feedback Loop</text>
    <text x="660" y="315" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="end" fill="#1e90ff">20%</text>
  </g>
  
  <!-- Legend -->
  <g transform="translate(400, 400)">
    <rect x="-120" y="0" width="240" height="50" fill="white" rx="5" ry="5" stroke="#e4e7eb" stroke-width="1" />
    
    <rect x="-100" y="15" width="20" height="20" fill="url(#costGradient)" opacity="0.3" />
    <text x="-70" y="30" font-family="Arial, sans-serif" font-size="14" fill="#2f3542">Original Cost</text>
    
    <rect x="20" y="15" width="20" height="20" fill="url(#savingsGradient)" />
    <text x="50" y="30" font-family="Arial, sans-serif" font-size="14" fill="#2f3542">Cost Savings</text>
  </g>
  
  <!-- Bottom Note -->
  <text x="400" y="480" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#2f3542">Up to 60-80% Total Cost Reduction</text>
</svg>