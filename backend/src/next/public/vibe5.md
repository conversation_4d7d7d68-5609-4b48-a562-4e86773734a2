# Software 2.0: The New Manifesto

There's a lot of noise around AI-generated code and "vibe coding" these days. On one end of the spectrum, skeptics dismiss it as a fad, claiming AI will never write proper, production-ready code. On the other end, techno-optimists predict the end of software engineering as a profession, imagining a future where business people simply instruct AI to materialize their ideas into working products.

Both sides miss the point entirely. What we're actually witnessing is the emergence of Software Engineering 2.0.

The Fear of Abstraction Is Nothing New
People get nervous when they hear that many YCombinator startups are using "vibe coding" for 95% of their codebase. But this fear of abstraction isn't new. In all my years of building dozens of projects, 100% of my binary code was generated by compilers and interpreters. While I've written some assembly programs, I've never manually built binaries. In fact, I don't understand most of the machine operations inside them - and that's fine. That complexity is abstracted away.

Programming languages themselves are just abstractions. They're higher-level interfaces that shield us from the complexity below. Just as I'm comfortable not understanding every instruction in my binary code, I should become comfortable not understanding every line of my "higher language" code generated with AI assistance.

Value Lies in Engineering, Not Just Code
The true value was never in writing individual lines of code. The value lies in the engineering that connects all the pieces and works incrementally with customers to create the right nodes, connections, and systems.

Civil engineers don't define their work as "laying bricks." They design plans that connect millions of bricks, steel beams, and other components in ways that are safe, efficient, and valuable. Mechanical engineers don't see themselves as "welding nuts." They build complex systems that transform metal components into sophisticated machines.

Our functions and modules are just those nuts and bolts. While we need to understand what they do, we shouldn't fixate on every single one. We should be working at the level of diagrams, flows, and interactions. As engineers shift their focus from coding to actual engineering, we should expect much higher quality software.

What AI Can and Cannot Do (Yet)
Engineering isn't something AI can fully handle today. It requires extensive memory capabilities that the human brain excels at, plus countless interactions with the real world and conceptual leaps that surpass what agentic systems can envision. This is where humans should operate – not in generating boilerplate TypeScript files.

I don't expect software teams to downsize (although there will be a temporary phase of realignment). Rather, we'll use the time saved to build software that doesn't suck.

From Mediocrity to Quality
For decades, we've tolerated poorly performing software that breaks frequently and becomes obsolete within a few years. Our operating systems and most applications are subpar – we've just grown accustomed to this mediocrity. Very few cars randomly crash and very few bridges come down crashing. On the other hand, we are accustomed to software that crash and we had to build a very complex layer around it to just handle this.

As we start focusing on flow rather than low-level coding, we could produce significantly higher quality software with substantially greater complexity.

There will undoubtedly be a wave of "indie apps" where amateurs create new products, similar to the flowing-text websites of the 1990s. HTML was super simple to learn and everyone built their own websites. Eventually, web development adopted rigorous engineering practices, leading to the rise of platforms like Google and Amazon.

Similarly, we'll see a temporary flood of questionable applications built on platforms like Vercel. But this phase will pass (AI itself can help with filtering), and we should expect dramatically higher quality software as AI helps clean up dead code, assists with continuous refactoring, and frees engineers to do actual engineering.

The Real Promise of AI in Software Development
Finally, after decades, software engineers can engage in genuine engineering – working with diagrams, flows, and systems, rather than being tied to "lower level" code abstractions.

The real promise isn't that AI will replace software engineers. It's that AI will elevate software engineering to its proper place – as a discipline focused on designing resilient, efficient systems that solve real problems, rather than one bogged down in the minutiae of syntax and boilerplate code.

Software Engineering 2.0 isn't about less engineering – it's about better engineering.