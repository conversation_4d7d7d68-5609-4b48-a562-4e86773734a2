import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/lib/blog';
import TagList from './TagList';

type PostCardProps = {
  post: BlogPost;
};

export default function PostCard({ post }: PostCardProps) {
  const { slug, frontmatter } = post;
  const { title, description, date, author, coverImage, tags, readingTime } = frontmatter;
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-200">
      {coverImage && (
        <Link href={`/blog/${slug}`} className="block relative h-48 overflow-hidden">
          <Image 
            src={coverImage} 
            alt={title}
            fill
            className="object-cover hover:scale-105 transition-transform duration-300"
          />
        </Link>
      )}
      
      <div className="p-6">
        <div className="mb-3">
          <TagList tags={tags} />
        </div>
        
        <Link href={`/blog/${slug}`} className="block group">
          <h3 className="text-xl font-bold mb-3 group-hover:text-purple-700 transition-colors">
            {title}
          </h3>
        </Link>
        
        <p className="text-gray-600 mb-4 line-clamp-3">{description}</p>
        
        <div className="flex items-center mt-6">
          {author.avatar && (
            <Image 
              src={author.avatar} 
              alt={author.name}
              width={40}
              height={40}
              className="rounded-full mr-3"
            />
          )}
          <div>
            <p className="font-medium">{author.name}</p>
            <p className="text-sm text-gray-500">{author.role}</p>
          </div>
          <div className="ml-auto text-sm text-gray-500">
            <time dateTime={date}>{date}</time>
            <span className="mx-2">•</span>
            <span>{readingTime}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
