import Link from 'next/link';

type TagListProps = {
  tags: string[];
};

export default function TagList({ tags }: TagListProps) {
  if (!tags || tags.length === 0) {
    return null;
  }
  
  return (
    <div className="flex flex-wrap gap-2">
      {tags.map(tag => (
        <Link 
          key={tag} 
          href={`/blog/tag/${encodeURIComponent(tag.toLowerCase())}`}
          className="inline-block bg-purple-100 text-purple-800 px-4 py-2 rounded-full hover:bg-purple-200 transition-colors"
        >
          {tag}
        </Link>
      ))}
    </div>
  );
}
