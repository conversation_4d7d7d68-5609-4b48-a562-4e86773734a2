import Image from 'next/image';

interface AuthorBioProps {
  author: {
    name: string;
    role: string;
    avatar: string;
  };
  date: string;
  readingTime: string;
}

export default function AuthorBio({ author, date, readingTime }: AuthorBioProps) {
  return (
    <div className="flex items-center space-x-4 text-gray-700">
      <Image
        src={author.avatar}
        alt={author.name}
        width={48}
        height={48}
        className="rounded-full"
      />
      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
        <span className="font-semibold text-lg">{author.name}</span>
        <span className="text-gray-500 text-sm sm:ml-1 sm:mt-0 mt-0.5">{author.role}</span>
        <span className="hidden sm:inline text-gray-400 mx-2">|</span>
        <span className="flex items-center text-gray-500 text-sm sm:ml-2">
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" /* calendar icon */ />
            {date}
          </span>
          <span className="mx-2">•</span>
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" /* clock icon */ />
            {readingTime} read
          </span>
        </span>
      </div>
    </div>
  );
}
