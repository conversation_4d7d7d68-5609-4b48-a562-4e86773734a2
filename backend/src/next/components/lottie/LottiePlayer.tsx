'use client';

import { useEffect, useRef } from 'react';
import Image from 'next/image';

interface LottiePlayerProps {
  src: string;
  fallbackSrc: string;
  width: number;
  height: number;
  alt: string;
}

export default function LottiePlayer({ 
  src, 
  fallbackSrc, 
  width, 
  height, 
  alt 
}: LottiePlayerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const lottieLoaded = useRef<boolean>(false);

  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') return;

    // Convert path like "/your-animation.lottie" to full URL
    const fullSrc = window.location.origin + src;
    
    const loadLottiePlayer = async () => {
      try {
        // Dynamically import the lottie-player library
        await import('@lottiefiles/lottie-player');
        
        // Create the lottie-player element manually
        if (containerRef.current && !lottieLoaded.current) {
          // Check if it already exists
          const existingPlayer = containerRef.current.querySelector('lottie-player');
          if (!existingPlayer) {
            // Create the element
            const player = document.createElement('lottie-player');
            
            // First set all other attributes
            player.setAttribute('background', 'transparent');
            player.setAttribute('speed', '1');
            player.setAttribute('style', 'width: 100%; height: 100%');
            player.setAttribute('loop', '');
            player.setAttribute('autoplay', '');
            
            // Add to DOM first before setting src
            containerRef.current.appendChild(player);
            
            // Now set src - doing this after appending to DOM can help with some loading issues
            setTimeout(() => {
              player.setAttribute('src', fullSrc);
            }, 100);
            
            lottieLoaded.current = true;
            
            // Set up error handler for fallback
            setTimeout(() => {
              const playerElement = player as HTMLElement;
              if (playerElement.clientHeight === 0) {
                showFallback();
              }
            }, 1500);

            // Add error event listener
            player.addEventListener('error', () => {
              console.error('Lottie player error');
              showFallback();
            });
          }
        }
      } catch (error) {
        console.error('Failed to load Lottie player:', error);
        showFallback();
      }
    };

    const showFallback = () => {
      if (containerRef.current) {
        const fallback = containerRef.current.querySelector('.lottie-fallback');
        if (fallback) {
          fallback.classList.remove('hidden');
        }
      }
    };

    loadLottiePlayer();

    // Clean up function
    return () => {
      if (containerRef.current) {
        const player = containerRef.current.querySelector('lottie-player');
        if (player) {
          player.remove();
          lottieLoaded.current = false;
        }
      }
    };
  }, [src]);

  return (
    <div ref={containerRef} className="w-full h-full relative min-h-[300px]">
      {/* Fallback image that shows if Lottie fails to load */}
      <div className="hidden lottie-fallback absolute inset-0 flex items-center justify-center">
        <Image
          src={fallbackSrc}
          alt={alt}
          width={width}
          height={height}
          className="mx-auto"
          priority
        />
      </div>
    </div>
  );
}
