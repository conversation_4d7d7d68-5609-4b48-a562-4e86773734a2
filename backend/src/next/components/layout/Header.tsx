"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and site name */}
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <div className="flex items-center">
                <Image 
                  src="/logo-light.png" 
                  alt="KAPI Logo" 
                  width={32} 
                  height={32}
                  className="mr-2"
                />
                <span className="font-bold text-2xl text-purple-800">KAPI</span>
              </div>
            </Link>
          </div>
          
          {/* Desktop navigation */}
          <nav className="hidden md:block">
            <div className="flex items-center space-x-8">
              <Link 
                href="/benefits" 
                className="text-gray-700 hover:text-purple-700 px-3 py-2 text-sm font-medium transition-colors"
              >
                Benefits
              </Link>
              <Link 
                href="/features" 
                className="text-gray-700 hover:text-purple-700 px-3 py-2 text-sm font-medium transition-colors"
              >
                Features
              </Link>
              <Link 
                href="/blog" 
                className="text-gray-700 hover:text-purple-700 px-3 py-2 text-sm font-medium transition-colors"
              >
                Blog
              </Link>
              <button 
                onClick={() => window.dispatchEvent(new Event('openWaitlistModal'))}
                className="bg-purple-700 hover:bg-purple-800 text-white px-4 py-2 text-sm font-medium rounded-md transition-colors"
              >
                Join Waitlist
              </button>
            </div>
          </nav>
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-purple-700 hover:bg-gray-100"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {/* Icon for menu - simple hamburger */}
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      {mobileMenuOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link
              href="/benefits"
              className="block text-gray-700 hover:bg-gray-100 hover:text-purple-700 px-3 py-2 rounded-md text-base font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Benefits
            </Link>
            <Link
              href="/features"
              className="block text-gray-700 hover:bg-gray-100 hover:text-purple-700 px-3 py-2 rounded-md text-base font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Features
            </Link>
            <Link
              href="/blog"
              className="block text-gray-700 hover:bg-gray-100 hover:text-purple-700 px-3 py-2 rounded-md text-base font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Blog
            </Link>
            <Link
              href="https://mitrarobot.com/modernaipro"
              className="block text-gray-700 hover:bg-gray-100 hover:text-purple-700 px-3 py-2 rounded-md text-base font-medium transition-colors"
              onClick={() => setMobileMenuOpen(false)}
            >
              Learn AI
            </Link>
            <button
              onClick={() => {
                setMobileMenuOpen(false);
                window.dispatchEvent(new Event('openWaitlistModal'));
              }}
              className="block text-center bg-purple-700 hover:bg-purple-800 text-white px-4 py-2 rounded-md text-base font-medium mt-4 transition-colors w-full"
            >
              Join Waitlist
            </button>
          </div>
        </div>
      )}
    </header>
  );
}
