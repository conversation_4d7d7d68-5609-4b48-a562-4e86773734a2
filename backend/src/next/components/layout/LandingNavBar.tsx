'use client';

import Link from 'next/link';
import Image from 'next/image';
import styles from '@/app/styles/landingnavbar.module.css';
import { useState } from 'react';
import { Menu, X } from 'lucide-react';

export default function LandingNavBar() {
  const [mobileOpen, setMobileOpen] = useState(false);

  return (
    <header className={styles.navContainer}>
      <nav className={styles.nav}>
        {/* Left: Logo */}
        <Link href="/" className={styles.logo}>
          <Image src="/logo.png?v=2" alt="Logo" width={40} height={40} />
          <span className={styles.brandText}>Kapi</span>
        </Link>

        {/* Hamburger Icon (mobile only) */}
        <button
          className={styles.hamburger}
          onClick={() => setMobileOpen((v) => !v)}
          aria-label="Toggle menu"
        >
          {mobileOpen ? <X size={28} /> : <Menu size={28} />}
        </button>

        {/* Center: Menu */}
        <ul className={`${styles.menu} ${mobileOpen ? styles.menuOpen : ''}`}>
          <li><Link href="/benefits" onClick={() => setMobileOpen(false)}>Benefits</Link></li>
          <li><Link href="/features" onClick={() => setMobileOpen(false)}>Features</Link></li>
          {/* <li><Link href="https://mitrarobot.com/modernaipro" onClick={() => setMobileOpen(false)}>Learn Coding</Link></li> */}
          <li><Link href="/blog" onClick={() => setMobileOpen(false)}>Blog</Link></li>
        </ul>

        {/* Right: Actions */}
        <div className={`${styles.actions} ${mobileOpen ? styles.menuOpen : ''}`}>
          <button 
            onClick={() => {
              setMobileOpen(false);
              // Trigger waitlist modal
              window.dispatchEvent(new Event('openWaitlistModal'));
            }}
            className={styles.signUp}
          >
            Join Waitlist →
          </button>
        </div>
      </nav>
    </header>
  );
}