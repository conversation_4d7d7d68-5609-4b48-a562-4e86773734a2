"use client";

import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import Header from './Header';
import LandingNavBar from './LandingNavBar';
import Footer from './Footer';
import WaitlistModal from '../../app/components/WaitlistModal';

type LayoutProps = {
  children: React.ReactNode;
};

export default function Layout({ children }: LayoutProps) {
  const pathname = usePathname();
  const isHomePage = pathname === '/';
  const [isWaitlistModalOpen, setIsWaitlistModalOpen] = useState(false);

  const openWaitlistModal = () => setIsWaitlistModalOpen(true);
  const closeWaitlistModal = () => setIsWaitlistModalOpen(false);

  useEffect(() => {
    // Listen for waitlist modal trigger from any component
    const handleWaitlistTrigger = () => openWaitlistModal();
    window.addEventListener('openWaitlistModal', handleWaitlistTrigger);
    
    return () => {
      window.removeEventListener('openWaitlistModal', handleWaitlistTrigger);
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      {isHomePage ? <LandingNavBar /> : <Header />}
      <main className="flex-grow">{children}</main>
      <Footer />
      
      {/* Global Waitlist Modal */}
      <WaitlistModal 
        isOpen={isWaitlistModalOpen} 
        onClose={closeWaitlistModal} 
      />
    </div>
  );
}
