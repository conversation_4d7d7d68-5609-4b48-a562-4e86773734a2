"use client";

import Link from 'next/link';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-100 border-t border-gray-200">
      <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company info */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">KAPI</h3>
            <p className="text-gray-600 mb-4">
              Bringing software discipline to AI development
            </p>
            <div className="flex space-x-4">
              <a href="https://twitter.com/balajivis" className="text-gray-500 hover:text-gray-700" aria-label="Twitter">
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="http://linkedin.com/company/kapiai" className="text-gray-500 hover:text-gray-700" aria-label="LinkedIn">
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                </svg>
              </a>
            </div>
          </div>
          
          {/* Products */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Products</h3>
            <ul className="space-y-3">
              <li>
                <Link href="https://kapihq.com" className="text-gray-600 hover:text-purple-700 transition-colors">
                  KAPI IDE
                </Link>
              </li>
              <li>
                <Link href="https://mitrarobot.com/modernaipro" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Modern AI Pro
                </Link>
              </li>
              <li>
              <Link href="/about" className="text-gray-600 hover:text-purple-700 transition-colors">
                  About Us
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Resources */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Resources</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/blog" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/blog/backwards-build-approach" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Our Philosophy
                </Link>
              </li>
              <li>
                <Link href="/blog/software-manifesto" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Future of Software Development
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Company */}
          {/*}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="text-gray-600 hover:text-purple-700 transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blog/backwards-build-approach" className="text-gray-600 hover:text-purple-700 transition-colors">
                  Backwards Build
                </Link>
              </li>
            </ul>
          </div>
          */}
        </div>
        
        {/* Bottom section */}
        <div className="border-t border-gray-200 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm">
              &copy; {currentYear} KAPI HQ. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/terms" className="text-gray-500 hover:text-gray-700 text-sm">
                Terms of Service
              </Link>
              <Link href="/privacy" className="text-gray-500 hover:text-gray-700 text-sm">
                Privacy Policy
              </Link>
              <Link href="/cookies" className="text-gray-500 hover:text-gray-700 text-sm">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
