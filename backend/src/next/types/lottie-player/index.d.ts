declare namespace JSX {
  interface IntrinsicElements {
    'lottie-player': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement> & {
      src: string;
      background?: string;
      speed?: string | number;
      loop?: boolean;
      autoplay?: boolean;
      mode?: "normal" | "bounce";
      hover?: boolean;
      controls?: boolean;
      renderer?: "svg" | "canvas";
      count?: string | number;
      direction?: number;
      style?: React.CSSProperties;
    }, HTMLElement>;
  }
}