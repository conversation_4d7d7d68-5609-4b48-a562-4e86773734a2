// Custom type declarations for Next.js metadata
declare module 'next/dist/lib/metadata/types/metadata-interface.js' {
  import type { Metadata } from 'next';
  
  export type ResolvingMetadata = Promise<Metadata>;
  export type ResolvingViewport = Promise<Viewport>;
  
  export interface Viewport {
    width?: string | number;
    height?: string | number;
    initialScale?: number;
    minimumScale?: number;
    maximumScale?: number;
    userScalable?: boolean;
    viewportFit?: 'auto' | 'cover' | 'contain';
    interactiveWidget?: 'resizes-visual' | 'resizes-content' | 'overlays-content';
  }
}
