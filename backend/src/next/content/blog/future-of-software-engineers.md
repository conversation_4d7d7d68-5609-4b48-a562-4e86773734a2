---
id: "future-of-software-engineers"
title: "What happens to software engineers when AI codes for them?"
description: "As AI takes over routine coding tasks, software engineering evolves into a more specialized, systems-oriented profession."
date: "Mar 15, 2025"
author:
  name: "<PERSON><PERSON><PERSON>"
  avatar: "/balaji.jpeg"
  role: "Founder, KAPI"
coverImage: "/vibe2.jpeg"
tags:
  - "Future of Work"
  - "AI Development"
  - "Software Engineering"
readingTime: "4 min read"
---

# What happens to software engineers when AI codes for them?

Vibe coding is not going to make everyone a software engineer. In fact, the opposite is going to happen -- the profession is likely to become a specialized one. Why?

200 years ago, any village artisan could build a hut or create a cart. You didn't need a specialized civil or mechanical engineer. Eventually, we added complex tools as a result of industrialization. Now, that village artisan cannot design a SUV or commission a cantilever bridge. A lot of simpler works that the artisans did have been automated. We now have cranes in project sites and robots in auto factories. But, the engineers didn't go away. We need far more engineers than we did a century ago.

## The Future of Software Engineering

Software engineering is at similar crossroads. Any motivated individual could build a simple Typescript website with a Stripe integration. That work is going to get automated completely. However, the powerful AI tools are also going to result in extremely complex engineering systems that will be beyond the scope of software artisans.

As AI starts generating 100s of files in a complex system, non-engineers are going to be intimidated very quickly. You're not talking in English anymore, but in "systemese". The work will become less language-focused and more visual like a CAD diagram. Software engineers, like other engineers, are going to go visual. Engineers will enjoy connecting systems through charts and graphs, thinking in webs of interactions. The syntax of Python, Typescript, or Rust will matter less compared to engineering tradeoffs on performance, cost, and compatibility.

## History Repeats Itself

When we replaced muscle power with fossil fuel engines 150 years ago, we did replace some industrial workers. But that replacement also created whole new sectors like aviation that could never have been powered by muscle no matter how many humans you put in it. We're going to similarly build systems we can't even imagine now -- creating entirely new ecosystems.

This is Software Engineering's moment to become real engineering. Civil engineers don't call their work "bricking" and mechanical engineers don't call their work "nutting". We no longer need to call our work "coding". A whole new dawn beckons us -- we are going to build a lot more with a lot more quality.