/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#3498db',
          dark: '#2980b9',
        },
        secondary: {
          DEFAULT: '#2c3e50',
          light: '#34495e',
        },
        purple: {
          50: '#d1cce6',
          100: '#a39acc',
          200: '#9a8fd1',
          300: '#473599',
          400: '#2e1f7f',
          500: '#1a1266',
          600: '#140f4d', // Darker KAPI purple
          700: '#0f0b3e',
          800: '#0a072f',
          900: '#05041f',
        },
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            color: theme('colors.gray.800'),
            h1: {
              color: theme('colors.purple.800'),
              fontWeight: '800',
            },
            h2: {
              color: theme('colors.purple.700'),
              fontWeight: '700',
            },
            h3: {
              color: theme('colors.purple.700'),
              fontWeight: '600',
            },
            a: {
              color: theme('colors.purple.600'),
              '&:hover': {
                color: theme('colors.purple.800'),
              },
            },
            pre: {
              backgroundColor: theme('colors.gray.800'),
              color: theme('colors.gray.100'),
            },
            code: {
              color: theme('colors.purple.700'),
              backgroundColor: theme('colors.purple.100'),
              paddingLeft: '4px',
              paddingRight: '4px',
              paddingTop: '2px',
              paddingBottom: '2px',
              borderRadius: '0.25rem',
            },
            'code::before': {
              content: '""',
            },
            'code::after': {
              content: '""',
            },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};
