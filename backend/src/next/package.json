{"name": "kapi-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "postinstall": "npm install @lottiefiles/lottie-player@latest @dotlottie/player-component@latest"}, "dependencies": {"@clerk/nextjs": "^6.19.5", "@dotlottie/player-component": "^2.7.12", "@lottiefiles/dotlottie-wc": "^0.5.3", "@lottiefiles/lottie-player": "^2.0.12", "dotenv": "^16.5.0", "gray-matter": "^4.0.3", "highlight.js": "^11.11.1", "js-yaml": "^4.1.0", "lottie-web": "^5.12.2", "marked": "^11.2.0", "marked-highlight": "^2.0.8", "next": "^15.3.2", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20.11.25", "@types/prismjs": "^1.26.5", "@types/react": "19.1.8", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.19", "case-sensitive-paths-webpack-plugin": "^2.4.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.4.2"}, "overrides": {"@clerk/shared": "3.9.3"}}