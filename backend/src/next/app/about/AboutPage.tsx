'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-[#f8fafc]">
      {/* Main Content Container */}
      <div className="container mx-auto px-4 py-20 md:pt-32">
        
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-center">
            Meet the <span className="text-purple-300">Team</span> Behind Kapi, making Vibe Coding Enterprise Ready!
          </h1>
          <p className="text-xl  max-w-3xl mx-auto text-center mb-20">
            We're not just building AI-powered development tools, we're crafting your new coding companions! Think of us as the Robin to your Batman, the Chewbacca to your Han Solo... but for coding!
          </p>
          
          {/* Founders Section */}
          <div className="max-w-6xl mx-auto">
            {/* Balaji */}
            <div className="flex flex-col md:flex-row items-center gap-10 mb-24 bg-purple-800 rounded-2xl p-8 backdrop-blur-sm border border-purple-800/30">
              <div className="md:w-1/3 lg:w-1/4">
                <div className="relative w-56 h-56 md:w-64 md:h-64 mx-auto">
                  <Image 
                    src="/balaji-headshot.webp"
                    alt="Balaji Viswanathan"
                    className="rounded-2xl object-cover shadow-xl"
                    fill
                    sizes="(max-width: 768px) 224px, 256px"
                  />
                </div>
              </div>
              <div className="md:w-2/3 lg:w-3/4">
                <h2 className="heading-white text-3xl font-bold mb-1">Balaji Viswanathan</h2>
                <p className="text-purple-100 text-xl mb-4">Co-founder & CEO (Chief Enthusiasm Officer)</p>
                <div className="space-y-4">
                  <p className="text-lg text-gray-200">
                    Balaji has a Ph.D. in robotics and an MBA, which is probably why he's so enthusiastic about building AI coding tools!
                  </p>
                  <p className="text-lg text-gray-200">
                    He's built operating systems, led research teams, and even wrangled developer tools. Some of his previous startups are even used as case studies on innovation at HBR. No big deal!
                  </p>
                  <p className="text-lg text-gray-200">
                    Before Kapi, he ran Mitra Robot, proving he can handle robots, Fortune 500 companies, and government agencies... which is a pretty unique skillset!
                  </p>
                </div>
                <div className="mt-6 flex gap-4">
                  <Link href="https://www.linkedin.com/in/balajivi/" target="_blank" className="px-4 py-2 bg-purple-100 hover:bg-purple-200 rounded-lg transition">LinkedIn</Link>
                  <Link href="https://twitter.com/balajivis" target="_blank" className="px-4 py-2 bg-purple-100 hover:bg-purple-200 rounded-lg transition">Twitter</Link>
                </div>
              </div>
            </div>
            
            {/* Mahalakshmi */}
            <div className="flex flex-col md:flex-row-reverse items-center gap-10 mb-24 bg-purple-800 rounded-2xl p-8 backdrop-blur-sm border border-purple-800/30">
              <div className="md:w-1/3 lg:w-1/4">
                <div className="relative w-56 h-56 md:w-64 md:h-64 mx-auto">
                  <Image 
                    src="/mahalakshmi.webp"
                    alt="Mahalakshmi Radhakrushnun"
                    className="rounded-2xl object-cover shadow-xl"
                    fill
                    sizes="(max-width: 768px) 224px, 256px"
                  />
                </div>
              </div>
              <div className="md:w-2/3 lg:w-3/4">
                <h2 className="heading-white text-3xl font-bold mb-1">Mahalakshmi Radhakrushnun</h2>
                <p className="text-purple-100 text-xl mb-4">Co-founder & CBO (Chief Business Oracle)</p>
                <div className="space-y-4">
                  <p className="text-lg text-gray-200">
                    Mahalakshmi is the business guru, an Electrical Engineer who speaks fluent "enterprise sales" and "operations."
                  </p>
                  <p className="text-lg text-gray-200">
                    She's closed deals with the US federal government and Fortune 500 companies, proving she can sell ice to Eskimos (if they needed it!).
                  </p>
                  <p className="text-lg text-gray-200">
                    Her secret? She understands the tech inside and out, allowing her to explain complex value propositions in a way that even your grandma could understand.
                  </p>
                </div>
                <div className="mt-6 flex gap-4">
                  <Link href="https://www.linkedin.com/in/manjubalaji/" target="_blank" className="px-4 py-2 bg-purple-100 hover:bg-purple-200 rounded-lg transition">LinkedIn</Link>
                  <Link href="#" target="_blank" className="px-4 py-2 bg-purple-100 hover:bg-purple-200 rounded-lg transition">Twitter</Link>
                </div>
              </div>
            </div>
          </div>

          {/* Recognition Section */}
          <div className="mt-24 max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Recognized by Industry Leaders (They Like Us, They Really Like Us!)</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 items-center justify-items-center opacity-80">
              <div className="h-12 w-32 grayscale hover:grayscale-0 transition">
                <div className="flex items-center justify-center h-full">
                  <span className="text-xl font-bold ">CNN</span>
                </div>
              </div>
              <div className="h-12 w-32 grayscale hover:grayscale-0 transition">
                <div className="flex items-center justify-center h-full">
                  <span className="text-xl font-bold">BBC</span>
                </div>
              </div>
              <div className="h-12 w-32 grayscale hover:grayscale-0 transition">
                <div className="flex items-center justify-center h-full">
                  <span className="text-xl font-bold">Forbes</span>
                </div>
              </div>
            </div>
          </div>

          {/* Our Story */}
          <div className="mt-24 max-w-4xl mx-auto bg-purple-800 rounded-2xl p-8 backdrop-blur-sm border border-purple-800/30">
            <h2 className="heading-white text-3xl font-bold mb-6 text-center">Our Story (How We Decided to Build Cool Stuff)</h2>
            <p className="text-lg text-gray-200 mb-6">
              The Kapi journey began when Balaji and Mahalakshmi, after working in the world of robotics with Mitra Robot, realized something was missing: AI that could truly understand and assist developers. They weren't satisfied with just text-generating parrots!
            </p>
            <p className="text-lg text-gray-200">
              So, armed with their combined expertise in robots, AI, and a healthy dose of caffeine, they set out to create a coding companion that's more than just a sophisticated autocomplete tool. Think voice commands, visual inputs, and AI that actually gets your jokes (well, most of them!).
            </p>
          </div>

          {/* Mission Section */}
          <div className="mt-24 max-w-4xl mx-auto bg-purple-800 rounded-2xl p-8 backdrop-blur-sm border border-purple-800/30">
            <h2 className="heading-white text-3xl font-bold mb-6 text-center">Our Mission (Why We Get Out of Bed in the Morning)</h2>
            <p className="text-xl text-center  mb-6 text-white">
              At Kapi, we're on a mission to make AI your coding soulmate, not just a code generator.
            </p>
            <p className="text-lg text-center text-white">
              We envision a future where coding is a natural conversation between humans and AI, where developers can focus on the big ideas while AI handles the nitty-gritty details. Basically, we want to make coding fun again!
            </p>
          </div>

          {/* Join Us CTA */}
          <div className="mt-24 max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Join Our Journey</h2>
            <p className="text-xl  mb-8">
              We're building the future of software development and we'd love for you to be a part of it. (Warning: May involve excessive use of caffeine and brainstorming sessions!)
            </p>
          </div>
      </div>
    </div>
  );
}
