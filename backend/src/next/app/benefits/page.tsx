import type { Metadata } from 'next';
import BenefitsPageClient from './BenefitsPageClient';

export const metadata: Metadata = {
  title: 'Benefits - Transform Your Development Experience | Kapi',
  description: 'Discover how <PERSON><PERSON> revolutionizes software development with 60-80% cost reduction, 3x faster development, and 95% fewer production bugs. Transform frustration into flow.',
  keywords: [
    'development productivity',
    'AI development benefits',
    'reduce development costs',
    'faster coding',
    'debug less',
    'software quality',
    'developer experience',
    'programming efficiency',
    'development transformation',
    'code optimization',
    'development workflow',
    'software engineering benefits'
  ],
  openGraph: {
    title: 'Benefits - Transform Your Development Experience | Kapi',
    description: 'Discover how <PERSON><PERSON> revolutionizes software development with 60-80% cost reduction, 3x faster development, and 95% fewer production bugs.',
    url: 'https://kapihq.com/benefits',
    siteName: 'Kapi',
    images: [
      {
        url: '/benefits-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Kapi Benefits - Transform Your Development Experience',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Benefits - Transform Your Development Experience | Kapi',
    description: 'Stop struggling with 3AM debugging sessions. <PERSON><PERSON> transforms development pain into productive flow with AI-powered tools.',
    images: ['/benefits-twitter.jpg'],
  },
  alternates: {
    canonical: '/benefits',
  },
};

export default function BenefitsPage() {
  // JSON-LD structured data for SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Kapi Benefits - Transform Your Development Experience",
    "description": "Discover how Kapi revolutionizes software development with 60-80% cost reduction, 3x faster development, and 95% fewer production bugs.",
    "url": "https://kapihq.com/benefits",
    "mainEntity": {
      "@type": "Product",
      "name": "Kapi",
      "description": "AI-powered development platform that transforms coding experience",
      "brand": {
        "@type": "Brand",
        "name": "Kapi"
      },
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "description": "Free to start"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "1250"
      }
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <BenefitsPageClient />
    </>
  );
}