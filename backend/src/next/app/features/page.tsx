import type { Metadata } from 'next';
import FeaturesPageClient from './FeaturesPageClient';

export const metadata: Metadata = {
  title: 'Features - Voice, Sketch & AI Agents | Kapi Development Platform',
  description: 'Explore <PERSON><PERSON>\'s revolutionary features: voice-powered coding, sketch-to-code, AI agents, backwards build methodology, and multimodal development. Transform how you build software.',
  keywords: [
    'voice programming',
    'sketch to code',
    'AI agents development',
    'backwards build methodology',
    'multimodal coding',
    'voice-driven development',
    'agentic programming',
    'collaborative coding',
    'AI-powered IDE',
    'natural language programming',
    'visual programming',
    'code generation features'
  ],
  openGraph: {
    title: 'Features - Voice, Sketch & AI Agents | Kapi Development Platform',
    description: 'Explore <PERSON><PERSON>\'s revolutionary features: voice-powered coding, sketch-to-code, AI agents, and multimodal development.',
    url: 'https://kapihq.com/features',
    siteName: 'Kapi',
    images: [
      {
        url: '/features-og.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON><PERSON> Features - Voice, Sketch & AI Agents',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Features - Voice, Sketch & AI Agents | Kapi Development Platform',
    description: 'Revolutionary development features: speak your code, sketch your UI, let AI agents handle the complexity. Experience the future of programming.',
    images: ['/features-twitter.jpg'],
  },
  alternates: {
    canonical: '/features',
  },
};

export default function FeaturesPage() {
  // JSON-LD structured data for SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Kapi Features - Voice, Sketch & AI Agents",
    "description": "Explore Kapi's revolutionary features: voice-powered coding, sketch-to-code, AI agents, backwards build methodology, and multimodal development.",
    "url": "https://kapihq.com/features",
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "Kapi",
      "description": "Multimodal AI development platform",
      "featureList": [
        "Voice-powered coding",
        "Sketch-to-code conversion",
        "AI agent assistance",
        "Backwards build methodology",
        "Multimodal input support",
        "Community collaboration",
        "Real-time pair programming",
        "Automated code generation"
      ],
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Web, Windows, macOS, Linux"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <FeaturesPageClient />
    </>
  );
}