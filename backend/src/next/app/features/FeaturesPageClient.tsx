'use client';

import React from "react";
import Link from "next/link";
import styles from '../styles/benefits.module.css'; // Use benefits styles for consistency
import whiteStyles from '../styles/white-section.module.css';

export default function FeaturesPageClient() {
  const openWaitlistModal = () => {
    window.dispatchEvent(new Event('openWaitlistModal'));
  };

  return (
    <main className="flex flex-col min-h-screen">
      {/* Chapter 1: The Vision */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.content}>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/f85785ec-be93-45cf-80b2-5792d2868c1c/BYFhobXy5J.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Start with End in Mind Animation"
                allowFullScreen
              />
            </div>
            <div className={styles.textContent}>
              <div className={styles.heroBadgeWhite}>Superpower 1</div>
              <h2 className={whiteStyles.title}>Backwards Build Approach</h2>
              <p className={whiteStyles.description}>
                Traditional development rushes to code. We believe in creating beautiful docs and slides that tell your story, tests that define success. Only then do we write code. Kapi guides you through this entire process.
              </p>
            <div className={styles.buildFlow}>
              <div className={styles.flowStep}>
                <div className={styles.stepIcon}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                  </svg>
                </div>
                Vision & Slides
              </div>
              <svg className={styles.flowArrow} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <div className={styles.flowStep}>
                <div className={styles.stepIcon}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                Documentation
              </div>
              <svg className={styles.flowArrow} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <div className={styles.flowStep}>
                <div className={styles.stepIcon}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                </div>
                Tests
              </div>
              <svg className={styles.flowArrow} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <div className={styles.flowStep}>
                <div className={styles.stepIcon}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                Code
              </div>
            </div>
            </div>
          </div>
        </div>
      </section>

      {/* Chapter 2: Voice-Driven Development */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>Superpower 2</div>
              <h2 className={styles.sectionTitle}>Speak Your Code Into Existence</h2>
              <p className={styles.sectionDescription}>
                Imagine describing what you want to build and watching it come to life. With KAPI&apos;s voice-driven development, you&apos;re not typing commands—you&apos;re having a conversation with your coding partner.
              </p>
              <div className={styles.modalityGrid}>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>&quot;Create a user authentication system with JWT&quot;</h3>
                    <p className={styles.modalityDesc}>→ Complete auth module with tests</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>&quot;Add real-time notifications to the dashboard&quot;</h3>
                    <p className={styles.modalityDesc}>→ WebSocket implementation ready</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>&quot;Make this mobile responsive&quot;</h3>
                    <p className={styles.modalityDesc}>→ Responsive CSS applied</p>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/84528c01-3e7f-4af7-974e-529543db7539/GxVGzRlRed.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Voice Development Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* Chapter 3: AI Agents */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.content}>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/fb57fc34-70e3-487d-a00e-e0cbc052a724/ioRs5LTXfw.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Agentic Coding Animation"
                allowFullScreen
              />
            </div>
            <div className={styles.textContent}>
              <div className={styles.heroBadgeWhite}>Superpower 3</div>
              <h2 className={whiteStyles.title}>Multiagentic Solution</h2>
              <p className={whiteStyles.description}>
                Experience autonomous AI agents that understand your codebase, write tests, review code, and handle complex development tasks. These intelligent agents work alongside you, learning your patterns and accelerating your development workflow.
              </p>
              <div className={styles.benefitsGrid}>
                <div className={styles.benefitCard}>
                  <div className={styles.cardIcon}>
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className={styles.cardTitle}>Autonomous Agents</h3>
                  <p>AI agents that understand context and execute complex coding tasks independently</p>
                </div>
                <div className={styles.benefitCard}>
                  <div className={styles.cardIcon}>
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h3 className={styles.cardTitle}>Smart Collaboration</h3>
                  <p>Agents that learn your coding style and preferences for seamless integration</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Chapter 4: Multi-Modal Input */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>Superpower 4</div>
              <h2 className={styles.sectionTitle}>Express Ideas Your Way</h2>
              <p className={styles.sectionDescription}>
                Whether you think in diagrams, speak in descriptions, or type in code, KAPI adapts to your natural workflow. Sketch a UI on paper, describe a feature verbally, or type traditionally—all paths lead to production-ready code.
              </p>
              <div className={styles.modalityGrid}>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>Voice</h3>
                    <p className={styles.modalityDesc}>Describe what you want to build</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>Sketch</h3>
                    <p className={styles.modalityDesc}>Draw your ideas, get working code</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>Code</h3>
                    <p className={styles.modalityDesc}>Type when precision matters</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>Mobile</h3>
                    <p className={styles.modalityDesc}>Code from anywhere, any device</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/b91fc2cd-6343-4b59-b6ea-72ad6f95323d/faGA0f5lT5.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Express Ideas Your Way Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* Chapter 5: Community & Collaboration */}
      <section className={whiteStyles.section}>
        <div className={whiteStyles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadgeWhite}>Superpower 5</div>
              <h2 className={whiteStyles.title}>Never Code Alone</h2>
              <p className={whiteStyles.description}>
                Join a community where developers help each other grow. Ask questions, share knowledge, and pair program with peers who understand your codebase. Every interaction makes the whole community smarter.
              </p>
              <div className={styles.modalityGrid}>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIconWhite}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitleWhite}>Karma System</h3>
                    <p className={styles.modalityDescWhite}>1 point to ask, 2 for answers, 5 for accepted solutions</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIconWhite}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitleWhite}>Smart Matching</h3>
                    <p className={styles.modalityDescWhite}>Connect with developers who know your tech stack</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIconWhite}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitleWhite}>Pair Programming</h3>
                    <p className={styles.modalityDescWhite}>Real-time collaboration with video and shared coding</p>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/96d13dcf-ff96-462f-b891-e8f8a24ef7be/u77ezBTqva.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Never Code Alone Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* Chapter 6: The Future */}
      <section className={styles.darkSection}>
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.textContent}>
              <div className={styles.heroBadge}>The Future</div>
              <h2 className={styles.sectionTitle}>This is Just the Beginning</h2>
              <p className={styles.sectionDescription}>
                KAPI is more than an IDE—it&apos;s a new way of thinking about software development. Where AI doesn&apos;t replace developers but elevates them. Where quality is built-in, not bolted on. Where your ideas become reality faster than ever before.
              </p>
              <div className={styles.modalityGrid}>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>60-80%</h3>
                    <p className={styles.modalityDesc}>Less AI tokens used</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>3x</h3>
                    <p className={styles.modalityDesc}>Faster development</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>95%</h3>
                    <p className={styles.modalityDesc}>Fewer production bugs</p>
                  </div>
                </div>
                <div className={styles.modalityItem}>
                  <div className={styles.modalityIcon}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className={styles.modalityTitle}>&lt; 3min</h3>
                    <p className={styles.modalityDesc}>Idea to working code</p>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.animationContainer}>
              <iframe 
                src="https://lottie.host/embed/9b03ac4a-9cb7-433a-95ad-b134337b96c4/YCOTGjCZEX.lottie"
                width="600"
                height="600"
                style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
                title="Express Ideas Your Way Animation"
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className={styles.ctaSection}>
        <div className={styles.ctaContent}>
          <h2 className={styles.ctaTitle}>Ready to Write Your Story?</h2>
          <p className={styles.ctaSubtitle}>
            Join thousands of developers who are building better software, faster
          </p>
          <div className={styles.ctaButtons}>
            <button onClick={openWaitlistModal} className={`${styles.ctaButton} ${styles.ctaPrimary}`}>
              Join Waitlist
              <svg className={styles.ctaIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <Link href="/benefits" className={`${styles.ctaButton} ${styles.ctaSecondary}`}>
              See the Benefits
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}