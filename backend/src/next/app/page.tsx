import type { Metadata } from 'next';
import HomePageClient from './components/HomePageClient';

export const metadata: Metadata = {
  title: 'Kapi - Voice & Sketch to Code | AI-Powered Development Studio',
  description: 'Revolutionary multimodal coding platform. Build real software using voice, sketches, and text. AI handles the complexity while you focus on engineering. Start building production apps in minutes.',
  keywords: [
    'voice coding',
    'AI development',
    'multimodal programming',
    'sketch to code',
    'voice programming',
    'AI IDE',
    'natural language coding',
    'visual programming',
    'AI-powered development',
    'software engineering 2.0',
    'accessible coding',
    'inclusive development',
    'backwards build methodology',
    'agentic programming',
    'cross-device development'
  ],
  authors: [{ name: '<PERSON><PERSON> Team' }],
  creator: '<PERSON><PERSON>',
  publisher: '<PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://kapihq.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Kapi - Voice & Sketch to Code | AI-Powered Development Studio',
    description: 'Revolutionary multimodal coding platform. Build real software using voice, sketches, and text. AI handles the complexity while you focus on engineering.',
    url: 'https://kapihq.com',
    siteName: 'Kapi',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Kapi - Multimodal AI Development Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kapi - Voice & Sketch to Code | AI-Powered Development Studio',
    description: 'Revolutionary multimodal coding platform. Build real software using voice, sketches, and text. Start building production apps in minutes.',
    images: ['/twitter-image.jpg'],
    creator: '@KapiHQ',
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code', // Replace with actual verification code
  },
  category: 'technology',
  classification: 'Development Tools',
  other: {
    'application-name': 'Kapi',
    'apple-mobile-web-app-title': 'Kapi',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'mobile-web-app-capable': 'yes',
    'msapplication-TileColor': '#000000',
    'theme-color': '#000000',
  },
};

export default function Home() {
  // JSON-LD structured data for SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Kapi",
    "description": "Revolutionary multimodal coding platform. Build real software using voice, sketches, and text. AI handles the complexity while you focus on engineering.",
    "url": "https://kapihq.com",
    "applicationCategory": "DeveloperApplication",
    "operatingSystem": "Web, Windows, macOS, Linux",
    "offers": {
      "@type": "Offer",
      "priceCurrency": "USD",
      "price": "0",
      "description": "Free to start"
    },
    "creator": {
      "@type": "Organization",
      "name": "Kapi",
      "url": "https://kapihq.com"
    },
    "featureList": [
      "Voice-powered coding",
      "Sketch-to-code conversion",
      "AI-powered development",
      "Multi-modal programming interface",
      "Cross-device development",
      "Automated code generation",
      "Natural language programming"
    ],
    "screenshot": "https://kapihq.com/og-image.jpg"
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <HomePageClient />
    </>
  );
}
