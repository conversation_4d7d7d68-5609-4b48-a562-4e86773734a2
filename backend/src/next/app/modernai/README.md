/nodejs_backend
├── src
│   ├── next                 # Unified Next.js frontend
│   │   ├── app
│   │   │   ├── (kapi)       # KAPI-specific routes (grouped layout)
│   │   │   │   ├── page.tsx # KAPI landing page
│   │   │   │   ├── ide/...  # KAPI IDE specific pages
│   │   │   │   └── layout.tsx # KAPI-specific layout
│   │   │   │
│   │   │   ├── (modern-ai) # ModernAI Pro routes (grouped layout)
│   │   │   │   ├── page.tsx # ModernAI landing page
│   │   │   │   ├── workshops/... # Workshop specific pages
│   │   │   │   └── layout.tsx # ModernAI-specific layout
│   │   │   │
│   │   │   ├── shared      # Shared components/pages between brands
│   │   │   │   ├── blog    # Shared blog system
│   │   │   │   ├── auth    # Shared authentication
│   │   │   │   └── account # Shared account management
│   │   │   │
│   │   │   ├── layout.tsx  # Common root layout
│   │   │   └── ...
│   ├── api                 # Shared backend API
│   └── ...