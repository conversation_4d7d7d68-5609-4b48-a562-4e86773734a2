// Import existing tokens from .env.local if they exist
// This allows using the same <PERSON> tokens across your legacy and new app

import { clerkClient } from '@clerk/nextjs/server';
import { auth } from '@clerk/nextjs/server';

// Helper function to validate a Clerk token
export async function validateClerkToken(token: string): Promise<boolean> {
  try {
    // In Next.js 15, we should use the auth() helper instead of directly calling verifyToken
    // This is a simplified validation that checks if the token is associated with a valid session
    const client = await clerkClient();
    const session = await client.sessions.getSession(token);
    return !!session;
  } catch (error) {
    console.error('Error validating Clerk token:', error);
    return false;
  }
}

// Get a user's data from their clerk ID
export async function getUserDataFromClerkId(userId: string) {
  try {
    const client = await clerkClient();
    const user = await client.users.getUser(userId);
    return user;
  } catch (error) {
    console.error('Error getting user data from Clerk:', error);
    return null;
  }
}
