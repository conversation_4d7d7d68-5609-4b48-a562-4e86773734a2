import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms of Service | KAPI",
  description: "Learn about the terms and conditions for using KAPI's services.",
};

export default function TermsPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">Terms of Service</h1>
        <p className="text-gray-500 mb-8">Last updated: May 20, 2025</p>
        
        <div className="prose prose-lg max-w-none">
          <h2 className="text-2xl font-semibold mt-8 mb-4">Introduction</h2>
          <p>
            These Terms of Service ("Terms") govern your access to and use of KAPI's services, including our website, platform, and any associated services (collectively, the "Services"). 
            By accessing or using the Services, you agree to be bound by these Terms.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Eligibility</h2>
          <p>
            You must be at least 18 years old to use our Services. By using our Services, you represent and warrant that you meet this requirement.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Your Account</h2>
          <p>
            To access certain features of our Services, you may need to create an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. 
            You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">User Conduct</h2>
          <p>You agree not to:</p>
          <ul>
            <li>Use the Services for any illegal purpose or in violation of any applicable laws or regulations.</li>
            <li>Violate or infringe the intellectual property rights or other rights of third parties.</li>
            <li>Attempt to gain unauthorized access to any portion of the Services or any other accounts, computer systems, or networks connected to the Services.</li>
            <li>Use the Services to develop, design, or create any competing product or service.</li>
            <li>Use any automated systems or software to extract data from the Services ("scraping") without our prior written consent.</li>
            <li>Interfere with the proper working of the Services or any activities conducted on the Services.</li>
            <li>Bypass any measures we may use to prevent or restrict access to the Services.</li>
          </ul>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Intellectual Property Rights</h2>
          <p>
            The Services and their entire contents, features, and functionality (including but not limited to all information, software, text, displays, images, and the design and arrangement thereof), are owned by KAPI, its licensors, or other providers and are protected by copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">User Content</h2>
          <p>
            You retain all rights in, and are solely responsible for, the content you submit, post, or display on or through the Services ("User Content"). By submitting User Content to the Services, you grant KAPI a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, and display such User Content for the purpose of providing and improving the Services.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Limitation of Liability</h2>
          <p>
            In no event shall KAPI, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Services.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Disclaimer of Warranties</h2>
          <p>
            The Services are provided "as is" and "as available" without any warranties of any kind, either express or implied, including but not limited to the implied warranties of merchantability, fitness for a particular purpose, or non-infringement.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Termination</h2>
          <p>
            We may terminate or suspend your access to the Services immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach these Terms.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Governing Law</h2>
          <p>
            These Terms shall be governed by and construed in accordance with the laws of California, without regard to its conflict of law provisions.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Changes to Terms</h2>
          <p>
            We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Contact Us</h2>
          <p>
            If you have any questions about these Terms, please contact us at:
          </p>
          <p className="font-semibold"><EMAIL></p>
        </div>
      </div>
    </div>
  );
}
