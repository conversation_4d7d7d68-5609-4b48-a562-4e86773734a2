import { Metadata } from 'next';
import React from 'react';
import './styles/globals.css';
import { Layout } from '@/components/layout';
import { ClerkProvider } from '@clerk/nextjs';
import UserRegistration from './components/auth/UserRegistration';

// Make sure the Clerk publishable key is available
const clerkPubKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

export const metadata: Metadata = {
  title: 'KAPI - Vibe coding that just works!',
  description: 'Build real software using natural input — voice, sketches, or text. AI handles the rest. Vibe coding that just works!',
  metadataBase: new URL('https://kapihq.com'),
  openGraph: {
    title: 'KAPI - Vibe coding that just works!',
    description: 'Build real software using natural input — voice, sketches, or text. AI handles the rest.',
    url: 'https://kapihq.com',
    siteName: 'KAPI',
    images: [
      {
        url: '/og-preview.png',
        width: 1200,
        height: 630,
        alt: 'KAPI - AI-Native Development Environment',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KAPI - Vibe coding that just works!',
    description: 'Build real software using natural input — voice, sketches, or text. AI handles the rest.',
    images: ['/og-preview.png'],
    creator: '@kapihq',
    site: '@kapihq',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code-here',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Always provide ClerkProvider - it handles build time gracefully
  return (
    <html lang="en">
      <head>
        {/* Critical meta tags for WhatsApp/social media - must come first */}
        <meta property="og:title" content="KAPI - Vibe coding that just works!" />
        <meta property="og:description" content="Build real software using natural input — voice, sketches, or text. AI handles the rest." />
        <meta property="og:image" content="https://kapihq.com/og-preview.png" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:url" content="https://kapihq.com/" />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="KAPI" />
        
        {/* Twitter Card meta tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="KAPI - Vibe coding that just works!" />
        <meta name="twitter:description" content="Build real software using natural input — voice, sketches, or text. AI handles the rest." />
        <meta name="twitter:image" content="https://kapihq.com/og-preview.png" />
        <meta name="twitter:creator" content="@kapihq" />
        <meta name="twitter:site" content="@kapihq" />
        
        {/* Additional meta tags */}
        <meta name="description" content="Build real software using natural input — voice, sketches, or text. AI handles the rest. Vibe coding that just works!" />
        <link rel="canonical" href="https://kapihq.com/" />
      </head>
      <body className="antialiased">
        <ClerkProvider 
          publishableKey={clerkPubKey || ''}
          waitlistUrl="/waitlist"
        >
          <UserRegistration />
          <Layout>{children}</Layout>
        </ClerkProvider>
      </body>
    </html>
  );
}
