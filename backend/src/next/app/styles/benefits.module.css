/* Import landing page styles for consistency - MUST be first */
@import './landing.module.css';

/* benefits.module.css - Benefits page specific styles matching landing page */

/* Use the same hero styles from landing page */
.heroSection {
  position: relative;
  min-height: 60vh;
  background-color: #000 !important;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: radial-gradient(circle at 50% 100%, rgba(139, 92, 246, 0.15) 0%, rgba(0, 0, 0, 0) 50%);
}

.heroContent {
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
  padding: 4rem 2rem;
  text-align: center;
}

.heroBadge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
}

.heroBadge svg {
  margin-right: 0.5rem;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  line-height: 1.1;
  letter-spacing: -0.02em;
  margin-bottom: 1.5rem;
  text-shadow: 0 0 80px rgba(139, 92, 246, 0.2);
}

.heroSubtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  max-width: 650px;
  margin: 0 auto;
  letter-spacing: 0.01em;
}

/* Container styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Benefits Grid */
.benefitsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.benefitCard {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.1);
}

.cardIcon {
  width: 48px;
  height: 48px;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.benefitsList li {
  color: #475569;
  font-size: 1rem;
  line-height: 1.6;
  padding-left: 1.5rem;
  position: relative;
}

.benefitsList li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #8B5CF6;
  font-weight: bold;
}

/* Dark Section for Multi-Modal */
.darkSection {
  background: #0f0f0f;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.darkSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

.content {
  display: flex;
  align-items: center;
  gap: 4rem;
  position: relative;
  z-index: 1;
}

.textContent {
  flex: 1;
  max-width: 600px;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.sectionDescription {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.modalityGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modalityItem {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modalityIcon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #B7A9FF;
}

.modalityTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.25rem;
}

.modalityDesc {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.animationContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Backwards Build Flow */
.buildFlow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.flowStep {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  color: #334155;
}

.stepIcon {
  width: 32px;
  height: 32px;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.flowArrow {
  width: 24px;
  height: 24px;
  color: #cbd5e1;
}

/* Gradient Section for AI Agents */
.gradientSection {
  background: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.sectionTitleDark {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 3rem;
}

.agentsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.agentCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.2s ease;
}

.agentCard:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
}

.agentTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.agentDesc {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: #f8fafc;
  text-align: center;
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.ctaTitle {
  font-size: 3rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.ctaSubtitle {
  font-size: 1.25rem;
  color: #475569;
  margin-bottom: 2rem;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.9rem 1.75rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-size: 1rem;
  text-decoration: none;
}

.ctaPrimary {
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  color: white;
  border: none;
  box-shadow: 0 10px 30px -10px rgba(139, 92, 246, 0.5);
}

.ctaPrimary:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(139, 92, 246, 0.6);
}

.ctaSecondary {
  background: white;
  border: 2px solid #e2e8f0;
  color: #334155;
}

.ctaSecondary:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.ctaIcon {
  margin-left: 0.5rem;
}

/* White Section Overrides - for use in white background sections */
.heroBadgeWhite {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 30px;
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  color: white;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px -4px rgba(139, 92, 246, 0.3);
}

.modalityTitleWhite {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.modalityDescWhite {
  font-size: 0.875rem;
  color: #64748b;
}

.modalityIconWhite {
  width: 48px;
  height: 48px;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

/* Story-Driven Styles for Benefits Page */

/* Pain Points Section */
.painPoints {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.painItem {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.painItem:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
}

.painIcon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.painItem p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Discovery Section */
.discoveryGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.discoveryItem {
  padding: 1.5rem;
  border-left: 3px solid transparent;
  border-image: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border-image-slice: 1;
}

.discoveryTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.discoveryDesc {
  color: #64748b;
  line-height: 1.6;
}

/* Transformation Section */
.transformationGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.transformCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.5rem;
}

.transformBefore {
  flex: 1;
  text-align: center;
}

.transformBefore h4 {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.transformBefore p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
}

.transformArrow {
  color: #8B5CF6;
  font-size: 1.5rem;
  font-weight: bold;
}

.transformAfter {
  flex: 1;
  text-align: center;
}

.transformAfter h4 {
  color: #B7A9FF;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.transformAfter p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  font-weight: 500;
}

/* Community Stats */
.communityStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin: 2rem 0;
}

.statCard {
  text-align: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #64748b;
  font-size: 0.875rem;
}

/* Testimonial */
.testimonial {
  margin-top: 3rem;
  padding: 2rem;
  background: #f1f5f9;
  border-radius: 16px;
  border-left: 4px solid;
  border-image: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  border-image-slice: 1;
}

.testimonialQuote {
  font-size: 1.125rem;
  color: #334155;
  font-style: italic;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.testimonialAuthor {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Results Section */
.resultsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.resultStory {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.resultStory:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-4px);
}

.resultIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.resultStory h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.resultStory p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.resultMetric {
  display: inline-block;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.825rem;
  font-weight: 600;
}

/* Choice Section - Updated to match design */
.choiceContent {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.pathComparison {
  display: flex;
  gap: 3rem;
  align-items: stretch;
  margin-top: 3rem;
  justify-content: center;
}

.pathCard {
  flex: 1;
  max-width: 450px;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 2.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.pathCard:first-child {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.pathCard:last-child {
  background: linear-gradient(135deg, #7c3aed 0%, #6366f1 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 20px 40px -10px rgba(124, 58, 237, 0.3);
}

.pathTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.pathCard:first-child .pathTitle {
  color: #1e293b;
}

.pathCard:last-child .pathTitle {
  color: white;
}

.pathList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.pathList li {
  font-size: 1rem;
  line-height: 1.6;
  padding-left: 1.75rem;
  position: relative;
}

.pathCard:first-child .pathList li {
  color: #64748b;
}

.pathCard:last-child .pathList li {
  color: rgba(255, 255, 255, 0.9);
}

.pathList li::before {
  content: '•';
  position: absolute;
  left: 0.5rem;
  font-size: 1.2rem;
}

.pathCard:first-child .pathList li::before {
  color: #cbd5e1;
}

.pathCard:last-child .pathList li::before {
  color: rgba(255, 255, 255, 0.6);
}

.pathDivider {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pathDivider span {
  background: linear-gradient(135deg, #7c3aed 0%, #6366f1 100%);
  color: white;
  padding: 0.5rem 1.25rem;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.1em;
  box-shadow: 0 4px 20px -4px rgba(124, 58, 237, 0.4);
  position: relative;
  z-index: 1;
}

/* CTA Note */
.ctaNote {
  margin-top: 1rem;
  color: #64748b;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 3rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
  }
  
  .content {
    flex-direction: column;
  }
  
  .agentsGrid {
    grid-template-columns: 1fr;
  }
  
  .buildFlow {
    flex-direction: column;
  }
  
  .flowArrow {
    transform: rotate(90deg);
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .painPoints {
    grid-template-columns: 1fr;
  }
  
  .communityStats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .resultsGrid {
    grid-template-columns: 1fr;
  }
  
  .pathComparison {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }
  
  .pathCard {
    max-width: 100%;
    width: 100%;
  }
  
  .pathCard:last-child {
    transform: scale(1);
  }
  
  .pathDivider {
    margin: 0.5rem 0;
  }
}
