.navContainer {
    position: sticky;
    top: 0;
    z-index: 50;
    background: #000 !important;
    backdrop-filter: blur(8px);
    border-bottom: none !important;
    box-shadow: none;
    overflow: visible; /* Changed from hidden to allow menu to be visible */
  }
  .navContainer::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -2;
    background: linear-gradient(
      to bottom,
      rgba(26, 9, 51, 0.95) 0%,
      rgba(26, 9, 51, 0.85) 60%,
      rgba(26, 9, 51, 0.7) 100%
    );
  }

  .navContainer::after {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    pointer-events: none;
    opacity: 0.15;
    background: url('https://www.transparenttextures.com/patterns/noise.png');
  }

  .nav {
    max-width: 1300px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    font-size: 1.1rem;
    color: white;
    text-decoration: none;
  }

  .brandText {
    font-family: 'JetBrains Mono', monospace;
  }

  .menu {
    display: flex;
    gap: 1.5rem;
    list-style: none;
  }

  .menu li a {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    transition: color 0.2s ease;
  }

  .menu li a:hover {
    color: #ffffff;
  }

  @media (max-width: 768px) {
    .menu li {
      width: 100%;
      text-align: center;
    }

    .menu li a {
      display: block;
      padding: 0.75rem 1rem;
      width: 100%;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .actions {
      width: 100%;
      padding: 0 1rem;
      justify-content: center;
    }
  }

  .signIn {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem 0;
    font-size: 1rem;
  }

  .signIn:hover {
    color: white;
  }

  .signUp {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
  }

  .signUp:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .hamburger {
    display: none;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
  }

  @media (max-width: 768px) {
    .menu, .actions {
      display: none;
      position: absolute;
      top: 64px; /* adjust as needed */
      left: 0;
      right: 0;
      background: rgba(26, 9, 51, 0.98);
      flex-direction: column;
      align-items: center;
      gap: 2rem;
      z-index: 100;
      padding: 2rem 0;
      width: 100%;
    }
    .menu.menuOpen, .actions.menuOpen {
      display: flex;
    }
    .hamburger {
      display: block;
      margin-left: auto;
    }
  }

  /* This class is redundant and causing conflicts - the specific classes above should be used instead */
  /* .menuOpen {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    position: absolute;
    top: 64px;
    left: 0;
    right: 0;
    background: rgba(26, 9, 51, 0.98);
    z-index: 100;
  } */
