/* Differentiators section styles */

.section {
  position: relative;
  padding: 6rem 0;
  background-color: #1A0634;
  color: white;
  overflow: hidden;
}

.sectionBg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
  z-index: 1;
}

.container {
  position: relative;
  z-index: 2;
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2rem;
}

.title {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.heading {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.headingSpan {
  color: #8B5CF6;
}

.description {
  max-width: 800px;
  margin: 0 auto 4rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
  margin-bottom: 4rem;
}

@media (min-width: 768px) {
  .statsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.statCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -20px rgba(139, 92, 246, 0.3);
}

.statIcon {
  font-size: 2.5rem;
  color: #8B5CF6;
  margin-bottom: 1.5rem;
}

.statValue {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statUnit {
  font-size: 2rem;
  font-weight: 700;
  color: #8B5CF6;
  margin-left: 0.25rem;
}

.statText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  line-height: 1.5;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 768px) {
  .featuresGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.featureCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -20px rgba(139, 92, 246, 0.3);
}

.featureTitle {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.featureIcon {
  color: #8B5CF6;
  margin-right: 0.75rem;
  font-size: 1.5rem;
}

.featureDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureListItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.featureListIcon {
  color: #22c55e;
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
}
