/* Import Google Fonts - MUST be first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fraunces:ital,wght@0,400;0,600;0,700;0,800;1,400;1,600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Literata:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* About page specific styles */
.about-container {
  background: #f8fafc;
  color: white;
}

.about-inner {
  background-image: linear-gradient(to bottom, #3F2484, #2D1B5C);
  color: white;
  border-radius: 1.5rem;
}

.about-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
}

.about-subtitle {
  font-size: 1.125rem;
  color: #e2e8f0;
}

.about-founder-card {
  background: rgba(45, 27, 92, 0.4);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(136, 105, 186, 0.3);
  border-radius: 1rem;
}

.about-section-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.about-text {
  color: #e2e8f0;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 252;
  --background-end-rgb: 255, 255, 255;

  /* Brand colors */
  --primary-purple: 63, 36, 132; /* Darker, richer purple */
  --primary-purple-light: 136, 105, 186; /* Secondary purple */
  --primary-purple-dark: 48, 24, 96; /* Dark purple for emphasis */
  --tag-purple-bg: 238, 235, 252; /* Very light purple for tag backgrounds */

  /* Text colors */
  --text-primary: 33, 37, 41;
  --text-secondary: 73, 80, 87;
  --text-tertiary: 108, 117, 125;
}

@layer base {
  html {
    font-size: 15px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  @media (min-width: 640px) {
    html {
      font-size: 16px;
    }
  }

  body {
    color: rgb(var(--text-primary));
    background: rgb(var(--background-start-rgb));
    font-family: 'Literata', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
    letter-spacing: -0.011em;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Fraunces', Georgia, serif;
    font-weight: 800;
    letter-spacing: -0.03em;
    color: rgb(var(--text-primary));
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  h1 {
    font-size: 2.25rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: 1.25rem;
    line-height: 1.25;
  }

  h3 {
    font-size: 1.375rem;
    line-height: 1.3;
  }

  p {
    margin-bottom: 1rem;
  }

  a {
    color: rgb(var(--primary-purple));
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
  }

  a:hover {
    color: rgba(var(--primary-purple), 0.8);
  }

  code, pre {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9rem;
  }

  pre {
    background: rgb(240, 240, 245);
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }

  blockquote {
    border-left: 3px solid rgb(var(--primary-purple));
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    color: rgb(var(--text-secondary));
  }
}

.heading-white {
  color: white;
}

@layer components {
  /* Blog specific styles */
  .blog-container {
    max-width: 720px;
    margin: 0 auto;
    padding: 1rem 1rem;
  }

  @media (min-width: 640px) {
    .blog-container {
      padding: 1.5rem 1.25rem;
    }
  }

  .blog-header {
    margin-bottom: 2rem;
  }

  .blog-title {
    font-family: 'Fraunces', Georgia, serif;
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 0.75rem;
    letter-spacing: -0.03em;
  }

  @media (min-width: 640px) {
    .blog-title {
      font-size: 3rem;
    }
  }

  .blog-subtitle {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 1.125rem;
    font-weight: 400;
    color: rgb(var(--text-secondary));
    margin-bottom: 1.5rem;
    line-height: 1.4;
    max-width: 100%;
  }

  @media (min-width: 640px) {
    .blog-subtitle {
      font-size: 1.25rem;
      max-width: 90%;
    }
  }

  .blog-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
  }

  .blog-author-info {
    display: flex;
    align-items: center;
  }

  .blog-author-image {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 0.75rem;
    object-fit: cover;
    border: 1px solid rgba(0, 0, 0, 0.08);
  }

  .blog-author-name {
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.125rem;
  }

  .blog-author-title {
    font-size: 0.875rem;
    color: rgb(var(--text-tertiary));
    font-weight: 400;
  }

  .blog-date {
    font-size: 0.875rem;
    color: rgb(var(--text-tertiary));
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .blog-tag {
    display: inline-block;
    padding: 0.35rem 1rem;
    background-color: rgb(var(--tag-purple-bg));
    color: rgb(var(--primary-purple));
    border-radius: 9999px;
    font-size: 0.8125rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.15s ease;
  }

  .blog-tag:hover {
    background-color: rgba(var(--primary-purple-light), 0.15);
  }

  .blog-content {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-top: 2rem;
  }

  .blog-content p {
    margin-bottom: 1.25rem;
  }

  .blog-content h2 {
    margin-top: 2rem;
    margin-bottom: 0.75rem;
  }

  .blog-content h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .blog-content img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 4px;
  }

  .blog-back-link {
    display: inline-flex;
    align-items: center;
    color: rgb(var(--primary-purple));
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
    margin-bottom: 1.25rem;
    padding: 0.375rem 0;
    font-size: 0.875rem;
    gap: 0.375rem;
  }

  .blog-back-link svg {
    height: 1rem;
    width: 1rem;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .prose-container {
    max-width: 720px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .prose-lg {
    font-size: 1.0625rem;
    line-height: 1.6;
  }

  .code-block {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9rem;
    background: rgb(240, 240, 245);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin: 1.5rem 0;
  }
}
