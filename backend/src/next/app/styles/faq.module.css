/* FAQ section styles */

.section {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(to bottom, #ffffff 0%, #f8f9ff 100%);
  overflow: hidden;
}

.container {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.heading {
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #1A0634 0%, #6366F1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.subheading {
  font-size: 1.25rem;
  text-align: center;
  color: #6b7280;
  max-width: 800px;
  margin: 0 auto 5rem;
  line-height: 1.6;
}

.faqGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .faqGrid {
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
  }
}

.faqSidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faqTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1A0634;
  margin-bottom: 1.5rem;
}

.faqDescription {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 2rem;
}

.faqCta {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.9rem 1.75rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-size: 1rem;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  color: white;
  border: none;
  box-shadow: 0 10px 30px -10px rgba(139, 92, 246, 0.5);
  text-decoration: none;
}

.faqCta:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(139, 92, 246, 0.6);
}

.faqList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faqItem {
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.faqItem:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
}

.faqQuestion {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-weight: 600;
  color: #1A0634;
  font-size: 1.1rem;
  user-select: none;
}

.faqIcon {
  color: #8B5CF6;
  transition: transform 0.3s ease;
}

.faqIconOpen {
  transform: rotate(180deg);
}

.faqAnswer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0 1.5rem;
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
}

.faqAnswerOpen {
  max-height: 500px;
  padding: 0 1.5rem 1.5rem;
}

/* Animation for FAQ items */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated {
  animation: fadeIn 0.5s ease forwards;
}

.delay1 {
  animation-delay: 0.1s;
}

.delay2 {
  animation-delay: 0.2s;
}

.delay3 {
  animation-delay: 0.3s;
}

.delay4 {
  animation-delay: 0.4s;
}

.delay5 {
  animation-delay: 0.5s;
}

.delay6 {
  animation-delay: 0.6s;
}

.delay7 {
  animation-delay: 0.7s;
}
