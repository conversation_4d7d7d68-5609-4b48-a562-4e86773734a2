.section {
  padding: 6rem 0;
  background: #f8fafc;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.textContent {
  flex: 0.8;
  max-width: 500px;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.description {
  font-size: 1.25rem;
  color: #475569;
  line-height: 1.6;
}

.animationContainer {
  flex: 1.2;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }

  .content {
    flex-direction: column;
    gap: 2rem;
  }

  .textContent {
    max-width: 100%;
    text-align: center;
  }

  .title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.125rem;
  }

  .animationContainer {
    min-height: 450px;
    width: 100%;
  }
} 