/* Testimonials section styles */

.section {
  position: relative;
  padding: 8rem 0;
  background-color: #1A0634;
  color: white;
  overflow: hidden;
}

.container {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.sectionBg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
  z-index: 1;
}

.heading {
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.subheading {
  font-size: 1.25rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 0 auto 5rem;
  line-height: 1.6;
}

.testimonialsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 768px) {
  .testimonialsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.testimonialCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.testimonialCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px -20px rgba(139, 92, 246, 0.3);
}

.testimonialQuote {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  flex-grow: 1;
}

.testimonialQuoteIcon {
  color: #8B5CF6;
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.testimonialAuthor {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonialAvatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
}

.testimonialInfo {
  display: flex;
  flex-direction: column;
}

.testimonialName {
  font-weight: 600;
  color: white;
  font-size: 1.1rem;
}

.testimonialRole {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Animation for testimonial cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

.delay1 {
  animation-delay: 0.1s;
}

.delay2 {
  animation-delay: 0.3s;
}

.delay3 {
  animation-delay: 0.5s;
}
