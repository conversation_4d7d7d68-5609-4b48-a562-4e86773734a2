.section {
  padding: 6rem 0;
  background: #2d0b5a;
  position: relative;
  overflow: hidden;
}

.compactSection {
  padding: 3rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.textContent {
  flex: 0.8;
  max-width: 500px;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.description {
  font-size: 1.25rem;
  color: #fff;
  line-height: 1.6;
}

.animationContainer {
  flex: 1.2;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compactAnimationContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  min-height: auto;
  padding: 1rem 0;
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }
  
  .compactSection {
    padding: 2rem 0;
  }

  .content {
    flex-direction: column;
    gap: 2rem;
  }

  .textContent {
    max-width: 100%;
    text-align: center;
  }

  .title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.125rem;
  }

  .animationContainer {
    min-height: 450px;
    width: 100%;
  }
  
  .compactAnimationContainer {
    padding: 0.5rem 0;
    width: 100%;
  }
} 