.featureSection {
  padding: 6rem 0;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.featureSection:first-child {
  background-color: #f1f5f9;
  border-top: 1px solid #e2e8f0;
}

.featureContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 4rem;
}

.featureText {
  flex: 1;
  max-width: 600px;
}

.featureTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.featureDescription {
  font-size: 1.25rem;
  color: #475569;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.featureAnimation {
  flex: 1;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.learnMore {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.learnMore:hover {
  color: #2563eb;
}

.arrowIcon {
  transition: transform 0.2s;
}

.learnMore:hover .arrowIcon {
  transform: translateX(4px);
}

.reversed .featureContent {
  flex-direction: row-reverse;
}

@media (max-width: 768px) {
  .featureContent {
    flex-direction: column;
    gap: 2rem;
    padding: 0 1rem;
  }

  .featureText {
    max-width: 100%;
    text-align: center;
  }

  .featureTitle {
    font-size: 2rem;
  }

  .featureDescription {
    font-size: 1.125rem;
  }

  .featureAnimation {
    min-height: 300px;
    width: 100%;
  }

  .reversed .featureContent {
    flex-direction: column;
  }
}
