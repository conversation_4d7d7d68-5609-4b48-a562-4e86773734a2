/* Import landing page styles for consistency - MUST be first */
@import './landing.module.css';

/* features.module.css - Features page storytelling styles */

/* Chapter Numbers */
.chapterNumber {
  display: inline-block;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 1rem;
  border-radius: 20px;
  margin-bottom: 1rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Story Sections */
.storySection {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

/* Backwards Build Flow */
.backwardsBuildFlow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.flowItem {
  flex: 1;
  min-width: 200px;
  max-width: 220px;
  text-align: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flowItem:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.1);
}

.flowIcon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #f3e7ff 0%, #e7e0ff 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8B5CF6;
}

.flowIcon svg {
  width: 32px;
  height: 32px;
}

.flowItem h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.flowItem p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.flowArrow {
  font-size: 2rem;
  color: #cbd5e1;
  font-weight: 300;
}

/* Voice Examples */
.voiceExamples {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.voiceExample {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.voiceQuote {
  flex: 1;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.voiceResult {
  color: #a78bfa;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Agent Grid */
.agentGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.agentItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.agentIcon {
  font-size: 2rem;
  line-height: 1;
}

.agentItem h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.agentItem p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Modal Grid */
.modalGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 3rem;
}

.modalCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.2s ease, background 0.2s ease;
}

.modalCard:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
}

.modalIcon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.modalIcon svg {
  width: 32px;
  height: 32px;
}

.modalCard h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.modalCard p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Multi-Modal Animation */
.multiModalAnimation {
  max-width: 600px;
  height: 400px;
  margin: 0 auto;
}

/* Community Features */
.communityFeatures {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.communityFeature {
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  text-align: left;
}

.communityFeature strong {
  display: block;
  font-size: 1.125rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.communityFeature p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Future Section */
.futureSection {
  background: linear-gradient(180deg, #0f0f0f 0%, #1a1a1a 100%);
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.futureSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
}

.futureContent {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.futureTitle {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.futureDescription {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.futureStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.stat {
  text-align: center;
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Section Description Light */
.sectionDescriptionLight {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .backwardsBuildFlow {
    flex-direction: column;
  }
  
  .flowArrow {
    transform: rotate(90deg);
  }
  
  .agentGrid {
    grid-template-columns: 1fr;
  }
  
  .modalGrid {
    grid-template-columns: 1fr;
  }
  
  .futureStats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .futureTitle {
    font-size: 2rem;
  }
  
  .statNumber {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .futureStats {
    grid-template-columns: 1fr;
  }
}
