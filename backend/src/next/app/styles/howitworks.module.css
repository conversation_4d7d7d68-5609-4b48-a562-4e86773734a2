/* How It Works section styles */

.section {
  position: relative;
  padding: 8rem 0;
  background-color: #1A0634;
  color: white;
  overflow: hidden;
}

.container {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.sectionBg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
  z-index: 1;
}

.heading {
  font-size: 3rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.subheading {
  font-size: 1.25rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 0 auto 5rem;
  line-height: 1.6;
}

.stepsContainer {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  max-width: 1000px;
  margin: 0 auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  position: relative;
}

@media (min-width: 768px) {
  .step {
    flex-direction: row;
    align-items: center;
  }
  
  .step:nth-child(even) {
    flex-direction: row-reverse;
  }
}

.stepNumber {
  font-size: 7rem;
  font-weight: 900;
  line-height: 1;
  background: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  opacity: 0.3;
  position: absolute;
  top: -3rem;
  left: 0;
  z-index: 0;
}

@media (min-width: 768px) {
  .stepNumber {
    top: -2rem;
    left: -2rem;
  }
  
  .step:nth-child(even) .stepNumber {
    left: auto;
    right: -2rem;
  }
}

.stepContent {
  flex: 1;
  position: relative;
  z-index: 1;
}

.stepTitle {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.stepDescription {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.stepImage {
  flex: 1;
  position: relative;
  width: 100%;
  max-width: 450px;
  aspect-ratio: 16/9;
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px -20px rgba(0, 0, 0, 0.5);
}

.stepImageInner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
}

.stepIcon {
  font-size: 5rem;
  color: #8B5CF6;
}

.stepList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.stepListItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.stepListIcon {
  color: #8B5CF6;
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

/* Connector line between steps */
.connector {
  position: absolute;
  width: 2px;
  background: linear-gradient(to bottom, rgba(139, 92, 246, 0.3), rgba(99, 102, 241, 0.3));
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 0;
  display: none;
}

@media (min-width: 768px) {
  .connector {
    display: block;
  }
}

/* Animation for steps */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

.delay1 {
  animation-delay: 0.1s;
}

.delay2 {
  animation-delay: 0.3s;
}

.delay3 {
  animation-delay: 0.5s;
}

.delay4 {
  animation-delay: 0.7s;
}
