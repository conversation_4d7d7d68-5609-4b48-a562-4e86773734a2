/* landing.module.css - Modern startup landing page styles */

/* Hero section styling */
.heroSection {
  position: relative;
  min-height: 100vh;
  background-color: #000 !important;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background-image: radial-gradient(circle at 50% 100%, rgba(139, 92, 246, 0.15) 0%, rgba(0, 0, 0, 0) 50%);
}

/* Animated background elements */
.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.heroCircle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.04;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.1) 100%);
  animation: pulse 15s infinite ease-in-out;
}

.heroCircle1 {
  width: 60vw;
  height: 60vw;
  top: -20vw;
  right: -20vw;
  animation-delay: 0s;
}

.heroCircle2 {
  width: 50vw;
  height: 50vw;
  bottom: -15vw;
  left: -15vw;
  animation-delay: 2s;
}

.heroCircle3 {
  width: 20vw;
  height: 20vw;
  top: 20%;
  right: 20%;
  opacity: 0.03;
  animation-delay: 4s;
}

.heroGrid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

/* Main content container */
.heroContent {
  position: relative;
  z-index: 10;
  max-width: 1300px;
  margin: 0 auto;
  padding: 5rem 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 95vh;
  width: 100%;
}

/* Hero headings */
.heroBadge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
}

.heroBadge svg {
  margin-right: 0.5rem;
}

.heroTitle {
  font-size: 4.5rem;
  font-weight: 800;
  background: linear-gradient(90deg, #FFFFFF 0%, #B7A9FF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  line-height: 1.1;
  letter-spacing: -0.02em;
  margin-bottom: 1.5rem;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 0 80px rgba(139, 92, 246, 0.2);
}

.heroSubtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.7);
  max-width: 650px;
  margin-bottom: 2.5rem;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: 0.01em;
}

/* Hero text container */
.heroText {
  padding-top: 2rem;
  max-width: 800px;
  position: relative;
  z-index: 10;
}

/* CTA buttons */
.heroCtaContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.9rem 1.75rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.ctaPrimary {
  background: linear-gradient(90deg, #8B5CF6 0%, #6366F1 100%);
  color: white;
  border: none;
  box-shadow: 0 10px 30px -10px rgba(139, 92, 246, 0.5);
}

.ctaPrimary:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(139, 92, 246, 0.6);
}

.ctaSecondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.ctaSecondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.ctaIcon {
  margin-left: 0.5rem;
}

/* Two-column layout for larger screens */
/*
.heroLayout {
  display: flex;
  flex-direction: column;
  align-items: center;
}
*/

.heroLayout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  gap: 3rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Animation for hero section */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.04;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.06;
  }
}

/* Hero animation and visual elements */
.heroAnimation {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  max-width: 600px;
  will-change: transform, opacity;
  transition: transform 0.15s ease-out, opacity 0.15s ease-out;
  opacity: 0.4; /* Start partially transparent */
}

.animationContainer {
  width: 100%;
  max-width: 600px;
  aspect-ratio: 1/1;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transform: scale(1.1);
  filter: drop-shadow(0 0 40px rgba(139, 92, 246, 0.3));
}

.animationGlow {
  position: absolute;
  width: 180%;
  height: 180%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, rgba(139, 92, 246, 0) 70%);
  pointer-events: none;
  z-index: -1;
  opacity: 0.8;
  animation: pulse 8s infinite ease-in-out;
}

/* Features Section */
.featuresSection {
  padding-top: 4rem;
  margin-top: -6rem;
  position: relative;
  z-index: 5;
}

/* Media Queries */
@media (min-width: 1024px) {
  .heroContent {
    padding-top: 5rem;
    padding-bottom: 0;
  }
  
  .heroLayout {
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 2rem;
  }
  
  .heroText {
    max-width: 850px;
    width: 100%;
    text-align: center;
    padding: 0 1rem;
    padding-top: 3rem;
  }
  
  .heroAnimation {
    max-width: 600px;
    margin-top: 0;
  }
  
  .heroTitle {
    font-size: 6rem;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 3.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .heroContent {
    padding-top: 4rem;
    padding-bottom: 0;
  }
  
  .heroCtaContainer {
    flex-direction: column;
    width: 100%;
  }
  
  .ctaButton {
    width: 100%;
  }
  
  .heroAnimation {
    max-width: 450px;
  }
}