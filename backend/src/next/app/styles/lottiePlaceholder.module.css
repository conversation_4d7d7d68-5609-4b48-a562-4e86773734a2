.container {
  position: relative;
  width: 100%;
  min-height: 320px;
  border-radius: 12px;
  border: 2px dashed rgba(156, 163, 175, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.02);
  transition: opacity 0.5s ease, transform 0.3s ease;
}

.container:hover {
  transform: translateY(-5px);
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 1.5rem;
}

.iconCircle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon {
  width: 30px;
  height: 30px;
}

.text {
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.25rem;
}

.id {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}

/* Animation for shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  pointer-events: none;
}
