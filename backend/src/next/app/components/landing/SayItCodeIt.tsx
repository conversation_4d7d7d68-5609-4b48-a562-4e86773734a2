'use client';

import styles from '../../styles/white-section.module.css';

export default function SayItCodeIt() {
  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>Say it. Code it.</h2>
            <p className={styles.description}>
              Write functions, build interfaces, or refactor — just with your voice.
            </p>
          </div>
          <div className={styles.animationContainer}>
            <iframe 
              src="https://lottie.host/embed/a0a65642-c0bf-4125-984f-24dae0dc2691/vNh4FTa1EL.lottie"
              width="600"
              height="600"
              style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
              title="Say It Code It Animation"
              allowFullScreen
            />
          </div>
        </div>
      </div>
    </section>
  );
} 