'use client';

import styles from '../../styles/purple-section.module.css';

export default function FocusOnEngineering() {
  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>Keep code, docs, and tests in perfect sync.</h2>
            <p className={styles.description}>
            Start from docs, slides, or tests. Our agent keeps everything aligned, automatically.


            </p>
          </div>
          <div className={styles.animationContainer}>
            <iframe 
              src="https://lottie.host/embed/0515d42b-b195-4104-aebf-5079b36c8f31/XyKfD5gX8a.lottie"
              width="600"
              height="600"
              style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
              title="Focus On Engineering Animation"
              allowFullScreen
            />
          </div>
        </div>
      </div>
    </section>
  );
} 