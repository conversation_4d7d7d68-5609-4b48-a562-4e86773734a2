'use client';

import styles from '../../styles/purple-section.module.css';

export default function DrawItWatchItCode() {
  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>Draw it. Watch it code itself.</h2>
            <p className={styles.description}>
              Sketch UI or logic — we'll turn it into production-ready code.
            </p>
          </div>
          <div className={styles.animationContainer}>
            <iframe 
              src="https://lottie.host/embed/6fc49365-a3c3-4b64-80bb-7b3ef4da45cc/MAdcSZkY2M.lottie"
              width="600"
              height="600"
              style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
              title="Draw It Watch It Code Animation"
              allowFullScreen
            />
          </div>
        </div>
      </div>
    </section>
  );
} 