'use client';

import styles from '../../styles/white-section.module.css';

export default function FromThoughtToCode() {
  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>From thought to code.</h2>
            <p className={styles.description}>
              Describe what you want — the system turns it into running software.
            </p>
          </div>
          <div className={styles.animationContainer}>
            <iframe 
              src="https://lottie.host/embed/326703bb-aee6-4f5e-908e-81f079986145/wToZcNKFS1.lottie"
              width="600"
              height="600"
              style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
              title="From Thought to Code Animation"
              allowFullScreen
            />
          </div>
        </div>
      </div>
    </section>
  );
} 