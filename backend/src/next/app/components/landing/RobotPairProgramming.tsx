'use client';

import styles from '../../styles/purple-section.module.css';

export default function RobotPairProgramming() {
  return (
    <section className={`${styles.section} ${styles.compactSection}`}>
      <div className={styles.container}>
        <div className={styles.content} style={{ flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
          <div className={styles.textContent} style={{ textAlign: 'center', maxWidth: '800px' }}>
            <h2 className={styles.title}>Introducing <PERSON><PERSON>, your new AI pair programmer.</h2>
            <p className={styles.description}>
              Work with a context-aware assistant that codes, thinks, and learns like a real teammate.
            </p>
          </div>
          <div className={styles.compactAnimationContainer}>
            <iframe 
              src="https://lottie.host/embed/c1824931-0616-431d-9bb8-5734dd7a4a76/WtVKdro5rm.lottie"
              width="800"
              height="400"
              style={{ display: 'block', margin: '0 auto', border: 'none', background: 'transparent' }}
              title="Robot Pair Programming Animation"
              allowFullScreen
            />
          </div>
        </div>
      </div>
    </section>
  );
} 