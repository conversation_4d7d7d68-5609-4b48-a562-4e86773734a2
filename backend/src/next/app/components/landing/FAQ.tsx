'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '../../styles/faq.module.css';

// FAQ data
const faqData = [
  {
    question: "How does KAPI support different developer motivations and goals?",
    answer: "KAPI features an adaptive project identity system that adjusts to your motivation—whether you're a Learner exploring concepts, a Contributor maintaining open source, or a Builder creating production applications. The IDE automatically configures its interface, features, and AI assistance based on your selected goal during project setup."
  },
  {
    question: "What makes KAPI's multi-modal interface unique?",
    answer: "KAPI enables development through voice, sketching, and typing—on any device from desktop to Apple Vision. Our voice-driven interface understands natural language intent, while sketch-to-code lets you design visually. This breaks traditional coding constraints and allows developers to work naturally across contexts and devices."
  },
  {
    question: "How does KAPI's backwards build approach benefit enterprises?",
    answer: "Our backwards build methodology ensures enterprise-grade quality by starting with documentation, slides, and tests before code. This approach guarantees alignment with business goals, maintains synchronization between requirements and implementation, and produces higher-quality, more maintainable software through our AI-driven quality assurance system."
  },
  {
    question: "What social and collaboration features does KAPI offer?",
    answer: "KAPI includes built-in pair programming with split-screen collaboration, a karma point system for helping other developers, ELO ratings for skill tracking, community achievement badges, and daily AI-driven coding challenges. Our social features create network effects that enhance learning and collaboration."
  },
  {
    question: "How does KAPI integrate with Modern AI Pro?",
    answer: "KAPI IDE serves as the natural next step for Modern AI Pro graduates. Workshop participants get exclusive IDE features, can continue projects started in workshops, access special templates and resources, and join our alumni network. The combination creates a complete journey from learning to professional-grade AI development."
  },
  {
    question: "What are the technical advantages over traditional IDEs?",
    answer: "KAPI offers pre-built templates reducing token usage by 60-80%, multi-model AI support with cost-conscious selection, mandatory AI-written unit tests, and continuous linting. Our system-first approach focuses on architecture and design patterns over syntax, dramatically increasing development efficiency and code quality."
  },
  {
    question: "How does KAPI ensure code quality and best practices?",
    answer: "KAPI enforces quality through automatic linting (ESLint, Ruff/mypy), mandatory unit tests, AI-driven code reviews, dead code elimination, and strong file modification rules. Our library management system provides updated copies of the top 100 libraries used by startups with continuous best practice advice."
  }
];

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const faqRef = useRef<HTMLDivElement>(null);

  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  useEffect(() => {
    // Add animation when FAQ comes into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const faqItems = document.querySelectorAll(`.${styles.faqItem}`);
            faqItems.forEach((item, index) => {
              item.classList.add(styles.animated, styles[`delay${index + 1}`]);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    if (faqRef.current) {
      observer.observe(faqRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <h2 className={styles.heading}>Frequently Asked Questions</h2>
        <p className={styles.subheading}>
          Find answers to common questions about KAPI's AI-native development environment, 
          multi-modal interface, and enterprise-grade backwards build approach.
        </p>

        <div className={styles.faqGrid}>
          <div className={styles.faqSidebar}>
            <h3 className={styles.faqTitle}>Still have questions?</h3>
            <p className={styles.faqDescription}>
              If you can't find the answer to your question here, feel free to contact our team. 
              We're always happy to help and provide more information about how KAPI can transform your development workflow.
            </p>
          </div>

          <div className={styles.faqList} ref={faqRef}>
            {faqData.map((faq, index) => (
              <div key={index} className={styles.faqItem}>
                <div 
                  className={styles.faqQuestion} 
                  onClick={() => toggleFaq(index)}
                >
                  {faq.question}
                  <svg 
                    className={`${styles.faqIcon} ${openIndex === index ? styles.faqIconOpen : ''}`} 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  >
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
                <div 
                  className={`${styles.faqAnswer} ${openIndex === index ? styles.faqAnswerOpen : ''}`}
                >
                  {faq.answer}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
