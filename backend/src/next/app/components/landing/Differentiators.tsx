'use client';

import { useEffect, useRef, useState } from 'react';
import styles from '../styles/differentiators.module.css';

export default function Differentiators() {
  const [countersInitialized, setCountersInitialized] = useState(false);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize counter animation when the stats section comes into view
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !countersInitialized) {
          // Start counter animations
          const counters = document.querySelectorAll(`.${styles.statValue}`);
          counters.forEach((counter) => {
            const target = parseInt(counter.getAttribute('data-count') || '0', 10);
            const duration = 2000; // Animation duration in milliseconds
            const step = Math.ceil(target / (duration / 16)); // Update every ~16ms for 60fps
            let current = 0;
            
            const updateCounter = () => {
              current += step;
              if (current >= target) {
                counter.textContent = target.toString();
              } else {
                counter.textContent = current.toString();
                requestAnimationFrame(updateCounter);
              }
            };
            
            updateCounter();
          });
          
          setCountersInitialized(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [countersInitialized]);

  return (
    <section className={styles.section}>
      <div className={styles.sectionBg}></div>
      <div className={styles.container}>
        <div className={styles.title}>Build with AI in a whole new way</div>
        <h2 className={styles.heading}>
          Welcome to <span className={styles.headingSpan}>Software Engineering 2.0</span>
        </h2>
        <p className={styles.description}>
          KAPI isn't just a tool—it's a fundamental shift in how software gets built. 
          We're ending the era of mediocre code and bringing true engineering to AI development.
        </p>

        {/* Stats Grid */}
        <div className={styles.statsGrid} ref={statsRef}>
          {/* Architecture First */}
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="3" y1="9" x2="21" y2="9"></line>
                <line x1="9" y1="21" x2="9" y2="9"></line>
              </svg>
            </div>
            <div className={styles.statValue} data-count="80">0
              <span className={styles.statUnit}>%</span>
            </div>
            <div className={styles.statText}>
              Reduction in development time from concept to deployment through architecture-first approach
            </div>
          </div>

          {/* Quality Renaissance */}
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
              </svg>
            </div>
            <div className={styles.statValue} data-count="5">0
              <span className={styles.statUnit}>x</span>
            </div>
            <div className={styles.statText}>
              Increase in code quality compared to traditional methods with backwards build approach
            </div>
          </div>

          {/* Enterprise Ready */}
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </div>
            <div className={styles.statValue} data-count="60">0
              <span className={styles.statUnit}>%</span>
            </div>
            <div className={styles.statText}>
              Reduction in token costs with intelligent templates and adaptive AI model selection
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className={styles.featuresGrid}>
          {/* Multi-Modal Revolution */}
          <div className={styles.featureCard}>
            <h3 className={styles.featureTitle}>
              <svg className={styles.featureIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
              Multi-Modal Revolution
            </h3>
            <p className={styles.featureDescription}>
              Code anywhere, any way. From desktop to mobile to Apple Vision - speak your ideas, sketch interfaces, and design flows without touching a keyboard.
            </p>
            <ul className={styles.featureList}>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Natural language programming across all devices</span>
              </li>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Sketch-to-code for visual component design</span>
              </li>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Seamless context switching between modalities</span>
              </li>
            </ul>
          </div>

          {/* Enterprise Transformation */}
          <div className={styles.featureCard}>
            <h3 className={styles.featureTitle}>
              <svg className={styles.featureIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              Enterprise Transformation
            </h3>
            <p className={styles.featureDescription}>
              Scale quality, not complexity. Begin with business cases and requirements - KAPI ensures every line of code aligns with business objectives.
            </p>
            <ul className={styles.featureList}>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Business-first development workflow</span>
              </li>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Automatic synchronization of all artifacts</span>
              </li>
              <li className={styles.featureListItem}>
                <svg className={styles.featureListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <span>Enterprise-grade quality management</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
