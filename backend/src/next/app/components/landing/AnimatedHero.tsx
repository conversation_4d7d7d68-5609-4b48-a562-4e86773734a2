'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '../../styles/landing.module.css';

interface AnimatedHeroProps {
  onJoinWaitlist?: () => void;
}

export default function AnimatedHero({ onJoinWaitlist }: AnimatedHeroProps) {
  const animationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!animationRef.current) return;

      const scrollY = window.scrollY;

      // Animation starts partially visible (100px from bottom), moves up on scroll
      const translateY = Math.max(0, 150 - scrollY * 0.8);
      const scale = Math.min(1.1, 1 + Math.max(0, (150 - scrollY)) * 0.0003);
      const opacity = Math.min(1, 0.4 + scrollY * 0.005);

      animationRef.current.style.transform = `translateY(${translateY}px) scale(${scale})`;
      animationRef.current.style.opacity = opacity.toString();
    };

    // Initial position
    handleScroll();

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section className={styles.heroSection}>
      <div className={styles.heroContent}>
        <div className={styles.heroLayout}>
          <div className={styles.heroText}>
            <div className={styles.heroBadge}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                <path d="M2 17l10 5 10-5"></path>
                <path d="M2 12l10 5 10-5"></path>
              </svg>
              <span>Build serious games, mobile apps, web, enterprise software, and more</span>
            </div>

            <h1 className={styles.heroTitle}>Vibe coding that just works!</h1>
            <p className={styles.heroSubtitle}>
              Build real software using natural input — voice, sketches, or text. AI handles the rest.
            </p>

            <div className={styles.heroCtaContainer}>
              <button 
                onClick={onJoinWaitlist}
                className={`${styles.ctaButton} ${styles.ctaPrimary}`}
              >
                Join Waitlist
                <svg className={styles.ctaIcon} width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <Link href="/benefits" className={`${styles.ctaButton} ${styles.ctaSecondary}`}>
                Learn More
              </Link>
            </div>
          </div>

          <div className={styles.heroAnimation} ref={animationRef}>
            <div className={styles.animationContainer}>
              <div className={styles.animationGlow}></div>
              <iframe 
                src="https://lottie.host/embed/4711d258-b3c9-4795-ad87-7c1f4a709a97/VK2hu4bRQw.lottie"
                style={{ width: '100%', height: '100%', border: 'none' }}
                title="Voice Coding Animation"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}