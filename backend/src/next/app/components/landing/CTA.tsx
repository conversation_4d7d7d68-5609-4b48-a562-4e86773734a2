'use client';

import Link from 'next/link';
import styles from '../../styles/cta.module.css';

interface CTAProps {
  onJoinWaitlist?: () => void;
}

export default function CTA({ onJoinWaitlist }: CTAProps) {
  return (
    <section className={styles.section}>
      <div className={styles.sectionBg}></div>
      <div 
        className={styles.decorCircle} 
        style={{ 
          width: '300px', 
          height: '300px', 
          top: '-150px', 
          right: '-100px',
          borderRadius: '50%',
          position: 'absolute',
          background: 'rgba(255, 255, 255, 0.1)',
          zIndex: 1
        }}
      ></div>
      <div 
        className={styles.decorCircle} 
        style={{ 
          width: '200px', 
          height: '200px', 
          bottom: '-100px', 
          left: '-50px',
          borderRadius: '50%',
          position: 'absolute',
          background: 'rgba(255, 255, 255, 0.1)',
          zIndex: 1
        }}
      ></div>
      
      <div className={styles.container}>
        <h2 className={styles.heading}>Ready to Transform Your Development Workflow?</h2>
        <p className={styles.subheading}>
          Join thousands of serious developers who are already building the future with our 
          Software Engineering 2.0 ecosystem. Start your journey today.
        </p>
        
        <div className={styles.ctaButtons}>
          <button onClick={onJoinWaitlist} className={styles.ctaPrimary}>
            Join Waitlist
            <svg className={styles.ctaIcon} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </button>
          <Link href="/benefits" className={styles.ctaSecondary}>
            Learn More
          </Link>
        </div>
      </div>
    </section>
  );
}
