'use client';

import FAQ from './landing/FAQ';
import CTA from './landing/CTA';
import TheNeed from './landing/TheNeed';
import RobotPairProgramming from './landing/RobotPairProgramming';
import FromThoughtToCode from './landing/FromThoughtToCode';
import DrawItWatchItCode from './landing/DrawItWatchItCode';
import SayItCodeIt from './landing/SayItCodeIt';
import FocusOnEngineering from './landing/FocusOnEngineering';
import DebugTheVibes from './landing/DebugTheVibes';
import SocialAI from './landing/SocialAI';
import FunFirePower from './landing/FunFirePower';
import EverythingInSync from './landing/EverythingInSync';
import AnimatedHero from './landing/AnimatedHero';

export default function HomePageClient() {
  const openWaitlistModal = () => {
    window.dispatchEvent(new Event('openWaitlistModal'));
  };

  return (
    <>
      <main className="flex flex-col min-h-screen">
        {/* Modern Hero Section */}
        <AnimatedHero onJoinWaitlist={openWaitlistModal} />

        <TheNeed />
        <RobotPairProgramming />
        <FromThoughtToCode />
        <DrawItWatchItCode />
        <SayItCodeIt />
        <FocusOnEngineering />
        <DebugTheVibes />
        <SocialAI />
        <FunFirePower />
        <EverythingInSync />

        {/* FAQ Section */}
        <FAQ />

        {/* Call to Action */}
        <CTA onJoinWaitlist={openWaitlistModal} />
      </main>
    </>
  );
}