'use client';

import { useState, useEffect } from 'react';
import styles from '../styles/lottiePlaceholder.module.css';

type LottiePlaceholderProps = {
  id: string;
  width?: string;
  height?: string;
};

const colors = {
  'ai-pair-programmer': '#8B5CF6', // <PERSON>
  'idea-to-app': '#EC4899', // <PERSON>
  'voice-coding': '#F59E0B', // <PERSON>
  'sketching-code': '#10B981', // Emerald
  'human-ai-collaboration': '#3B82F6', // Blue
  'vibe-debug': '#EF4444', // Red
  'backwards-build': '#8B5CF6', // Purple
  'enterprise-grade': '#6366F1', // Indigo
  'flow-state': '#14B8A6', // Teal
  'default': '#9CA3AF', // Gray
};

export default function LottiePlaceholder({ id, width = '100%', height = '320px' }: LottiePlaceholderProps) {
  const [opacity, setOpacity] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setOpacity(1);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const getColor = () => {
    return colors[id as keyof typeof colors] || colors.default;
  };

  return (
    <div 
      className={styles.container}
      style={{ 
        width, 
        height,
        opacity,
        backgroundColor: `${getColor()}10`, // 10% opacity version of the color
        borderColor: `${getColor()}30`, // 30% opacity version of the color
      }}
    >
      <div className={styles.content}>
        <div 
          className={styles.iconCircle}
          style={{ backgroundColor: `${getColor()}20` }} // 20% opacity version of the color
        >
          <svg 
            className={styles.icon} 
            style={{ color: getColor() }}
            xmlns="http://www.w3.org/2000/svg" 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M7 4v16M17 4v16M3 8h18M3 16h18" />
          </svg>
        </div>
        <p className={styles.text}>Lottie Animation</p>
        <p className={styles.id}>{id}</p>
      </div>
    </div>
  );
}
