'use client';

import { useState, useEffect } from 'react';

interface PerformanceToggleProps {
  onToggle: (useOptimized: boolean) => void;
  initialValue?: boolean;
}

export default function PerformanceToggle({ onToggle, initialValue = true }: PerformanceToggleProps) {
  const [useOptimized, setUseOptimized] = useState(initialValue);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show toggle in development or when ?debug=true
    const isDev = process.env.NODE_ENV === 'development';
    const hasDebug = typeof window !== 'undefined' && 
      new URLSearchParams(window.location.search).has('debug');
    
    setIsVisible(isDev || hasDebug);
  }, []);

  const handleToggle = () => {
    const newValue = !useOptimized;
    setUseOptimized(newValue);
    onToggle(newValue);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-lg border p-3">
      <div className="flex items-center space-x-2 text-sm">
        <span className="text-gray-600">Performance Mode:</span>
        <button
          onClick={handleToggle}
          className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
            useOptimized 
              ? 'bg-green-100 text-green-800 border border-green-200' 
              : 'bg-gray-100 text-gray-800 border border-gray-200'
          }`}
        >
          {useOptimized ? '🚀 Optimized' : '🎬 Original'}
        </button>
      </div>
      
      {useOptimized && (
        <div className="mt-2 text-xs text-gray-500">
          <div>✓ Lazy loading enabled</div>
          <div>✓ Mobile optimizations</div>
          <div>✓ Staggered animations</div>
        </div>
      )}
    </div>
  );
}