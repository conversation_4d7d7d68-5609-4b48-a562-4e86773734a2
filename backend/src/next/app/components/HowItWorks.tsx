'use client';

import { useEffect, useRef } from 'react';
import styles from '../styles/howitworks.module.css';

export default function HowItWorks() {
  const stepsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add animation when steps come into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const steps = document.querySelectorAll(`.${styles.step}`);
            steps.forEach((step, index) => {
              step.classList.add(styles.animated, styles[`delay${index + 1}`]);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    if (stepsRef.current) {
      observer.observe(stepsRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section className={styles.section}>
      <div className={styles.sectionBg}></div>
      <div className={styles.container}>
        <h2 className={styles.heading}>How KAPI Works</h2>
        <p className={styles.subheading}>
          Our backwards build approach revolutionizes the development process, focusing on quality and alignment with business goals from the start.
        </p>

        <div className={styles.stepsContainer} ref={stepsRef}>
          <div className={styles.connector}></div>
          
          {/* Step 1: Documentation First */}
          <div className={styles.step}>
            <div className={styles.stepNumber}>1</div>
            <div className={styles.stepContent}>
              <h3 className={styles.stepTitle}>Documentation First</h3>
              <p className={styles.stepDescription}>
                Begin with clear documentation that outlines your project's goals, requirements, and architecture. 
                KAPI helps you create comprehensive documentation that serves as the foundation for your entire project.
              </p>
              <ul className={styles.stepList}>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Define project scope and requirements</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Create architecture diagrams and flowcharts</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Establish API contracts and data models</span>
                </li>
              </ul>
            </div>
            <div className={styles.stepImage}>
              <div className={styles.stepImageInner}>
                <svg className={styles.stepIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
            </div>
          </div>
          
          {/* Step 2: Test Creation */}
          <div className={styles.step}>
            <div className={styles.stepNumber}>2</div>
            <div className={styles.stepContent}>
              <h3 className={styles.stepTitle}>Test Creation</h3>
              <p className={styles.stepDescription}>
                Write comprehensive tests before implementing any code. This ensures your implementation 
                will meet all requirements and helps prevent bugs and regressions.
              </p>
              <ul className={styles.stepList}>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Define expected behavior with unit tests</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Create integration tests for component interactions</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Establish performance and security benchmarks</span>
                </li>
              </ul>
            </div>
            <div className={styles.stepImage}>
              <div className={styles.stepImageInner}>
                <svg className={styles.stepIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                </svg>
              </div>
            </div>
          </div>
          
          {/* Step 3: Implementation */}
          <div className={styles.step}>
            <div className={styles.stepNumber}>3</div>
            <div className={styles.stepContent}>
              <h3 className={styles.stepTitle}>Implementation</h3>
              <p className={styles.stepDescription}>
                With documentation and tests in place, implementation becomes focused and efficient. 
                KAPI's multi-modal interface allows you to code using voice, sketching, or typing across any device.
              </p>
              <ul className={styles.stepList}>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Use voice commands to generate code structures</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Sketch interfaces that convert to production code</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Leverage AI-powered code generation and optimization</span>
                </li>
              </ul>
            </div>
            <div className={styles.stepImage}>
              <div className={styles.stepImageInner}>
                <svg className={styles.stepIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="16 18 22 12 16 6"></polyline>
                  <polyline points="8 6 2 12 8 18"></polyline>
                </svg>
              </div>
            </div>
          </div>
          
          {/* Step 4: Continuous Quality */}
          <div className={styles.step}>
            <div className={styles.stepNumber}>4</div>
            <div className={styles.stepContent}>
              <h3 className={styles.stepTitle}>Continuous Quality</h3>
              <p className={styles.stepDescription}>
                KAPI maintains synchronization between documentation, tests, and code throughout the development process, 
                ensuring consistent quality and alignment with business objectives.
              </p>
              <ul className={styles.stepList}>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Automated linting and code quality checks</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>AI-driven code reviews and refactoring suggestions</span>
                </li>
                <li className={styles.stepListItem}>
                  <svg className={styles.stepListIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>Continuous synchronization of all project artifacts</span>
                </li>
              </ul>
            </div>
            <div className={styles.stepImage}>
              <div className={styles.stepImageInner}>
                <svg className={styles.stepIcon} xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
