'use client';

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';

// Only declare dotlottie-player since lottie-player is already declared elsewhere
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'dotlottie-player': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement> & {
        src?: string;
        background?: string;
        speed?: string | number;
        loop?: boolean;
        autoplay?: boolean;
        mode?: string;
        load?: (src: string) => void;
        animation?: string;
      }, HTMLElement>;
    }
  }
}

// This component tries multiple animation formats in sequence
export default function LottieWithFallbacks({
  lottieFile = 'voice-animation',
  fallbackIcon = 'voice-icon',
  cdnUrl
}: {
  lottieFile?: string;
  fallbackIcon?: string;
  cdnUrl?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationLoaded = useRef<boolean>(false);
  const [loadingFailed, setLoadingFailed] = useState<boolean>(false);

  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined' || animationLoaded.current) return;

    const loadAnimation = async () => {
      try {
        console.log(`Trying to load animation: ${lottieFile}`);

        // First try dotLottie player
        try {
          await loadDotLottiePlayer();
        } catch (dotLottieError) {
          console.warn("DotLottie loading failed, trying standard Lottie:", dotLottieError);

          // If dotLottie fails, try standard Lottie player
          try {
            await loadStandardLottiePlayer();
          } catch (lottieError) {
            console.error("Standard Lottie loading also failed:", lottieError);
            setLoadingFailed(true);
            showFallback();
          }
        }
      } catch (error) {
        console.error("All animation attempts failed:", error);
        setLoadingFailed(true);
        showFallback();
      }
    };

    const loadDotLottiePlayer = async () => {
      console.log(`Trying dotLottie player for: ${lottieFile}`);

      // Dynamically import the dotlottie player component
      await import('@dotlottie/player-component');
      console.log("DotLottie module loaded");

      if (containerRef.current) {
        // Create and add element
        const player = document.createElement('dotlottie-player') as HTMLElement & {
          load: (src: string) => void;
        };

        // Use CDN URL if provided, otherwise use local file
        const animationSrc = cdnUrl || `/animations/${encodeURIComponent(lottieFile)}.lottie`;
        console.log("Loading DotLottie animation from:", animationSrc);

        // Configure it
        player.setAttribute('background', 'transparent');
        player.setAttribute('speed', '1');
        player.setAttribute('style', 'width: 100%; height: 100%');
        player.setAttribute('loop', '');
        player.setAttribute('autoplay', '');
        player.setAttribute('mode', 'normal');

        // For problematic animations, try to set the animation name explicitly
        const problematicAnimations = [
          'Man with Creative Web Design',
          'Man doing Coding on Phone',
          'Developer doing Bug Finding'
        ];

        if (problematicAnimations.includes(lottieFile)) {
          // Try different animation names
          const possibleAnimationNames = ['main', 'animation', lottieFile, 'default'];
          const animName = possibleAnimationNames[0]; // Start with first one
          player.setAttribute('animation', animName);
          console.log(`Setting explicit animation name "${animName}" for problematic file: ${lottieFile}`);
        }

        // Add to DOM first
        containerRef.current.appendChild(player);

        // Create promise to track load/error
        return new Promise((resolve, reject) => {
          // Set up event listeners before setting src
          player.addEventListener('ready', () => {
            console.log("dotLottie player ready!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('load', () => {
            console.log("dotLottie player loaded animation!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('play', () => {
            console.log("dotLottie player playing!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('error', (e) => {
            console.error("dotLottie player error:", e);
            console.error("Failed to load animation from:", animationSrc);

            // Try different animation names for problematic files
            if (problematicAnimations.includes(lottieFile)) {
              const possibleAnimationNames = ['main', 'animation', lottieFile, 'default'];

              // Try each animation name
              let attemptCount = 0;
              const tryNextAnimation = () => {
                attemptCount++;
                if (attemptCount < possibleAnimationNames.length) {
                  const nextAnimName = possibleAnimationNames[attemptCount];
                  console.log(`Trying animation name: "${nextAnimName}"`);
                  player.setAttribute('animation', nextAnimName);

                  // Give it a moment to try the new animation name
                  setTimeout(() => {
                    if (!animationLoaded.current) {
                      tryNextAnimation();
                    }
                  }, 500);
                } else {
                  // We've tried all animation names, give up on dotLottie
                  player.remove();
                  reject(e);
                }
              };

              // Start trying different animation names
              setTimeout(tryNextAnimation, 500);
            } else {
              // For non-problematic files, just reject immediately
              setTimeout(() => {
                if (!animationLoaded.current) {
                  player.remove();
                  reject(e);
                }
              }, 1000);
            }
          });

          // Set src after a small delay to ensure player is ready
          setTimeout(() => {
            try {
              // Try using the src attribute first
              player.setAttribute('src', animationSrc);
              console.log("Animation src set for:", animationSrc);

              // As a backup, also try the load method after a short delay
              setTimeout(() => {
                if (!animationLoaded.current) {
                  try {
                    player.load(animationSrc);
                    console.log("Animation load requested for:", animationSrc);
                  } catch (loadError) {
                    console.error("Error calling load():", loadError);
                  }
                }
              }, 300);
            } catch (error) {
              console.error("Error setting src attribute:", error);
              reject(error);
            }
          }, 100);

          // Safety timeout
          setTimeout(() => {
            if (!animationLoaded.current) {
              console.warn("dotLottie player timeout");
              player.remove();
              reject(new Error("Timeout loading dotLottie"));
            }
          }, 4000);
        });
      }

      throw new Error("Container not available");
    };

    const loadStandardLottiePlayer = async () => {
      console.log(`Trying standard Lottie player for: ${lottieFile}`);

      // Dynamically import the lottie-player component
      await import('@lottiefiles/lottie-player');
      console.log("Standard Lottie module loaded");

      if (containerRef.current) {
        // Create and add element
        const player = document.createElement('lottie-player') as HTMLElement;

        // Encode the filename to handle spaces
        const encodedFilename = encodeURIComponent(lottieFile);
        // Try the JSON format instead of .lottie
        const jsonPath = `/animations/${encodedFilename}.json`;
        console.log("Loading standard Lottie animation from:", jsonPath);

        // Configure it
        player.setAttribute('background', 'transparent');
        player.setAttribute('speed', '1');
        player.setAttribute('style', 'width: 100%; height: 100%');
        player.setAttribute('loop', '');
        player.setAttribute('autoplay', '');
        player.setAttribute('mode', 'normal');

        // Add to DOM first
        containerRef.current.appendChild(player);

        // Create promise to track load/error
        return new Promise((resolve, reject) => {
          // Set up event listeners before setting src
          player.addEventListener('ready', () => {
            console.log("Lottie player ready!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('load', () => {
            console.log("Lottie player loaded animation!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('play', () => {
            console.log("Lottie player playing!");
            animationLoaded.current = true;
            resolve(true);
          });

          player.addEventListener('error', (e) => {
            console.error("Lottie player error:", e);
            player.remove();
            reject(e);
          });

          // Set src after a small delay to ensure player is ready
          setTimeout(() => {
            try {
              player.setAttribute('src', jsonPath);
              console.log("Animation src set for:", jsonPath);
            } catch (error) {
              console.error("Error setting src attribute:", error);
              reject(error);
            }
          }, 100);

          // Safety timeout
          setTimeout(() => {
            if (!animationLoaded.current) {
              console.warn("Lottie player timeout");
              player.remove();
              reject(new Error("Timeout loading standard Lottie"));
            }
          }, 3000);
        });
      }

      throw new Error("Container not available");
    };

    const showFallback = () => {
      console.log(`Showing fallback for: ${lottieFile}`);
      if (containerRef.current) {
        // First try to find the existing fallback
        let fallback = containerRef.current.querySelector('.lottie-fallback');

        // If no fallback exists, create one
        if (!fallback) {
          console.log("Creating fallback element");
          const fallbackDiv = document.createElement('div') as HTMLDivElement;
          fallbackDiv.className = 'lottie-fallback';
          fallbackDiv.style.width = '100%';
          fallbackDiv.style.height = '100%';
          fallbackDiv.style.display = 'flex';
          fallbackDiv.style.alignItems = 'center';
          fallbackDiv.style.justifyContent = 'center';

          // Create an image element for the fallback
          const img = document.createElement('img') as HTMLImageElement;
          img.src = `/${fallbackIcon}.svg`;
          img.alt = "Feature illustration";
          img.width = 400;
          img.height = 400;
          img.style.maxWidth = '100%';
          img.style.maxHeight = '100%';

          fallbackDiv.appendChild(img);
          containerRef.current.appendChild(fallbackDiv);
        } else {
          // Show the existing fallback
          fallback.classList.remove('hidden');
        }
      }
    };

    loadAnimation();

    // Cleanup
    return () => {
      if (containerRef.current) {
        const dotLottiePlayers = containerRef.current.querySelectorAll('dotlottie-player');
        dotLottiePlayers.forEach(player => player.remove());

        const lottiePlayers = containerRef.current.querySelectorAll('lottie-player');
        lottiePlayers.forEach(player => player.remove());
      }
    };
  }, [lottieFile, fallbackIcon, cdnUrl]);

  return (
    <div className="w-full h-full relative">
      <div
        ref={containerRef}
        className="w-full h-full flex items-center justify-center"
      >
        {/* Fallback SVG - will be shown if animation fails to load */}
        <div className={`lottie-fallback ${loadingFailed ? '' : 'hidden'}`}>
          <Image
            src={`/${fallbackIcon}.svg`}
            alt="Feature illustration"
            width={400}
            height={400}
            className="mx-auto"
            priority
          />
        </div>
      </div>
    </div>
  );
}
