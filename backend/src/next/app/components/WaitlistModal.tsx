'use client';

import { useState } from 'react';
import { Waitlist } from '@clerk/nextjs';
import { X, CheckCircle } from 'lucide-react';

interface WaitlistModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WaitlistModal({ isOpen, onClose }: WaitlistModalProps) {
  const [showSuccess, setShowSuccess] = useState(false);
  if (!isOpen) return null;

  // Success confirmation view
  if (showSuccess) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* Success Modal */}
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 border border-gray-100">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-50 transition-colors duration-200 ease-in-out"
            style={{
              color: 'rgb(108, 117, 125)'
            }}
          >
            <X size={20} />
          </button>
          
          {/* Success Content */}
          <div className="px-8 pt-12 pb-8 text-center">
            {/* Success Icon */}
            <div className="mx-auto mb-6 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle size={32} className="text-green-600" />
            </div>
            
            <h2 style={{ 
              fontFamily: "'Fraunces', Georgia, serif",
              fontSize: '1.875rem',
              fontWeight: '800',
              letterSpacing: '-0.03em',
              lineHeight: '1.15',
              color: 'rgb(33, 37, 41)',
              marginBottom: '1rem'
            }}>
              🎉 You're In!
            </h2>
            
            <p style={{
              fontFamily: "'Literata', -apple-system, BlinkMacSystemFont, sans-serif",
              fontSize: '1rem',
              lineHeight: '1.6',
              color: 'rgb(73, 80, 87)',
              letterSpacing: '-0.011em',
              marginBottom: '1.5rem'
            }}>
              <strong>Welcome to the KAPI waitlist!</strong><br/>
              We'll email you once you're approved for early access.
            </p>
            
            <div className="bg-purple-50 rounded-lg p-4 mb-6">
              <p style={{
                fontFamily: "'Literata', -apple-system, BlinkMacSystemFont, sans-serif",
                fontSize: '0.875rem',
                lineHeight: '1.5',
                color: 'rgb(63, 36, 132)',
                letterSpacing: '-0.011em',
                margin: '0'
              }}>
                💡 <strong>What's next?</strong><br/>
                • Check your email for confirmation<br/>
                • Follow us for updates<br/>
                • Get ready to revolutionize your coding workflow
              </p>
            </div>
            
            <button
              onClick={onClose}
              className="bg-[rgb(63,36,132)] hover:bg-[rgb(48,24,96)] text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 ease-in-out"
              style={{
                fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                fontSize: '15px',
                fontWeight: '500',
                letterSpacing: '-0.011em'
              }}
            >
              Got it!
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto border border-gray-100">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-50 transition-colors duration-200 ease-in-out"
          style={{
            color: 'rgb(108, 117, 125)'
          }}
        >
          <X size={20} />
        </button>
        
        {/* Content */}
        <div className="px-8 pt-8 pb-6">
          <div className="text-center mb-8">
            <h2 style={{ 
              fontFamily: "'Fraunces', Georgia, serif",
              fontSize: '1.875rem',
              fontWeight: '800',
              letterSpacing: '-0.03em',
              lineHeight: '1.15',
              color: 'rgb(33, 37, 41)',
              marginBottom: '1rem',
              marginTop: '0.5rem'
            }}>
              Join the Future of Coding
            </h2>
            <p style={{
              fontFamily: "'Literata', -apple-system, BlinkMacSystemFont, sans-serif",
              fontSize: '0.9375rem',
              lineHeight: '1.5',
              color: 'rgb(73, 80, 87)',
              letterSpacing: '-0.011em',
              maxWidth: '280px',
              margin: '0 auto'
            }}>
              Be among the first to experience voice-powered development, sketch-to-code, and AI agents.
            </p>
          </div>
          
          <Waitlist 
            appearance={{
              elements: {
                card: "shadow-none border-none p-0",
                header: "hidden",
                logoBox: "hidden",
                logoImage: "hidden",
                logoLink: "hidden",
                formButtonPrimary: `
                  bg-[rgb(63,36,132)] hover:bg-[rgb(48,24,96)] 
                  text-white px-6 py-3.5 rounded-lg font-medium w-full
                  transition-colors duration-200 ease-in-out
                  font-['Inter',-apple-system,BlinkMacSystemFont,sans-serif]
                  font-medium text-[15px] tracking-[-0.011em]
                  shadow-sm hover:shadow-md
                  border-0 outline-none focus:ring-2 focus:ring-[rgb(63,36,132)] focus:ring-opacity-20
                `,
                formFieldInput: `
                  border-2 border-gray-200 focus:border-[rgb(63,36,132)] focus:ring-0
                  rounded-lg px-4 py-3.5 w-full
                  font-['Literata',-apple-system,BlinkMacSystemFont,sans-serif]
                  text-[15px] leading-[1.5] tracking-[-0.011em]
                  text-[rgb(33,37,41)] placeholder-[rgb(108,117,125)]
                  transition-all duration-200 ease-in-out
                  outline-none shadow-sm
                `,
                formFieldLabel: "hidden",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                // Hide the sign in link by making it invisible
                footerActionLink: "hidden",
                footerActionText: "hidden",
                footerText: `
                  font-['Literata',-apple-system,BlinkMacSystemFont,sans-serif]
                  text-[13px] text-[rgb(108,117,125)] tracking-[-0.011em]
                  text-center mt-4
                `
              }
            }}
          />
          
          <div className="mt-6 text-center">
            <p style={{
              fontFamily: "'Literata', -apple-system, BlinkMacSystemFont, sans-serif",
              fontSize: '13px',
              color: 'rgb(108, 117, 125)',
              letterSpacing: '-0.011em',
              marginBottom: '0'
            }}>
              Join thousands of developers already on the waitlist
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}