'use client';

import { useEffect, useRef } from 'react';
import styles from '../styles/testimonials.module.css';

// Testimonial data
const testimonialData = [
  {
    quote: "KAPI has completely transformed how I approach software development. The backwards build approach ensures I create higher quality code with better documentation, and the multi-modal interface lets me work from anywhere.",
    name: "<PERSON>",
    role: "Senior Developer at TechInnovate",
    initial: "<PERSON>"
  },
  {
    quote: "As a startup founder, I need to move quickly without sacrificing quality. KAPI's voice-driven coding and adaptive project system have cut our development time by 70% while improving our code quality.",
    name: "<PERSON>",
    role: "Founder & CTO at DataFlow",
    initial: "MR"
  },
  {
    quote: "The integration between Modern AI Pro workshops and KAPI IDE creates a seamless learning-to-building experience. I've gone from AI novice to shipping production features in weeks.",
    name: "<PERSON><PERSON>",
    role: "Full-Stack Developer",
    initial: "<PERSON>"
  }
];

export default function Testimonials() {
  const testimonialsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add animation when testimonials come into view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const testimonialCards = document.querySelectorAll(`.${styles.testimonialCard}`);
            testimonialCards.forEach((card, index) => {
              card.classList.add(styles.animated, styles[`delay${index + 1}`]);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    if (testimonialsRef.current) {
      observer.observe(testimonialsRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section className={styles.section}>
      <div className={styles.sectionBg}></div>
      <div className={styles.container}>
        <h2 className={styles.heading}>What Developers Are Saying</h2>
        <p className={styles.subheading}>
          Hear from the developers and teams who are using KAPI to transform their development workflow 
          and build better software faster.
        </p>

        <div className={styles.testimonialsGrid} ref={testimonialsRef}>
          {testimonialData.map((testimonial, index) => (
            <div key={index} className={styles.testimonialCard}>
              <div className={styles.testimonialQuoteIcon}>
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>
              <p className={styles.testimonialQuote}>{testimonial.quote}</p>
              <div className={styles.testimonialAuthor}>
                <div className={styles.testimonialAvatar}>
                  {testimonial.initial}
                </div>
                <div className={styles.testimonialInfo}>
                  <div className={styles.testimonialName}>{testimonial.name}</div>
                  <div className={styles.testimonialRole}>{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
