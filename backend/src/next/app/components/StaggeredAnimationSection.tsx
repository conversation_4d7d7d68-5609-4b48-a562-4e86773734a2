'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useStaggeredAnimations } from '../hooks/useStaggeredAnimations';
import LazyLottie from './LazyLottie';

interface StaggeredAnimationSectionProps {
  children: ReactNode;
  animations: {
    src: string;
    fallbackSrc?: string;
    mobileStatic?: string;
    className?: string;
    width?: number;
    height?: number;
  }[];
  staggerDelay?: number;
  className?: string;
}

export default function StaggeredAnimationSection({
  children,
  animations,
  staggerDelay = 300,
  className = ''
}: StaggeredAnimationSectionProps) {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const {
    animations: animationStates,
    setAnimationVisible,
    setAnimationLoaded,
    canLoad,
    isVisible
  } = useStaggeredAnimations({
    totalAnimations: animations.length,
    staggerDelay: isMobile ? staggerDelay * 2 : staggerDelay, // Slower on mobile
    isMobile,
    enabled: true
  });

  return (
    <div className={className}>
      {children}
      
      {/* Render animations with staggered loading */}
      {animations.map((animation, index) => (
        <LazyAnimationWrapper
          key={index}
          index={index}
          animation={animation}
          canLoad={canLoad(index)}
          isVisible={isVisible(index)}
          isMobile={isMobile}
          onVisible={setAnimationVisible}
          onLoaded={setAnimationLoaded}
        />
      ))}
    </div>
  );
}

// Individual animation wrapper with intersection observer
function LazyAnimationWrapper({
  index,
  animation,
  canLoad,
  isVisible,
  isMobile,
  onVisible,
  onLoaded
}: {
  index: number;
  animation: StaggeredAnimationSectionProps['animations'][0];
  canLoad: boolean;
  isVisible: boolean;
  isMobile: boolean;
  onVisible: (index: number) => void;
  onLoaded: (index: number) => void;
}) {
  const [elementRef, setElementRef] = useState<HTMLDivElement | null>(null);

  // Intersection observer to detect when animation enters viewport
  useEffect(() => {
    if (!elementRef || isVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            onVisible(index);
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(elementRef);
    return () => observer.disconnect();
  }, [elementRef, isVisible, index, onVisible]);

  // Show placeholder until animation can load
  if (!canLoad) {
    return (
      <div 
        ref={setElementRef}
        className={`${animation.className} bg-gray-100 rounded-lg flex items-center justify-center`}
        style={{ 
          width: animation.width || 400, 
          height: animation.height || 400,
          aspectRatio: `${animation.width || 400}/${animation.height || 400}`
        }}
      >
        <div className="text-gray-400 text-sm">
          {isMobile ? '📱' : '⏳'} Loading...
        </div>
      </div>
    );
  }

  // Mobile optimization: show static content
  if (isMobile && animation.mobileStatic) {
    return (
      <div ref={setElementRef} className={animation.className}>
        <img
          src={animation.mobileStatic}
          alt="Animation preview"
          width={animation.width}
          height={animation.height}
          className="w-full h-auto"
          onLoad={() => onLoaded(index)}
        />
      </div>
    );
  }

  // Load full animation with LazyLottie
  return (
    <div ref={setElementRef}>
      <LazyLottie
        src={animation.src}
        fallbackSrc={animation.fallbackSrc}
        mobileStatic={animation.mobileStatic}
        width={animation.width}
        height={animation.height}
        className={animation.className}
        priority={false}
        threshold={0.1}
      />
    </div>
  );
}