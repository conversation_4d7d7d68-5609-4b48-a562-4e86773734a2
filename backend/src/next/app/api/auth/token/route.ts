import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if the user is authenticated
    if (!session || !session.userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the session token from Clerk - you might need to use session.getToken()
    // depending on your version
    const token = session.getToken ? await session.getToken() : session.userId;

    return NextResponse.json({ token });
  } catch (error) {
    console.error('Error getting auth token:', error);
    return NextResponse.json(
      { error: 'Failed to get authentication token' },
      { status: 500 }
    );
  }
}