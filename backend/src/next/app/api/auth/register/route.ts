import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    // Ensure user is authenticated
    if (!session || !session.userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userId = session.userId;
    
    // Here you would typically make a call to your backend to register the user
    // For example:
    // const response = await fetch('https://your-api.example.com/register', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${process.env.API_SECRET_KEY}`
    //   },
    //   body: JSON.stringify({ clerkId: userId })
    // });
    
    // For now, we'll just return a success message
    console.log(`User registered with Clerk ID: ${userId}`);
    
    return NextResponse.json({ 
      success: true, 
      message: 'User registered successfully',
      userId 
    });
  } catch (error) {
    console.error('Error registering user:', error);
    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    );
  }
}