import type { Metadata } from 'next';
import WaitlistClient from './WaitlistClient';

export const metadata: Metadata = {
  title: 'Join the Waitlist - Early Access to KAPI | AI Development Platform',
  description: 'Be among the first to experience the future of software development. Join the KAPI waitlist for early access to voice coding, sketch-to-code, and AI-powered development tools.',
  keywords: [
    'kapi waitlist',
    'early access',
    'AI development platform',
    'voice coding beta',
    'software development tools',
    'multimodal programming',
    'developer preview',
    'beta access',
    'AI coding assistant',
    'development platform preview'
  ],
  openGraph: {
    title: 'Join the Waitlist - Early Access to KAPI',
    description: 'Be among the first to experience the future of software development with voice coding, sketch-to-code, and AI agents.',
    url: 'https://kapihq.com/waitlist',
    siteName: 'KAPI',
    images: [
      {
        url: '/og-preview.png',
        width: 1200,
        height: 630,
        alt: 'Join KAPI Waitlist - Early Access',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Join the Waitlist - Early Access to KAPI',
    description: 'Be among the first to experience the future of software development. Limited early access spots available.',
    images: ['/og-preview.png'],
    creator: '@kapihq',
    site: '@kapihq',
  },
  alternates: {
    canonical: '/waitlist',
  },
};

export default function WaitlistPage() {
  return <WaitlistClient />;
}