'use client';

import { useState } from 'react';
import { Waitlist } from '@clerk/nextjs';
import { CheckCircle } from 'lucide-react';

export default function WaitlistClient() {
  const [showSuccess, setShowSuccess] = useState(false);

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            {/* Success Icon */}
            <div className="mx-auto mb-6 w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle size={40} className="text-green-600" />
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              🎉 You're In!
            </h1>
            
            <p className="text-lg text-gray-700 mb-6">
              <strong>Welcome to the KAPI waitlist!</strong><br/>
              We'll email you once you're approved for early access.
            </p>
            
            <div className="bg-purple-50 rounded-xl p-6 mb-6">
              <p className="text-purple-800 text-sm leading-relaxed">
                💡 <strong>What's next?</strong><br/>
                • Check your email for confirmation<br/>
                • Follow us for updates<br/>
                • Get ready to revolutionize your coding workflow
              </p>
            </div>
            
            <button
              onClick={() => window.location.href = '/'}
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 ease-in-out"
            >
              Back to Home
            </button>
            
            <div className="mt-6">
              <p className="text-sm text-gray-500">
                🚀 Join thousands of developers already on the waitlist
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Join the Future of Coding
          </h1>
          <p className="text-gray-600">
            Be among the first to experience voice-powered development, sketch-to-code, and AI agents.
          </p>
        </div>
        
        <Waitlist 
          appearance={{
            elements: {
              // Hide the sign in link by making it invisible
              footerActionLink: "hidden",
              footerActionText: "hidden",
            }
          }}
        />
        
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Join thousands of developers already on the waitlist
          </p>
        </div>
      </div>
    </div>
  );
}