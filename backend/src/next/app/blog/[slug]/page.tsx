import { getPostBySlug, getAllPostSlugs } from '@/lib/blog';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Metadata } from 'next';
import markdownToHtml from '@/lib/markdownToHtml';

export async function generateMetadata(props: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const post = getPostBySlug((await props.params).slug);
  
  if (!post) {
    return { title: 'Post Not Found | KAPI Blog' };
  }
  
  return {
    title: `${post.frontmatter.title} | KAPI Blog`,
    description: post.frontmatter.description,
  };
}

export function generateStaticParams() {
  const slugs = getAllPostSlugs();
  return slugs.map(slug => ({ slug }));
}

export default async function Page(
  props: {
    params: Promise<{ slug: string }>;
  }
) {
  const post = getPostBySlug((await props.params).slug);
  
  if (!post) {
    notFound();
  }
  
  const { frontmatter, content } = post;

  // To prevent duplicate title in the post
  let htmlContent = markdownToHtml(content);
  htmlContent = htmlContent.replace(/<h1[^>]*>[\s\S]*?<\/h1>/i, '');
  
  return (
    <div className="blog-container">
      <Link href="/blog" className="blog-back-link">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
        Back to all posts
      </Link>
      
      <header className="blog-header">
        <div className="blog-tags">
          {frontmatter.tags && frontmatter.tags.map((tag: string) => (
            <Link key={tag} href={`/blog/tag/${tag}`} className="blog-tag">
              {tag}
            </Link>
          ))}
        </div>
        
        <h1 className="blog-title">{frontmatter.title}</h1>
        
        {frontmatter.description && (
          <p className="blog-subtitle">{frontmatter.description}</p>
        )}
        
        <div className="blog-meta">
          {frontmatter.author && frontmatter.author.avatar && (
            <Image
              src={frontmatter.author.avatar}
              alt={frontmatter.author.name || 'Author'}
              width={60}
              height={60}
              className="blog-author-image"
            />
          )}
          <div>
            <div className="blog-author-name">{frontmatter.author?.name}</div>
            <div className="blog-author-title">{frontmatter.author?.role}</div>
          </div>
          <div className="ml-auto flex items-center">
            <time className="blog-date">
              {frontmatter.date} 
              {frontmatter.readingTime && ` • ${frontmatter.readingTime}`}
            </time>
          </div>
        </div>
      </header>
      
      {!frontmatter.hideImageInPost && frontmatter.coverImage && (
        <div className="relative w-full aspect-video mb-12 rounded-lg overflow-hidden">
          <Image
            src={frontmatter.coverImage}
            alt={frontmatter.title}
            fill
            className="object-cover"
            priority
          />
        </div>
      )}
      
      <div 
        className="blog-content" 
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
}
