import { getAllPosts } from '@/lib/blog';
import PostCard from '@/components/blog/PostCard';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Ka<PERSON> Blog - AI Development Insights & Software Engineering 2.0',
  description: 'Expert insights on AI-native development, voice programming, multimodal coding, and the future of software engineering. Learn from industry leaders building tomorrow\'s development tools.',
  keywords: [
    'AI development blog',
    'software engineering 2.0',
    'voice programming insights',
    'multimodal coding tutorials',
    'AI-native development',
    'development productivity',
    'coding best practices',
    'software engineering trends',
    'AI programming tools',
    'developer insights',
    'coding innovation',
    'programming future'
  ],
  openGraph: {
    title: 'Kapi Blog - AI Development Insights & Software Engineering 2.0',
    description: 'Expert insights on AI-native development, voice programming, multimodal coding, and the future of software engineering.',
    url: 'https://kapihq.com/blog',
    siteName: 'Kapi',
    images: [
      {
        url: '/blog-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Kapi Blog - AI Development Insights',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kapi Blog - AI Development Insights & Software Engineering 2.0',
    description: 'Expert insights on AI-native development, voice programming, and the future of software engineering.',
    images: ['/blog-twitter.jpg'],
  },
  alternates: {
    canonical: '/blog',
  },
};

export default function BlogPage() {
  const posts = getAllPosts();

  // JSON-LD structured data for SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "Kapi Blog",
    "description": "Expert insights on AI-native development, voice programming, multimodal coding, and the future of software engineering.",
    "url": "https://kapihq.com/blog",
    "publisher": {
      "@type": "Organization",
      "name": "Kapi",
      "url": "https://kapihq.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://kapihq.com/kapi-logo.png"
      }
    },
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": posts.slice(0, 10).map((post, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "BlogPosting",
          "headline": post.frontmatter.title,
          "description": post.frontmatter.description,
          "url": `https://kapihq.com/blog/${post.slug}`,
          "datePublished": post.frontmatter.date,
          "author": {
            "@type": "Person",
            "name": post.frontmatter.author.name
          }
        }
      }))
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="container mx-auto px-4 py-12 max-w-7xl">
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold mb-4" style={{ color: 'rgb(63, 36, 132)' }}>KAPI Blog</h1>
        <h2 className="text-xl text-gray-600">
          Insights on bringing software discipline to AI development
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {posts.map(post => (
          <PostCard key={post.slug} post={post} />
        ))}
      </div>
    </div>
    </>
  );
}
