import { getAllPosts } from '@/lib/blog';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import type { Metadata } from 'next';

export async function generateMetadata(props: { params: Promise<{ tag: string }> }): Promise<Metadata> {
  const params = await props.params;
  const posts = getAllPosts();
  const tagPosts = posts.filter(post => post.frontmatter.tags.includes(params.tag));

  if (tagPosts.length === 0) {
    return {
      title: 'Tag Not Found | KAPI Blog',
    };
  }

  return {
    title: `Posts tagged with "${params.tag}" | KAPI Blog`,
    description: `Browse all posts tagged with "${params.tag}" on the KAPI Blog.`,
  };
}

export async function generateStaticParams() {
  const posts = getAllPosts();
  const tags = new Set(posts.flatMap(post => post.frontmatter.tags));
  return Array.from(tags).map(tag => ({ tag }));
}

export default async function TagPage(props: { params: Promise<{ tag: string }> }) {
  const params = await props.params;
  const posts = getAllPosts();
  const tagPosts = posts.filter(post => post.frontmatter.tags.includes(params.tag));

  if (tagPosts.length === 0) {
    notFound();
  }

  return (
    <div className="blog-container">
      <Link
        href="/blog"
        className="blog-back-link"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
        Back to all posts
      </Link>

      <h1 className="blog-title mb-12">
        Posts tagged with "{params.tag}"
      </h1>

      <div className="space-y-12">
        {tagPosts.map(post => (
          <article key={post.slug} className="border-b border-gray-200 pb-12">
            <Link href={`/blog/${post.slug}`}>
              <h2 className="text-2xl font-bold mb-4 hover:text-purple-700 transition-colors">
                {post.frontmatter.title}
              </h2>
            </Link>

            <p className="text-gray-600 mb-4">
              {post.frontmatter.description}
            </p>

            <div className="flex items-center text-sm text-gray-500">
              <span>{post.frontmatter.date}</span>
              <span className="mx-2">•</span>
              <span>{post.frontmatter.readingTime}</span>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
}
