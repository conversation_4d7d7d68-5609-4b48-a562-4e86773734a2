# KAPI Blog Implementation

A simple blog system for KAPI built with Next.js and Markdown.

## Features

- Markdown-based content
- Tag filtering
- Responsive design
- Syntax highlighting for code blocks
- Simple and maintainable architecture

## Structure

- `/app/blog/` - Blog pages
- `/components/blog/` - Blog-specific components
- `/content/blog/` - Markdown files for blog posts
- `/lib/` - Utility functions for blog functionality
- `/public/` - Static assets (images, avatars)

## How to Add a New Post

1. Create a new Markdown file in `/content/blog/` with the following frontmatter:

```md
---
id: "unique-post-id"
title: "Your Post Title"
description: "A brief description of your post."
date: "Month Day, Year"
author:
  name: "Author Name"
  avatar: "/path-to-avatar.jpg"
  role: "Author Role"
coverImage: "/path-to-cover-image.jpg"
hideImageInPost: false
tags:
  - "Tag One"
  - "Tag Two"
readingTime: "X min read"
---

# Your Content Starts Here

Write your post content in Markdown format.
```

2. Add any images to the `/public/` directory
3. The post will automatically appear in the blog listing

## Dependencies

- `gray-matter` - For parsing frontmatter
- `marked` - For converting Markdown to HTML
- `highlight.js` - For code syntax highlighting
- `@tailwindcss/typography` - For styling Markdown content

## Styling

Blog styling uses Tailwind CSS with the Typography plugin for content formatting.
