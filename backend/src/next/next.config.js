const path = require('path');

/** @type {import('next').NextConfig} */
require('dotenv').config({ path: '../../.env.local' });

const nextConfig = {
    reactStrictMode: true,
    output: 'standalone',
    // Ensure static files are properly served
    assetPrefix: process.env.NODE_ENV === 'production' ? undefined : '',
    // Explicitly pass environment variables to Next.js
    env: {
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
      CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
      NEXT_PUBLIC_CLERK_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '/signin',
      NEXT_PUBLIC_CLERK_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '/signup',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '/kapi',
      NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '/kapi',
    },
    // Add headers for security including CSP for Clerk
    async headers() {
      return [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'Content-Security-Policy',
              value: [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.clerk.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com blob:",
                "worker-src 'self' blob: https://js.clerk.com",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
                "img-src 'self' data: https: blob:",
                "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com data:",
                "connect-src 'self' https://api.clerk.com https://clerk.com https://js.clerk.com wss://clerk.com",
                "frame-src 'self' https://js.clerk.com https://lottie.host",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'"
              ].join('; ')
            },
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff'
            },
            {
              key: 'X-Frame-Options',
              value: 'DENY'
            },
            {
              key: 'X-XSS-Protection',
              value: '1; mode=block'
            },
            {
              key: 'Referrer-Policy',
              value: 'strict-origin-when-cross-origin'
            },
            {
              key: 'Permissions-Policy',
              value: 'camera=(), microphone=(), geolocation=(), payment=()'
            }
          ]
        }
      ];
    },
    // This enables relative API URLs to work properly
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:3000/api/:path*',
        },
        {
          source: '/health',
          destination: 'http://localhost:3000/health',
        },
      ];
    },
    webpack: (config, { isServer }) => {
      config.resolve.alias['@'] = __dirname; // project root
      
      // Handle potential ESM issues
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
          net: false,
          tls: false,
        };
      }
      
      return config;
    },
    transpilePackages: ['@lottiefiles/lottie-player', '@dotlottie/player-component', 'lottie-web'],
    experimental: {
      esmExternals: 'loose'
    },
  };
  
  module.exports = nextConfig;