#!/bin/bash

# Test Python Documentation Generation Script
# This script tests the Python documentation generation directly

set -e

echo "🐍 Testing Python Documentation Generation Script..."
echo ""

# Configuration
BACKEND_DIR="/home/<USER>/Desktop/kapi-main/kapi/backend"
SCRIPT_PATH="$BACKEND_DIR/scripts/generate_docs.py"
TEST_PROJECT_DIR="$BACKEND_DIR/test-project-python"
TEST_FILE="$TEST_PROJECT_DIR/sample.py"

# Create test directory
echo "📁 Creating test project directory..."
mkdir -p "$TEST_PROJECT_DIR"

# Create a sample Python file
echo "📄 Creating sample Python file..."
cat > "$TEST_FILE" << 'EOF'
"""
Sample Python module for testing documentation generation.

This module demonstrates various Python constructs that should be documented.
"""

import os
import json
from typing import List, Dict, Optional


class DataProcessor:
    """
    A class for processing and managing data.

    This class provides methods for data validation, transformation,
    and storage operations.
    """

    def __init__(self, config: Dict[str, str]):
        """
        Initialize the DataProcessor.

        Args:
            config: Configuration dictionary with processing parameters
        """
        self.config = config
        self.data_cache = {}

    def validate_data(self, data: List[Dict]) -> bool:
        """
        Validate input data structure.

        Args:
            data: List of dictionaries to validate

        Returns:
            True if data is valid, False otherwise

        Raises:
            ValueError: If data format is invalid
        """
        if not isinstance(data, list):
            raise ValueError("Data must be a list")

        for item in data:
            if not isinstance(item, dict):
                return False
            if 'id' not in item or 'value' not in item:
                return False

        return True

    def process_data(self, data: List[Dict]) -> List[Dict]:
        """
        Process and transform data.

        Args:
            data: Input data to process

        Returns:
            Processed data with transformations applied
        """
        if not self.validate_data(data):
            raise ValueError("Invalid data format")

        processed = []
        for item in data:
            processed_item = {
                'id': item['id'],
                'value': item['value'] * 2,  # Example transformation
                'processed': True
            }
            processed.append(processed_item)

        return processed

    def save_data(self, data: List[Dict], filename: str) -> bool:
        """
        Save processed data to file.

        Args:
            data: Data to save
            filename: Output filename

        Returns:
            True if save successful, False otherwise
        """
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving data: {e}")
            return False


def utility_function(input_string: str) -> str:
    """
    A utility function for string processing.

    Args:
        input_string: String to process

    Returns:
        Processed string
    """
    return input_string.strip().lower()


def main():
    """Main function demonstrating the DataProcessor usage."""
    config = {'mode': 'production', 'debug': 'false'}
    processor = DataProcessor(config)

    sample_data = [
        {'id': 1, 'value': 10},
        {'id': 2, 'value': 20},
        {'id': 3, 'value': 30}
    ]

    processed = processor.process_data(sample_data)
    processor.save_data(processed, 'output.json')
    print("Data processing complete!")


if __name__ == "__main__":
    main()
EOF

echo "✅ Sample Python file created: $TEST_FILE"

# Test 1: Check if the Python script exists
echo ""
echo "🔍 Checking if documentation generation script exists..."
if [ -f "$SCRIPT_PATH" ]; then
    echo "✅ Documentation script found: $SCRIPT_PATH"
else
    echo "❌ Documentation script not found: $SCRIPT_PATH"
    echo "💡 Make sure the backend is properly set up"
    exit 1
fi

# Test 2: Check Python dependencies
echo ""
echo "🔍 Checking Python environment..."
cd "$BACKEND_DIR"

# Create and use virtual environment
VENV_DIR="$BACKEND_DIR/venv"
echo "🐍 Setting up Python virtual environment..."

if [ ! -d "$VENV_DIR" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv "$VENV_DIR"
fi

echo "🔧 Activating virtual environment..."
source "$VENV_DIR/bin/activate"

echo "📦 Installing Python dependencies..."
pip install -r "$BASE_DIR/requirements.txt"
echo "✅ Dependencies installed"

# Check if required packages are available
echo "🔍 Checking required packages..."
python -c "import requests, openai, anthropic" 2>/dev/null && echo "✅ Required packages available" || echo "❌ Missing required packages"

# Test 3: Run documentation generation on the test file
echo ""
echo "📄 Generating documentation for test file..."
python "$SCRIPT_PATH" --file "$TEST_FILE"

# Test 4: Run documentation generation on the test directory
echo ""
echo "📁 Generating documentation for test directory..."
python "$SCRIPT_PATH" --dir "$TEST_PROJECT_DIR"

# Test 5: Check for generated documentation
echo ""
echo "🔍 Checking for generated documentation..."
DOCS_DIR="$TEST_PROJECT_DIR/docs/generated/files"

if [ -d "$DOCS_DIR" ]; then
    echo "✅ Documentation directory found: $DOCS_DIR"

    # List generated files
    echo "📋 Generated documentation files:"
    find "$DOCS_DIR" -type f | while read file; do
        echo "   - $(basename "$file")"

        # Show preview of JSON files
        if [[ "$file" == *.json ]]; then
            echo "     📄 Preview:"
            head -n 5 "$file" | sed 's/^/       /'
        fi
    done
else
    echo "ℹ️  No documentation directory found (may still be generating)"
fi

# Test 6: Check for markdown files
echo ""
echo "🔍 Checking for generated markdown files..."
find "$TEST_PROJECT_DIR" -name "*.md" -type f | while read file; do
    echo "✅ Found markdown file: $(basename "$file")"
    echo "📝 Preview:"
    head -n 10 "$file" | sed 's/^/   /'
    echo ""
done

# Cleanup
echo ""
echo "🧹 Cleaning up test files..."
rm -rf "$TEST_PROJECT_DIR"
echo "✅ Test files cleaned up"

echo ""
echo "🎉 Python documentation generation testing complete!"
echo ""
echo "💡 What was tested:"
echo "   ✅ Python script execution"
echo "   ✅ File-level documentation generation"
echo "   ✅ Directory-level documentation generation"
echo "   ✅ JSON and Markdown output generation"
echo ""
echo "💡 Next steps:"
echo "   1. Test with your actual project files"
echo "   2. Check the IDE integration"
echo "   3. Verify semantic search indexing"
