#!/usr/bin/env node

/**
 * Documentation Semantic Search Integration Test
 *
 * This script tests the complete documentation semantic search workflow
 * simulating real documentation indexing and search scenarios
 */

const { ChromaClient } = require('chromadb');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function testDocumentationSemanticSearch() {
  console.log('📚 Testing Documentation Semantic Search Integration...\n');

  try {
    // Initialize ChromaDB client
    const client = new ChromaClient({
      host: process.env.CHROMA_HOST || 'localhost',
      port: parseInt(process.env.CHROMA_PORT || '8000', 10)
    });

    console.log('✅ ChromaDB client initialized');

    // Use the actual collection name from environment
    const collectionName = process.env.CHROMA_COLLECTION || 'documentation_embeddings';
    console.log(`📁 Using collection: ${collectionName}`);

    let collection;
    try {
      collection = await client.getCollection({ name: collectionName });
      console.log('✅ Connected to existing collection');
    } catch (error) {
      console.log('📁 Creating new collection...');
      collection = await client.createCollection({ name: collectionName });
      console.log('✅ New collection created');
    }

    // Simulate real documentation content
    console.log('\n📄 Adding realistic documentation content...');
    const documentationSamples = [
      {
        id: 'auth_middleware_js',
        content: `
/**
 * Authentication Middleware
 *
 * This middleware handles JWT token validation and user authentication
 * for protected routes in the Express.js application.
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
const authMiddleware = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ error: 'Access denied. No token provided.' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ error: 'Invalid token.' });
  }
};
        `,
        metadata: {
          type: 'middleware',
          language: 'javascript',
          framework: 'express',
          category: 'authentication',
          file_path: '/src/middleware/auth.js',
          last_modified: '2025-07-15'
        }
      },
      {
        id: 'user_model_py',
        content: `
"""
User Model

This module defines the User model for the application database.
Includes user authentication, profile management, and relationship handling.

Attributes:
    id (int): Primary key
    username (str): Unique username
    email (str): User email address
    password_hash (str): Hashed password
    created_at (datetime): Account creation timestamp
    is_active (bool): Account status
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from werkzeug.security import generate_password_hash, check_password_hash

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
        `,
        metadata: {
          type: 'model',
          language: 'python',
          framework: 'sqlalchemy',
          category: 'database',
          file_path: '/models/user.py',
          last_modified: '2025-07-15'
        }
      },
      {
        id: 'responsive_css',
        content: `
/**
 * Responsive Layout Styles
 *
 * Mobile-first responsive design system with flexbox and grid layouts.
 * Includes breakpoints for mobile, tablet, and desktop viewports.
 */

/* Base mobile styles */
.container {
  width: 100%;
  padding: 0 16px;
  margin: 0 auto;
}

.flex-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

/* Tablet breakpoint */
@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 24px;
  }

  .flex-container {
    flex-direction: row;
    gap: 24px;
  }

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

/* Desktop breakpoint */
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
    padding: 0 32px;
  }

  .grid-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
}
        `,
        metadata: {
          type: 'styles',
          language: 'css',
          category: 'responsive-design',
          features: 'flexbox,grid,mobile-first',
          file_path: '/styles/responsive.css',
          last_modified: '2025-07-15'
        }
      },
      {
        id: 'react_component_tsx',
        content: `
/**
 * UserProfile Component
 *
 * React component for displaying and editing user profile information.
 * Includes form validation, image upload, and real-time updates.
 */

import React, { useState, useEffect } from 'react';
import { User, Camera, Save, X } from 'lucide-react';

interface UserProfileProps {
  userId: string;
  onUpdate?: (user: User) => void;
}

interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  bio?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId, onUpdate }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchUserProfile();
  }, [userId]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(\`/api/users/\${userId}\`);
      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    try {
      const response = await fetch(\`/api/users/\${userId}\`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(user)
      });

      if (response.ok) {
        setIsEditing(false);
        onUpdate?.(user);
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-profile">
      <div className="profile-header">
        <div className="avatar-container">
          {user.avatar ? (
            <img src={user.avatar} alt="Profile" className="avatar" />
          ) : (
            <User className="avatar-placeholder" />
          )}
          {isEditing && (
            <button className="avatar-upload">
              <Camera size={16} />
            </button>
          )}
        </div>

        <div className="profile-info">
          {isEditing ? (
            <input
              value={user.username}
              onChange={(e) => setUser({ ...user, username: e.target.value })}
              className="username-input"
            />
          ) : (
            <h2>{user.username}</h2>
          )}

          <p className="email">{user.email}</p>
        </div>
      </div>

      <div className="profile-actions">
        {isEditing ? (
          <>
            <button onClick={handleSave} className="save-btn">
              <Save size={16} /> Save
            </button>
            <button onClick={() => setIsEditing(false)} className="cancel-btn">
              <X size={16} /> Cancel
            </button>
          </>
        ) : (
          <button onClick={() => setIsEditing(true)} className="edit-btn">
            Edit Profile
          </button>
        )}
      </div>
    </div>
  );
};
        `,
        metadata: {
          type: 'component',
          language: 'typescript',
          framework: 'react',
          category: 'ui-component',
          features: 'hooks,typescript,api-integration',
          file_path: '/components/UserProfile.tsx',
          last_modified: '2025-07-15'
        }
      }
    ];

    // Add all documentation samples
    const ids = documentationSamples.map(doc => doc.id);
    const documents = documentationSamples.map(doc => doc.content);
    const metadatas = documentationSamples.map(doc => doc.metadata);

    await collection.add({
      ids: ids,
      documents: documents,
      metadatas: metadatas
    });

    console.log(`✅ Added ${documentationSamples.length} documentation samples`);

    // Test realistic search scenarios
    console.log('\n🔍 Testing realistic search scenarios...');

    const searchScenarios = [
      {
        query: 'user authentication JWT token middleware',
        description: 'Finding authentication-related code',
        expectedType: 'middleware'
      },
      {
        query: 'database user model password hashing',
        description: 'Finding database models and user management',
        expectedType: 'model'
      },
      {
        query: 'responsive design mobile CSS flexbox grid',
        description: 'Finding responsive styling code',
        expectedType: 'styles'
      },
      {
        query: 'React component TypeScript user profile form',
        description: 'Finding React components and UI code',
        expectedType: 'component'
      },
      {
        query: 'API endpoint user management',
        description: 'Finding API and backend code',
        expectedLanguage: 'javascript'
      }
    ];

    for (const scenario of searchScenarios) {
      console.log(`\n🔎 ${scenario.description}`);
      console.log(`   Query: "${scenario.query}"`);

      const results = await collection.query({
        queryTexts: [scenario.query],
        nResults: 3
      });

      if (results.ids[0].length > 0) {
        console.log(`✅ Found ${results.ids[0].length} relevant results:`);

        results.ids[0].forEach((id, index) => {
          const metadata = results.metadatas[0][index];
          const distance = results.distances[0][index];

          console.log(`   ${index + 1}. ${id}`);
          console.log(`      Type: ${metadata.type} | Language: ${metadata.language}`);
          console.log(`      Relevance: ${(1 - distance).toFixed(3)} | Distance: ${distance.toFixed(4)}`);

          // Validate expected results
          if (scenario.expectedType && metadata.type === scenario.expectedType) {
            console.log(`      ✅ Correctly identified ${scenario.expectedType}`);
          }
          if (scenario.expectedLanguage && metadata.language === scenario.expectedLanguage) {
            console.log(`      ✅ Correctly identified ${scenario.expectedLanguage}`);
          }
        });
      } else {
        console.log(`⚠️  No results found for this query`);
      }
    }

    // Test metadata filtering
    console.log('\n🔍 Testing metadata filtering...');

    const filterTests = [
      { filter: { language: 'javascript' }, description: 'JavaScript files only' },
      { filter: { type: 'component' }, description: 'Components only' },
      { filter: { category: 'authentication' }, description: 'Authentication-related code' }
    ];

    for (const test of filterTests) {
      console.log(`\n🔎 Filter test: ${test.description}`);

      const results = await collection.query({
        queryTexts: ['code documentation'],
        nResults: 10,
        where: test.filter
      });

      console.log(`📊 Found ${results.ids[0].length} filtered results`);
      results.ids[0].forEach((id, index) => {
        const metadata = results.metadatas[0][index];
        console.log(`   - ${id}: ${metadata.type} (${metadata.language})`);
      });
    }

    // Performance test
    console.log('\n⚡ Testing search performance...');
    const startTime = Date.now();

    for (let i = 0; i < 10; i++) {
      await collection.query({
        queryTexts: ['user authentication'],
        nResults: 5
      });
    }

    const endTime = Date.now();
    const avgTime = (endTime - startTime) / 10;
    console.log(`✅ Average search time: ${avgTime.toFixed(2)}ms (10 searches)`);

    // Clean up test documents
    console.log('\n🧹 Cleaning up test documents...');
    await collection.delete({ ids: ids });
    console.log('✅ Test documents deleted successfully');

    console.log('\n🎉 Documentation Semantic Search Integration Test Complete!');
    console.log('📊 Summary:');
    console.log(`   ✅ Successfully indexed ${documentationSamples.length} documentation samples`);
    console.log(`   ✅ Tested ${searchScenarios.length} realistic search scenarios`);
    console.log(`   ✅ Verified metadata filtering functionality`);
    console.log(`   ✅ Performance: ${avgTime.toFixed(2)}ms average search time`);
    console.log('\n💡 Your semantic search system is ready for production use!');

    return true;

  } catch (error) {
    console.error('❌ Documentation semantic search test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Ensure ChromaDB server is running: npm run chromadb:start');
    console.log('   2. Check environment configuration in .env file');
    console.log('   3. Verify ChromaDB collection permissions');
    console.log('   4. Test basic connection: npm run chromadb:test');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testDocumentationSemanticSearch()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testDocumentationSemanticSearch };
