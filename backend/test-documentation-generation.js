#!/usr/bin/env node

/**
 * Test Documentation Generation
 *
 * This script tests the documentation generation functionality
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

async function testDocumentationGeneration() {
  console.log('📚 Testing Documentation Generation...\n');

  try {
    // Test 1: Check if backend is running
    console.log('🔍 Checking backend connection...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Backend is running');
    } catch (error) {
      console.log('❌ Backend is not running. Please start it with: npm run dev');
      return false;
    }

    // Test 2: Create a sample file for documentation
    const testProjectPath = path.join(__dirname, 'test-project');
    const testFilePath = path.join(testProjectPath, 'sample.js');

    // Create test directory and file
    if (!fs.existsSync(testProjectPath)) {
      fs.mkdirSync(testProjectPath, { recursive: true });
    }

    const sampleCode = `
/**
 * Sample JavaScript file for testing documentation generation
 */

class UserManager {
  constructor() {
    this.users = [];
  }

  /**
   * Add a new user to the system
   * @param {Object} user - User object with name and email
   * @returns {string} User ID
   */
  addUser(user) {
    const userId = \`user_\${Date.now()}\`;
    this.users.push({ ...user, id: userId });
    return userId;
  }

  /**
   * Get user by ID
   * @param {string} userId - The user ID
   * @returns {Object|null} User object or null if not found
   */
  getUser(userId) {
    return this.users.find(user => user.id === userId) || null;
  }

  /**
   * Delete user by ID
   * @param {string} userId - The user ID to delete
   * @returns {boolean} True if deleted, false if not found
   */
  deleteUser(userId) {
    const index = this.users.findIndex(user => user.id === userId);
    if (index !== -1) {
      this.users.splice(index, 1);
      return true;
    }
    return false;
  }
}

module.exports = UserManager;
`;

    fs.writeFileSync(testFilePath, sampleCode);
    console.log(`✅ Created test file: ${testFilePath}`);

    // Test 3: Generate documentation for specific files
    console.log('\n📄 Testing documentation generation for specific files...');
    try {
      const docResponse = await axios.post(`${BASE_URL}/api/documentation/generate`, {
        projectPath: testProjectPath,
        selectedFiles: [testFilePath],
        priority: 'high'
      });

      console.log('✅ Documentation generation request successful');
      console.log('📊 Response:', docResponse.data);
    } catch (error) {
      console.log('❌ Documentation generation failed:', error.response?.data || error.message);
    }

    // Test 4: Test directory-level documentation generation
    console.log('\n📁 Testing directory documentation generation...');
    try {
      const directoryResponse = await axios.post(`${BASE_URL}/api/documentation/generate`, {
        projectPath: testProjectPath,
        directoryPath: testProjectPath,
        priority: 'normal',
        includeSubdirectories: true
      });

      console.log('✅ Directory documentation generation successful');
      console.log('📊 Response:', directoryResponse.data);
    } catch (error) {
      console.log('❌ Directory documentation generation failed:', error.response?.data || error.message);
    }

    // Test 5: Test documentation search
    console.log('\n🔍 Testing documentation search...');
    try {
      // Wait a moment for documentation to be generated and indexed
      console.log('⏳ Waiting for documentation generation to complete...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      const searchResponse = await axios.post(`${BASE_URL}/api/documentation/search`, {
        query: 'user management functions',
        projectPath: testProjectPath,
        limit: 5,
        threshold: 0.5
      });

      console.log('✅ Documentation search successful');
      console.log('📊 Search results:', searchResponse.data.results?.length || 0);
      if (searchResponse.data.results?.length > 0) {
        searchResponse.data.results.slice(0, 2).forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.id || 'Unknown ID'}`);
          console.log(`      Content: ${(result.content || '').substring(0, 80)}...`);
          console.log(`      Similarity: ${(result.similarity || 0).toFixed(3)}`);
        });
      }
    } catch (error) {
      console.log('❌ Documentation search failed:', error.response?.data || error.message);
    }

    // Test 6: Check for generated documentation files
    console.log('\n🔍 Checking for generated documentation files...');
    const docsDir = path.join(testProjectPath, 'docs', 'generated', 'files');
    if (fs.existsSync(docsDir)) {
      const docFiles = fs.readdirSync(docsDir);
      console.log(`✅ Found ${docFiles.length} documentation files:`);
      docFiles.forEach(file => {
        console.log(`   - ${file}`);

        // Read and display a sample of the content
        const filePath = path.join(docsDir, file);
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          if (file.endsWith('.json')) {
            const jsonContent = JSON.parse(content);
            console.log(`     📄 Units documented: ${jsonContent.units?.length || 0}`);
          } else if (file.endsWith('.md')) {
            const lines = content.split('\n').slice(0, 3);
            console.log(`     📝 Preview: ${lines.join(' ').substring(0, 100)}...`);
          }
        } catch (err) {
          console.log(`     ⚠️  Could not read file: ${err.message}`);
        }
      });
    } else {
      console.log('ℹ️  No documentation files found yet (may take a moment to generate)');
    }

    // Test 7: Test documentation health check
    console.log('\n🏥 Testing documentation service health...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/documentation/health`);
      console.log('✅ Documentation service health check passed');
      console.log('📊 Status:', healthResponse.data);
    } catch (error) {
      console.log('❌ Documentation service health check failed:', error.response?.data || error.message);
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test files...');
    try {
      fs.rmSync(testProjectPath, { recursive: true, force: true });
      console.log('✅ Test files cleaned up');
    } catch (error) {
      console.log('⚠️  Could not clean up test files:', error.message);
    }

    console.log('\n🎉 Documentation generation testing complete!');
    console.log('\n💡 Next steps:');
    console.log('   1. Check the IDE Documentation panel for generated docs');
    console.log('   2. Test with your actual project files');
    console.log('   3. Verify semantic search indexing of generated docs');

    return true;

  } catch (error) {
    console.error('❌ Documentation generation test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure the backend is running: npm run dev');
    console.log('   2. Check if Python dependencies are installed');
    console.log('   3. Verify the documentation generation script exists');
    console.log('   4. Check backend logs for detailed error messages');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testDocumentationGeneration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testDocumentationGeneration };
