#!/usr/bin/env node

/**
 * Test Semantic Search Service
 * 
 * This script tests the semantic search service functionality
 */

const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function testSemanticSearchService() {
  console.log('🔍 Testing Semantic Search Service...\n');

  try {
    // Import the ChromaDB service (using dynamic import for ES modules)
    const { ChromaDBService } = await import('./dist/src/services/chroma-db.service.js');
    
    console.log('✅ ChromaDBService imported successfully');

    // Create service instance
    const chromaService = new ChromaDBService();
    console.log('✅ ChromaDBService instance created');

    // Test initialization
    console.log('\n🔧 Initializing ChromaDB service...');
    await chromaService.initialize();
    console.log('✅ ChromaDB service initialized successfully');

    // Test adding documents
    console.log('\n📄 Adding test documents...');
    const testDocs = [
      {
        id: 'doc1',
        content: 'This is a React component that handles user authentication and login functionality.',
        metadata: { type: 'component', language: 'javascript', framework: 'react' }
      },
      {
        id: 'doc2', 
        content: 'Python function for data processing and machine learning model training.',
        metadata: { type: 'function', language: 'python', domain: 'ml' }
      },
      {
        id: 'doc3',
        content: 'CSS styles for responsive web design and mobile-first approach.',
        metadata: { type: 'styles', language: 'css', responsive: true }
      }
    ];

    for (const doc of testDocs) {
      await chromaService.addDocument(doc.id, doc.content, doc.metadata);
      console.log(`✅ Added document: ${doc.id}`);
    }

    // Test semantic search
    console.log('\n🔍 Testing semantic search...');
    
    const searchQueries = [
      'authentication login',
      'machine learning python',
      'responsive design mobile'
    ];

    for (const query of searchQueries) {
      console.log(`\n🔎 Searching for: "${query}"`);
      const results = await chromaService.semanticSearch(query, 2);
      
      console.log(`📊 Found ${results.length} results:`);
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. ID: ${result.id}`);
        console.log(`      Content: ${result.content.substring(0, 60)}...`);
        console.log(`      Distance: ${result.distance.toFixed(4)}`);
        console.log(`      Metadata: ${JSON.stringify(result.metadata)}`);
      });
    }

    // Test cleanup
    console.log('\n🧹 Cleaning up test documents...');
    await chromaService.deleteDocuments(['doc1', 'doc2', 'doc3']);
    console.log('✅ Test documents deleted successfully');

    console.log('\n🎉 All semantic search tests passed! The service is working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Semantic search service test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure the backend is built: npm run build');
    console.log('   2. Check ChromaDB server is running: chroma run --host localhost --port 8000');
    console.log('   3. Verify environment variables are set correctly');
    console.log('   4. Check Azure OpenAI API key is configured for embeddings');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testSemanticSearchService()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testSemanticSearchService };
