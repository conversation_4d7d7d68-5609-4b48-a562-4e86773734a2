#!/usr/bin/env node

/**
 * Test Azure OpenAI Embeddings
 * 
 * This script tests the Azure OpenAI embeddings service
 */

const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function testAzureEmbeddings() {
  console.log('🔍 Testing Azure OpenAI Embeddings...\n');

  try {
    // Check environment variables
    console.log('🔧 Checking environment configuration...');
    const azureApiKey = process.env.AZURE_API_KEY;
    const azureEndpoint = process.env.AZURE_ENDPOINT;
    const embeddingModel = process.env.EMBEDDING_MODEL;

    if (!azureApiKey) {
      console.log('❌ AZURE_API_KEY not found in environment');
      return false;
    }

    if (!azureEndpoint) {
      console.log('❌ AZURE_ENDPOINT not found in environment');
      return false;
    }

    console.log('✅ Azure API Key found');
    console.log('✅ Azure Endpoint found:', azureEndpoint);
    console.log('✅ Embedding Model:', embeddingModel);

    // Try to import and test the Azure service
    console.log('\n🔍 Testing Azure service import...');
    
    try {
      // Import the Azure service (adjust path as needed)
      const { azureService } = await import('./dist/src/services/azure.service.js');
      console.log('✅ Azure service imported successfully');

      // Test embedding generation
      console.log('\n📄 Testing embedding generation...');
      const testTexts = [
        'This is a test document for embedding generation.',
        'Another test document to verify batch processing.'
      ];

      const result = await azureService.generateEmbeddings(testTexts, {
        model: embeddingModel
      });

      console.log('✅ Embeddings generated successfully');
      console.log(`📊 Generated ${result.embeddings.length} embeddings`);
      console.log(`📏 Embedding dimension: ${result.embeddings[0]?.length || 0}`);
      console.log(`💰 Tokens used: ${result.usage?.total_tokens || 'Unknown'}`);

      return true;

    } catch (importError) {
      console.log('❌ Failed to import Azure service:', importError.message);
      console.log('💡 This might be because the backend is not built yet');
      
      // Try direct Azure OpenAI API call
      console.log('\n🔍 Testing direct Azure OpenAI API call...');
      
      const axios = require('axios');
      
      const response = await axios.post(
        `${azureEndpoint}/openai/deployments/${embeddingModel}/embeddings?api-version=2023-05-15`,
        {
          input: ['This is a test document for embedding generation.'],
          model: embeddingModel
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'api-key': azureApiKey
          }
        }
      );

      console.log('✅ Direct Azure OpenAI API call successful');
      console.log(`📊 Generated ${response.data.data.length} embeddings`);
      console.log(`📏 Embedding dimension: ${response.data.data[0]?.embedding?.length || 0}`);
      console.log(`💰 Tokens used: ${response.data.usage?.total_tokens || 'Unknown'}`);

      return true;
    }

  } catch (error) {
    console.error('❌ Azure embeddings test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Check Azure OpenAI API key and endpoint');
    console.log('   2. Verify the embedding model deployment name');
    console.log('   3. Check network connectivity to Azure');
    console.log('   4. Ensure the API version is correct');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testAzureEmbeddings()
    .then(success => {
      if (success) {
        console.log('\n🎉 Azure OpenAI embeddings are working correctly!');
        console.log('💡 The ChromaDB service should be able to use Azure embeddings.');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testAzureEmbeddings };
