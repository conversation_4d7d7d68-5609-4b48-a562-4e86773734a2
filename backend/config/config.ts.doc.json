{"path": "/home/<USER>/Desktop/kapi-main/kapi/backend/config/config.ts", "contentHash": "bfc6187d7a12b058c3889b4a32b0a954c1c334e813ea70df6a4e6e142e4485a6", "commit": "bc3df31a2ad7cf06456e71484539b06ca55f315c", "timestamp": "2025-07-15T18:27:27+05:30", "units": [{"unitName": "loadYamlFile", "unitType": "function", "purpose": "Loads and parses a YAML file from a given file path, returning its contents as a JavaScript object.", "humanReadableExplanation": "This `loadYamlFile` function is designed to read and parse YAML (YAML Ain't Markup Language) files. It takes a single argument, `filePath`, which is a string representing the location of the YAML file on the file system. The function attempts to synchronously read the file's content using Node.js's built-in `fs.readFileSync` function. Once the content is read, it uses an external `yaml` library (likely `js-yaml`) to parse the YAML string into a JavaScript object. If both reading and parsing are successful, the resulting JavaScript object is returned. The entire operation is wrapped in a `try-catch` block. If any error occurs during the file reading (e.g., file not found, permission issues) or YAML parsing (e.g., malformed YAML), the error is caught. In such cases, a warning message is logged to the console using `console.warn`, including the file path and the error details, and an empty JavaScript object `{}` is returned instead of throwing an error. This ensures that the application can continue running even if a configuration or data file is missing or malformed, providing a graceful fallback.", "dependencies": [{"type": "external", "name": "fs"}, {"type": "external", "name": "yaml"}], "inputs": [{"name": "filePath", "type": "string", "description": "The absolute or relative path to the YAML file to be loaded."}], "outputs": {"type": "any", "description": "The parsed content of the YAML file as a JavaScript object, or an empty object `{}` if an error occurs during loading or parsing.", "throws": []}, "visualDiagram": "graph TD\n    A[Start loadYamlFile(filePath)] --> B{Try Block};\n    B --> C[fileContents = fs.readFileSync(filePath, 'utf8')];\n    C --> D[parsedObject = yaml.load(fileContents)];\n    D --> E[Return parsedObject];\n    B --> F{Catch Block};\n    C -- Error --> F;\n    D -- Error --> F;\n    F --> G[console.warn(\"Warning: Could not load YAML file...\", error)];\n    G --> H[Return {}];\n    E --> I[End];\n    H --> I;"}, {"unitName": "loadTsvFile", "unitType": "function", "purpose": "This function reads and parses a Tab Separated Value (TSV) file, converting its contents into an array of JavaScript objects.", "humanReadableExplanation": "The `loadTsvFile` function is designed to ingest data from a TSV file and transform it into a more usable structured format, specifically an array of objects. It takes a `filePath` string as input, which should point to the TSV file. Internally, it first attempts to read the entire file's content as a UTF-8 string. It then processes this content by trimming any leading/trailing whitespace and splitting it into individual lines. The very first line is treated as the header row, and its values (separated by tabs) are used as keys for the resulting objects. For every subsequent line, it splits the line by tabs to get the values. It then pairs each header with its corresponding value from the current line to construct a JavaScript object. All these row-objects are collected into an array, which is the function's return value. If any error occurs during file reading or parsing (e.g., file not found, permission issues), the function catches the error, logs a warning message to the console including the file path and the error details, and gracefully returns an empty array instead of throwing an exception, preventing application crashes.", "dependencies": [{"type": "external", "name": "fs"}], "inputs": [{"name": "filePath", "type": "string", "description": "The absolute or relative path to the TSV file to be loaded."}], "outputs": {"type": "any[]", "description": "An array of JavaScript objects. Each object represents a row from the TSV file, with keys corresponding to the TSV headers and values being the cell contents. Returns an empty array if the file cannot be loaded or an error occurs during parsing.", "throws": []}, "visualDiagram": "graph TD\n    A[Start] --> B{loadTsvFile(filePath)};\n    B --> C{Try...Catch Block};\n    C -- Success --> D[Read file contents (fs.readFileSync)];\n    D --> E[Trim & Split into lines];\n    E --> F[Extract headers from first line];\n    F --> G[Map remaining lines to objects];\n    G --> H[Return array of objects];\n    H --> I[End];\n    C -- Error --> J[Catch Error];\n    J --> K[Log warning to console];\n    K --> L[Return empty array];\n    L --> I;"}]}