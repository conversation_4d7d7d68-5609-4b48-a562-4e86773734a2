import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

// Load environment variables from .env and .env.local files
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Define paths to config files
const CONFIG_DIR = path.resolve(process.cwd(), 'config');
const CONFIG_YAML_PATH = path.join(CONFIG_DIR, 'config.yaml');
const PROMPTS_YAML_PATH = path.join(CONFIG_DIR, 'prompts.yaml');
const MODELS_TSV_PATH = path.join(CONFIG_DIR, 'models.tsv');

// Helper function to load YAML files
function loadYamlFile(filePath: string): any {
  try {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    return yaml.load(fileContents);
  } catch (error) {
    console.warn(`Warning: Could not load YAML file at ${filePath}`, error);
    return {};
  }
}

// Helper function to load TSV files
function loadTsvFile(filePath: string): any[] {
  try {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    const lines = fileContents.trim().split('\n');
    const headers = lines[0].split('\t');

    return lines.slice(1).map(line => {
      const values = line.split('\t');
      return headers.reduce((obj, header, index) => {
        obj[header] = values[index];
        return obj;
      }, {} as Record<string, string>);
    });
  } catch (error) {
    console.warn(`Warning: Could not load TSV file at ${filePath}`, error);
    return [];
  }
}

// Load configuration files
const configYaml = loadYamlFile(CONFIG_YAML_PATH);
const promptsYaml = loadYamlFile(PROMPTS_YAML_PATH);
const modelsTsv = loadTsvFile(MODELS_TSV_PATH);

// Environment-based configuration
export const config = {
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),

  // Database configuration
  db: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_NAME || 'kapi',
    url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/kapi'
  },

  // Authentication configuration
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'dev-secret',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
    // Clerk Authentication
    clerkPublishableKey: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
    clerkSecretKey: process.env.CLERK_SECRET_KEY
  },

  // AI service API keys
  ai: {
    anthropicApiKey: process.env.ANTHROPIC_API_KEY,
    openaiApiKey: process.env.OPENAI_API_KEY,
    googleApiKey: process.env.GOOGLE_API_KEY,
    // Azure Services
    azureApiKey: process.env.AZURE_API_KEY, // Azure API key for all Azure services
    azureEndpoint: process.env.AZURE_ENDPOINT, // Azure endpoint for LLMs
    azureWhisperEndpoint: process.env.AZURE_WHISPER_ENDPOINT, // Azure endpoint specifically for Whisper
    // Google Cloud credentials
    googleApplicationCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS || path.join(process.cwd(), 'config', 'google-tts-key.json'),
    // Google TTS credentials as JSON string
    googleTtsCredentialsJson: process.env.GOOGLE_TTS_CREDENTIALS_JSON,
    // Google TTS credentials as base64 encoded JSON
    googleTtsCredentialsBase64: process.env.GOOGLE_TTS_CREDENTIALS_BASE64
  },

  // AWS configuration
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    profile: process.env.AWS_PROFILE || 'bedrock-test'
  },

  // YAML-based configuration
  yamlConfig: configYaml,
  prompts: promptsYaml,
  models: modelsTsv,

  // Helper methods to access configuration
  getModelPriorities(useCase: string): string[] {
    return configYaml?.priorities?.[useCase] || [];
  },

  getPrompt(promptKey: string): string {
    return promptsYaml?.[promptKey] || '';
  },

  getModelById(modelId: string): any {
    return modelsTsv.find(model => model.model_id === modelId);
  }
};

export default config;
