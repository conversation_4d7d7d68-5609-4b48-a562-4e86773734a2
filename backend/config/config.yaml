# Model priorities for KAPI IDE
version: "1.0"
last_updated: "2025-05-15"

use_case_descriptions:
  chat: "For onboarding, general questions, documentation, and conversation titles"
  code_review: "For reviewing code within the backwards build process"
  code_gen_big: "For generating large code components"
  code_gen_agentic: "For generating code components that are part of an agentic process -- small, incremental, and iterative"

# Use case priorities (in order of preference)
priorities:
  chat:
    - gpt-4.1
    - nova-lite
    - gemini-2.0-flash
    - gpt-4.1-nano
    - gemini-2.0-flash-lite
    - nova-micro

  code_review:
    - gemini-2.5-flash
    - o4-mini
    - gpt-4.1
    - claude-3.7-sonnet
    - claude-3.5-sonnet
    - nova-premier
    - gemini-2.5-pro
    - nova-pro

  # These use cases share the same priority list
  code_gen_big: &code_gen_big_list
    - claude-3.7-sonnet
    - o4-mini
    - gpt-4.1
    - nova-premier
    - nova-pro
    - gemini-2.5-flash

  svg_mockup: *code_gen_big_list
  slides: *code_gen_big_list

  # These use cases share the same priority list
  code_gen_agentic: &code_gen_agentic_list
    - claude-3.7-sonnet
    - gpt-4.1
    - nova-premier
    - claude-3.5-sonnet
    - gemini-2.5-flash
    - nova-pro
    - gemini-2.0-flash

  test_cases: *code_gen_agentic_list

# Model provider configurations
models:
  google:
    base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
    system_prompt: "You are a helpful AI assistant."
    reasoning_effort: "medium"

  azure_openai:
    api_version: "2024-02-01"
    system_prompt: "You are a helpful AI assistant."
    thinking_system_prompt: "You are a helpful AI assistant. Think step by step to solve problems."
    reasoning_effort: "medium"

  bedrock:
    default_region: "us-east-1"
    read_timeout: 120
    system_prompt: "You are a helpful AI assistant."
    thinking_system_prompt: "You are a helpful AI assistant. Think step by step to solve problems."

# System settings
settings:
  fallback_strategy: "next_in_list"
  max_retries: 3
  timeout_seconds: 30
  max_tokens: 10000
  temperature: 1.0
