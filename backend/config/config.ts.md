# Documentation for `/home/<USER>/Desktop/kapi-main/kapi/backend/config/config.ts`

**Commit:** `bc3df31a2ad7cf06456e71484539b06ca55f315c` | **Last Updated:** `2025-07-15T18:27:27+05:30`

---

## `loadYamlFile` (Function)

**Purpose:** Loads and parses a YAML file from a given file path, returning its contents as a JavaScript object.

### Detailed Explanation

This `loadYamlFile` function is designed to read and parse YAML (YAML Ain't Markup Language) files. It takes a single argument, `filePath`, which is a string representing the location of the YAML file on the file system. The function attempts to synchronously read the file's content using Node.js's built-in `fs.readFileSync` function. Once the content is read, it uses an external `yaml` library (likely `js-yaml`) to parse the YAML string into a JavaScript object. If both reading and parsing are successful, the resulting JavaScript object is returned. The entire operation is wrapped in a `try-catch` block. If any error occurs during the file reading (e.g., file not found, permission issues) or YAML parsing (e.g., malformed YAML), the error is caught. In such cases, a warning message is logged to the console using `console.warn`, including the file path and the error details, and an empty JavaScript object `{}` is returned instead of throwing an error. This ensures that the application can continue running even if a configuration or data file is missing or malformed, providing a graceful fallback.

### Visual Representation

```mermaid
graph TD
    A[Start loadYamlFile(filePath)] --> B{Try Block};
    B --> C[fileContents = fs.readFileSync(filePath, 'utf8')];
    C --> D[parsedObject = yaml.load(fileContents)];
    D --> E[Return parsedObject];
    B --> F{Catch Block};
    C -- Error --> F;
    D -- Error --> F;
    F --> G[console.warn("Warning: Could not load YAML file...", error)];
    G --> H[Return {}];
    E --> I[End];
    H --> I;
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `filePath` | `string` | The absolute or relative path to the YAML file to be loaded. |

### Outputs

- **Returns:** `any` - The parsed content of the YAML file as a JavaScript object, or an empty object `{}` if an error occurs during loading or parsing.

### Dependencies

- **fs** (external)
- **yaml** (external)

---

## `loadTsvFile` (Function)

**Purpose:** This function reads and parses a Tab Separated Value (TSV) file, converting its contents into an array of JavaScript objects.

### Detailed Explanation

The `loadTsvFile` function is designed to ingest data from a TSV file and transform it into a more usable structured format, specifically an array of objects. It takes a `filePath` string as input, which should point to the TSV file. Internally, it first attempts to read the entire file's content as a UTF-8 string. It then processes this content by trimming any leading/trailing whitespace and splitting it into individual lines. The very first line is treated as the header row, and its values (separated by tabs) are used as keys for the resulting objects. For every subsequent line, it splits the line by tabs to get the values. It then pairs each header with its corresponding value from the current line to construct a JavaScript object. All these row-objects are collected into an array, which is the function's return value. If any error occurs during file reading or parsing (e.g., file not found, permission issues), the function catches the error, logs a warning message to the console including the file path and the error details, and gracefully returns an empty array instead of throwing an exception, preventing application crashes.

### Visual Representation

```mermaid
graph TD
    A[Start] --> B{loadTsvFile(filePath)};
    B --> C{Try...Catch Block};
    C -- Success --> D[Read file contents (fs.readFileSync)];
    D --> E[Trim & Split into lines];
    E --> F[Extract headers from first line];
    F --> G[Map remaining lines to objects];
    G --> H[Return array of objects];
    H --> I[End];
    C -- Error --> J[Catch Error];
    J --> K[Log warning to console];
    K --> L[Return empty array];
    L --> I;
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `filePath` | `string` | The absolute or relative path to the TSV file to be loaded. |

### Outputs

- **Returns:** `any[]` - An array of JavaScript objects. Each object represents a row from the TSV file, with keys corresponding to the TSV headers and values being the cell contents. Returns an empty array if the file cannot be loaded or an error occurs during parsing.

### Dependencies

- **fs** (external)

---

