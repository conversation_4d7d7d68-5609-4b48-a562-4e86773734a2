/**
 * Test script for commit-aware documentation system
 * 
 * This script tests the integration between:
 * 1. Pre-commit documentation generation
 * 2. Post-commit documentation indexing
 * 3. Commit-based documentation queries
 * 4. Documentation change tracking
 */

const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  projectPath: '/home/<USER>/Desktop/kapi-main/kapi',
  testFile: '/home/<USER>/Desktop/kapi-main/kapi/test-commit-doc.js'
};

async function getCurrentCommitHash() {
  try {
    return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    console.error('Failed to get current commit hash:', error.message);
    return null;
  }
}

async function getPreviousCommitHash() {
  try {
    return execSync('git rev-parse HEAD~1', { encoding: 'utf8' }).trim();
  } catch (error) {
    console.error('Failed to get previous commit hash:', error.message);
    return null;
  }
}

async function createTestCommit() {
  console.log('📝 Creating test commit to trigger documentation generation...');
  
  try {
    // Create a test file
    const testContent = `
// Test file for commit-aware documentation
// Generated at: ${new Date().toISOString()}

/**
 * Test function for documentation generation
 * @param {string} message - The message to log
 * @returns {void}
 */
function testFunction(message) {
  console.log('Test:', message);
}

/**
 * Another test function with different signature
 * @param {number} value - A numeric value
 * @param {boolean} flag - A boolean flag
 * @returns {string} The processed result
 */
function anotherTestFunction(value, flag) {
  return flag ? value.toString() : 'disabled';
}

module.exports = { testFunction, anotherTestFunction };
`;

    fs.writeFileSync(TEST_CONFIG.testFile, testContent);
    
    // Stage the file
    execSync(`git add "${TEST_CONFIG.testFile}"`);
    
    // Commit (this will trigger the pre-commit hook)
    const commitMessage = `Test commit for documentation system - ${Date.now()}`;
    execSync(`git commit -m "${commitMessage}"`);
    
    console.log('✅ Test commit created successfully');
    return await getCurrentCommitHash();
  } catch (error) {
    console.error('❌ Failed to create test commit:', error.message);
    throw error;
  }
}

async function testCommitDocumentationIndexing() {
  console.log('\n🔍 Testing commit-specific documentation indexing...');
  
  const commitHash = await getCurrentCommitHash();
  if (!commitHash) {
    throw new Error('Could not get current commit hash');
  }
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/commit/${commitHash}`, {
      projectPath: TEST_CONFIG.projectPath
    });
    
    console.log('✅ Commit documentation indexing started:', response.data);
    
    // Wait for indexing to complete
    console.log('⏳ Waiting for indexing to complete...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ Commit documentation indexing failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testDocumentationChanges() {
  console.log('\n📊 Testing documentation changes between commits...');
  
  const currentCommit = await getCurrentCommitHash();
  const previousCommit = await getPreviousCommitHash();
  
  if (!currentCommit || !previousCommit) {
    console.log('⚠️  Skipping changes test - need at least 2 commits');
    return;
  }
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/changes`, {
      fromCommit: previousCommit,
      toCommit: currentCommit,
      projectPath: TEST_CONFIG.projectPath
    });
    
    const changes = response.data;
    console.log('✅ Documentation changes retrieved:');
    console.log(`   From: ${changes.fromCommit.substring(0, 8)}`);
    console.log(`   To: ${changes.toCommit.substring(0, 8)}`);
    console.log(`   Total changes: ${changes.total}`);
    
    if (changes.changes.length > 0) {
      console.log('\n   Changes:');
      changes.changes.forEach((change, index) => {
        console.log(`   ${index + 1}. [${change.action.toUpperCase()}] ${change.filePath}`);
      });
    } else {
      console.log('   No documentation changes detected');
    }
    
  } catch (error) {
    console.error('❌ Documentation changes test failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSemanticSearchWithCommit() {
  console.log('\n🔎 Testing semantic search with commit-aware documentation...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/search`, {
      query: 'test function documentation',
      projectPath: TEST_CONFIG.projectPath,
      limit: 5,
      threshold: 0.3
    });
    
    const results = response.data;
    console.log(`✅ Semantic search completed: ${results.total} results`);
    
    if (results.results.length > 0) {
      console.log('\n   Search results:');
      results.results.forEach((result, index) => {
        const commit = result.metadata.commit ? result.metadata.commit.substring(0, 8) : 'unknown';
        console.log(`   ${index + 1}. [${(result.similarity * 100).toFixed(1)}%] ${result.metadata.unit || 'Unknown'}`);
        console.log(`      Commit: ${commit} | File: ${result.metadata.sourceFile || result.metadata.filePath}`);
        console.log(`      Content: ${result.content.substring(0, 80)}...`);
      });
    } else {
      console.log('   No search results found');
    }
    
  } catch (error) {
    console.error('❌ Semantic search test failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testCommitWorkflow() {
  console.log('\n🔄 Testing complete commit-aware documentation workflow...');
  
  console.log('\n1️⃣ Step 1: Create test commit (triggers pre-commit doc generation)');
  const newCommitHash = await createTestCommit();
  console.log(`   New commit: ${newCommitHash}`);
  
  console.log('\n2️⃣ Step 2: Index documentation for the new commit');
  await testCommitDocumentationIndexing();
  
  console.log('\n3️⃣ Step 3: Test documentation change tracking');
  await testDocumentationChanges();
  
  console.log('\n4️⃣ Step 4: Test semantic search with commit-aware docs');
  await testSemanticSearchWithCommit();
  
  console.log('\n✅ Complete workflow test successful!');
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test files...');
  
  try {
    // Remove the test file
    if (fs.existsSync(TEST_CONFIG.testFile)) {
      fs.unlinkSync(TEST_CONFIG.testFile);
      console.log('   Removed test file');
    }
    
    // Note: We don't remove the commit as it's part of git history
    // In a real scenario, you might want to reset to previous commit in a test branch
    
  } catch (error) {
    console.error('⚠️  Cleanup failed:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting Commit-Aware Documentation Tests\n');
  console.log('Configuration:', TEST_CONFIG);
  console.log('=' .repeat(60));
  
  try {
    // Test individual components
    await testCommitDocumentationIndexing();
    await testDocumentationChanges();
    await testSemanticSearchWithCommit();
    
    // Test complete workflow
    await testCommitWorkflow();
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 All commit-aware documentation tests completed successfully!');
    console.log('\n📋 Summary of tested features:');
    console.log('   ✅ Commit-specific documentation indexing');
    console.log('   ✅ Documentation change tracking between commits');
    console.log('   ✅ Semantic search with commit metadata');
    console.log('   ✅ Pre-commit documentation generation integration');
    console.log('   ✅ Post-commit indexing workflow');
    
  } catch (error) {
    console.log('\n' + '=' .repeat(60));
    console.error('💥 Tests failed:', error.message);
    process.exit(1);
  } finally {
    await cleanup();
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testCommitDocumentationIndexing,
  testDocumentationChanges,
  testSemanticSearchWithCommit,
  testCommitWorkflow
};
