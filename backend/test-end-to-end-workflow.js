#!/usr/bin/env node

/**
 * End-to-End Workflow Test
 * 
 * This script tests the complete documentation generation and search workflow
 * without relying on Azure OpenAI embeddings
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

async function testEndToEndWorkflow() {
  console.log('🔄 Testing End-to-End Documentation Workflow...\n');

  try {
    // Test 1: Check if backend is running
    console.log('🔍 Checking backend connection...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Backend is running');
    } catch (error) {
      console.log('❌ Backend is not running. Please start it with: npm run dev');
      return false;
    }

    // Test 2: Create a realistic test project
    const testProjectPath = path.join(__dirname, 'test-workflow-project');
    console.log(`\n📁 Creating test project: ${testProjectPath}`);
    
    // Create test directory structure
    if (!fs.existsSync(testProjectPath)) {
      fs.mkdirSync(testProjectPath, { recursive: true });
    }

    const srcDir = path.join(testProjectPath, 'src');
    if (!fs.existsSync(srcDir)) {
      fs.mkdirSync(srcDir, { recursive: true });
    }

    // Create multiple test files with different types of code
    const testFiles = [
      {
        path: path.join(srcDir, 'auth.js'),
        content: `
/**
 * Authentication Service
 * Handles user login, logout, and session management
 */

class AuthService {
  constructor() {
    this.sessions = new Map();
    this.users = new Map();
  }

  /**
   * Authenticate user with username and password
   * @param {string} username - User's username
   * @param {string} password - User's password
   * @returns {Object} Authentication result with token
   */
  async login(username, password) {
    const user = this.users.get(username);
    if (!user || user.password !== password) {
      throw new Error('Invalid credentials');
    }

    const token = this.generateToken();
    this.sessions.set(token, { userId: user.id, createdAt: Date.now() });
    
    return {
      success: true,
      token: token,
      user: { id: user.id, username: user.username }
    };
  }

  /**
   * Generate a secure session token
   * @returns {string} Session token
   */
  generateToken() {
    return 'token_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Validate session token
   * @param {string} token - Session token to validate
   * @returns {Object|null} User session or null if invalid
   */
  validateToken(token) {
    return this.sessions.get(token) || null;
  }
}

module.exports = AuthService;
        `
      },
      {
        path: path.join(srcDir, 'database.js'),
        content: `
/**
 * Database Connection Manager
 * Handles database connections and query execution
 */

class DatabaseManager {
  constructor(config) {
    this.config = config;
    this.connection = null;
  }

  /**
   * Connect to the database
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      console.log('Connecting to database...');
      // Simulate database connection
      this.connection = { connected: true, timestamp: Date.now() };
      console.log('Database connected successfully');
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  }

  /**
   * Execute a database query
   * @param {string} query - SQL query to execute
   * @param {Array} params - Query parameters
   * @returns {Promise<Array>} Query results
   */
  async query(query, params = []) {
    if (!this.connection) {
      throw new Error('Database not connected');
    }

    console.log('Executing query:', query);
    // Simulate query execution
    return [{ id: 1, result: 'success' }];
  }

  /**
   * Close database connection
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (this.connection) {
      this.connection = null;
      console.log('Database disconnected');
    }
  }
}

module.exports = DatabaseManager;
        `
      },
      {
        path: path.join(srcDir, 'utils.js'),
        content: `
/**
 * Utility Functions
 * Common helper functions used throughout the application
 */

/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} True if email is valid
 */
function validateEmail(email) {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generate a random ID
 * @param {number} length - Length of the ID
 * @returns {string} Random ID
 */
function generateId(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Format date to ISO string
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string
 */
function formatDate(date) {
  return date.toISOString();
}

module.exports = {
  validateEmail,
  generateId,
  formatDate
};
        `
      }
    ];

    // Write test files
    for (const file of testFiles) {
      fs.writeFileSync(file.path, file.content);
      console.log(`✅ Created: ${path.basename(file.path)}`);
    }

    // Test 3: Generate documentation for the project
    console.log('\n📚 Generating documentation for test project...');
    try {
      const docResponse = await axios.post(`${BASE_URL}/api/documentation/generate`, {
        projectPath: testProjectPath,
        directoryPath: srcDir,
        priority: 'high',
        includeSubdirectories: true
      });

      console.log('✅ Documentation generation started');
      console.log('📊 Response:', docResponse.data);
    } catch (error) {
      console.log('❌ Documentation generation failed:', error.response?.data || error.message);
    }

    // Test 4: Wait for documentation generation to complete
    console.log('\n⏳ Waiting for documentation generation to complete...');
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds

    // Test 5: Check for generated documentation files
    console.log('\n🔍 Checking for generated documentation...');
    const docsDir = path.join(testProjectPath, 'docs', 'generated', 'files');
    let documentationFound = false;

    if (fs.existsSync(docsDir)) {
      const docFiles = fs.readdirSync(docsDir);
      console.log(`✅ Found ${docFiles.length} documentation files:`);
      
      docFiles.forEach(file => {
        console.log(`   - ${file}`);
        documentationFound = true;
      });

      // Show content of first documentation file
      if (docFiles.length > 0) {
        const firstDocFile = path.join(docsDir, docFiles[0]);
        try {
          const content = fs.readFileSync(firstDocFile, 'utf8');
          if (docFiles[0].endsWith('.json')) {
            const jsonContent = JSON.parse(content);
            console.log(`\n📄 Sample documentation content:`);
            console.log(`   File: ${jsonContent.path}`);
            console.log(`   Units documented: ${jsonContent.units?.length || 0}`);
            if (jsonContent.units && jsonContent.units.length > 0) {
              console.log(`   First unit: ${jsonContent.units[0].name} (${jsonContent.units[0].type})`);
            }
          }
        } catch (err) {
          console.log(`   ⚠️  Could not read documentation file: ${err.message}`);
        }
      }
    } else {
      console.log('ℹ️  No documentation directory found yet');
    }

    // Test 6: Test documentation search (if documentation was generated)
    if (documentationFound) {
      console.log('\n🔍 Testing documentation search...');
      
      const searchQueries = [
        'authentication login user',
        'database connection query',
        'utility functions email validation'
      ];

      for (const query of searchQueries) {
        try {
          console.log(`\n🔎 Searching for: "${query}"`);
          const searchResponse = await axios.post(`${BASE_URL}/api/documentation/search`, {
            query: query,
            projectPath: testProjectPath,
            limit: 3,
            threshold: 0.3
          });

          const results = searchResponse.data.results || [];
          console.log(`✅ Found ${results.length} results`);
          
          if (results.length > 0) {
            results.slice(0, 2).forEach((result, index) => {
              console.log(`   ${index + 1}. Similarity: ${(result.similarity || 0).toFixed(3)}`);
              console.log(`      Content preview: ${(result.content || '').substring(0, 80)}...`);
            });
          }
        } catch (error) {
          console.log(`❌ Search failed for "${query}":`, error.response?.data?.error || error.message);
        }
      }
    } else {
      console.log('\n⚠️  Skipping search tests - no documentation found');
    }

    // Test 7: Test health endpoints
    console.log('\n🏥 Testing service health...');
    try {
      const docHealthResponse = await axios.get(`${BASE_URL}/api/documentation/health`);
      console.log('✅ Documentation service health:', docHealthResponse.data.status);
    } catch (error) {
      console.log('❌ Documentation health check failed:', error.response?.data || error.message);
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test files...');
    try {
      fs.rmSync(testProjectPath, { recursive: true, force: true });
      console.log('✅ Test files cleaned up');
    } catch (error) {
      console.log('⚠️  Could not clean up test files:', error.message);
    }

    console.log('\n🎉 End-to-End Workflow Test Complete!');
    console.log('\n📊 Summary:');
    console.log(`   ✅ Backend connectivity: Working`);
    console.log(`   ✅ Documentation generation: ${documentationFound ? 'Working' : 'Needs attention'}`);
    console.log(`   ✅ File processing: Working`);
    console.log(`   ✅ Service health: Working`);
    
    if (documentationFound) {
      console.log('\n💡 Your documentation generation system is working correctly!');
      console.log('🎯 Next steps:');
      console.log('   1. Test with your actual project files');
      console.log('   2. Use the IDE documentation panel');
      console.log('   3. Try semantic search in the IDE interface');
    } else {
      console.log('\n💡 Documentation generation needs attention:');
      console.log('   1. Check Python environment and dependencies');
      console.log('   2. Verify the documentation generation script');
      console.log('   3. Check backend logs for detailed errors');
    }
    
    return documentationFound;

  } catch (error) {
    console.error('❌ End-to-end workflow test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure the backend is running: npm run dev');
    console.log('   2. Check if all required services are available');
    console.log('   3. Verify file permissions in the test directory');
    console.log('   4. Check backend logs for detailed error messages');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testEndToEndWorkflow()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testEndToEndWorkflow };
