# ChromaDB Setup Guide for KAPI Backend

This guide explains how to set up and use ChromaDB for semantic search functionality in the KAPI backend.

## 🚀 Quick Start

### 1. Start ChromaDB Server
```bash
# Using npm script (recommended)
npm run chromadb:start

# Or directly
./start-chromadb.sh
```

### 2. Test the Connection
```bash
# Test basic ChromaDB connection
npm run chromadb:test

# Test semantic search functionality
npm run chromadb:test-semantic
```

### 3. Stop ChromaDB Server
```bash
# Using npm script
npm run chromadb:stop

# Or directly
./stop-chromadb.sh
```

## 📋 Prerequisites

- Node.js 18+ 
- Python 3.8+ (for ChromaDB CLI)
- pipx (recommended) or pip

## 🔧 Configuration

ChromaDB configuration is managed through environment variables in `.env`:

```bash
# ChromaDB Configuration
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_COLLECTION=documentation_embeddings
EMBEDDING_MODEL=text-embedding-ada-002

# Required for embeddings (Azure OpenAI)
AZURE_API_KEY=your_azure_api_key
AZURE_ENDPOINT=your_azure_endpoint
```

## 📚 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run chromadb:start` | Start ChromaDB server |
| `npm run chromadb:stop` | Stop ChromaDB server |
| `npm run chromadb:restart` | Restart ChromaDB server |
| `npm run chromadb:test` | Test basic connection |
| `npm run chromadb:test-semantic` | Test semantic search |

## 🔍 Usage Examples

### Basic Document Indexing
```javascript
const { ChromaDBService } = require('./dist/src/services/chroma-db.service.js');

const chromaService = new ChromaDBService();
await chromaService.initialize();

// Add a document
await chromaService.addDocument(
  'doc_id_1',
  'This is a React component for user authentication',
  { type: 'component', language: 'javascript', framework: 'react' }
);
```

### Semantic Search
```javascript
// Search for similar documents
const results = await chromaService.semanticSearch(
  'user login authentication',
  5  // limit to 5 results
);

console.log(results);
// [
//   {
//     id: 'doc_id_1',
//     content: 'This is a React component for user authentication',
//     metadata: { type: 'component', language: 'javascript', framework: 'react' },
//     distance: 0.234
//   }
// ]
```

### Filtered Search
```javascript
// Search with metadata filters
const results = await chromaService.semanticSearch(
  'authentication',
  5,
  { language: 'javascript' }  // only JavaScript files
);
```

## 🛠️ Troubleshooting

### ChromaDB Won't Start
1. Check if port 8000 is available:
   ```bash
   lsof -i :8000
   ```

2. Install ChromaDB CLI:
   ```bash
   pipx install chromadb
   # or
   pip install chromadb
   ```

3. Check logs:
   ```bash
   tail -f chromadb.log
   ```

### Connection Errors
1. Verify ChromaDB is running:
   ```bash
   curl http://localhost:8000/api/v2/version
   ```

2. Check environment variables:
   ```bash
   echo $CHROMA_HOST $CHROMA_PORT
   ```

3. Test with basic script:
   ```bash
   npm run chromadb:test
   ```

### Embedding Errors
1. Verify Azure OpenAI credentials:
   ```bash
   echo $AZURE_API_KEY $AZURE_ENDPOINT
   ```

2. Test embedding generation separately
3. Check API quotas and limits

## 📊 Performance Tips

1. **Batch Operations**: Use `addDocuments()` for multiple documents
2. **Collection Management**: Create separate collections for different document types
3. **Metadata Indexing**: Use consistent metadata structure for efficient filtering
4. **Embedding Caching**: Consider caching embeddings for frequently accessed documents

## 🔒 Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Network Access**: Restrict ChromaDB port access in production
3. **Data Encryption**: Consider encrypting sensitive document content
4. **Access Control**: Implement proper authentication for ChromaDB endpoints

## 📈 Monitoring

### Health Checks
```bash
# Check ChromaDB status
curl http://localhost:8000/api/v2/version

# Check collection status
curl http://localhost:8000/api/v2/collections
```

### Logs
- ChromaDB logs: `chromadb.log`
- Backend logs: Check your application logs for ChromaDB service messages

## 🚀 Production Deployment

For production deployment:

1. Use a dedicated ChromaDB server
2. Configure persistent storage
3. Set up monitoring and alerting
4. Implement backup strategies
5. Use environment-specific configurations

### Docker Deployment
```bash
# Run ChromaDB in Docker
docker run -p 8000:8000 chromadb/chroma:latest
```

### Environment Variables for Production
```bash
CHROMA_HOST=your-chromadb-server.com
CHROMA_PORT=8000
CHROMA_COLLECTION=production_docs
EMBEDDING_MODEL=text-embedding-ada-002
```

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review ChromaDB logs
3. Test with the provided test scripts
4. Check ChromaDB documentation: https://docs.trychroma.com/

## 🎉 Success!

If all tests pass, your semantic search functionality is ready to use! The ChromaDB service will automatically:

- Handle document indexing
- Generate embeddings using Azure OpenAI
- Provide fast semantic search capabilities
- Support metadata filtering
- Scale with your documentation needs
