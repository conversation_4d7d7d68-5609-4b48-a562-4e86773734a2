#!/usr/bin/env node

/**
 * Simple Semantic Search Test
 * 
 * This script tests semantic search functionality without requiring the full backend build
 */

const { ChromaClient } = require('chromadb');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

async function testSemanticSearchSimple() {
  console.log('🔍 Testing Semantic Search (Simple)...\n');

  try {
    // Initialize ChromaDB client with updated configuration
    const client = new ChromaClient({
      host: process.env.CHROMA_HOST || 'localhost',
      port: parseInt(process.env.CHROMA_PORT || '8000', 10)
    });

    console.log('✅ ChromaDB client initialized');
    console.log(`📍 Connecting to: ${process.env.CHROMA_HOST || 'localhost'}:${process.env.CHROMA_PORT || '8000'}`);

    // Test connection
    const version = await client.version();
    console.log(`✅ ChromaDB server version: ${version}`);

    // Create or get collection
    const collectionName = process.env.CHROMA_COLLECTION || 'documentation_embeddings';
    console.log(`\n📁 Working with collection: ${collectionName}`);
    
    let collection;
    try {
      collection = await client.getCollection({ name: collectionName });
      console.log('✅ Connected to existing collection');
    } catch (error) {
      console.log('📁 Creating new collection...');
      collection = await client.createCollection({ name: collectionName });
      console.log('✅ New collection created');
    }

    // Add test documents for semantic search
    console.log('\n📄 Adding test documents...');
    const testDocs = [
      {
        id: 'react_auth_component',
        content: 'React authentication component with login form, password validation, and user session management. Includes hooks for handling form state and API calls.',
        metadata: { 
          type: 'component', 
          language: 'javascript', 
          framework: 'react',
          category: 'authentication'
        }
      },
      {
        id: 'python_ml_function',
        content: 'Python machine learning function for training neural networks using TensorFlow. Includes data preprocessing, model compilation, and evaluation metrics.',
        metadata: { 
          type: 'function', 
          language: 'python', 
          domain: 'machine-learning',
          library: 'tensorflow'
        }
      },
      {
        id: 'css_responsive_styles',
        content: 'CSS responsive design styles with flexbox layout, media queries for mobile devices, and grid system for modern web applications.',
        metadata: { 
          type: 'styles', 
          language: 'css', 
          responsive: true,
          layout: 'flexbox'
        }
      },
      {
        id: 'nodejs_api_endpoint',
        content: 'Node.js Express API endpoint for handling user registration, email verification, and password reset functionality with JWT tokens.',
        metadata: { 
          type: 'endpoint', 
          language: 'javascript', 
          framework: 'express',
          feature: 'user-management'
        }
      }
    ];

    // Add documents to collection
    const ids = testDocs.map(doc => doc.id);
    const documents = testDocs.map(doc => doc.content);
    const metadatas = testDocs.map(doc => doc.metadata);

    await collection.add({
      ids: ids,
      documents: documents,
      metadatas: metadatas
    });

    console.log(`✅ Added ${testDocs.length} test documents`);

    // Test semantic search queries
    console.log('\n🔍 Testing semantic search queries...');
    
    const searchQueries = [
      {
        query: 'user authentication login',
        description: 'Authentication-related code'
      },
      {
        query: 'machine learning neural network training',
        description: 'ML and AI functionality'
      },
      {
        query: 'responsive mobile design layout',
        description: 'CSS and styling'
      },
      {
        query: 'API endpoint user registration',
        description: 'Backend API functionality'
      }
    ];

    for (const { query, description } of searchQueries) {
      console.log(`\n🔎 Searching for: "${query}" (${description})`);
      
      const results = await collection.query({
        queryTexts: [query],
        nResults: 3
      });

      console.log(`📊 Found ${results.ids[0].length} results:`);
      
      results.ids[0].forEach((id, index) => {
        const content = results.documents[0][index];
        const metadata = results.metadatas[0][index];
        const distance = results.distances[0][index];
        
        console.log(`   ${index + 1}. ID: ${id}`);
        console.log(`      Content: ${content.substring(0, 80)}...`);
        console.log(`      Distance: ${distance.toFixed(4)} (lower = more similar)`);
        console.log(`      Metadata: ${JSON.stringify(metadata)}`);
      });
    }

    // Test filtering by metadata
    console.log('\n🔍 Testing metadata filtering...');
    
    const filterResults = await collection.query({
      queryTexts: ['programming code'],
      nResults: 5,
      where: { language: 'javascript' }
    });

    console.log(`📊 JavaScript-only results: ${filterResults.ids[0].length} found`);
    filterResults.ids[0].forEach((id, index) => {
      console.log(`   - ${id}: ${filterResults.metadatas[0][index].framework || 'No framework'}`);
    });

    // Clean up test documents
    console.log('\n🧹 Cleaning up test documents...');
    await collection.delete({ ids: ids });
    console.log('✅ Test documents deleted successfully');

    console.log('\n🎉 All semantic search tests passed!');
    console.log('💡 The semantic search functionality is working correctly and ready for use.');
    
    return true;

  } catch (error) {
    console.error('❌ Semantic search test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure ChromaDB server is running: chroma run --host localhost --port 8000');
    console.log('   2. Check environment variables in .env file');
    console.log('   3. Verify ChromaDB installation: npm list chromadb');
    console.log('   4. Check if port 8000 is accessible: curl http://localhost:8000/api/v2/version');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testSemanticSearchSimple()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testSemanticSearchSimple };
