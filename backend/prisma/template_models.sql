-- Template models for the KAPI platform

-- Template table
CREATE TABLE IF NOT EXISTS templates (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  version TEXT NOT NULL DEFAULT '1.0.0',
  is_public BOOLEAN NOT NULL DEFAULT false,
  creator_id INTEGER REFERENCES users(id),
  thumbnail_url TEXT,
  metadata JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Template File table
CREATE TABLE IF NOT EXISTS template_files (
  id SERIAL PRIMARY KEY,
  template_id INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
  path TEXT NOT NULL,
  content TEXT NOT NULL,
  is_executable BOOLEAN NOT NULL DEFAULT false,
  is_binary BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE (template_id, path)
);

-- Template Variable table
CREATE TABLE IF NOT EXISTS template_variables (
  id SERIAL PRIMARY KEY,
  template_id INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  default_value TEXT,
  description TEXT,
  required BOOLEAN NOT NULL DEFAULT false,
  variable_type TEXT NOT NULL DEFAULT 'string',
  options JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE (template_id, name)
);

-- Template Tag table
CREATE TABLE IF NOT EXISTS template_tags (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Template-Tag relationship table
CREATE TABLE IF NOT EXISTS template_to_tags (
  template_id INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
  tag_id INTEGER NOT NULL REFERENCES template_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (template_id, tag_id)
);

-- Template Usage table
CREATE TABLE IF NOT EXISTS template_usages (
  id SERIAL PRIMARY KEY,
  template_id INTEGER NOT NULL REFERENCES templates(id),
  user_id INTEGER NOT NULL REFERENCES users(id),
  project_id INTEGER REFERENCES projects(id),
  variable_values JSONB,
  used_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Template Collection table
CREATE TABLE IF NOT EXISTS template_collections (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN NOT NULL DEFAULT false,
  creator_id INTEGER REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Template-Collection relationship table
CREATE TABLE IF NOT EXISTS collection_templates (
  collection_id INTEGER NOT NULL REFERENCES template_collections(id) ON DELETE CASCADE,
  template_id INTEGER NOT NULL REFERENCES templates(id) ON DELETE CASCADE,
  PRIMARY KEY (collection_id, template_id)
);

-- Project Template table
CREATE TABLE IF NOT EXISTS project_templates (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  structure JSONB NOT NULL,
  default_branch TEXT NOT NULL DEFAULT 'main',
  creator_id INTEGER REFERENCES users(id),
  is_public BOOLEAN NOT NULL DEFAULT false,
  usage_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
