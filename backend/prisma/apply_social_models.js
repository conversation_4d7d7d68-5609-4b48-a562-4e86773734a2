
  const fs = require('fs');
  const path = require('path');

  // Paths
  const schemaPath = path.join(__dirname, 'schema.prisma');
  const socialModelsPath = path.join(__dirname, 'social_models.prisma');

  // Read files
  const mainSchema = fs.readFileSync(schemaPath, 'utf8');
  const socialModels = fs.readFileSync(socialModelsPath, 'utf8');

  // Check if social models are already in the schema
  if (mainSchema.includes('model Channel {')) {
    console.log('Social models already exist in schema.prisma');
    process.exit(0);
  }

  // Find the User model definition to add relations
  const userModelRegex = /model User \{([\s\S]*?)\n\}/;
  const userMatch = mainSchema.match(userModelRegex);

  if (!userMatch) {
    console.error('Could not find User model in schema.prisma');
    process.exit(1);
  }

  const userModelContent = userMatch[1];
  const userModelWithRelations = userModelContent + `
  channelsCreated        Channel[]              @relation("ChannelCreator")
  channelMemberships     ChannelMember[]
  socialMessages         SocialMessage[]
  messageReactions       MessageReaction[]
  notifications          Notification[]`;

  // Replace the User model
  const updatedSchema = mainSchema.replace(
    userModelRegex,
    `model User {${userModelWithRelations}}`
  );

  // Find the Project model to add relations
  const projectModelRegex = /model Project \{([\s\S]*?)\n\}/;
  const projectMatch = updatedSchema.match(projectModelRegex);

  if (!projectMatch) {
    console.error('Could not find Project model in schema.prisma');
    process.exit(1);
  }

  const projectModelContent = projectMatch[1];
  const projectModelWithRelations = projectModelContent + `
  channels          Channel[]`;

  // Replace the Project model
  const finalSchema = updatedSchema.replace(
    projectModelRegex,
    `model Project {${projectModelWithRelations}}`
  );

  // Add the new models at the end
  const schemaWithSocialModels = finalSchema + '\
  ' + socialModels;

  // Write the updated schema
  fs.writeFileSync(schemaPath, schemaWithSocialModels);

  console.log('Successfully applied social models to schema.prisma');
