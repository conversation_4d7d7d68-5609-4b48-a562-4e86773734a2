const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Paths
const schemaPath = path.join(__dirname, 'schema.prisma');
const schemaBackupPath = path.join(__dirname, 'schema.prisma.backup');
const schemaFixedPath = path.join(__dirname, 'schema_fixed.prisma');

// Create a backup of the current schema
console.log('Creating backup of current schema...');
fs.copyFileSync(schemaPath, schemaBackupPath);
console.log('Backup created at', schemaBackupPath);

// Run the migration
try {
  // Create a migration for the social and template models
  console.log('Creating migration for social and template models...');
  
  // Execute the migration command
  const migrationCommand = 'npx prisma migrate dev --name add_social_and_template_models --schema=./prisma/schema_fixed.prisma';
  console.log(`Executing: ${migrationCommand}`);
  execSync(migrationCommand, { stdio: 'inherit' });
  
  console.log('Migration completed successfully!');
  
  // Restore the original schema but with the new models
  console.log('Updating schema.prisma with the new models...');
  
  // Run prisma db pull to update the schema with the new models
  console.log('Running prisma db pull to update schema...');
  execSync('npx prisma db pull', { stdio: 'inherit' });
  
  // Format the schema
  console.log('Formatting schema...');
  execSync('npx prisma format', { stdio: 'inherit' });
  
  console.log('Schema updated successfully!');
  
} catch (error) {
  console.error('Error during migration:', error.message);
  
  // Restore the backup if there was an error
  console.log('Restoring schema from backup...');
  fs.copyFileSync(schemaBackupPath, schemaPath);
  console.log('Schema restored from backup.');
  
  process.exit(1);
}
