// Script to apply template models to the schema
const fs = require('fs');
const path = require('path');

// Paths
const schemaPath = path.join(__dirname, 'schema.prisma');
const templateModelsPath = path.join(__dirname, 'template_models.prisma');
const fixSchemaPath = path.join(__dirname, 'fix_schema.js');

// First run the fix script to ensure we don't have duplicates
require(fixSchemaPath);

// Read files
let mainSchema = fs.readFileSync(schemaPath, 'utf8');
const templateModels = fs.readFileSync(templateModelsPath, 'utf8');

// Check if template models already exist
if (mainSchema.includes('model Template {')) {
  console.log('Template models already exist in schema.prisma');
  process.exit(0);
}

// First remove any duplicate model definitions
const userModelRegex = /model User \{([\s\S]*?)\n\}/;
const userMatch = mainSchema.match(userModelRegex);

if (!userMatch) {
  console.error('Could not find User model in schema.prisma');
  process.exit(1);
}

// Extract User model content without duplicates
const userModelContent = userMatch[1];

// Add template relations to User model
const userTemplateRelations = `
  createdTemplates       Template[]
  createdCollections     TemplateCollection[]
  templateUsages         TemplateUsage[]
  projectTemplates       ProjectTemplate[]`;

// Create updated User model with template relations
const updatedUserModel = `model User {${userModelContent}${userTemplateRelations}
}`;

// Replace User model in schema
mainSchema = mainSchema.replace(userModelRegex, updatedUserModel);

// Do the same for Project model
const projectModelRegex = /model Project \{([\s\S]*?)\n\}/;
const projectMatch = mainSchema.match(projectModelRegex);

if (!projectMatch) {
  console.error('Could not find Project model in schema.prisma');
  process.exit(1);
}

// Extract Project model content
const projectModelContent = projectMatch[1];

// Add template relations to Project model
const projectTemplateRelations = `
  templateUsages         TemplateUsage[]`;

// Create updated Project model with template relations
const updatedProjectModel = `model Project {${projectModelContent}${projectTemplateRelations}
}`;

// Replace Project model in schema
mainSchema = mainSchema.replace(projectModelRegex, updatedProjectModel);

// Remove User and Project extensions from template models
const templateModelsWithoutExtensions = templateModels
  .replace(/\/\/ User relation extensions[\s\S]*?}/, '')
  .replace(/\/\/ Project relation extensions[\s\S]*?}/, '');

// Add template models to schema
const updatedSchema = mainSchema + '\n' + templateModelsWithoutExtensions;

// Write updated schema
fs.writeFileSync(schemaPath, updatedSchema);

console.log('Successfully applied template models to schema.prisma');
