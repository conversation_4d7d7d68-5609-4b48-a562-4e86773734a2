// Payment & Subscription models for the KAPI platform
model Payment {
  id                Int                @id @default(autoincrement())
  userId            Int                @map("user_id")
  amount            Float
  currency          String             @default("USD")
  status            PaymentStatus      @default(PENDING)
  provider          PaymentProvider
  providerPaymentId String?            @map("provider_payment_id")
  paymentMethod     String?            @map("payment_method")
  description       String?
  metadata          Json?
  refunded          Boolean            @default(false)
  refundAmount      Float?             @map("refund_amount")
  webhookResponse   Json?              @map("webhook_response")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @default(now()) @updatedAt @map("updated_at")
  user              User               @relation(fields: [userId], references: [id])
  subscriptionPayments SubscriptionPayment[]
  refunds           PaymentRefund[]

  @@map("payments")
}

model PaymentRefund {
  id                Int                @id @default(autoincrement())
  paymentId         Int                @map("payment_id")
  amount            Float
  reason            String?
  status            RefundStatus       @default(PENDING)
  providerRefundId  String?            @map("provider_refund_id")
  createdAt         DateTime           @default(now()) @map("created_at")
  processedAt       DateTime?          @map("processed_at")
  metadata          Json?
  payment           Payment            @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@map("payment_refunds")
}

model Subscription {
  id                Int                @id @default(autoincrement())
  userId            Int                @map("user_id")
  planId            String             @map("plan_id")
  status            SubscriptionStatus @default(ACTIVE)
  startDate         DateTime           @map("start_date")
  endDate           DateTime?          @map("end_date")
  renewalDate       DateTime?          @map("renewal_date")
  canceledAt        DateTime?          @map("canceled_at")
  trialEndDate      DateTime?          @map("trial_end_date")
  isOnTrial         Boolean            @default(false) @map("is_on_trial")
  providerSubId     String?            @map("provider_subscription_id")
  providerCustomerId String?           @map("provider_customer_id")
  metadata          Json?
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @default(now()) @updatedAt @map("updated_at")
  user              User               @relation(fields: [userId], references: [id])
  events            SubscriptionEvent[]
  payments          SubscriptionPayment[]

  @@map("subscriptions")
}

model SubscriptionEvent {
  id                Int                @id @default(autoincrement())
  subscriptionId    Int                @map("subscription_id")
  eventType         SubscriptionEventType
  description       String?
  metadata          Json?
  createdAt         DateTime           @default(now()) @map("created_at")
  subscription      Subscription       @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("subscription_events")
}

model SubscriptionPayment {
  id                Int                @id @default(autoincrement())
  subscriptionId    Int                @map("subscription_id")
  paymentId         Int                @map("payment_id")
  billingPeriodStart DateTime          @map("billing_period_start")
  billingPeriodEnd  DateTime           @map("billing_period_end")
  createdAt         DateTime           @default(now()) @map("created_at")
  subscription      Subscription       @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  payment           Payment            @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@map("subscription_payments")
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentProvider {
  STRIPE
  PAYPAL
  MANUAL
}

enum RefundStatus {
  PENDING
  COMPLETED
  FAILED
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PAST_DUE
  UNPAID
  TRIALING
}

enum SubscriptionEventType {
  CREATED
  RENEWED
  CANCELED
  PAYMENT_FAILED
  PAYMENT_SUCCEEDED
  TRIAL_STARTED
  TRIAL_ENDED
  PLAN_CHANGED
}

// User relation extensions
model User {
  payments          Payment[]
  subscriptions     Subscription[]
}