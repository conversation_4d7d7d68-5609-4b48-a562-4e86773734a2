// Template models for the KAPI platform
model Template {
  id               Int                @id @default(autoincrement())
  name             String
  description      String?
  category         String
  version          String             @default("1.0.0")
  isPublic         Boolean            @default(false) @map("is_public")
  creatorId        Int?               @map("creator_id")
  thumbnailUrl     String?            @map("thumbnail_url")
  metadata         Json?
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  creator          User?              @relation(fields: [creatorId], references: [id])
  files            TemplateFile[]
  tags             TemplateTag[]
  usages           TemplateUsage[]
  variables        TemplateVariable[]
  collections      TemplateCollection[] @relation("CollectionTemplates")

  @@map("templates")
}

model TemplateFile {
  id               Int                @id @default(autoincrement())
  templateId       Int                @map("template_id")
  path             String
  content          String
  isExecutable     Boolean            @default(false) @map("is_executable")
  isBinary         Boolean            @default(false) @map("is_binary")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  template         Template           @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, path])
  @@map("template_files")
}

model TemplateVariable {
  id               Int                @id @default(autoincrement())
  templateId       Int                @map("template_id")
  name             String
  defaultValue     String?            @map("default_value")
  description      String?
  required         Boolean            @default(false)
  variableType     String             @default("string") @map("variable_type")
  options          Json?
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  template         Template           @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, name])
  @@map("template_variables")
}

model TemplateTag {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  description      String?
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  templates        Template[]

  @@map("template_tags")
}

model TemplateUsage {
  id               Int                @id @default(autoincrement())
  templateId       Int                @map("template_id")
  userId           Int                @map("user_id")
  projectId        Int?               @map("project_id")
  variableValues   Json?              @map("variable_values")
  usedAt           DateTime           @default(now()) @map("used_at")
  template         Template           @relation(fields: [templateId], references: [id])
  user             User               @relation(fields: [userId], references: [id])
  project          Project?           @relation(fields: [projectId], references: [id])

  @@map("template_usages")
}

model TemplateCollection {
  id               Int                @id @default(autoincrement())
  name             String
  description      String?
  isPublic         Boolean            @default(false) @map("is_public")
  creatorId        Int?               @map("creator_id")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  creator          User?              @relation(fields: [creatorId], references: [id])
  templates        Template[]         @relation("CollectionTemplates")

  @@map("template_collections")
}

model ProjectTemplate {
  id               Int                @id @default(autoincrement())
  name             String
  description      String?
  structure        Json
  defaultBranch    String             @default("main") @map("default_branch")
  creatorId        Int?               @map("creator_id")
  isPublic         Boolean            @default(false) @map("is_public")
  usageCount       Int                @default(0) @map("usage_count")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  creator          User?              @relation(fields: [creatorId], references: [id])

  @@map("project_templates")
}

// User relation extensions for Template models
model User {
  createdTemplates       Template[]
  createdCollections     TemplateCollection[]
  templateUsages         TemplateUsage[]
  projectTemplates       ProjectTemplate[]
}

// Project relation extensions for Template models
model Project {
  templateUsages         TemplateUsage[]
}