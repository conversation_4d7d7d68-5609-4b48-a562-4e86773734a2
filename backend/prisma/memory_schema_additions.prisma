// Memory Architecture Schema Additions

// New enums
enum ContextType {
  USER
  BUSINESS
  TECHNICAL
  CODE
  TASK
}

enum SlideDeckType {
  BUSINESS
  TECHNICAL
  USER
  CODE
  TASK
}

enum ContextFormat {
  MARKDOWN
  YAML
  JSON
  MERMAID
}

// Memory system models
model SlideDeck {
  id                Int           @id @default(autoincrement())
  projectId         Int           @map("project_id")
  title             String
  description       String?
  type              SlideDeckType
  tokenCount        Int?          @map("token_count")
  lastUpdated       DateTime      @default(now()) @map("last_updated")
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  isActive          Boolean       @default(true) @map("is_active")
  metadata          Json?
  
  // Relationships
  project           Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  slides            MemorySlide[]
  memoryContexts    MemoryContext[]

  @@map("slide_decks")
}

model MemorySlide {
  id                Int           @id @default(autoincrement())
  slideDeckId       Int           @map("slide_deck_id")
  title             String?
  content           String        // Markdown content
  order             Int
  format            ContextFormat @default(MARKDOWN)
  tokenCount        Int?          @map("token_count")
  lastUpdated       DateTime      @default(now()) @map("last_updated")
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  slideDeck         SlideDeck     @relation(fields: [slideDeckId], references: [id], onDelete: Cascade)

  @@map("memory_slides")
}

model MemoryContext {
  id                Int           @id @default(autoincrement())
  projectId         Int           @map("project_id")
  slideDeckId       Int?          @map("slide_deck_id")
  contextType       ContextType
  name              String
  description       String?
  tokenCount        Int?          @map("token_count")
  lastUpdated       DateTime      @default(now()) @map("last_updated")
  lastSyncedAt      DateTime?     @map("last_synced_at")
  version           Int           @default(1)
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  isActive          Boolean       @default(true) @map("is_active")
  metadata          Json?
  
  // Relationships
  project           Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  slideDeck         SlideDeck?    @relation(fields: [slideDeckId], references: [id])
  versions          ContextVersion[]

  @@map("memory_contexts")
}

model ContextVersion {
  id                Int           @id @default(autoincrement())
  contextId         Int           @map("context_id")
  version           Int
  content           String        // Full context content at this version
  format            ContextFormat @default(MARKDOWN)
  tokenCount        Int?          @map("token_count")
  changedBy         String?       @map("changed_by")
  changeReason      String?       @map("change_reason")
  createdAt         DateTime      @default(now()) @map("created_at")
  
  // Relationships
  memoryContext     MemoryContext @relation(fields: [contextId], references: [id], onDelete: Cascade)

  @@map("context_versions")
  @@unique([contextId, version])
}

model ContextRegistry {
  id                Int           @id @default(autoincrement())
  name              String        @unique
  description       String?
  contextType       ContextType
  isEnabled         Boolean       @default(true) @map("is_enabled")
  priority          Int           @default(5)
  maxTokens         Int           @default(300) @map("max_tokens")
  defaultFormat     ContextFormat @default(MARKDOWN) @map("default_format")
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  metadata          Json?
  
  @@map("context_registry")
}

model FileSummary {
  id                Int           @id @default(autoincrement())
  projectId         Int           @map("project_id")
  filePath          String        @map("file_path")
  summary           String
  tokenCount        Int?          @map("token_count")
  lastUpdated       DateTime      @default(now()) @map("last_updated")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  gitHash           String?       @map("git_hash")
  isStale           Boolean       @default(false) @map("is_stale")
  
  // Relationships
  project           Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("file_summaries")
  @@unique([projectId, filePath])
}

model ModuleSummary {
  id                Int           @id @default(autoincrement())
  projectId         Int           @map("project_id")
  modulePath        String        @map("module_path")
  summary           String
  tokenCount        Int?          @map("token_count")
  lastUpdated       DateTime      @default(now()) @map("last_updated")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  isStale           Boolean       @default(false) @map("is_stale")
  
  // Relationships
  project           Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("module_summaries")
  @@unique([projectId, modulePath])
}

model ChatContext {
  id                Int           @id @default(autoincrement())
  conversationId    Int           @map("conversation_id")
  contextSnapshot   Json          @map("context_snapshot") // Snapshot of context used
  contextSelectionRationale String? @map("context_selection_rationale")
  tokenUsage        Int?          @map("token_usage")
  createdAt         DateTime      @default(now()) @map("created_at")
  
  // Relationships
  conversation      Conversation  @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("chat_contexts")
}

model UserContextPreference {
  id                Int           @id @default(autoincrement())
  userId            Int           @map("user_id")
  contextType       ContextType
  isPinned          Boolean       @default(false) @map("is_pinned")
  isDisabled        Boolean       @default(false) @map("is_disabled") 
  priority          Int?
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_context_preferences")
  @@unique([userId, contextType])
}

// Add relationship fields to existing models
model Project {
  // Add these to your existing Project model
  slideDecks        SlideDeck[]
  memoryContexts    MemoryContext[]
  fileSummaries     FileSummary[]
  moduleSummaries   ModuleSummary[]
}

model User {
  // Add these to your existing User model
  contextPreferences UserContextPreference[]
}

model Conversation {
  // Add these to your existing Conversation model
  chatContexts      ChatContext[]
}