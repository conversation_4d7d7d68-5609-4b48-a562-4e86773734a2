const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
require('dotenv').config();

// Get database connection string from environment
const databaseUrl = process.env.DATABASE_URL;

// Paths to SQL files
const socialModelsPath = path.join(__dirname, 'social_models.sql');
const templateModelsPath = path.join(__dirname, 'template_models.sql');
const advancedProjectModelsPath = path.join(__dirname, 'advanced_project_models.sql');
const paymentModelsPath = path.join(__dirname, 'payment_models.sql');

// Function to execute SQL file
async function executeSqlFile(filePath) {
  console.log(`Executing SQL file: ${filePath}`);

  // Read SQL file
  const sql = fs.readFileSync(filePath, 'utf8');

  // Connect to database
  const client = new Client({
    connectionString: databaseUrl
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Execute SQL
    await client.query(sql);
    console.log('SQL executed successfully');

  } catch (error) {
    console.error('Error executing SQL:', error.message);
    throw error;
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

// Main function
async function main() {
  try {
    // Execute social models SQL
    console.log('Applying social models...');
    await executeSqlFile(socialModelsPath);

    // Execute template models SQL
    console.log('Applying template models...');
    await executeSqlFile(templateModelsPath);

    // Execute advanced project models SQL
    console.log('Applying advanced project models...');
    await executeSqlFile(advancedProjectModelsPath);

    // Execute payment models SQL
    console.log('Applying payment models...');
    await executeSqlFile(paymentModelsPath);

    // Copy clean schema
    console.log('Copying clean schema...');
    fs.copyFileSync(path.join(__dirname, 'schema_clean.prisma'), path.join(__dirname, 'schema.prisma'));

    // Update Prisma schema
    console.log('Updating Prisma schema...');
    execSync('npx prisma db pull --force', { stdio: 'inherit' });

    // Format schema
    console.log('Formatting schema...');
    execSync('npx prisma format', { stdio: 'inherit' });

    // Generate Prisma client
    console.log('Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    console.log('All models applied successfully!');

  } catch (error) {
    console.error('Error applying models:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
