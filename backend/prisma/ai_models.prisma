// This file contains the additions to be applied to the main schema.prisma
// AI Integration models
model AIAgent {
  id                Int              @id @default(autoincrement())
  name              String
  description       String?
  provider          String
  modelName         String           @map("model_name")
  systemPrompt      String?          @map("system_prompt")
  temperature       Float            @default(0.7)
  maxTokens         Int              @default(4000) @map("max_tokens")
  capabilities      Json             @default("[]")
  dailyTokenLimit   Int              @default(100000) @map("daily_token_limit")
  monthlyTokenLimit Int?             @map("monthly_token_limit")
  costPerInputToken Float            @default(0.0) @map("cost_per_1k_tokens_input")
  costPerOutputToken Float           @default(0.0) @map("cost_per_1k_tokens_output")
  userId            Int?             @map("user_id")
  projectId         Int?             @map("project_id")
  isActive          Boolean          @default(true) @map("is_active")
  createdAt         DateTime         @default(now()) @map("created_at")
  updatedAt         DateTime         @default(now()) @updatedAt @map("updated_at")
  user              User?            @relation(fields: [userId], references: [id])
  project           Project?         @relation(fields: [projectId], references: [id])
  conversations     Conversation[]
  actions           AIAgentAction[]

  @@map("ai_agents")
}

model AIAgentAction {
  id             Int       @id @default(autoincrement())
  agentId        Int       @map("agent_id")
  actionType     String    @map("action_type")
  input          String?
  output         String?
  executionTime  Float?    @map("execution_time")
  status         String    @default("completed")
  errorMessage   String?   @map("error_message")
  createdAt      DateTime  @default(now()) @map("created_at")
  metadata       Json?
  agent          AIAgent   @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("ai_agent_actions")
}

model AIPromptTemplate {
  id           Int       @id @default(autoincrement())
  name         String
  description  String?
  systemPrompt String?   @map("system_prompt")
  template     String
  parameters   Json      @default("[]")
  userId       Int?      @map("user_id")
  isPublic     Boolean   @default(false) @map("is_public")
  version      Int       @default(1)
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at")
  user         User?     @relation(fields: [userId], references: [id])

  @@map("ai_prompt_templates")
}

// Workshop models
enum WorkshopType {
  ESSENTIALS
  PRACTITIONER
  VIBECODERS
  AGENTIC
}

enum ParticipationStatus {
  REGISTERED
  PAID
  CONFIRMED
  ATTENDED
  COMPLETED
  CANCELLED
}

model Workshop {
  id                 Int                @id @default(autoincrement())
  name               String
  description        String?
  workshopType       WorkshopType       @map("workshop_type")
  startDate          DateTime           @map("start_date")
  endDate            DateTime           @map("end_date")
  syllabusLink       String?            @map("syllabus_link")
  geography          String?
  paymentLink        String?            @map("payment_link")
  cost               Float?
  maxParticipants    Int?               @map("max_participants")
  isActive           Boolean            @default(true) @map("is_active")
  enrollmentDeadline DateTime?          @map("enrollment_deadline")
  prerequisites      String?
  instructorIds      Json               @default("[]") @map("instructor_ids")
  timezone           String             @default("UTC")
  schedule           Json?
  isOnline           Boolean            @default(true) @map("is_online")
  venueDetails       Json?              @map("venue_details")
  featured           Boolean            @default(false)
  createdAt          DateTime           @default(now()) @map("created_at")
  updatedAt          DateTime           @default(now()) @updatedAt @map("updated_at")
  participants       WorkshopParticipant[]
  materials          WorkshopMaterial[]
  modules            Module[]
  resources          Resource[]

  @@map("workshops")
}

model WorkshopParticipant {
  workshopId           Int                  @map("workshop_id")
  userId               Int                  @map("user_id")
  registrationDate     DateTime             @default(now()) @map("registration_date")
  status               ParticipationStatus  @default(REGISTERED)
  paymentStatus        String               @default("unpaid") @map("payment_status")
  paymentId            Int?                 @map("payment_id")
  completionPercentage Int                  @default(0) @map("completion_percentage")
  feedbackSubmitted    Boolean              @default(false) @map("feedback_submitted")
  notes                String?
  lastUpdated          DateTime             @default(now()) @updatedAt @map("last_updated")
  workshop             Workshop             @relation(fields: [workshopId], references: [id], onDelete: Cascade)
  user                 User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([workshopId, userId])
  @@map("workshop_participants")
}

model WorkshopMaterial {
  id          Int      @id @default(autoincrement())
  workshopId  Int      @map("workshop_id")
  title       String
  description String?
  materialType String   @map("material_type")
  filePath    String?   @map("file_path")
  externalUrl String?   @map("external_url")
  orderIndex  Int       @default(0) @map("order_index")
  isRequired  Boolean   @default(false) @map("is_required")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  workshop    Workshop  @relation(fields: [workshopId], references: [id], onDelete: Cascade)

  @@map("workshop_materials")
}

// Module and Lesson models
enum LessonType {
  LECTURE
  INTERACTIVE
  LAB
  QUIZ
  PROJECT
  DISCUSSION
}

model Module {
  id          Int       @id @default(autoincrement())
  title       String
  description String?
  orderIndex  Int       @default(0) @map("order_index")
  workshopId  Int       @map("workshop_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  workshop    Workshop  @relation(fields: [workshopId], references: [id], onDelete: Cascade)
  lessons     Lesson[]

  @@map("modules")
}

model Lesson {
  id              Int           @id @default(autoincrement())
  title           String
  description     String?
  content         String?
  lessonType      LessonType    @default(LECTURE) @map("lesson_type")
  durationMinutes Int           @default(30) @map("duration_minutes")
  orderIndex      Int           @default(0) @map("order_index")
  moduleId        Int           @map("module_id")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @default(now()) @updatedAt @map("updated_at")
  module          Module        @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  completions     LessonCompletion[]

  @@map("lessons")
}

model LessonCompletion {
  id          Int       @id @default(autoincrement())
  completedAt DateTime  @default(now()) @map("completed_at")
  score       Float?
  feedback    String?
  userId      Int       @map("user_id")
  lessonId    Int       @map("lesson_id")
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson      Lesson    @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([userId, lessonId])
  @@map("lesson_completions")
}

model Resource {
  id          Int       @id @default(autoincrement())
  title       String
  description String?
  type        String
  url         String?
  content     String?
  isRequired  Boolean   @default(false) @map("is_required")
  workshopId  Int       @map("workshop_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  workshop    Workshop  @relation(fields: [workshopId], references: [id], onDelete: Cascade)

  @@map("resources")
}

// Gamification Models
model EloHistory {
  id                   Int                @id @default(autoincrement())
  userId               Int                @map("user_id")
  previousRating       Int                @map("previous_rating")
  newRating            Int                @map("new_rating")
  change               Int
  reason               String
  programmingSessionId Int?               @map("programming_session_id")
  opponentId           Int?               @map("opponent_id")
  timestamp            DateTime           @default(now())
  user                 User               @relation("UserEloHistory", fields: [userId], references: [id])
  opponent             User?              @relation("OpponentEloHistory", fields: [opponentId], references: [id])
  programmingSession   ProgrammingSession? @relation(fields: [programmingSessionId], references: [id])

  @@map("elo_history")
}

// Q&A Models
model Tag {
  id          Int         @id @default(autoincrement())
  name        String      @unique
  description String?
  category    String?
  questions   Question[]  @relation("QuestionTags")

  @@map("tags")
}

model Question {
  id               Int           @id @default(autoincrement())
  title            String
  content          String
  codeSnippet      String?       @map("code_snippet")
  codeLanguage     String?       @map("code_language")
  timestamp        DateTime      @default(now())
  updatedAt        DateTime?     @updatedAt @map("updated_at")
  isResolved       Boolean       @default(false) @map("is_resolved")
  acceptedAnswerId Int?          @map("accepted_answer_id")
  viewCount        Int           @default(0) @map("view_count")
  projectId        Int?          @map("project_id")
  contextType      String?       @map("context_type")
  contextId        Int?          @map("context_id")
  authorId         Int           @map("author_id")
  author           User          @relation(fields: [authorId], references: [id], onDelete: Cascade)
  answers          Answer[]
  project          Project?      @relation(fields: [projectId], references: [id])
  tags             Tag[]         @relation("QuestionTags")
  votes            QuestionVote[]
  
  @@map("questions")
}

model Answer {
  id              Int          @id @default(autoincrement())
  content         String
  codeSnippet     String?      @map("code_snippet")
  codeLanguage    String?      @map("code_language")
  timestamp       DateTime     @default(now())
  updatedAt       DateTime?    @updatedAt @map("updated_at")
  isAccepted      Boolean      @default(false) @map("is_accepted")
  questionId      Int          @map("question_id")
  authorId        Int          @map("author_id")
  question        Question     @relation(fields: [questionId], references: [id], onDelete: Cascade)
  author          User         @relation(fields: [authorId], references: [id], onDelete: Cascade)
  votes           AnswerVote[]

  @@map("answers")
}

model QuestionVote {
  id         Int      @id @default(autoincrement())
  questionId Int      @map("question_id")
  userId     Int      @map("user_id")
  voteValue  Int      @map("vote_value")
  createdAt  DateTime @default(now()) @map("created_at")
  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([questionId, userId])
  @@map("question_votes")
}

model AnswerVote {
  id         Int      @id @default(autoincrement())
  answerId   Int      @map("answer_id")
  userId     Int      @map("user_id")
  voteValue  Int      @map("vote_value")
  createdAt  DateTime @default(now()) @map("created_at")
  answer     Answer   @relation(fields: [answerId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([answerId, userId])
  @@map("answer_votes")
}

model KarmaTransaction {
  id                Int      @id @default(autoincrement())
  userId            Int      @map("user_id")
  amount            Int
  reason            String
  relatedEntityType String?  @map("related_entity_type")
  relatedEntityId   Int?     @map("related_entity_id")
  createdAt         DateTime @default(now()) @map("created_at")
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("karma_transactions")
}

model Bookmark {
  id         Int      @id @default(autoincrement())
  userId     Int      @map("user_id")
  entityType String   @map("entity_type")
  entityId   Int      @map("entity_id")
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, entityType, entityId])
  @@map("bookmarks")
}

// Existing model extensions
model Conversation {
  agentId           Int?             @map("agent_id")
  filePaths         Json?            @default("[]") @map("file_paths")
  totalTokens       Int?             @default(0) @map("total_tokens")
  totalCost         Float?           @default(0.0) @map("total_cost")
  agent             AIAgent?         @relation(fields: [agentId], references: [id])
}

model Message {
  codeLanguage      String?          @map("code_language")
  codeSnippet       String?          @map("code_snippet")
  filePath          String?          @map("file_path")
  lineStart         Int?             @map("line_start")
  lineEnd           Int?             @map("line_end")
}

// User relation extensions
model User {
  aiAgents          AIAgent[]
  aiPromptTemplates AIPromptTemplate[]
  eloHistories      EloHistory[]      @relation("UserEloHistory")
  eloOpponents      EloHistory[]      @relation("OpponentEloHistory")
  questions         Question[]
  answers           Answer[]
  questionVotes     QuestionVote[]
  answerVotes       AnswerVote[]
  karmaTransactions KarmaTransaction[]
  bookmarks         Bookmark[]
  workshopParticipations WorkshopParticipant[]
  lessonCompletions LessonCompletion[]
}