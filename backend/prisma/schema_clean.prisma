generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                                         Int                     @id @default(autoincrement())
  email                                      String                  @unique
  username                                   String?                 @unique
  firstName                                  String?                 @map("first_name")
  lastName                                   String?                 @map("last_name")
  clerkId                                    String                  @unique @map("clerk_id")
  role                                       UserRole                @default(FREE)
  isActive                                   Boolean                 @default(true) @map("is_active")
  bio                                        String?
  profileImageUrl                            String?                 @map("profile_image_url")
  emailVerified                              Boolean                 @default(false) @map("email_verified")
  emailVerificationToken                     String?                 @map("email_verification_token")
  emailVerificationSentAt                    DateTime?               @map("email_verification_sent_at")
  eloRating                                  Int                     @default(1200) @map("elo_rating")
  karma                                      Int                     @default(10)
  skillsGraph                                Json?                   @map("skills_graph")
  totalChallengesCompleted                   Int                     @default(0) @map("total_challenges_completed")
  streakCount                                Int                     @default(0) @map("streak_count")
  lastActiveDate                             DateTime?               @map("last_active_date")
  referredBy                                 Int?                    @map("referred_by")
  failedLoginAttempts                        Int                     @default(0) @map("failed_login_attempts")
  accountLockedUntil                         DateTime?               @map("account_locked_until")
  lastPasswordChange                         DateTime?               @map("last_password_change")
  dailyLlmTokenQuota                         Int                     @default(10000) @map("daily_llm_token_quota")
  dailyLlmTokenUsage                         Int                     @default(0) @map("daily_llm_token_usage")
  quotaResetDate                             DateTime                @default(now()) @map("quota_reset_date")
  createdAt                                  DateTime                @default(now()) @map("created_at")
  updatedAt                                  DateTime                @default(now()) @updatedAt @map("updated_at")
  lastLogin                                  DateTime?               @map("last_login")
  preferredIde                               String?                 @map("preferred_ide")
  learningStyle                              String?                 @map("learning_style")
  developerStrengths                         Json                    @default("[]") @map("developer_strengths")
  preferredAiModels                          Json                    @default("[]") @map("preferred_ai_models")
  additionalInfo                             String?                 @map("additional_info")
  onboardingCompleted                        Boolean                 @default(false) @map("onboarding_completed")
  onboardingCompletedAt                      DateTime?               @map("onboarding_completed_at")
  ai_agents                                  ai_agents[]
  ai_prompt_templates                        ai_prompt_templates[]
  answer_votes                               answer_votes[]
  answers                                    answers[]
  blogPosts                                  BlogPost[]
  bookmarks                                  bookmarks[]
  conversations                              Conversation[]
  elo_history_elo_history_opponent_idTousers elo_history[]           @relation("elo_history_opponent_idTousers")
  elo_history_elo_history_user_idTousers     elo_history[]           @relation("elo_history_user_idTousers")
  karma_transactions                         karma_transactions[]
  lesson_completions                         lesson_completions[]
  llmUsages                                  ModelUsage[]
  programmingSessions                        ProgrammingSession[]
  projects                                   Project[]
  question_votes                             question_votes[]
  questions                                  questions[]
  contextPreferences                         UserContextPreference[]
  completedOnboardingPartner                 UserOnboardingState[]   @relation("OnboardingPartner")
  userOnboardingStates                       UserOnboardingState[]
  sessions                                   UserSession[]
  referrer                                   User?                   @relation("UserReferrals", fields: [referredBy], references: [id])
  referredUsers                              User[]                  @relation("UserReferrals")
  workshop_participants                      workshop_participants[]

  @@map("users")
}

enum UserRole {
  FREE
  DEVELOPER
  ADMIN
}

enum ProjectType {
  WEB_APPLICATION
  MOBILE_APPLICATION
  LIBRARY
  CLI_TOOL
  API
  OTHER
}

enum ProjectMotivationType {
  learner
  contributor
  builder
}

enum CodeType {
  fresh
  legacy
  throwaway
}

enum ConversationStatus {
  active
  archived
}

enum ConversationCategory {
  chat
  agent
  code_assist
  debugging
  planning
  learning
  other
}

enum AgentStatus {
  initializing
  running
  paused
  completed
  failed
  waiting
  cancelled
}

enum LastActivityType {
  thinking
  command
  file_edit
  complete
  error
  user_input
}

enum TaskType {
  chat
  code_review
  code_gen_big
  code_gen_agentic
  svg_mockup
  slides
  test_cases
  general
}

enum Provider {
  bedrock
  azure
  google
  other
}

enum ContextType {
  USER
  BUSINESS
  TECHNICAL
  CODE
  TASK
}

enum SlideDeckType {
  BUSINESS
  TECHNICAL
  USER
  CODE
  TASK
}

enum ContextFormat {
  MARKDOWN
  YAML
  JSON
  MERMAID
}

enum LessonType {
  LECTURE
  INTERACTIVE
  LAB
  QUIZ
  PROJECT
  DISCUSSION
}

enum ParticipationStatus {
  REGISTERED
  PAID
  CONFIRMED
  ATTENDED
  COMPLETED
  CANCELLED
}

enum WorkshopType {
  ESSENTIALS
  PRACTITIONER
  VIBECODERS
  AGENTIC
}
