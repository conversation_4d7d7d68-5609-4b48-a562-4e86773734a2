generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ai_agent_actions {
  id             Int       @id @default(autoincrement())
  agent_id       Int
  action_type    String    @db.VarChar
  input          String?
  output         String?
  execution_time Float?
  status         String    @default("completed") @db.VarChar
  error_message  String?
  created_at     DateTime  @default(now())
  metadata       Json?
  ai_agents      ai_agents @relation(fields: [agent_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model ai_agents {
  id                        Int                @id @default(autoincrement())
  name                      String             @db.VarChar
  description               String?
  provider                  String             @db.VarChar
  model_name                String             @db.VarChar
  system_prompt             String?
  temperature               Float              @default(0.7)
  max_tokens                Int                @default(4000)
  capabilities              Json               @default("[]")
  daily_token_limit         Int                @default(100000)
  monthly_token_limit       Int?
  cost_per_1k_tokens_input  Float              @default(0.0)
  cost_per_1k_tokens_output Float              @default(0.0)
  user_id                   Int?
  project_id                Int?
  is_active                 Boolean            @default(true)
  created_at                DateTime           @default(now())
  updated_at                DateTime           @default(now())
  ai_agent_actions          ai_agent_actions[]
  projects                  projects?          @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users                     users?             @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  conversations             conversations[]
}

model ai_prompt_templates {
  id            Int      @id @default(autoincrement())
  name          String   @db.VarChar
  description   String?
  system_prompt String?
  template      String
  parameters    Json     @default("[]")
  user_id       Int?
  is_public     Boolean  @default(false)
  version       Int      @default(1)
  created_at    DateTime @default(now())
  updated_at    DateTime @default(now())
  users         users?   @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model answer_votes {
  id         Int      @id @default(autoincrement())
  answer_id  Int
  user_id    Int
  vote_value Int
  created_at DateTime @default(now())
  answers    answers  @relation(fields: [answer_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users      users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([answer_id, user_id])
}

model answers {
  id            Int            @id @default(autoincrement())
  content       String
  code_snippet  String?
  code_language String?        @db.VarChar
  timestamp     DateTime       @default(now())
  updated_at    DateTime?
  is_accepted   Boolean        @default(false)
  question_id   Int
  author_id     Int
  answer_votes  answer_votes[]
  users         users          @relation(fields: [author_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  questions     questions      @relation(fields: [question_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model blog_post_tags {
  blog_post_id Int
  tag_id       Int
  blog_posts   blog_posts @relation(fields: [blog_post_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  tags         tags       @relation(fields: [tag_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([blog_post_id, tag_id])
}

model blog_posts {
  id             Int              @id @default(autoincrement())
  title          String
  content        String
  slug           String           @unique
  author_id      Int
  published      Boolean          @default(false)
  published_at   DateTime?
  created_at     DateTime         @default(now())
  updated_at     DateTime         @default(now())
  blog_post_tags blog_post_tags[]
  users          users            @relation(fields: [author_id], references: [id])
}

model bookmarks {
  id          Int      @id @default(autoincrement())
  user_id     Int
  entity_type String   @db.VarChar
  entity_id   Int
  created_at  DateTime @default(now())
  users       users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, entity_type, entity_id])
}

model channel_members {
  channel_id   Int
  user_id      Int
  joined_at    DateTime  @default(now()) @db.Timestamp(6)
  role         String    @default("member")
  last_read_at DateTime? @db.Timestamp(6)
  channels     channels  @relation(fields: [channel_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users        users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([channel_id, user_id])
}

model channels {
  id              Int               @id @default(autoincrement())
  name            String
  description     String?
  is_private      Boolean           @default(false)
  created_at      DateTime          @default(now()) @db.Timestamp(6)
  updated_at      DateTime          @default(now()) @db.Timestamp(6)
  creator_id      Int
  project_id      Int?
  channel_members channel_members[]
  users           users             @relation(fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  projects        projects?         @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  social_messages social_messages[]
}

model chat_contexts {
  id                          Int           @id @default(autoincrement())
  conversation_id             Int
  context_snapshot            Json
  context_selection_rationale String?
  token_usage                 Int?
  created_at                  DateTime      @default(now())
  conversations               conversations @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
}

model collection_templates {
  collection_id        Int
  template_id          Int
  template_collections template_collections @relation(fields: [collection_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  templates            templates            @relation(fields: [template_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([collection_id, template_id])
}

model context_registry {
  id             Int           @id @default(autoincrement())
  name           String        @unique
  description    String?
  contextType    ContextType
  is_enabled     Boolean       @default(true)
  priority       Int           @default(5)
  max_tokens     Int           @default(300)
  default_format ContextFormat @default(MARKDOWN)
  created_at     DateTime      @default(now())
  updated_at     DateTime      @default(now())
  metadata       Json?
}

model context_versions {
  id              Int             @id @default(autoincrement())
  context_id      Int
  version         Int
  content         String
  format          ContextFormat   @default(MARKDOWN)
  token_count     Int?
  changed_by      String?
  change_reason   String?
  created_at      DateTime        @default(now())
  memory_contexts memory_contexts @relation(fields: [context_id], references: [id], onDelete: Cascade)

  @@unique([context_id, version])
}

model conversations {
  id                       Int                   @id @default(autoincrement())
  user_id                  Int
  title                    String?
  status                   ConversationStatus    @default(active)
  created_at               DateTime              @default(now())
  updated_at               DateTime              @default(now())
  meta_data                Json?
  settings                 Json?
  project_id               Int?
  key_objective            String?
  category                 ConversationCategory?
  user_rating              Int?
  user_feedback            String?
  is_pinned                Boolean               @default(false)
  agent_status             AgentStatus?
  agent_progress           Json?
  agent_iteration_count    Int                   @default(0)
  agent_allowed_actions    Json?
  agent_commands_executed  Json?
  agent_completion_summary String?
  last_activity_type       LastActivityType?
  parent_id                Int?
  agent_id                 Int?
  file_paths               Json?                 @default("[]")
  total_tokens             Int?                  @default(0)
  total_cost               Float?                @default(0.0)
  agent_pipeline_stages    Json?                 @default("[]")
  current_stage            String?               @default("evidence")
  chat_contexts            chat_contexts[]
  ai_agents                ai_agents?            @relation(fields: [agent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  conversations            conversations?        @relation("conversationsToconversations", fields: [parent_id], references: [id])
  other_conversations      conversations[]       @relation("conversationsToconversations")
  projects                 projects?             @relation(fields: [project_id], references: [id])
  users                    users                 @relation(fields: [user_id], references: [id])
  messages                 messages[]
}

model elo_history {
  id                                   Int                   @id @default(autoincrement())
  user_id                              Int
  previous_rating                      Int
  new_rating                           Int
  change                               Int
  reason                               String                @db.VarChar
  programming_session_id               Int?
  opponent_id                          Int?
  timestamp                            DateTime              @default(now())
  users_elo_history_opponent_idTousers users?                @relation("elo_history_opponent_idTousers", fields: [opponent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programming_sessions                 programming_sessions? @relation(fields: [programming_session_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_elo_history_user_idTousers     users                 @relation("elo_history_user_idTousers", fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model file_summaries {
  id           Int      @id @default(autoincrement())
  project_id   Int
  file_path    String
  summary      String
  token_count  Int?
  last_updated DateTime @default(now())
  updated_at   DateTime @default(now())
  git_hash     String?
  is_stale     Boolean  @default(false)
  projects     projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@unique([project_id, file_path])
}

model karma_transactions {
  id                  Int      @id @default(autoincrement())
  user_id             Int
  amount              Int
  reason              String   @db.VarChar
  related_entity_type String?  @db.VarChar
  related_entity_id   Int?
  created_at          DateTime @default(now())
  users               users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model lesson_completions {
  id           Int      @id @default(autoincrement())
  completed_at DateTime @default(now())
  score        Float?
  feedback     String?
  user_id      Int
  lesson_id    Int
  lessons      lessons  @relation(fields: [lesson_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users        users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, lesson_id])
}

model lessons {
  id                 Int                  @id @default(autoincrement())
  title              String               @db.VarChar
  description        String?
  content            String?
  lesson_type        LessonType           @default(LECTURE)
  duration_minutes   Int                  @default(30)
  order_index        Int                  @default(0)
  module_id          Int
  created_at         DateTime             @default(now())
  updated_at         DateTime             @default(now())
  lesson_completions lesson_completions[]
  modules            modules              @relation(fields: [module_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model memory_contexts {
  id               Int                @id @default(autoincrement())
  project_id       Int
  slide_deck_id    Int?
  contextType      ContextType
  name             String
  description      String?
  token_count      Int?
  last_updated     DateTime           @default(now())
  last_synced_at   DateTime?
  version          Int                @default(1)
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  is_active        Boolean            @default(true)
  metadata         Json?
  context_versions context_versions[]
  projects         projects           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  slide_decks      slide_decks?       @relation(fields: [slide_deck_id], references: [id])
}

model memory_slides {
  id            Int           @id @default(autoincrement())
  slide_deck_id Int
  title         String?
  content       String
  order         Int
  format        ContextFormat @default(MARKDOWN)
  token_count   Int?
  last_updated  DateTime      @default(now())
  created_at    DateTime      @default(now())
  updated_at    DateTime      @default(now())
  slide_decks   slide_decks   @relation(fields: [slide_deck_id], references: [id], onDelete: Cascade)
}

model message_reactions {
  id              Int             @id @default(autoincrement())
  message_id      Int
  user_id         Int
  reaction_type   String
  created_at      DateTime        @default(now()) @db.Timestamp(6)
  social_messages social_messages @relation(fields: [message_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users           users           @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([message_id, user_id, reaction_type])
}

model messages {
  id                Int           @id @default(autoincrement())
  conversation_id   Int
  role              String
  content           String
  model             String?
  prompt_tokens     Int?
  completion_tokens Int?
  cost              Float?
  duration_ms       Int?
  user_feedback     String?
  project_id        Int?
  created_at        DateTime      @default(now())
  meta_data         Json?
  code_language     String?       @db.VarChar
  code_snippet      String?
  file_path         String?       @db.VarChar
  line_start        Int?
  line_end          Int?
  conversations     conversations @relation(fields: [conversation_id], references: [id], onDelete: Cascade)
  projects          projects?     @relation(fields: [project_id], references: [id])
}

model model_usage {
  id                Int       @id @default(autoincrement())
  user_id           Int
  model_name        String
  provider          Provider
  prompt_tokens     Int
  completion_tokens Int
  total_tokens      Int
  estimated_cost    Float
  response_time     Int?
  taskType          TaskType?
  success           Boolean   @default(true)
  error_message     String?
  timestamp         DateTime  @default(now())
  metadata          Json?
  users             users     @relation(fields: [user_id], references: [id])
}

model module_summaries {
  id           Int      @id @default(autoincrement())
  project_id   Int
  module_path  String
  summary      String
  token_count  Int?
  last_updated DateTime @default(now())
  updated_at   DateTime @default(now())
  is_stale     Boolean  @default(false)
  projects     projects @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@unique([project_id, module_path])
}

model modules {
  id          Int       @id @default(autoincrement())
  title       String    @db.VarChar
  description String?
  order_index Int       @default(0)
  workshop_id Int
  created_at  DateTime  @default(now())
  updated_at  DateTime  @default(now())
  lessons     lessons[]
  workshops   workshops @relation(fields: [workshop_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model notifications {
  id                  Int       @id @default(autoincrement())
  user_id             Int
  type                String
  title               String
  message             String?
  is_read             Boolean   @default(false)
  link                String?
  related_entity_type String?
  related_entity_id   Int?
  created_at          DateTime  @default(now()) @db.Timestamp(6)
  expires_at          DateTime? @db.Timestamp(6)
  users               users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model payment_refunds {
  id                  Int       @id @default(autoincrement())
  payment_id          Int
  amount              Decimal   @db.Decimal(10, 2)
  reason              String?
  status              String    @default("pending")
  provider_refund_id  String?
  refunded_by_user_id Int?
  created_at          DateTime  @default(now()) @db.Timestamp(6)
  updated_at          DateTime  @default(now()) @db.Timestamp(6)
  completed_at        DateTime? @db.Timestamp(6)
  payments            payments  @relation(fields: [payment_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users               users?    @relation(fields: [refunded_by_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model payments {
  id                    Int                     @id @default(autoincrement())
  user_id               Int
  amount                Decimal                 @db.Decimal(10, 2)
  currency              String                  @default("USD")
  payment_method        String
  payment_provider      String
  provider_payment_id   String?
  status                String                  @default("pending")
  description           String?
  metadata              Json?
  created_at            DateTime                @default(now()) @db.Timestamp(6)
  updated_at            DateTime                @default(now()) @db.Timestamp(6)
  completed_at          DateTime?               @db.Timestamp(6)
  payment_refunds       payment_refunds[]
  users                 users                   @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subscription_payments subscription_payments[]
}

model programming_sessions {
  id                 Int           @id @default(autoincrement())
  user_id            Int
  project_id         Int?
  start_time         DateTime
  end_time           DateTime?
  duration           Int?
  lines_added        Int?
  lines_deleted      Int?
  files_modified     Int?
  ai_interactions    Int?
  tokens_consumed    Int?
  summary            String?
  code_quality_delta Float?
  elo_history        elo_history[]
  projects           projects?     @relation(fields: [project_id], references: [id])
  users              users         @relation(fields: [user_id], references: [id])
}

model project_code_quality {
  id                  Int       @id @default(autoincrement())
  project_id          Int       @unique
  code_coverage       Float?
  lint_score          Float?
  avg_function_length Float?
  code_duplication    Float?
  tech_debt_score     Float?
  last_analysis       DateTime? @default(now())
  projects            projects  @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model code_health_reports {
  id                Int             @id @default(autoincrement())
  project_id        Int
  overall_score     Float
  complexity_score  Float
  maintainability   Float
  technical_debt    Float
  created_at        DateTime        @default(now())
  analysis_duration Int
  file_count        Int
  ai_analysis_model String?
  projects          projects        @relation(fields: [project_id], references: [id], onDelete: Cascade)
  code_patterns     code_patterns[]
  file_metrics      file_metrics[]
}

model file_metrics {
  id                 Int                 @id @default(autoincrement())
  report_id          Int
  file_path          String
  complexity         Float
  nesting_depth      Int
  function_count     Int
  line_count         Int
  has_error_handling Boolean
  risk_level         String
  issues             Json
  report             code_health_reports @relation(fields: [report_id], references: [id], onDelete: Cascade)
  function_metrics   function_metrics[]
}

model function_metrics {
  id                 Int          @id @default(autoincrement())
  file_metric_id     Int
  function_name      String
  start_line         Int
  end_line           Int
  complexity         Float
  parameter_count    Int
  return_count       Int
  is_async           Boolean
  has_error_handling Boolean
  file_metric        file_metrics @relation(fields: [file_metric_id], references: [id], onDelete: Cascade)
}

model code_patterns {
  id           Int                 @id @default(autoincrement())
  report_id    Int
  pattern_type String
  severity     String
  file_path    String
  line_number  Int?
  description  String
  suggestion   String?
  report       code_health_reports @relation(fields: [report_id], references: [id], onDelete: Cascade)
}

model project_dependencies {
  id              Int      @id @default(autoincrement())
  project_id      Int
  name            String
  version         String?
  type            String?
  security_issues Int      @default(0)
  license         String?
  projects        projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_documentation {
  id           Int      @id @default(autoincrement())
  project_id   Int
  type         String
  title        String
  content      String?
  format       String?
  last_updated DateTime @default(now())
  projects     projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_git_repos {
  id           Int      @id @default(autoincrement())
  project_id   Int
  url          String
  branch       String   @default("main")
  access_token String?
  projects     projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_objectives {
  id          Int      @id @default(autoincrement())
  project_id  Int
  title       String
  description String?
  priority    Int?
  status      String?
  projects    projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_quality_metrics {
  id         Int      @id @default(autoincrement())
  project_id Int
  name       String
  value      Float?
  target     Float?
  projects   projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_slides {
  id         Int      @id @default(autoincrement())
  project_id Int
  title      String?
  content    String?
  order      Int?
  projects   projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_tech_stacks {
  id         Int      @id @default(autoincrement())
  project_id Int
  category   String?
  technology String
  version    String?
  projects   projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_templates {
  id             Int      @id @default(autoincrement())
  name           String
  description    String?
  structure      Json
  default_branch String   @default("main")
  creator_id     Int?
  is_public      Boolean  @default(false)
  usage_count    Int      @default(0)
  created_at     DateTime @default(now()) @db.Timestamp(6)
  updated_at     DateTime @default(now()) @db.Timestamp(6)
  users          users?   @relation(fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model project_tests {
  id         Int      @id @default(autoincrement())
  project_id Int
  name       String
  type       String?
  status     String?
  code       String?
  projects   projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model project_visualizations {
  id         Int      @id @default(autoincrement())
  project_id Int
  type       String
  title      String?
  content    String?
  format     String?
  projects   projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model projects {
  id                      Int                       @id @default(autoincrement())
  name                    String
  description             String?
  user_id                 Int?
  repository_url          String?
  main_branch             String                    @default("main")
  local_path              String?
  language                String?
  framework               String?
  tech_stack              Json                      @default("{}")
  project_type            ProjectType?
  project_motivation      ProjectMotivationType?
  template_id             Int?
  domain                  String?
  target_audience         Json                      @default("[]")
  constraints             Json                      @default("{}")
  stage                   String?
  is_public               Boolean                   @default(false)
  is_active               Boolean                   @default(true)
  created_at              DateTime                  @default(now())
  updated_at              DateTime                  @default(now())
  ai_agents               ai_agents[]
  channels                channels[]
  code_health_reports     code_health_reports[]
  conversations           conversations[]
  file_summaries          file_summaries[]
  memory_contexts         memory_contexts[]
  messages                messages[]
  module_summaries        module_summaries[]
  programming_sessions    programming_sessions[]
  project_code_quality    project_code_quality?
  project_dependencies    project_dependencies[]
  project_documentation   project_documentation[]
  project_git_repos       project_git_repos[]
  project_objectives      project_objectives[]
  project_quality_metrics project_quality_metrics[]
  project_slides          project_slides[]
  project_tech_stacks     project_tech_stacks[]
  project_tests           project_tests[]
  project_visualizations  project_visualizations[]
  users                   users?                    @relation(fields: [user_id], references: [id])
  questions               questions[]
  slide_decks             slide_decks[]
  tasks                   tasks[]
  template_usages         template_usages[]
  user_stories            user_stories[]
}

model question_tags {
  question_id Int
  tag_id      Int
  questions   questions @relation(fields: [question_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  tags        tags      @relation(fields: [tag_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([question_id, tag_id])
}

model question_votes {
  id          Int       @id @default(autoincrement())
  question_id Int
  user_id     Int
  vote_value  Int
  created_at  DateTime  @default(now())
  questions   questions @relation(fields: [question_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users       users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([question_id, user_id])
}

model questions {
  id                 Int              @id @default(autoincrement())
  title              String           @db.VarChar
  content            String
  code_snippet       String?
  code_language      String?          @db.VarChar
  timestamp          DateTime         @default(now())
  updated_at         DateTime?
  is_resolved        Boolean          @default(false)
  accepted_answer_id Int?
  view_count         Int              @default(0)
  project_id         Int?
  context_type       String?          @db.VarChar
  context_id         Int?
  author_id          Int
  answers            answers[]
  question_tags      question_tags[]
  question_votes     question_votes[]
  users              users            @relation(fields: [author_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projects           projects?        @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model resources {
  id          Int       @id @default(autoincrement())
  title       String    @db.VarChar
  description String?
  type        String    @db.VarChar
  url         String?   @db.VarChar
  content     String?
  is_required Boolean   @default(false)
  workshop_id Int
  created_at  DateTime  @default(now())
  updated_at  DateTime  @default(now())
  workshops   workshops @relation(fields: [workshop_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model slide_decks {
  id              Int               @id @default(autoincrement())
  project_id      Int
  title           String
  description     String?
  type            SlideDeckType
  token_count     Int?
  last_updated    DateTime          @default(now())
  created_at      DateTime          @default(now())
  updated_at      DateTime          @default(now())
  is_active       Boolean           @default(true)
  metadata        Json?
  memory_contexts memory_contexts[]
  memory_slides   memory_slides[]
  projects        projects          @relation(fields: [project_id], references: [id], onDelete: Cascade)
}

model social_messages {
  id                    Int                 @id @default(autoincrement())
  channel_id            Int
  user_id               Int
  content               String
  is_edited             Boolean             @default(false)
  is_pinned             Boolean             @default(false)
  parent_id             Int?
  created_at            DateTime            @default(now()) @db.Timestamp(6)
  updated_at            DateTime            @default(now()) @db.Timestamp(6)
  metadata              Json?
  message_reactions     message_reactions[]
  channels              channels            @relation(fields: [channel_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  social_messages       social_messages?    @relation("social_messagesTosocial_messages", fields: [parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  other_social_messages social_messages[]   @relation("social_messagesTosocial_messages")
  users                 users               @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model subscription_events {
  id              Int           @id @default(autoincrement())
  subscription_id Int
  event_type      String
  description     String?
  metadata        Json?
  created_at      DateTime      @default(now()) @db.Timestamp(6)
  subscriptions   subscriptions @relation(fields: [subscription_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model subscription_payments {
  id                   Int           @id @default(autoincrement())
  subscription_id      Int
  payment_id           Int
  billing_period_start DateTime      @db.Timestamp(6)
  billing_period_end   DateTime      @db.Timestamp(6)
  created_at           DateTime      @default(now()) @db.Timestamp(6)
  payments             payments      @relation(fields: [payment_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  subscriptions        subscriptions @relation(fields: [subscription_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([subscription_id, payment_id])
}

model subscriptions {
  id                       Int                     @id @default(autoincrement())
  user_id                  Int
  plan_name                String
  plan_id                  String
  status                   String                  @default("active")
  current_period_start     DateTime                @db.Timestamp(6)
  current_period_end       DateTime                @db.Timestamp(6)
  cancel_at_period_end     Boolean                 @default(false)
  provider                 String
  provider_subscription_id String?
  metadata                 Json?
  created_at               DateTime                @default(now()) @db.Timestamp(6)
  updated_at               DateTime                @default(now()) @db.Timestamp(6)
  canceled_at              DateTime?               @db.Timestamp(6)
  ended_at                 DateTime?               @db.Timestamp(6)
  subscription_events      subscription_events[]
  subscription_payments    subscription_payments[]
  users                    users                   @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model tags {
  id             Int              @id @default(autoincrement())
  name           String           @unique @db.VarChar
  description    String?
  category       String?          @db.VarChar
  blog_post_tags blog_post_tags[]
  question_tags  question_tags[]
}

model task_comments {
  id         Int      @id @default(autoincrement())
  task_id    Int
  user_id    Int
  content    String
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)
  tasks      tasks    @relation(fields: [task_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users      users    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model task_dependencies {
  id                                                Int      @id @default(autoincrement())
  task_id                                           Int
  depends_on_task_id                                Int
  dependency_type                                   String   @default("blocks")
  created_at                                        DateTime @default(now()) @db.Timestamp(6)
  tasks_task_dependencies_depends_on_task_idTotasks tasks    @relation("task_dependencies_depends_on_task_idTotasks", fields: [depends_on_task_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  tasks_task_dependencies_task_idTotasks            tasks    @relation("task_dependencies_task_idTotasks", fields: [task_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([task_id, depends_on_task_id])
}

model tasks {
  id                                                            Int                 @id @default(autoincrement())
  project_id                                                    Int
  title                                                         String
  description                                                   String?
  status                                                        String              @default("TODO")
  priority                                                      Int                 @default(1)
  due_date                                                      DateTime?
  assigned_to_user_id                                           Int?
  created_at                                                    DateTime            @default(now())
  updated_at                                                    DateTime            @default(now())
  completed_at                                                  DateTime?
  tags                                                          Json                @default("[]")
  task_comments                                                 task_comments[]
  task_dependencies_task_dependencies_depends_on_task_idTotasks task_dependencies[] @relation("task_dependencies_depends_on_task_idTotasks")
  task_dependencies_task_dependencies_task_idTotasks            task_dependencies[] @relation("task_dependencies_task_idTotasks")
  projects                                                      projects            @relation(fields: [project_id], references: [id])
}

model template_collections {
  id                   Int                    @id @default(autoincrement())
  name                 String
  description          String?
  is_public            Boolean                @default(false)
  creator_id           Int?
  created_at           DateTime               @default(now()) @db.Timestamp(6)
  updated_at           DateTime               @default(now()) @db.Timestamp(6)
  collection_templates collection_templates[]
  users                users?                 @relation(fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model template_files {
  id            Int       @id @default(autoincrement())
  template_id   Int
  path          String
  content       String
  is_executable Boolean   @default(false)
  is_binary     Boolean   @default(false)
  created_at    DateTime  @default(now()) @db.Timestamp(6)
  updated_at    DateTime  @default(now()) @db.Timestamp(6)
  templates     templates @relation(fields: [template_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([template_id, path])
}

model template_tags {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  description      String?
  created_at       DateTime           @default(now()) @db.Timestamp(6)
  updated_at       DateTime           @default(now()) @db.Timestamp(6)
  template_to_tags template_to_tags[]
}

model template_to_tags {
  template_id   Int
  tag_id        Int
  template_tags template_tags @relation(fields: [tag_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  templates     templates     @relation(fields: [template_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([template_id, tag_id])
}

model template_usages {
  id              Int       @id @default(autoincrement())
  template_id     Int
  user_id         Int
  project_id      Int?
  variable_values Json?
  used_at         DateTime  @default(now()) @db.Timestamp(6)
  projects        projects? @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  templates       templates @relation(fields: [template_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users           users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model template_variables {
  id            Int       @id @default(autoincrement())
  template_id   Int
  name          String
  default_value String?
  description   String?
  required      Boolean   @default(false)
  variable_type String    @default("string")
  options       Json?
  created_at    DateTime  @default(now()) @db.Timestamp(6)
  updated_at    DateTime  @default(now()) @db.Timestamp(6)
  templates     templates @relation(fields: [template_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([template_id, name])
}

model templates {
  id                   Int                    @id @default(autoincrement())
  name                 String
  description          String?
  category             String
  version              String                 @default("1.0.0")
  is_public            Boolean                @default(false)
  creator_id           Int?
  thumbnail_url        String?
  metadata             Json?
  created_at           DateTime               @default(now()) @db.Timestamp(6)
  updated_at           DateTime               @default(now()) @db.Timestamp(6)
  collection_templates collection_templates[]
  template_files       template_files[]
  template_to_tags     template_to_tags[]
  template_usages      template_usages[]
  template_variables   template_variables[]
  users                users?                 @relation(fields: [creator_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model user_context_preferences {
  id          Int         @id @default(autoincrement())
  user_id     Int
  contextType ContextType
  is_pinned   Boolean     @default(false)
  is_disabled Boolean     @default(false)
  priority    Int?
  created_at  DateTime    @default(now())
  updated_at  DateTime    @default(now())
  users       users       @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, contextType])
}

model user_onboarding_states {
  id                                                            Int                    @id @default(autoincrement())
  user_id                                                       Int
  step_key                                                      String?
  title                                                         String?
  completion_status                                             Boolean                @default(false)
  start_date                                                    DateTime               @default(now())
  completion_date                                               DateTime?
  attempts                                                      Int                    @default(0)
  last_activity                                                 DateTime?
  project_motivation                                            ProjectMotivationType?
  code_type                                                     CodeType?
  project_goals                                                 Json?
  team_size                                                     Int?
  ai_preferences                                                Json?
  feature_adaptations                                           Json?
  completed_with_partner_id                                     Int?
  achievement_unlocked                                          Boolean                @default(false)
  feedback_rating                                               Int?
  users_user_onboarding_states_completed_with_partner_idTousers users?                 @relation("user_onboarding_states_completed_with_partner_idTousers", fields: [completed_with_partner_id], references: [id])
  users_user_onboarding_states_user_idTousers                   users                  @relation("user_onboarding_states_user_idTousers", fields: [user_id], references: [id])
}

model user_sessions {
  id            Int      @id @default(autoincrement())
  user_id       Int
  session_token String   @unique
  ip_address    String?
  user_agent    String?
  device_info   Json?
  last_activity DateTime @default(now())
  expires_at    DateTime
  is_active     Boolean  @default(true)
  created_at    DateTime @default(now())
  users         users    @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model user_stories {
  id                                            Int       @id @default(autoincrement())
  project_id                                    Int
  title                                         String
  description                                   String?
  acceptance_criteria                           String?
  status                                        String    @default("backlog")
  priority                                      Int?      @default(0)
  story_points                                  Int?
  assigned_to_user_id                           Int?
  created_by_user_id                            Int?
  created_at                                    DateTime  @default(now()) @db.Timestamp(6)
  updated_at                                    DateTime  @default(now()) @db.Timestamp(6)
  completed_at                                  DateTime? @db.Timestamp(6)
  users_user_stories_assigned_to_user_idTousers users?    @relation("user_stories_assigned_to_user_idTousers", fields: [assigned_to_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_user_stories_created_by_user_idTousers  users?    @relation("user_stories_created_by_user_idTousers", fields: [created_by_user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  projects                                      projects  @relation(fields: [project_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model users {
  id                                                                             Int                        @id @default(autoincrement())
  email                                                                          String                     @unique
  username                                                                       String?                    @unique
  first_name                                                                     String?
  last_name                                                                      String?
  clerk_id                                                                       String                     @unique
  role                                                                           UserRole                   @default(FREE)
  is_active                                                                      Boolean                    @default(true)
  bio                                                                            String?
  profile_image_url                                                              String?
  email_verified                                                                 Boolean                    @default(false)
  email_verification_token                                                       String?
  email_verification_sent_at                                                     DateTime?
  elo_rating                                                                     Int                        @default(1200)
  karma                                                                          Int                        @default(10)
  skills_graph                                                                   Json?
  total_challenges_completed                                                     Int                        @default(0)
  streak_count                                                                   Int                        @default(0)
  last_active_date                                                               DateTime?
  referred_by                                                                    Int?
  failed_login_attempts                                                          Int                        @default(0)
  account_locked_until                                                           DateTime?
  last_password_change                                                           DateTime?
  daily_llm_token_quota                                                          Int                        @default(10000)
  daily_llm_token_usage                                                          Int                        @default(0)
  quota_reset_date                                                               DateTime                   @default(now())
  created_at                                                                     DateTime                   @default(now())
  updated_at                                                                     DateTime                   @default(now())
  last_login                                                                     DateTime?
  preferred_ide                                                                  String?
  learning_style                                                                 String?
  developer_strengths                                                            Json                       @default("[]")
  preferred_ai_models                                                            Json                       @default("[]")
  additional_info                                                                String?
  onboarding_completed                                                           Boolean                    @default(false)
  onboarding_completed_at                                                        DateTime?
  ai_agents                                                                      ai_agents[]
  ai_prompt_templates                                                            ai_prompt_templates[]
  answer_votes                                                                   answer_votes[]
  answers                                                                        answers[]
  blog_posts                                                                     blog_posts[]
  bookmarks                                                                      bookmarks[]
  channel_members                                                                channel_members[]
  channels                                                                       channels[]
  conversations                                                                  conversations[]
  elo_history_elo_history_opponent_idTousers                                     elo_history[]              @relation("elo_history_opponent_idTousers")
  elo_history_elo_history_user_idTousers                                         elo_history[]              @relation("elo_history_user_idTousers")
  karma_transactions                                                             karma_transactions[]
  lesson_completions                                                             lesson_completions[]
  message_reactions                                                              message_reactions[]
  model_usage                                                                    model_usage[]
  notifications                                                                  notifications[]
  payment_refunds                                                                payment_refunds[]
  payments                                                                       payments[]
  programming_sessions                                                           programming_sessions[]
  project_templates                                                              project_templates[]
  projects                                                                       projects[]
  question_votes                                                                 question_votes[]
  questions                                                                      questions[]
  social_messages                                                                social_messages[]
  subscriptions                                                                  subscriptions[]
  task_comments                                                                  task_comments[]
  template_collections                                                           template_collections[]
  template_usages                                                                template_usages[]
  templates                                                                      templates[]
  user_context_preferences                                                       user_context_preferences[]
  user_onboarding_states_user_onboarding_states_completed_with_partner_idTousers user_onboarding_states[]   @relation("user_onboarding_states_completed_with_partner_idTousers")
  user_onboarding_states_user_onboarding_states_user_idTousers                   user_onboarding_states[]   @relation("user_onboarding_states_user_idTousers")
  user_sessions                                                                  user_sessions[]
  user_stories_user_stories_assigned_to_user_idTousers                           user_stories[]             @relation("user_stories_assigned_to_user_idTousers")
  user_stories_user_stories_created_by_user_idTousers                            user_stories[]             @relation("user_stories_created_by_user_idTousers")
  users                                                                          users?                     @relation("usersTousers", fields: [referred_by], references: [id])
  other_users                                                                    users[]                    @relation("usersTousers")
  workshop_participants                                                          workshop_participants[]
}

model workshop_materials {
  id            Int       @id @default(autoincrement())
  workshop_id   Int
  title         String    @db.VarChar
  description   String?
  material_type String    @db.VarChar
  file_path     String?   @db.VarChar
  external_url  String?   @db.VarChar
  order_index   Int       @default(0)
  is_required   Boolean   @default(false)
  created_at    DateTime  @default(now())
  updated_at    DateTime  @default(now())
  workshops     workshops @relation(fields: [workshop_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model workshop_participants {
  workshop_id           Int
  user_id               Int
  registration_date     DateTime            @default(now())
  status                ParticipationStatus @default(REGISTERED)
  payment_status        String              @default("unpaid") @db.VarChar
  payment_id            Int?
  completion_percentage Int                 @default(0)
  feedback_submitted    Boolean             @default(false)
  notes                 String?
  last_updated          DateTime            @default(now())
  users                 users               @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  workshops             workshops           @relation(fields: [workshop_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([workshop_id, user_id])
}

model workshops {
  id                    Int                     @id @default(autoincrement())
  name                  String                  @db.VarChar
  description           String?
  workshop_type         WorkshopType
  start_date            DateTime
  end_date              DateTime
  syllabus_link         String?                 @db.VarChar
  geography             String?                 @db.VarChar
  payment_link          String?                 @db.VarChar
  cost                  Float?
  max_participants      Int?
  is_active             Boolean                 @default(true)
  enrollment_deadline   DateTime?
  prerequisites         String?
  instructor_ids        Json                    @default("[]")
  timezone              String                  @default("UTC") @db.VarChar
  schedule              Json?
  is_online             Boolean                 @default(true)
  venue_details         Json?
  featured              Boolean                 @default(false)
  created_at            DateTime                @default(now())
  updated_at            DateTime                @default(now())
  modules               modules[]
  resources             resources[]
  workshop_materials    workshop_materials[]
  workshop_participants workshop_participants[]
}

model Workshop {
  id              Int                   @id @default(autoincrement())
  name            String
  description     String
  startDate       DateTime?
  endDate         DateTime?
  maxParticipants Int?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  modules         Module[]
  participants    WorkshopParticipant[]
}

model Module {
  id          Int              @id @default(autoincrement())
  workshopId  Int
  title       String
  description String?
  orderIndex  Int              @default(0)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  lessons     Lesson[]
  workshop    Workshop         @relation(fields: [workshopId], references: [id])
  progress    ModuleProgress[]
}

model Lesson {
  id           Int                @id @default(autoincrement())
  moduleId     Int
  title        String
  content      String
  type         String             @default("LECTURE")
  orderIndex   Int                @default(0)
  codeSnippet  String?
  codeLanguage String?
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  module       Module             @relation(fields: [moduleId], references: [id])
  completions  LessonCompletion[]
}

model WorkshopParticipant {
  workshopId Int
  userId     Int
  status     String   @default("ENROLLED")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id])
  workshop   Workshop @relation(fields: [workshopId], references: [id])

  @@id([workshopId, userId])
}

model ModuleProgress {
  workshopId           Int
  userId               Int
  moduleId             Int
  completionPercentage Int      @default(0)
  notes                String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  module               Module   @relation(fields: [moduleId], references: [id])
  user                 User     @relation(fields: [userId], references: [id])

  @@id([workshopId, userId, moduleId])
}

model LessonCompletion {
  id        Int      @id @default(autoincrement())
  lessonId  Int
  userId    Int
  score     Int?
  feedback  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lesson    Lesson   @relation(fields: [lessonId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}

model User {
  id                   Int                   @id @default(autoincrement())
  email                String                @unique
  name                 String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  lessonCompletions    LessonCompletion[]
  moduleProgress       ModuleProgress[]
  workshopParticipants WorkshopParticipant[]
}

enum AgentStatus {
  initializing
  running
  paused
  completed
  failed
  waiting
  cancelled
}

enum CodeType {
  fresh
  legacy
  throwaway
}

enum ContextFormat {
  MARKDOWN
  YAML
  JSON
  MERMAID
}

enum ContextType {
  USER
  BUSINESS
  TECHNICAL
  CODE
  TASK
}

enum ConversationCategory {
  chat
  agent
  code_assist
  debugging
  planning
  learning
  other
}

enum ConversationStatus {
  active
  archived
}

enum LastActivityType {
  thinking
  command
  file_edit
  complete
  error
  user_input
}

enum LessonType {
  LECTURE
  INTERACTIVE
  LAB
  QUIZ
  PROJECT
  DISCUSSION
}

enum ParticipationStatus {
  REGISTERED
  PAID
  CONFIRMED
  ATTENDED
  COMPLETED
  CANCELLED
}

enum ProjectMotivationType {
  learner
  contributor
  builder
}

enum ProjectType {
  WEB_APPLICATION
  MOBILE_APPLICATION
  LIBRARY
  CLI_TOOL
  API
  OTHER
}

enum Provider {
  bedrock
  azure
  google
  other
}

enum SlideDeckType {
  BUSINESS
  TECHNICAL
  USER
  CODE
  TASK
}

enum TaskType {
  chat
  code_review
  code_gen_big
  code_gen_agentic
  svg_mockup
  slides
  test_cases
  general
}

enum UserRole {
  FREE
  DEVELOPER
  ADMIN
}

enum WorkshopType {
  ESSENTIALS
  PRACTITIONER
  VIBECODERS
  AGENTIC
}
