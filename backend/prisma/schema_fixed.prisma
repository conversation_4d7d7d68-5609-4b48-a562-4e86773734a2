generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Add User and Project model references
model User {
  id                    Int                  @id @default(autoincrement())
  // Other fields omitted for brevity
  channelsCreated       Channel[]            @relation("ChannelCreator")
  channelMemberships    ChannelMember[]
  socialMessages        SocialMessage[]
  messageReactions      MessageReaction[]
  notifications         Notification[]
  createdTemplates      Template[]
  createdCollections    TemplateCollection[]
  templateUsages        TemplateUsage[]
  projectTemplates      ProjectTemplate[]
}

model Project {
  id                    Int                  @id @default(autoincrement())
  // Other fields omitted for brevity
  channels              Channel[]
  templateUsages        TemplateUsage[]
}

// Social models for the KAPI platform
model Channel {
  id          Int             @id @default(autoincrement())
  name        String
  description String?
  isPrivate   Boolean         @default(false) @map("is_private")
  createdAt   DateTime        @default(now()) @map("created_at")
  updatedAt   DateTime        @default(now()) @updatedAt @map("updated_at")
  creatorId   Int             @map("creator_id")
  projectId   Int?            @map("project_id")
  creator     User            @relation("ChannelCreator", fields: [creatorId], references: [id])
  project     Project?        @relation(fields: [projectId], references: [id])
  members     ChannelMember[]
  messages    SocialMessage[]

  @@map("channels")
}

model ChannelMember {
  channelId  Int       @map("channel_id")
  userId     Int       @map("user_id")
  joinedAt   DateTime  @default(now()) @map("joined_at")
  role       String    @default("member") // member, admin, moderator
  lastReadAt DateTime? @map("last_read_at")
  channel    Channel   @relation(fields: [channelId], references: [id], onDelete: Cascade)
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([channelId, userId])
  @@map("channel_members")
}

model SocialMessage {
  id        Int               @id @default(autoincrement())
  channelId Int               @map("channel_id")
  userId    Int               @map("user_id")
  content   String
  isEdited  Boolean           @default(false) @map("is_edited")
  isPinned  Boolean           @default(false) @map("is_pinned")
  parentId  Int?              @map("parent_id")
  createdAt DateTime          @default(now()) @map("created_at")
  updatedAt DateTime          @default(now()) @updatedAt @map("updated_at")
  metadata  Json?
  channel   Channel           @relation(fields: [channelId], references: [id], onDelete: Cascade)
  user      User              @relation(fields: [userId], references: [id])
  parent    SocialMessage?    @relation("ThreadReplies", fields: [parentId], references: [id])
  replies   SocialMessage[]   @relation("ThreadReplies")
  reactions MessageReaction[]

  @@map("social_messages")
}

model MessageReaction {
  id           Int           @id @default(autoincrement())
  messageId    Int           @map("message_id")
  userId       Int           @map("user_id")
  reactionType String        @map("reaction_type")
  createdAt    DateTime      @default(now()) @map("created_at")
  message      SocialMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user         User          @relation(fields: [userId], references: [id])

  @@unique([messageId, userId, reactionType])
  @@map("message_reactions")
}

model Notification {
  id                Int       @id @default(autoincrement())
  userId            Int       @map("user_id")
  type              String
  title             String
  message           String?
  isRead            Boolean   @default(false) @map("is_read")
  link              String?
  relatedEntityType String?   @map("related_entity_type")
  relatedEntityId   Int?      @map("related_entity_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  expiresAt         DateTime? @map("expires_at")
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Template models for the KAPI platform
model Template {
  id               Int                  @id @default(autoincrement())
  name             String
  description      String?
  category         String
  version          String               @default("1.0.0")
  isPublic         Boolean              @default(false) @map("is_public")
  creatorId        Int?                 @map("creator_id")
  thumbnailUrl     String?              @map("thumbnail_url")
  metadata         Json?
  createdAt        DateTime             @default(now()) @map("created_at")
  updatedAt        DateTime             @default(now()) @updatedAt @map("updated_at")
  creator          User?                @relation(fields: [creatorId], references: [id])
  files            TemplateFile[]
  tags             TemplateTag[]
  usages           TemplateUsage[]
  variables        TemplateVariable[]
  collections      TemplateCollection[] @relation("CollectionTemplates")

  @@map("templates")
}

model TemplateFile {
  id           Int      @id @default(autoincrement())
  templateId   Int      @map("template_id")
  path         String
  content      String
  isExecutable Boolean  @default(false) @map("is_executable")
  isBinary     Boolean  @default(false) @map("is_binary")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  template     Template @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, path])
  @@map("template_files")
}

model TemplateVariable {
  id           Int      @id @default(autoincrement())
  templateId   Int      @map("template_id")
  name         String
  defaultValue String?  @map("default_value")
  description  String?
  required     Boolean  @default(false)
  variableType String   @default("string") @map("variable_type")
  options      Json?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  template     Template @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, name])
  @@map("template_variables")
}

model TemplateTag {
  id          Int        @id @default(autoincrement())
  name        String     @unique
  description String?
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @default(now()) @updatedAt @map("updated_at")
  templates   Template[]

  @@map("template_tags")
}

model TemplateUsage {
  id             Int       @id @default(autoincrement())
  templateId     Int       @map("template_id")
  userId         Int       @map("user_id")
  projectId      Int?      @map("project_id")
  variableValues Json?     @map("variable_values")
  usedAt         DateTime  @default(now()) @map("used_at")
  template       Template  @relation(fields: [templateId], references: [id])
  user           User      @relation(fields: [userId], references: [id])
  project        Project?  @relation(fields: [projectId], references: [id])

  @@map("template_usages")
}

model TemplateCollection {
  id          Int        @id @default(autoincrement())
  name        String
  description String?
  isPublic    Boolean    @default(false) @map("is_public")
  creatorId   Int?       @map("creator_id")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @default(now()) @updatedAt @map("updated_at")
  creator     User?      @relation(fields: [creatorId], references: [id])
  templates   Template[] @relation("CollectionTemplates")

  @@map("template_collections")
}

model ProjectTemplate {
  id            Int       @id @default(autoincrement())
  name          String
  description   String?
  structure     Json
  defaultBranch String    @default("main") @map("default_branch")
  creatorId     Int?      @map("creator_id")
  isPublic      Boolean   @default(false) @map("is_public")
  usageCount    Int       @default(0) @map("usage_count")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")
  creator       User?     @relation(fields: [creatorId], references: [id])

  @@map("project_templates")
}
