// This file contains instructions for applying the memory architecture schema to your database
// Run this file with: node prisma/apply_memory_schema.js

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function applyMemorySchema() {
  console.log('Starting memory architecture schema migration...');

  // Step 1: Create a backup of the original schema.prisma
  console.log('Creating backup of current schema...');
  fs.copyFileSync(
    path.join(__dirname, 'schema.prisma'),
    path.join(__dirname, 'schema.prisma.backup')
  );
  console.log('Backup created as schema.prisma.backup');

  // Step 2: Read the current schema and memory additions
  const currentSchema = fs.readFileSync(
    path.join(__dirname, 'schema.prisma'),
    'utf8'
  );
  
  const memoryAdditions = fs.readFileSync(
    path.join(__dirname, 'memory_schema_additions.prisma'),
    'utf8'
  );

  // Step 3: Combine the schemas
  console.log('Merging memory schema with existing schema...');
  
  // Split the current schema to find where to insert the new models
  const schemaLines = currentSchema.split('\n');
  
  // Find where to append the memory models - after the last model definition
  let insertPosition = schemaLines.length;
  for (let i = schemaLines.length - 1; i >= 0; i--) {
    if (schemaLines[i].startsWith('model ')) {
      insertPosition = i;
      // Look for the end of this model
      while (insertPosition < schemaLines.length && !schemaLines[insertPosition].includes('}')) {
        insertPosition++;
      }
      if (insertPosition < schemaLines.length) {
        insertPosition++; // Move past the closing brace
      }
      break;
    }
  }

  // Add the memory schema after the last model
  const mergedSchemaLines = [
    ...schemaLines.slice(0, insertPosition),
    '',
    '// Memory Architecture Models',
    memoryAdditions,
    ...schemaLines.slice(insertPosition)
  ];

  // Write the combined schema to a temporary file
  const mergedSchema = mergedSchemaLines.join('\n');
  fs.writeFileSync(
    path.join(__dirname, 'schema.prisma.new'),
    mergedSchema,
    'utf8'
  );

  console.log('Schema merged. Review the file at prisma/schema.prisma.new');
  console.log('');
  console.log('To apply this migration, follow these steps:');
  console.log('');
  console.log('1. Review the merged schema in prisma/schema.prisma.new');
  console.log('   You will need to manually ensure there are no duplicate model references or conflicting fields.');
  console.log('   Specifically, check that the additions to Project, User, and Conversation models are correct.');
  console.log('');
  console.log('2. Once reviewed, replace your schema.prisma with the new version:');
  console.log('   mv prisma/schema.prisma.new prisma/schema.prisma');
  console.log('');
  console.log('3. Create a migration with:');
  console.log('   npx prisma migrate dev --name add_memory_architecture');
  console.log('');
  console.log('4. After the migration is applied, seed the ContextRegistry:');
  console.log('   node prisma/seed_context_registry.js');
}

applyMemorySchema().catch(e => {
  console.error('Error applying memory schema:', e);
  process.exit(1);
});
