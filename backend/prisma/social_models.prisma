// Social models for the KAPI platform

// New models
model Channel {
  id              Int                @id @default(autoincrement())
  name            String
  description     String?
  isPrivate       <PERSON><PERSON><PERSON>            @default(false) @map("is_private")
  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @default(now()) @updatedAt @map("updated_at")
  creatorId       Int                @map("creator_id")
  projectId       Int?               @map("project_id")
  creator         User               @relation("ChannelCreator", fields: [creatorId], references: [id])
  project         Project?           @relation(fields: [projectId], references: [id])
  members         ChannelMember[]
  messages        SocialMessage[]

  @@map("channels")
}

model ChannelMember {
  channelId       Int                @map("channel_id")
  userId          Int                @map("user_id")
  joinedAt        DateTime           @default(now()) @map("joined_at")
  role            String             @default("member") // member, admin, moderator
  lastReadAt      DateTime?          @map("last_read_at")
  channel         Channel            @relation(fields: [channelId], references: [id], onDelete: Cascade)
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([channelId, userId])
  @@map("channel_members")
}

model SocialMessage {
  id              Int                @id @default(autoincrement())
  channelId       Int                @map("channel_id")
  userId          Int                @map("user_id")
  content         String
  isEdited        Boolean            @default(false) @map("is_edited")
  isPinned        Boolean            @default(false) @map("is_pinned")
  parentId        Int?               @map("parent_id")
  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @default(now()) @updatedAt @map("updated_at")
  metadata        Json?
  channel         Channel            @relation(fields: [channelId], references: [id], onDelete: Cascade)
  user            User               @relation(fields: [userId], references: [id])
  parent          SocialMessage?     @relation("ThreadReplies", fields: [parentId], references: [id])
  replies         SocialMessage[]    @relation("ThreadReplies")
  reactions       MessageReaction[]

  @@map("social_messages")
}

model MessageReaction {
  id              Int                @id @default(autoincrement())
  messageId       Int                @map("message_id")
  userId          Int                @map("user_id")
  reactionType    String             @map("reaction_type")
  createdAt       DateTime           @default(now()) @map("created_at")
  message         SocialMessage      @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user            User               @relation(fields: [userId], references: [id])

  @@unique([messageId, userId, reactionType])
  @@map("message_reactions")
}

model Notification {
  id              Int                @id @default(autoincrement())
  userId          Int                @map("user_id")
  type            String
  title           String
  message         String?
  isRead          Boolean            @default(false) @map("is_read")
  link            String?
  relatedEntityType String?          @map("related_entity_type")
  relatedEntityId Int?               @map("related_entity_id")
  createdAt       DateTime           @default(now()) @map("created_at")
  expiresAt       DateTime?          @map("expires_at")
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}