// This file seeds the ContextRegistry table with default entries
// Run this file with: node prisma/seed_context_registry.js

// Import the Prisma client from the generated location
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function seedContextRegistry() {
  console.log('Seeding ContextRegistry...');

  const defaultRegistryEntries = [
    {
      name: 'User Profile',
      description: 'User preferences, skills, and learning focus',
      contextType: 'USER',
      priority: 4,
      maxTokens: 200,
      defaultFormat: 'YAML',
    },
    {
      name: 'Project Business Context',
      description: 'Project purpose, target audience, and value proposition',
      contextType: 'BUSINESS',
      priority: 3,
      maxTokens: 300,
      defaultFormat: 'MARKDOWN',
    },
    {
      name: 'Project Technical Context',
      description: 'Tech stack, architecture, and system design',
      contextType: 'TECHNICAL',
      priority: 2,
      maxTokens: 800,
      defaultFormat: 'MARKDOWN',
    },
    {
      name: 'Code Context',
      description: 'File layout, modules, and key functions',
      contextType: 'CODE',
      priority: 1,
      maxTokens: 300,
      defaultFormat: 'MARKDOWN',
    },
    {
      name: 'Active Task Context',
      description: 'Current task objectives, status, and requirements',
      contextType: 'TASK',
      priority: 0, // Highest priority
      maxTokens: 1500,
      defaultFormat: 'MARKDOWN',
    },
  ];

  for (const entry of defaultRegistryEntries) {
    await prisma.contextRegistry.upsert({
      where: { name: entry.name },
      update: entry,
      create: entry,
    });
    console.log(`Seeded or updated: ${entry.name}`);
  }

  console.log('Context registry seeded successfully');
}

seedContextRegistry()
  .catch((e) => {
    console.error('Error seeding context registry:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
