// Script to fix the duplicated models in the schema
const fs = require('fs');
const path = require('path');

// Path to schema
const schemaPath = path.join(__dirname, 'schema.prisma');

// Read schema
let schema = fs.readFileSync(schemaPath, 'utf8');

// Fix duplicate User model
schema = schema.replace(/\/\/ User relation extensions\nmodel User \{[\s\S]*?\}/, '');

// Fix duplicate Project model
schema = schema.replace(/\/\/ Project relation extensions\nmodel Project \{[\s\S]*?\}/, '');

// Save fixed schema
fs.writeFileSync(schemaPath, schema);

console.log('Successfully fixed duplicate models in schema.prisma');
