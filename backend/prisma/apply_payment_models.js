// Script to apply payment models to the main schema
const fs = require('fs');
const path = require('path');

// Paths
const schemaPath = path.join(__dirname, 'schema.prisma');
const paymentModelsPath = path.join(__dirname, 'payment_models.prisma');

// Read files
let mainSchema = fs.readFileSync(schemaPath, 'utf8');
const paymentModels = fs.readFileSync(paymentModelsPath, 'utf8');

// Check if payment models are already in the schema
if (mainSchema.includes('model Payment {')) {
  console.log('Payment models already exist in schema.prisma');
  process.exit(0);
}

// Find the User model definition
const userModelRegex = /model User \{([\s\S]*?)\n\}/;
const userMatch = mainSchema.match(userModelRegex);

if (!userMatch) {
  console.error('Could not find User model in schema.prisma');
  process.exit(1);
}

// Extract the User model content
const userModelContent = userMatch[1];

// Add the payment relations to the User model
const updatedUserModel = `model User {${userModelContent}
  payments          Payment[]
  subscriptions     Subscription[]
}`;

// Replace the User model in the schema
mainSchema = mainSchema.replace(userModelRegex, updatedUserModel);

// Add the payment models at the end of the file
mainSchema += '\n' + paymentModels;

// Write the updated schema
fs.writeFileSync(schemaPath, mainSchema);

console.log('Successfully applied payment models to schema.prisma');
