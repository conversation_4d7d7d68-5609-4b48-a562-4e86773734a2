const fs = require('fs');
const path = require('path');

// Paths
const mainSchemaPath = path.join(__dirname, 'schema.prisma');
const aiModelsSchemaPath = path.join(__dirname, 'ai_models.prisma');
const backupSchemaPath = path.join(__dirname, 'schema.prisma.backup');
const outputSchemaPath = path.join(__dirname, 'schema_with_ai_models.prisma');

// Read files
const mainSchema = fs.readFileSync(mainSchemaPath, 'utf8');
const aiModelsSchema = fs.readFileSync(aiModelsSchemaPath, 'utf8');

// Create backup of original schema
fs.writeFileSync(backupSchemaPath, mainSchema);

// Combine schemas
const combinedSchema = mainSchema + '\n' + aiModelsSchema;

// Write output
fs.writeFileSync(outputSchemaPath, combinedSchema);

console.log('✅ Successfully created schema_with_ai_models.prisma');
console.log('⚠️ Make sure to review the file for any conflicts before applying!');
console.log('\nTo apply this schema run:');
console.log('npx prisma migrate dev --name migrate_ai_models');
