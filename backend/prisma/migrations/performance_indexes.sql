-- Performance optimization indexes for KAPI database
-- These indexes significantly improve query performance for common operations

-- Conversations indexes
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_conversations_project_id ON conversations(project_id);

-- Messages indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role);

-- Model usage indexes for analytics
CREATE INDEX IF NOT EXISTS idx_model_usage_user_id ON model_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_model_usage_timestamp ON model_usage(timestamp);
CREATE INDEX IF NOT EXISTS idx_model_usage_model_name ON model_usage(model_name);
CREATE INDEX IF NOT EXISTS idx_model_usage_provider ON model_usage(provider);

-- Composite index for common query pattern
CREATE INDEX IF NOT EXISTS idx_model_usage_user_timestamp ON model_usage(user_id, timestamp);

-- User sessions for auth performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_projects_is_active ON projects(is_active);

-- Questions and answers for social features
CREATE INDEX IF NOT EXISTS idx_questions_author_id ON questions(author_id);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(timestamp);
CREATE INDEX IF NOT EXISTS idx_questions_is_resolved ON questions(is_resolved);
CREATE INDEX IF NOT EXISTS idx_answers_question_id ON answers(question_id);
CREATE INDEX IF NOT EXISTS idx_answers_author_id ON answers(author_id);

-- File summaries for code intelligence
CREATE INDEX IF NOT EXISTS idx_file_summaries_project_id ON file_summaries(project_id);
CREATE INDEX IF NOT EXISTS idx_file_summaries_last_updated ON file_summaries(last_updated);

-- Programming sessions for analytics
CREATE INDEX IF NOT EXISTS idx_programming_sessions_user_id ON programming_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_programming_sessions_project_id ON programming_sessions(project_id);
CREATE INDEX IF NOT EXISTS idx_programming_sessions_start_time ON programming_sessions(start_time);

-- AI agents for agent conversations
CREATE INDEX IF NOT EXISTS idx_ai_agents_user_id ON ai_agents(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_agents_project_id ON ai_agents(project_id);
CREATE INDEX IF NOT EXISTS idx_ai_agents_is_active ON ai_agents(is_active);

-- Workshop participants for Modern AI Pro
CREATE INDEX IF NOT EXISTS idx_workshop_participants_user_id ON workshop_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_workshop_participants_workshop_id ON workshop_participants(workshop_id);
CREATE INDEX IF NOT EXISTS idx_workshop_participants_status ON workshop_participants(status);

-- Notifications for real-time features
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Subscriptions for billing
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_current_period_end ON subscriptions(current_period_end);

-- Memory contexts for RAG
CREATE INDEX IF NOT EXISTS idx_memory_contexts_project_id ON memory_contexts(project_id);
CREATE INDEX IF NOT EXISTS idx_memory_contexts_contextType ON memory_contexts(contextType);
CREATE INDEX IF NOT EXISTS idx_memory_contexts_is_active ON memory_contexts(is_active);

-- Performance: Partial indexes for common filters
CREATE INDEX IF NOT EXISTS idx_conversations_active ON conversations(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_projects_active ON projects(user_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id) WHERE is_read = false;
