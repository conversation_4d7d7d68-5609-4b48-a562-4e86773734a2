const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function listTables() {
  try {
    // Execute a raw query to get all tables in the database
    const result = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;

    console.log('Tables in the database:');
    console.log('======================');

    result.forEach((row, index) => {
      console.log(`${index + 1}. ${row.table_name}`);
    });

    // Check if social and template models exist
    const socialModels = ['channels', 'channel_members', 'social_messages', 'message_reactions', 'notifications'];
    const templateModels = ['templates', 'template_files', 'template_variables', 'template_tags', 'template_usages', 'template_collections', 'project_templates'];
    const advancedProjectModels = ['user_stories', 'task_dependencies', 'task_comments'];
    const associationTables = ['workshop_participants', 'question_tags', 'channel_members', 'blog_post_tags', 'collection_templates'];
    const paymentModels = ['payments', 'payment_refunds', 'subscriptions', 'subscription_payments', 'subscription_events'];

    console.log('\nSocial Models Status:');
    console.log('====================');
    for (const model of socialModels) {
      const exists = result.some(row => row.table_name === model);
      console.log(`${model}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    }

    console.log('\nTemplate Models Status:');
    console.log('======================');
    for (const model of templateModels) {
      const exists = result.some(row => row.table_name === model);
      console.log(`${model}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    }

    console.log('\nAdvanced Project Models Status:');
    console.log('=============================');
    for (const model of advancedProjectModels) {
      const exists = result.some(row => row.table_name === model);
      console.log(`${model}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    }

    console.log('\nAssociation Tables Status:');
    console.log('========================');
    for (const model of associationTables) {
      const exists = result.some(row => row.table_name === model);
      console.log(`${model}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    }

    console.log('\nPayment & Subscription Models Status:');
    console.log('==================================');
    for (const model of paymentModels) {
      const exists = result.some(row => row.table_name === model);
      console.log(`${model}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    }

  } catch (error) {
    console.error('Error listing tables:', error);
  } finally {
    await prisma.$disconnect();
  }
}

listTables();
