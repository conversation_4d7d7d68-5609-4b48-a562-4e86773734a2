#!/usr/bin/env bash
# Setup verification script for commit-time documentation system

echo "🔍 Checking Documentation System Setup..."
echo "=" * 50

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Not in a git repository"
    exit 1
fi
echo "✅ Git repository detected"

# Check if <PERSON><PERSON> is installed and pre-commit hook exists
if [ -f ".husky/pre-commit" ]; then
    echo "✅ <PERSON>sky pre-commit hook found"
    echo "   Content preview:"
    head -5 .husky/pre-commit | sed 's/^/   /'
else
    echo "❌ <PERSON>sky pre-commit hook not found at .husky/pre-commit"
fi

# Check if documentation generation script exists
if [ -f "backend/scripts/generate_docs.py" ]; then
    echo "✅ Documentation generation script found"
else
    echo "❌ Documentation generation script not found at backend/scripts/generate_docs.py"
fi

# Check if documentation generation shell script exists
if [ -f "backend/scripts/generate_docs.sh" ]; then
    echo "✅ Documentation generation shell script found"
    if [ -x "backend/scripts/generate_docs.sh" ]; then
        echo "✅ Shell script is executable"
    else
        echo "⚠️  Shell script is not executable - run: chmod +x backend/scripts/generate_docs.sh"
    fi
else
    echo "❌ Documentation generation shell script not found"
fi

# Check if Python dependencies are available
echo ""
echo "🐍 Checking Python environment..."
if command -v poetry > /dev/null 2>&1; then
    echo "✅ Poetry found"
    cd backend/scripts
    if poetry check > /dev/null 2>&1; then
        echo "✅ Poetry environment is valid"
    else
        echo "⚠️  Poetry environment issues detected"
    fi
    cd - > /dev/null
else
    echo "⚠️  Poetry not found - make sure it's installed"
fi

# Check if backend server dependencies are available
echo ""
echo "🟢 Checking Node.js environment..."
if [ -f "backend/package.json" ]; then
    echo "✅ Backend package.json found"
    if [ -d "backend/node_modules" ]; then
        echo "✅ Node modules installed"
    else
        echo "⚠️  Node modules not installed - run: cd backend && npm install"
    fi
else
    echo "❌ Backend package.json not found"
fi

# Check if frontend dependencies are available
if [ -f "ide/package.json" ]; then
    echo "✅ Frontend package.json found"
    if [ -d "ide/node_modules" ]; then
        echo "✅ Frontend node modules installed"
    else
        echo "⚠️  Frontend node modules not installed - run: cd ide && npm install"
    fi
else
    echo "❌ Frontend package.json not found"
fi

# Check current git status
echo ""
echo "📊 Current Git Status:"
git status --porcelain | head -10 | sed 's/^/   /'

echo ""
echo "🎯 Setup check complete!"
echo "If you see any ❌ or ⚠️  above, please fix those issues before testing."
