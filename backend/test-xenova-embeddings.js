const { xenovaEmbeddingService } = require('./dist/src/services/xenova-embedding.service.js');

async function testXenovaEmbeddings() {
  console.log('🧠 Testing Xenova Embedding Service...\n');

  try {
    // Test 1: Initialize the service
    console.log('1. Initializing Xenova embedding service...');
    await xenovaEmbeddingService.initialize();
    console.log('✅ Service initialized successfully');
    console.log(`   Model: ${xenovaEmbeddingService.getModelInfo().name}`);
    console.log(`   Dimension: ${xenovaEmbeddingService.getModelInfo().dimension}`);
    console.log('');

    // Test 2: Generate embedding for a single text
    console.log('2. Testing single text embedding...');
    const testText = "user authentication login password";
    console.log(`   Text: "${testText}"`);
    
    const embedding = await xenovaEmbeddingService.embedText(testText);
    console.log(`✅ Generated embedding with dimension: ${embedding.length}`);
    console.log(`   First 5 values: [${embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}]`);
    console.log(`   Embedding magnitude: ${Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0)).toFixed(4)}`);
    console.log('');

    // Test 3: Generate embeddings for multiple texts
    console.log('3. Testing batch text embeddings...');
    const testTexts = [
      "user authentication login password",
      "database connection error handling",
      "React component state management"
    ];
    
    console.log(`   Texts: ${testTexts.length} items`);
    const embeddings = await xenovaEmbeddingService.embedTexts(testTexts);
    console.log(`✅ Generated ${embeddings.length} embeddings`);
    
    for (let i = 0; i < embeddings.length; i++) {
      console.log(`   Text ${i + 1}: dimension ${embeddings[i].length}, magnitude ${Math.sqrt(embeddings[i].reduce((sum, val) => sum + val * val, 0)).toFixed(4)}`);
    }
    console.log('');

    // Test 4: Calculate similarity between embeddings
    console.log('4. Testing embedding similarity...');
    const embedding1 = embeddings[0]; // "user authentication login password"
    const embedding2 = embeddings[1]; // "database connection error handling"
    const embedding3 = embeddings[2]; // "React component state management"
    
    // Calculate cosine similarity
    function cosineSimilarity(a, b) {
      const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
      const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
      const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
      return dotProduct / (magnitudeA * magnitudeB);
    }
    
    // Calculate Euclidean distance (what ChromaDB uses)
    function euclideanDistance(a, b) {
      return Math.sqrt(a.reduce((sum, val, i) => sum + Math.pow(val - b[i], 2), 0));
    }
    
    const sim1_2 = cosineSimilarity(embedding1, embedding2);
    const sim1_3 = cosineSimilarity(embedding1, embedding3);
    const sim2_3 = cosineSimilarity(embedding2, embedding3);
    
    const dist1_2 = euclideanDistance(embedding1, embedding2);
    const dist1_3 = euclideanDistance(embedding1, embedding3);
    const dist2_3 = euclideanDistance(embedding2, embedding3);
    
    console.log('   Cosine Similarities:');
    console.log(`     Auth vs Database: ${sim1_2.toFixed(4)}`);
    console.log(`     Auth vs React: ${sim1_3.toFixed(4)}`);
    console.log(`     Database vs React: ${sim2_3.toFixed(4)}`);
    console.log('');
    console.log('   Euclidean Distances:');
    console.log(`     Auth vs Database: ${dist1_2.toFixed(4)}`);
    console.log(`     Auth vs React: ${dist1_3.toFixed(4)}`);
    console.log(`     Database vs React: ${dist2_3.toFixed(4)}`);
    console.log('');

    // Test 5: Test with the same text (should have 0 distance)
    console.log('5. Testing identical text embeddings...');
    const sameText = "user authentication login password";
    const embedding_same1 = await xenovaEmbeddingService.embedText(sameText);
    const embedding_same2 = await xenovaEmbeddingService.embedText(sameText);
    
    const distance_same = euclideanDistance(embedding_same1, embedding_same2);
    const similarity_same = cosineSimilarity(embedding_same1, embedding_same2);
    
    console.log(`   Same text distance: ${distance_same.toFixed(6)}`);
    console.log(`   Same text similarity: ${similarity_same.toFixed(6)}`);
    console.log('');

    console.log('🎉 Xenova embedding service test completed successfully!');
    
    if (distance_same > 0.001) {
      console.log('⚠️  WARNING: Same text should have near-zero distance');
    }
    
    if (Math.abs(similarity_same - 1.0) > 0.001) {
      console.log('⚠️  WARNING: Same text should have similarity near 1.0');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testXenovaEmbeddings()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testXenovaEmbeddings };
