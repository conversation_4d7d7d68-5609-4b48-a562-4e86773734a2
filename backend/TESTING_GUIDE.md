# 🧪 Complete Testing Guide: Documentation Generation + Semantic Search

This guide provides step-by-step instructions for testing both the documentation generation and semantic search functionality in KAPI.

## 🚀 Quick Start Testing

### Prerequisites
1. **Backend running**: `npm run dev` (in backend directory)
2. **ChromaDB running**: `npm run chromadb:start`
3. **IDE running**: Start the KAPI IDE application

### One-Command Test Suite
```bash
# Test all features at once
npm run test:all-features
```

---

## 📚 Testing Documentation Generation

### Method 1: Backend API Testing
```bash
# Test documentation generation API
npm run test:docs
```

**What this tests:**
- ✅ Backend connectivity
- ✅ Documentation generation endpoints
- ✅ File processing
- ✅ Selective documentation generation
- ✅ Directory scanning
- ✅ Generated file validation

### Method 2: Python Script Testing
```bash
# Test Python documentation script directly
npm run test:docs-python
```

**What this tests:**
- ✅ Python environment setup
- ✅ Direct script execution
- ✅ File-level documentation
- ✅ Directory-level documentation
- ✅ JSON and Markdown output

### Method 3: IDE Interface Testing

1. **Open KAPI IDE**
2. **Load a project** with code files
3. **Access Documentation Panel**:
   - Look for Documentation panel in the IDE sidebar
   - Click "Create Documentation" if no docs exist
4. **Generate documentation**:
   - Right-click files in explorer → "Generate Documentation"
   - Use the documentation generation buttons in the UI

**Expected Results:**
- Documentation files appear in `docs/generated/` folder
- JSON files with structured documentation data
- Markdown files with human-readable documentation
- Documentation indexed for semantic search

---

## 🧠 Testing Semantic Search

### Method 1: Backend API Testing
```bash
# Test semantic search API endpoints
npm run test:semantic-api
```

**What this tests:**
- ✅ ChromaDB connectivity
- ✅ Document indexing
- ✅ Semantic search queries
- ✅ Result relevance scoring
- ✅ Metadata filtering
- ✅ Search statistics

### Method 2: IDE Search Interface Testing

#### Using the Search Bar
1. **Open Search**: Press `Ctrl+Shift+F` (or `Cmd+Shift+F`)
2. **Switch to Semantic Mode**: Click the "Semantic" tab (🧠 icon)
3. **Index Project** (if needed): Click "Reindex" button (🔄)
4. **Test Queries**:
   ```
   user authentication login
   database connection setup
   error handling functions
   API endpoint creation
   ```

#### Using the Search Modal
1. **Open Modal**: Press `Ctrl+P` (or `Cmd+P`)
2. **Enable AI Search**: Click the "AI" button
3. **Natural Language Queries**:
   ```
   How do I handle user login?
   Show me database models
   Find error handling code
   Where is the API configuration?
   ```

### Method 3: ChromaDB Direct Testing
```bash
# Test ChromaDB connection and basic operations
npm run chromadb:test

# Test semantic search with sample data
npm run chromadb:test-semantic
```

---

## 🔄 End-to-End Workflow Testing

### Complete Documentation → Search Workflow

1. **Start Services**:
   ```bash
   npm run chromadb:start    # Start ChromaDB
   npm run dev              # Start backend
   ```

2. **Generate Documentation**:
   - Use IDE to generate docs for your project files
   - Or use API: `npm run test:docs`

3. **Index Documentation**:
   - Documentation should auto-index into ChromaDB
   - Or manually trigger indexing in IDE

4. **Test Semantic Search**:
   - Use IDE search interface
   - Try natural language queries about your code

5. **Verify Results**:
   - Search results should include generated documentation
   - Results should be relevant to your queries
   - Metadata filtering should work

---

## 📊 Expected Test Results

### Documentation Generation
- ✅ **JSON Files**: Structured documentation in `docs/generated/files/`
- ✅ **Markdown Files**: Human-readable docs alongside JSON
- ✅ **Code Analysis**: Functions, classes, and modules documented
- ✅ **Git Integration**: Commit hashes and timestamps included

### Semantic Search
- ✅ **Relevance**: Search results match query intent
- ✅ **Speed**: Sub-second search response times
- ✅ **Filtering**: Metadata filters work correctly
- ✅ **Indexing**: Documents properly indexed and searchable

---

## 🛠️ Troubleshooting

### Documentation Generation Issues

**Problem**: Documentation generation fails
```bash
# Check Python environment
python3 --version
poetry --version

# Check backend logs
tail -f logs/backend.log

# Test Python script directly
npm run test:docs-python
```

**Problem**: No documentation files generated
- Check file permissions in project directory
- Verify Python dependencies are installed
- Check for errors in backend logs

### Semantic Search Issues

**Problem**: Search returns no results
```bash
# Check ChromaDB status
curl http://localhost:8000/api/v2/version

# Check indexing status
npm run test:semantic-api

# Restart ChromaDB
npm run chromadb:restart
```

**Problem**: Poor search relevance
- Ensure documents are properly indexed
- Check embedding generation (requires Azure OpenAI)
- Verify search query formatting

### IDE Integration Issues

**Problem**: Search interface not working
- Check browser console for errors
- Verify backend API connectivity
- Restart IDE application

**Problem**: Documentation panel empty
- Generate documentation first
- Check file permissions
- Verify backend documentation service

---

## 🎯 Performance Benchmarks

### Expected Performance
- **Documentation Generation**: 1-5 seconds per file
- **Semantic Search**: <500ms per query
- **Indexing**: 10-50 documents per second
- **Memory Usage**: <100MB for ChromaDB

### Monitoring Commands
```bash
# Check ChromaDB performance
curl http://localhost:8000/api/v2/collections

# Monitor backend performance
npm run test:all-features

# Check system resources
htop
```

---

## 📝 Test Checklist

### Before Testing
- [ ] Backend server running (`npm run dev`)
- [ ] ChromaDB server running (`npm run chromadb:start`)
- [ ] IDE application started
- [ ] Test project with code files available

### Documentation Generation
- [ ] API endpoints respond correctly
- [ ] Python script executes successfully
- [ ] JSON documentation files generated
- [ ] Markdown files created
- [ ] Git information included
- [ ] IDE integration works

### Semantic Search
- [ ] ChromaDB connection established
- [ ] Documents index successfully
- [ ] Search queries return relevant results
- [ ] Metadata filtering works
- [ ] IDE search interface functional
- [ ] Performance meets benchmarks

### Integration
- [ ] Generated docs appear in search results
- [ ] End-to-end workflow completes
- [ ] Real project files work correctly
- [ ] Error handling graceful
- [ ] Cleanup functions properly

---

## 🎉 Success Criteria

Your setup is working correctly when:

1. **Documentation Generation**:
   - ✅ Files generate documentation without errors
   - ✅ Both JSON and Markdown outputs created
   - ✅ IDE shows generated documentation

2. **Semantic Search**:
   - ✅ Natural language queries return relevant results
   - ✅ Search completes in under 1 second
   - ✅ IDE search interface fully functional

3. **Integration**:
   - ✅ Generated documentation appears in search results
   - ✅ Complete workflow from code → docs → search works
   - ✅ Real-world usage scenarios successful

**🎊 Congratulations! Your KAPI documentation and semantic search system is fully operational!**
