const { chromaDBService } = require('./dist/src/services/chroma-db.service.js');

async function testChromaDBEmbeddings() {
  console.log('🔍 Testing ChromaDB Embedding Integration...\n');

  try {
    // Test 1: Initialize ChromaDB service
    console.log('1. Initializing ChromaDB service...');
    await chromaDBService.initialize();
    console.log('✅ ChromaDB service initialized');
    console.log('');

    // Test 2: Clear any existing test documents
    console.log('2. Cleaning up any existing test documents...');
    try {
      await chromaDBService.deleteDocuments(['test_doc_1', 'test_doc_2', 'test_doc_3']);
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.log('ℹ️  No existing documents to clean up');
    }
    console.log('');

    // Test 3: Add test documents with known content
    console.log('3. Adding test documents...');
    const testDocs = [
      {
        id: 'test_doc_1',
        content: 'User authentication and login functionality with password validation',
        metadata: { type: 'auth', category: 'security' }
      },
      {
        id: 'test_doc_2', 
        content: 'Database connection error handling and retry logic',
        metadata: { type: 'database', category: 'infrastructure' }
      },
      {
        id: 'test_doc_3',
        content: 'React component state management and hooks',
        metadata: { type: 'frontend', category: 'ui' }
      }
    ];

    for (const doc of testDocs) {
      console.log(`   Adding: ${doc.id}`);
      await chromaDBService.addDocument(doc.id, doc.content, doc.metadata);
    }
    console.log('✅ All test documents added');
    console.log('');

    // Test 4: Perform semantic searches
    console.log('4. Testing semantic search...');
    
    const queries = [
      'user login authentication',
      'database error connection',
      'react component state'
    ];

    for (const query of queries) {
      console.log(`\n   Query: "${query}"`);
      const results = await chromaDBService.semanticSearch(query, 3);
      
      console.log(`   Found ${results.length} results:`);
      results.forEach((result, index) => {
        const similarity = Math.max(0, 1 - result.distance);
        console.log(`     ${index + 1}. ${result.id}`);
        console.log(`        Distance: ${result.distance.toFixed(4)}`);
        console.log(`        Similarity: ${similarity.toFixed(4)}`);
        console.log(`        Content: ${result.content.substring(0, 50)}...`);
      });
    }
    console.log('');

    // Test 5: Test with exact match
    console.log('5. Testing exact content match...');
    const exactQuery = 'User authentication and login functionality with password validation';
    console.log(`   Query: "${exactQuery}"`);
    
    const exactResults = await chromaDBService.semanticSearch(exactQuery, 3);
    console.log(`   Found ${exactResults.length} results:`);
    exactResults.forEach((result, index) => {
      const similarity = Math.max(0, 1 - result.distance);
      console.log(`     ${index + 1}. ${result.id}`);
      console.log(`        Distance: ${result.distance.toFixed(6)}`);
      console.log(`        Similarity: ${similarity.toFixed(6)}`);
    });
    console.log('');

    // Test 6: Check collection stats
    console.log('6. Checking collection statistics...');
    const healthCheck = await chromaDBService.healthCheck();
    console.log(`   Health check: ${healthCheck ? 'HEALTHY' : 'UNHEALTHY'}`);
    console.log('');

    // Cleanup
    console.log('7. Cleaning up test documents...');
    await chromaDBService.deleteDocuments(['test_doc_1', 'test_doc_2', 'test_doc_3']);
    console.log('✅ Cleanup completed');
    console.log('');

    console.log('🎉 ChromaDB embedding integration test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testChromaDBEmbeddings()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testChromaDBEmbeddings };
