{"name": "Existing Project", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend", "fileTree": [{"name": "backend", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend", "type": "directory", "children": [{"name": ".env", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.env", "type": "file"}, {"name": ".env.example", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.env.example", "type": "file"}, {"name": ".eslintrc.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.eslintrc.js", "type": "file"}, {"name": ".giti<PERSON>re", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.gitignore", "type": "file"}, {"name": ".prettieri<PERSON>re", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.prettierignore", "type": "file"}, {"name": ".prettierrc.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/.prettierrc.json", "type": "file"}, {"name": "CHROMADB_SETUP.md", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/CHROMADB_SETUP.md", "type": "file"}, {"name": "README.md", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/README.md", "type": "file"}, {"name": "SECURITY.md", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/SECURITY.md", "type": "file"}, {"name": "TESTING_GUIDE.md", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/TESTING_GUIDE.md", "type": "file"}, {"name": "build_output.txt", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/build_output.txt", "type": "file"}, {"name": "check-links.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/check-links.sh", "type": "file"}, {"name": "chroma_data", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/chroma_data", "type": "directory"}, {"name": "chromadb.log", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/chromadb.log", "type": "file"}, {"name": "chromadb.pid", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/chromadb.pid", "type": "file"}, {"name": "clerk-dependencies.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/clerk-dependencies.json", "type": "file"}, {"name": "config", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/config", "type": "directory"}, {"name": "create-test-user.sql", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/create-test-user.sql", "type": "file"}, {"name": "debug", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/debug", "type": "directory"}, {"name": "deploy.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/deploy.sh", "type": "file"}, {"name": "dist", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/dist", "type": "directory"}, {"name": "docs", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/docs", "type": "directory"}, {"name": "ecosystem.config.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/ecosystem.config.json", "type": "file"}, {"name": "enhanced-test", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/enhanced-test", "type": "directory"}, {"name": "errors.txt", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/errors.txt", "type": "file"}, {"name": "jest.config.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/jest.config.ts", "type": "file"}, {"name": "nginx", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/nginx", "type": "directory"}, {"name": "node_modules", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/node_modules", "type": "directory"}, {"name": "nodemon.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/nodemon.json", "type": "file"}, {"name": "package-lock.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/package-lock.json", "type": "file"}, {"name": "package.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/package.json", "type": "file"}, {"name": "prisma", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/prisma", "type": "directory"}, {"name": "public", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/public", "type": "directory", "children": [{"name": "favicon.ico", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/public/favicon.ico", "type": "file"}, {"name": "kapi-logo.png", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/public/kapi-logo.png", "type": "file"}, {"name": "og-image.png", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/public/og-image.png", "type": "file"}]}, {"name": "requirements.txt", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/requirements.txt", "type": "file"}, {"name": "reset-chromadb-collection.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/reset-chromadb-collection.js", "type": "file"}, {"name": "run_dev.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/run_dev.sh", "type": "file"}, {"name": "screenshot-pages.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/screenshot-pages.js", "type": "file"}, {"name": "scripts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/scripts", "type": "directory"}, {"name": "setup-audit-tools.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/setup-audit-tools.sh", "type": "file"}, {"name": "setup-prisma.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/setup-prisma.sh", "type": "file"}, {"name": "setup-production.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/setup-production.sh", "type": "file"}, {"name": "site-audit.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/site-audit.sh", "type": "file"}, {"name": "src", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src", "type": "directory", "children": [{"name": "admin", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/admin", "type": "directory"}, {"name": "api", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api", "type": "directory", "children": [{"name": "agent", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/agent", "type": "directory"}, {"name": "ai", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/ai", "type": "directory"}, {"name": "auth", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/auth", "type": "directory"}, {"name": "blog", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/blog", "type": "directory"}, {"name": "code-analysis", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/code-analysis", "type": "directory"}, {"name": "conversations", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/conversations", "type": "directory"}, {"name": "payment", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/payment", "type": "directory"}, {"name": "projects", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/projects", "type": "directory"}, {"name": "qa", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/qa", "type": "directory"}, {"name": "search", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search", "type": "directory", "children": [{"name": "rag-search.controller.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search/rag-search.controller.ts", "type": "file"}, {"name": "rag-search.controller.ts.doc.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search/rag-search.controller.ts.doc.json", "type": "file"}, {"name": "rag-search.controller.ts.md", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/search/rag-search.controller.ts.md", "type": "file"}]}, {"name": "social", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/social", "type": "directory"}, {"name": "template", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/template", "type": "directory"}, {"name": "users", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/users", "type": "directory"}, {"name": "workshop", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/api/workshop", "type": "directory"}]}, {"name": "app", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/app", "type": "directory"}, {"name": "app.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/app.ts", "type": "file"}, {"name": "common", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/common", "type": "directory"}, {"name": "config", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/config", "type": "directory"}, {"name": "db", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/db", "type": "directory"}, {"name": "declarations.d.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/declarations.d.ts", "type": "file"}, {"name": "decorators", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/decorators", "type": "directory"}, {"name": "docs", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/docs", "type": "directory"}, {"name": "generated", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/generated", "type": "directory"}, {"name": "guards", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/guards", "type": "directory"}, {"name": "initialize.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/initialize.ts", "type": "file"}, {"name": "inversify.config.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/inversify.config.ts", "type": "file"}, {"name": "middleware", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/middleware", "type": "directory"}, {"name": "next", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/next", "type": "directory"}, {"name": "public", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/public", "type": "directory"}, {"name": "routes", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/routes", "type": "directory"}, {"name": "server.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/server.ts", "type": "file"}, {"name": "services", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/services", "type": "directory"}, {"name": "test-config.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/test-config.ts", "type": "file"}, {"name": "test_doc_advanced.py", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/test_doc_advanced.py", "type": "file"}, {"name": "test_doc_advanced.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/test_doc_advanced.ts", "type": "file"}, {"name": "test_doc_edge_cases.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/test_doc_edge_cases.ts", "type": "file"}, {"name": "test_doc_feature.py", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/test_doc_feature.py", "type": "file"}, {"name": "types", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/types", "type": "directory"}, {"name": "types.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/types.ts", "type": "file"}, {"name": "utils", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/utils", "type": "directory"}, {"name": "views", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/views", "type": "directory"}, {"name": "websockets", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/src/websockets", "type": "directory"}]}, {"name": "start-chromadb.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/start-chromadb.sh", "type": "file"}, {"name": "stop-chromadb.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/stop-chromadb.sh", "type": "file"}, {"name": "swagger-packages.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/swagger-packages.json", "type": "file"}, {"name": "test-azure-embeddings.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-azure-embeddings.js", "type": "file"}, {"name": "test-canvas-flow.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-canvas-flow.js", "type": "file"}, {"name": "test-chromadb-connection.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-chromadb-connection.js", "type": "file"}, {"name": "test-classification.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-classification.js", "type": "file"}, {"name": "test-commit-documentation.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-commit-documentation.js", "type": "file"}, {"name": "test-conversation-multimodal.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-conversation-multimodal.js", "type": "file"}, {"name": "test-doc-all-button.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-doc-all-button.js", "type": "file"}, {"name": "test-doc-generation.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-doc-generation.sh", "type": "file"}, {"name": "test-documentation-generation.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-documentation-generation.js", "type": "file"}, {"name": "test-documentation-semantic-search.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-documentation-semantic-search.js", "type": "file"}, {"name": "test-end-to-end-workflow.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-end-to-end-workflow.js", "type": "file"}, {"name": "test-multimodal-integration-simple.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-multimodal-integration-simple.js", "type": "file"}, {"name": "test-multimodal.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-multimodal.js", "type": "file"}, {"name": "test-project", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-project", "type": "directory"}, {"name": "test-project-python", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-project-python", "type": "directory"}, {"name": "test-python-doc-generation.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-python-doc-generation.sh", "type": "file"}, {"name": "test-selective-documentation.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-selective-documentation.js", "type": "file"}, {"name": "test-semantic-search-api.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-semantic-search-api.js", "type": "file"}, {"name": "test-semantic-search-service.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-semantic-search-service.js", "type": "file"}, {"name": "test-semantic-search-simple.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-semantic-search-simple.js", "type": "file"}, {"name": "test-semantic-search.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-semantic-search.js", "type": "file"}, {"name": "test-setup-check.sh", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-setup-check.sh", "type": "file"}, {"name": "test-slide-integration.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-slide-integration.js", "type": "file"}, {"name": "test-slide-model.js", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-slide-model.js", "type": "file"}, {"name": "test-workflow-project", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test-workflow-project", "type": "directory"}, {"name": "test.ts", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/test.ts", "type": "file"}, {"name": "tests", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/tests", "type": "directory"}, {"name": "tsconfig.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/tsconfig.json", "type": "file"}, {"name": "tsconfig.prod.json", "path": "/home/<USER>/Desktop/kapi-main/kapi/backend/tsconfig.prod.json", "type": "file"}]}], "recentFiles": [], "lastOpenedFiles": {}, "panels": {"leftPanelWidth": "300px", "rightPanelWidth": "300px", "bottomPanelHeight": "190px", "leftPanelCollapsed": false, "rightPanelCollapsed": false, "bottomPanelCollapsed": false}, "metadata": {"createdAt": "2025-07-15T13:42:34.238Z", "lastModified": "2025-07-15T13:44:32.567Z", "backwardsBuildProgress": 0, "description": ""}, "git": {"isGitRepository": false, "repositoryName": null, "currentBranch": null}}