# KAPI Backend Migration Implementation Guide

This guide provides step-by-step instructions for implementing the FastAPI to Node.js/Prisma migration of AI and Workshop models.

## Step 1: Apply Schema Changes

1. The migration provides a new Prisma schema with AI and Workshop models in `prisma/ai_models.prisma`.

2. Run the migration script to apply these changes:
   ```bash
   chmod +x scripts/migrate_ai_models.sh
   ./scripts/migrate_ai_models.sh
   ```

3. This will:
   - Create a combined schema with the new models
   - Run the Prisma migration to update your database
   - Generate an updated Prisma client

## Step 2: Register Repositories in Dependency Injection

1. Add the repository types in `src/types.ts` (already created in this migration)

2. Register the repositories in your dependency injection container. Example:
   ```typescript
   // In your inversify container configuration
   container.bind<AIAgentRepository>(TYPES.AIAgentRepository).to(AIAgentRepository);
   container.bind<WorkshopRepository>(TYPES.WorkshopRepository).to(WorkshopRepository);
   container.bind<ModuleRepository>(TYPES.ModuleRepository).to(ModuleRepository);
   container.bind<EloHistoryRepository>(TYPES.EloHistoryRepository).to(EloHistoryRepository);
   container.bind<KarmaRepository>(TYPES.KarmaRepository).to(KarmaRepository);
   ```

## Step 3: Register Controllers

1. Register the new controllers in your application:
   ```typescript
   // In your controller registration
   container.bind<AIAgentController>(TYPES.AIAgentController).to(AIAgentController);
   container.bind<WorkshopController>(TYPES.WorkshopController).to(WorkshopController);
   ```

2. Add the controllers to your OvernightJS server:
   ```typescript
   // In your server setup
   const aiAgentController = container.get<AIAgentController>(TYPES.AIAgentController);
   const workshopController = container.get<WorkshopController>(TYPES.WorkshopController);
   
   this.app.use(aiAgentController.router);
   this.app.use(workshopController.router);
   ```

## Step 4: Add API Route Documentation

Update your API documentation (e.g., Swagger) to include the new endpoints:

- AI Agent Endpoints:
  - GET /api/ai/agents
  - GET /api/ai/agents/:id
  - POST /api/ai/agents
  - PUT /api/ai/agents/:id
  - DELETE /api/ai/agents/:id
  - GET /api/ai/agents/:id/actions
  - POST /api/ai/agents/:id/actions

- Workshop Endpoints:
  - GET /api/workshops
  - GET /api/workshops/:id
  - POST /api/workshops
  - PUT /api/workshops/:id
  - DELETE /api/workshops/:id
  - GET /api/workshops/:id/participants
  - POST /api/workshops/:id/participants
  - PUT /api/workshops/:id/participants/:userId
  - DELETE /api/workshops/:id/participants/:userId
  - GET /api/workshops/:id/materials
  - POST /api/workshops/:id/materials

## Step 5: Update Conversation Model Integration

The migration adds new fields to the Conversation model. Make sure to update any existing conversation-related code to handle these fields:

1. Update conversation repository functions to include agent relations
2. Update conversation controllers to handle agent information
3. Update conversation service logic to integrate with AI agents

## Testing the Migration

1. Create a sample AI agent:
   ```bash
   curl -X POST http://localhost:3000/api/ai/agents \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "name": "Test Agent",
       "provider": "claude",
       "modelName": "claude-3-opus-20240229",
       "systemPrompt": "You are a helpful assistant."
     }'
   ```

2. Create a sample workshop:
   ```bash
   curl -X POST http://localhost:3000/api/workshops \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "name": "Introduction to AI",
       "workshopType": "ESSENTIALS",
       "startDate": "2025-06-01T09:00:00.000Z",
       "endDate": "2025-06-03T17:00:00.000Z"
     }'
   ```

## Next Steps

1. Implement services for business logic between controllers and repositories
2. Add validation middleware for all endpoints
3. Enhance error handling and logging
4. Add tests for the new endpoints
5. Implement the remaining models (Q&A system) if needed

## Support

If you encounter any issues with the migration, please check:

1. Prisma logs for database migration errors
2. Application logs for runtime errors
3. The migration documentation for detailed model information

