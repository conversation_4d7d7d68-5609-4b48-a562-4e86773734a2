{"path": "backend/src/test_doc_advanced.ts", "contentHash": "eda31020e209a587b83b9845b242b646951d2e4efe83ee92af0e26a38d5254cf", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "file_overview", "unitType": "file", "purpose": "This code defines data structures and functions for managing user profiles and data.", "humanReadableExplanation": "This TypeScript code snippet demonstrates several key concepts.  It starts by defining an interface `UserProfile` to represent user data, including an ID, username, and an optional email. It also defines a type alias `UserStatus` representing the possible states of a user.  The `formatUsername` function takes a `UserProfile` object and returns a formatted username string.  The `DataManager` class is a generic class that manages an array of data. It provides methods to add items, retrieve items by index, and get the count of items. The `fetchUserData` function simulates fetching user data asynchronously using a Promise. Finally, the `createGreeting` function demonstrates optional parameters and default values.", "dependencies": [], "inputs": [{"name": "user", "type": "UserProfile", "description": "The user profile object containing user ID and username."}, {"name": "userId", "type": "string", "description": "The ID of the user whose data needs to be fetched."}, {"name": "name", "type": "string", "description": "The name to include in the greeting."}, {"name": "greeting", "type": "string", "description": "The greeting to use (defaults to 'Hello')."}, {"name": "initialData", "type": "T[]", "description": "Initial data for the DataManager."}, {"name": "item", "type": "T", "description": "Item to be added to the DataManager."}, {"name": "index", "type": "number", "description": "Index of the item to retrieve from the DataManager."}], "outputs": {"type": "string", "description": "A formatted username string.", "throws": []}, "visualDiagram": "classDiagram\n    class UserProfile{\n        id: string\n        username: string\n        email?: string\n    }\n    class DataManager<T>{\n        -data: T[]\n        +addItem(item: T): void\n        +getItem(index: number): T | undefined\n        +count: number\n        +DataManager(initialData: T[] = [])\n    }\n    function formatUsername(user: UserProfile): string\n    function fetchUserData(userId: string): Promise<UserProfile>\n    function createGreeting(name: string, greeting: string = \"Hello\"): string\n"}]}