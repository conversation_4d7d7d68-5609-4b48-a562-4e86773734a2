# Documentation for `../src/services/documentation.service.ts`

**Commit:** `1b54a030ed1b088c138034b5554ddddd69e29d2b` | **Last Updated:** `2025-07-10T15:36:17+05:30`

---

## `generateDocumentation` (Function)

**Purpose:** This function generates documentation for a project or a specific file by executing a Python script.

### Detailed Explanation

The `generateDocumentation` function orchestrates the generation of documentation by calling an external Python script. It takes the project path and optionally a file path as input.  If a file path is provided, documentation is generated for that specific file; otherwise, it generates documentation for the entire project. The function uses Node.js's `child_process.spawn` to execute the Python script via `poetry`, handling both standard output and error streams.  The function returns a Promise that resolves if the Python script executes successfully (exit code 0) and rejects otherwise, providing informative error messages in both cases.  The Python script's location is hardcoded relative to the location of this TypeScript file.

### Visual Representation

```mermaid
graph TD
    A[generateDocumentation(projectPath, filePath)] --> B{filePath provided?};
    B -- Yes --> C[Execute python3 generate_docs.py --file filePath];
    B -- No --> D[Execute python3 generate_docs.py --dir projectPath];
    C --> E[Handle stdout/stderr];
    D --> E;
    E --> F{Exit code 0?};
    F -- Yes --> G[Resolve Promise];
    F -- No --> H[Reject Promise with error];
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `projectPath` | `string` | The path to the project directory. Required if filePath is not provided. |
| `filePath` | `string` | The path to the specific file for which to generate documentation. Optional. If provided, projectPath is used only to locate the python script relative to it. |

### Outputs

- **Returns:** `Promise<void>` - This function returns a Promise that resolves if the documentation generation is successful and rejects if there's an error during execution.
- **Throws:** `Error: Documentation generation failed with exit code {code}`, `Error: Failed to start documentation generation script: {error details}`

### Dependencies

- **path** (internal)
- **poetry** (external)
- **python3** (external)

---

