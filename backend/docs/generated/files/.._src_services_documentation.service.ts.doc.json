{"path": "../src/services/documentation.service.ts", "contentHash": "9a964c3a4dfb67c9561cb28a22572ae80f0563e207f2e1e8ce1584c228d6e1e2", "commit": "1b54a030ed1b088c138034b5554ddddd69e29d2b", "timestamp": "2025-07-10T15:36:17+05:30", "units": [{"unitName": "generateDocumentation", "unitType": "function", "purpose": "This function generates documentation for a project or a specific file by executing a Python script.", "humanReadableExplanation": "The `generateDocumentation` function orchestrates the generation of documentation by calling an external Python script. It takes the project path and optionally a file path as input.  If a file path is provided, documentation is generated for that specific file; otherwise, it generates documentation for the entire project. The function uses Node.js's `child_process.spawn` to execute the Python script via `poetry`, handling both standard output and error streams.  The function returns a Promise that resolves if the Python script executes successfully (exit code 0) and rejects otherwise, providing informative error messages in both cases.  The Python script's location is hardcoded relative to the location of this TypeScript file.", "dependencies": [{"type": "internal", "name": "path"}, {"type": "external", "name": "poetry"}, {"type": "external", "name": "python3"}], "inputs": [{"name": "projectPath", "type": "string", "description": "The path to the project directory. Required if filePath is not provided."}, {"name": "filePath", "type": "string", "description": "The path to the specific file for which to generate documentation. Optional. If provided, projectPath is used only to locate the python script relative to it."}], "outputs": {"type": "Promise<void>", "description": "This function returns a Promise that resolves if the documentation generation is successful and rejects if there's an error during execution.", "throws": ["Error: Documentation generation failed with exit code {code}", "Error: Failed to start documentation generation script: {error details}"]}, "visualDiagram": "graph TD\n    A[generateDocumentation(projectPath, filePath)] --> B{filePath provided?};\n    B -- Yes --> C[Execute python3 generate_docs.py --file filePath];\n    B -- No --> D[Execute python3 generate_docs.py --dir projectPath];\n    C --> E[Handle stdout/stderr];\n    D --> E;\n    E --> F{Exit code 0?};\n    F -- Yes --> G[Resolve Promise];\n    F -- No --> H[Reject Promise with error];"}]}