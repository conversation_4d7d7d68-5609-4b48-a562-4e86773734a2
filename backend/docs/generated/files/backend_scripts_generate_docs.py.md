# Documentation for `backend/scripts/generate_docs.py`

**Commit:** `52c73789c52a307b5ea20a22736f0b03959c85e2` | **Last Updated:** `2025-07-05T14:14:09+05:30`

---

## `log_error` (Function)

**Purpose:** The `log_error` function prints an error message to the standard error stream.

### Detailed Explanation

This function takes a single argument, `message`, which is expected to be a string representing an error message.  It then uses the built-in `print` function to output this message. The `file=sys.stderr` argument redirects the output to the standard error stream (stderr).  Stderr is typically used for error messages to distinguish them from standard output (stdout), which is usually reserved for program results. This separation helps in debugging and monitoring applications, as error messages are clearly identifiable.

### Visual Representation

```mermaid
graph TD
    A[Start] --> B{log_error(message)};
    B --> C[print(message, file=sys.stderr)];
    C --> D[End];
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `message` | `str` | The error message to be printed. |

### Outputs

- **Returns:** `null` - This function does not return any value.

---

## `log_info` (Function)

**Purpose:** The `log_info` function prints informational messages to the standard error stream.

### Detailed Explanation

This function takes a message as input and prints it to the standard error stream (sys.stderr).  Standard error is typically used for logging and displaying non-critical messages that should be visible to the user or system administrator. This helps separate informational messages from the standard output (stdout) which is usually reserved for the main program output. Using sys.stderr ensures that even if the standard output is redirected, these informational messages will still be visible.

### Visual Representation

```mermaid
graph TD
    A[Start];
    B[log_info(message)];
    C[print(message, file=sys.stderr)];
    D[End];
    A --> B;
    B --> C;
    C --> D;
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `message` | `str` | The message to be printed. |

### Outputs

- **Returns:** `None` - This function does not return any value.

### Dependencies

- **sys** (internal)

---

## `LLMClient` (Class)

**Purpose:** The LLMClient class facilitates interaction with a large language model (LLM) API, such as Gemini, to generate documentation and diagrams.

### Detailed Explanation

This class acts as an interface to an LLM API.  It initializes with an API key and model name from environment variables.  The `generate_documentation` method sends a prompt to the LLM to generate documentation in JSON format for a given code snippet.  The `generate_diagram` method takes the generated JSON documentation and a code snippet to create a Mermaid.js diagram.  Both methods include retry logic and handle potential errors during API calls or JSON parsing.  The prompts are carefully constructed to guide the LLM in generating accurate and detailed documentation and diagrams.

### Visual Representation

```mermaid
```mermaid
classDiagram
    class LLMClient {
        -api_key : string
        -model_name : string
        -api_endpoint : string
        +__init__()
        +_build_prompt()
        +_build_diagram_prompt()
        +generate_diagram()
        +generate_documentation()
    }

    LLMClient : __init__()
    LLMClient : _build_prompt()
    LLMClient : _build_diagram_prompt()
    LLMClient : generate_diagram()
    LLMClient : generate_documentation()

    LLMClient --> requests
    LLMClient --> os
    LLMClient --> json
    LLMClient --> google.generativelanguage

    class requests {
    }

    class os {
    }

    class json {
    }

    class google.generativelanguage {
    }
```
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `file_path` | `string` | The path to the file containing the code snippet. |
| `file_content` | `string` | The full content of the file. |
| `code_snippet` | `string` | The code snippet to analyze. |
| `unit_type` | `string` | The type of code unit (e.g., 'function', 'class'). |
| `max_retries` | `integer` | The maximum number of retries for API requests. |
| `timeout` | `integer` | The timeout for API requests in seconds. |

### Outputs

- **Returns:** `object|None` - For `generate_documentation`, returns a JSON object containing the generated documentation. For `generate_diagram`, returns a string containing the Mermaid.js diagram syntax. Returns None if there is an error.
- **Throws:** `ValueError`, `requests.exceptions.RequestException`, `json.JSONDecodeError`, `KeyError`, `IndexError`

### Dependencies

- **requests** (external)
- **os** (external)
- **json** (external)
- **google.generativelanguage** (external)

---

## `get_staged_files` (Function)

**Purpose:** This function retrieves a list of files that have been staged in the Git repository.

### Detailed Explanation

The `get_staged_files` function uses the `subprocess` module to interact with the Git command-line interface. It first determines the root directory of the Git repository using `git rev-parse --show-toplevel`. Then, it changes the current working directory to the Git root using `os.chdir`.  After that, it executes the command `git diff --cached --name-only --diff-filter=AM` to get a list of staged files that have been added or modified. The output of this command is a newline-separated list of file paths. The function then splits this list into individual file paths, removes any empty strings, and returns the resulting list.  If any error occurs during the Git commands execution (e.g., `git` is not found or a Git command fails), it catches the exception, logs an error message using the `log_error` function, and returns an empty list.

### Visual Representation

```mermaid
graph TD
    A[Start] --> B{Get Git Root (git rev-parse --show-toplevel)};
    B -- Success --> C[Change Directory to Git Root (os.chdir)];
    B -- Failure --> G[Error Handling (log_error, return [])];
    C --> D{Run git diff (git diff --cached --name-only --diff-filter=AM)};
    D -- Success --> E{Split & Clean Output (split('\n'), remove empty)};
    D -- Failure --> G;
    E --> F[Return File List];
    G --> A;
```

### Outputs

- **Returns:** `list` - A list of strings, where each string is the path to a staged file in the Git repository. Returns an empty list if there is an error or no staged files.
- **Throws:** `subprocess.CalledProcessError`, `FileNotFoundError`

### Dependencies

- **subprocess** (external)
- **os** (external)
- **git** (external)

---

## `calculate_hash` (Function)

**Purpose:** The `calculate_hash` function computes a SHA-256 hash of the input content.

### Detailed Explanation

This function takes a string as input and uses the `hashlib` library to generate a SHA-256 hash.  SHA-256 is a cryptographic hash function that produces a 256-bit (64-character hexadecimal) hash. This hash is used to uniquely identify the input string; even a small change in the input will result in a drastically different hash. The function first encodes the input string into UTF-8 bytes before hashing it, ensuring consistent results across different systems. The resulting hexadecimal representation of the hash is then returned.

### Visual Representation

```mermaid
graph TD
    A[Start] --> B{Input: content (str)};
    B --> C[Encode content to UTF-8];
    C --> D[Compute SHA-256 hash];
    D --> E[Return hexadecimal hash (str)];
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `content` | `str` | The string content to be hashed. |

### Outputs

- **Returns:** `str` - A 64-character hexadecimal string representing the SHA-256 hash of the input content.

### Dependencies

- **hashlib** (internal)

---

## `parse_js_ts_with_helper` (Function)

**Purpose:** This function parses JavaScript or TypeScript files using a Node.js helper script or falls back to a regular expression-based parser.

### Detailed Explanation

The `parse_js_ts_with_helper` function aims to parse JavaScript and TypeScript files efficiently. It prioritizes using a Node.js helper script (`parse_js_ts.ts`) for robust parsing.  The helper script's path is constructed relative to the current script's location. The function then uses `subprocess.run` to execute the helper script, passing the file path as an argument. The script's standard output, which is expected to be a JSON string, is parsed using `json.loads`. If any error occurs during this process (e.g., the helper script fails, the output is not valid JSON, or the helper script file is not found), the function gracefully falls back to a regular expression-based parser (`parse_with_regex`). This fallback mechanism ensures that parsing attempts to continue even if the preferred method encounters issues. The fallback parser reads the file content and attempts to extract relevant information using regular expressions. The function then returns the parsed data, either from the helper script or the fallback parser.

### Visual Representation

```mermaid
graph TD;A[parse_js_ts_with_helper(file_path)];B{Try ts-node helper};C[Execute ts-node parse_js_ts.ts file_path];D[json.loads(result.stdout)];E[Return parsed JSON];F{Catch Error};G[parse_with_regex(file_content)];H[Return parsed data];A-->B;B-->C;C-->D;D-->E;B-->F;F-->G;G-->H;E-->A;H-->A
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `file_path` | `string` | The path to the JavaScript or TypeScript file to be parsed. |

### Outputs

- **Returns:** `object` - A JSON object representing the parsed content of the file. The structure of this object depends on the parsing method used (helper script or regex).  If parsing fails completely, it may return None.
- **Throws:** `subprocess.CalledProcessError`, `json.JSONDecodeError`, `FileNotFoundError`

### Dependencies

- **parse_with_regex** (internal)
- **ts-node** (external)
- **parse_js_ts.ts** (external)
- **subprocess** (external)
- **json** (external)
- **os** (external)
- **FileNotFoundError** (external)
- **json.JSONDecodeError** (external)
- **subprocess.CalledProcessError** (external)

---

## `parse_file_for_units` (Function)

**Purpose:** The `parse_file_for_units` function determines the appropriate parsing method for a given file based on its extension and parses the file content to extract its constituent units (functions or classes).

### Detailed Explanation

This function acts as a router for parsing different file types.  It takes a file path and its content as input. If the file ends with '.py', it uses the `parse_python_with_ast` function to parse the file using Abstract Syntax Trees (ASTs). This is a more robust method for Python files as it leverages Python's internal understanding of the code structure.  If the file ends with '.js', '.ts', or '.tsx', it uses the `parse_js_ts_with_helper` function, which likely employs a Node.js helper script for more accurate parsing of JavaScript and TypeScript files. For all other file types, it falls back to a regular expression-based parser (`parse_with_regex`), which is less precise but provides broader support. The function then returns a list of parsed units, each represented as a dictionary containing the unit's name, type ('function' or 'class'), and its source code segment.

### Visual Representation

```mermaid
graph TD
    A[parse_file_for_units(file_path, content)] --> B{file_path.endswith('.py')?};
    B -- Yes --> C[parse_python_with_ast(content)];
    B -- No --> D{file_path.endswith('.js','.ts','.tsx')?};
    D -- Yes --> E[parse_js_ts_with_helper(file_path)];
    D -- No --> F[parse_with_regex(content)];
    C --> G[return units];
    E --> G;
    F --> G;
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `file_path` | `string` | The path to the file to be parsed. |
| `content` | `string` | The content of the file to be parsed. |

### Outputs

- **Returns:** `list` - A list of dictionaries, where each dictionary represents a parsed unit (function or class) from the file. Each unit dictionary contains the keys: 'name', 'type', and 'content'.  Returns an empty list if no units are found or parsing fails.

### Dependencies

- **parse_python_with_ast** (internal)
- **parse_js_ts_with_helper** (internal)
- **parse_with_regex** (internal)

---

## `parse_python_with_ast` (Function)

**Purpose:** This function parses Python code using the Abstract Syntax Tree (AST) to extract top-level functions and classes, returning them as a list of dictionaries.

### Detailed Explanation

The `parse_python_with_ast` function takes Python code as input and uses the `ast` module to analyze its structure. It iterates through the top-level nodes of the AST, identifying functions (`ast.FunctionDef`, `ast.AsyncFunctionDef`) and classes (`ast.ClassDef`). For each identified function or class, it extracts the source code segment using `ast.get_source_segment` and stores it in a dictionary along with the unit's name and type.  If no functions or classes are found, it returns a dictionary representing the file overview. If a `SyntaxError` occurs during AST parsing, it falls back to a regular expression-based parsing method (`parse_with_regex`). The function returns a list of dictionaries, where each dictionary represents a function or class found in the code, or a file overview if no functions or classes are found.

### Visual Representation

```mermaid
graph TD
    A[parse_python_with_ast(content)] --> B{SyntaxError?};
    B -- Yes --> C[parse_with_regex(content)];
    B -- No --> D[Iterate through AST nodes];
    D --> E{Is node a function or class?};
    E -- Yes --> F[Extract source, add to units];
    E -- No --> G[Continue iteration];
    F --> H[Return units];
    G --> H;
    C --> H;
    H --> I[List of dictionaries (functions, classes, or file overview)];
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `content` | `str` | The Python code to be parsed. |

### Outputs

- **Returns:** `list` - A list of dictionaries. Each dictionary contains the name, type ('function' or 'class'), and source code of a function or class found in the input. If no functions or classes are found, it contains a single dictionary with information about the file overview.
- **Throws:** `SyntaxError`

### Dependencies

- **ast** (internal)
- **log_error** (internal)
- **parse_with_regex** (internal)

---

## `parse_with_regex` (Function)

**Purpose:** This function parses code content to identify functions and classes, returning them as a list of units.

### Detailed Explanation

The `parse_with_regex` function uses regular expressions to locate and extract function and class definitions from a given code string.  It first defines two regular expression patterns: `class_pattern` for classes and `func_pattern` for functions (handling both regular and async functions). These patterns are designed to match even if decorators are present. The function then iterates through the code using these patterns, finding all matches. Each match provides the type ('class' or 'function'), name, and start and end indices within the code string.  These matches are stored as tuples in the `found_spans` list. The list is sorted by starting index to ensure correct order. Finally, it iterates through the sorted matches, extracting the code segment between the start and end indices and creating a dictionary for each unit with its name, type, and content. If no functions or classes are found, it returns a single unit representing the entire file content. The function returns a list of dictionaries, where each dictionary represents a function or class found in the input code, containing its name, type, and code content.

### Visual Representation

```mermaid
graph TD
    A[Input: Code Content] --> B{Regex Matching (class_pattern)};
    A --> C{Regex Matching (func_pattern)};
    B --> D[found_spans (class)];
    C --> E[found_spans (function)];
    D --> F{Merge & Sort by Start Index};
    E --> F;
    F --> G{Extract Code Segments};
    G --> H[Output: List of Units];
    H -- No functions/classes found --> I[Output: Single "file_overview" Unit];
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `content` | `str` | The code content to be parsed. |

### Outputs

- **Returns:** `list` - A list of dictionaries, where each dictionary represents a function or class found in the input code. Each dictionary contains the keys "name", "type", and "content". If no functions or classes are found, it returns a list containing a single dictionary representing the entire file content.

### Dependencies

- **re** (internal)

---

## `generate_markdown_from_doc` (Function)

**Purpose:** This function generates a Markdown documentation file from structured JSON documentation data.

### Detailed Explanation

The function takes a JSON file path and its corresponding data as input. It then creates a Markdown file with the same name but a '.md' extension. The Markdown file contains a structured representation of the documentation, including the file path, commit hash, timestamp, and individual units (functions or classes). For each unit, it includes its purpose, detailed explanation, visual diagram (if available), inputs, outputs, and dependencies.  The function handles potential errors during file writing and returns the path to the generated Markdown file or None if an error occurs.

### Visual Representation

```mermaid
graph TD
    A[generate_markdown_from_doc(doc_json_path, doc_data)] --> B{Open & Check doc_json_path};
    B -- Success --> C[Write Markdown Header];
    C --> D{Iterate through doc_data['units']};
    D -- More Units --> E[Write Unit Section];
    E --> D;
    D -- No More Units --> F[Write Footer];
    F --> G{Close File};
    G -- Success --> H[Return md_path];
    B -- Error --> I[Return None];
    G -- Error --> I;
    E --> J{Write Purpose, Explanation, Diagram, Inputs, Outputs, Dependencies};
    I[Error Handling] --> K[log_error];
    K --> Return None
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `doc_json_path` | `str` | The path to the input JSON documentation file. |
| `doc_data` | `dict` | A dictionary containing the structured documentation data. |

### Outputs

- **Returns:** `str or None` - The path to the generated Markdown file if successful, otherwise None.
- **Throws:** `IOError`, `Exception`

### Dependencies

- **log_info** (internal)
- **log_error** (internal)

---

## `process_file` (Function)

**Purpose:** The `process_file` function processes a given file, generating and saving its documentation as JSON and Markdown files, utilizing an LLM client for content generation.

### Detailed Explanation

This function is the core of the documentation generation process. It takes a file path and an LLM client as input. First, it reads the file's content and calculates its SHA256 hash to detect changes. If the file has not changed since the last documentation generation, it skips processing. Otherwise, it parses the file to identify code units (functions, classes, etc.) using the `parse_file_for_units` function. For each unit, it uses the LLM client to generate documentation in JSON format and then generates a Mermaid.js diagram using the `generate_diagram` function.  The generated documentation, along with the diagram and metadata (file path, commit hash, timestamp), is saved as a JSON file. Finally, it generates a Markdown file from this JSON data for easier readability.  The function returns the paths to the generated JSON and Markdown files, or None if any errors occur during the process.

### Visual Representation

```mermaid
graph TD
    A[Start];
    B{Read File};
    C{Calculate Hash};
    D{Check for Existing Doc};
    E{File Changed?};
    F[Parse File for Units];
    G{Units Found?};
    H[Generate Documentation (LLM)];
    I[Generate Diagram (LLM)];
    J[Save JSON Doc];
    K[Generate Markdown];
    L[Save Markdown Doc];
    M[Return Paths];
    N[Return (None, None)];
    A --> B;
    B -- Error --> N;
    B --> C;
    C --> D;
    D -- Exists & Unchanged --> N;
    D --> E;
    E -- Yes --> F;
    E -- No --> N;
    F -- Error --> N;
    F --> G;
    G -- Yes --> H;
    G -- No --> N;
    H -- Error --> N;
    H --> I;
    I --> J;
    J -- Error --> N;
    J --> K;
    K -- Error --> N;
    K --> L;
    L --> M;
```

### Inputs

| Name | Type | Description |
|------|------|-------------|
| `file_path` | `str` | The path to the file to be processed. |
| `llm_client` | `LLMClient` | An instance of the LLMClient class, used to generate documentation and diagrams via an LLM API. |

### Outputs

- **Returns:** `[str, str] or [None, None]` - Returns a tuple containing the paths to the generated JSON and Markdown documentation files. Returns (None, None) if any error occurs or if the file is unchanged.
- **Throws:** `IOError`, `json.JSONDecodeError`, `Exception`

### Dependencies

- **calculate_hash** (internal)
- **parse_file_for_units** (internal)
- **get_git_info** (internal)
- **generate_markdown_from_doc** (internal)
- **LLMClient** (internal)

---

## `get_git_info` (Function)

**Purpose:** The `get_git_info` function retrieves the latest commit hash and timestamp from the Git repository.

### Detailed Explanation

This function attempts to fetch the most recent commit's hash and timestamp from the local Git repository. It uses the `subprocess` module to execute Git commands.  If the `git` commands are successful, it returns the commit hash (a string) and the timestamp (a string in ISO 8601 format). If any error occurs during the Git command execution (e.g., not being in a Git repository), it returns default values: "unknown_commit" for the hash and the current timestamp in ISO 8601 format with Z timezone indicator for the timestamp.

### Visual Representation

```mermaid
graph TD
    A[Start];
    B{Execute 'git rev-parse HEAD'};
    C[Success];
    D{Execute 'git log -1 --format=%cd --date=iso-strict'};
    E[Success];
    F[Return commit_hash, timestamp];
    G[Error];
    H[Return "unknown_commit", current timestamp];
    A --> B;
    B --> C;
    B --> G;
    C --> D;
    D --> E;
    D --> G;
    E --> F;
    G --> H;
```

### Outputs

- **Returns:** `tuple` - Returns a tuple containing the commit hash (string) and timestamp (string). If Git commands fail, it returns ("unknown_commit", current timestamp).
- **Throws:** `subprocess.CalledProcessError`

### Dependencies

- **subprocess** (internal)
- **datetime** (internal)

---

## `load_config` (Function)

**Purpose:** This function loads the documentation generation configuration from a JSON file, providing default values if the file is missing or unreadable.

### Detailed Explanation

The `load_config` function attempts to read a JSON configuration file located at a relative path from the script's location.  The path is constructed using `os.path.join` to ensure platform independence. It uses a `try-except` block to handle potential errors: `FileNotFoundError` if the file doesn't exist and `json.JSONDecodeError` if the file's contents are not valid JSON. If an error occurs, it logs a warning message using the `log_error` function and returns a default configuration dictionary containing lists of allowed file extensions and directories to ignore. If the file is successfully read, the function uses `json.load` to parse the JSON data and returns the resulting Python dictionary.

### Visual Representation

```mermaid
graph TD
    A[Start];
    B{File Exists?};
    C[Read config file];
    D{Valid JSON?};
    E[Return config];
    F[Log Error & Return Default Config];
    A --> B;
    B -- Yes --> C;
    C --> D;
    D -- Yes --> E;
    D -- No --> F;
    B -- No --> F;
    E --> G[End];
    F --> G;
```

### Outputs

- **Returns:** `dict` - A dictionary containing the configuration settings. Returns a default configuration if the file is not found or is invalid.
- **Throws:** `FileNotFoundError`, `json.JSONDecodeError`

### Dependencies

- **os** (internal)
- **json** (internal)
- **log_error** (internal)

---

## `main` (Function)

**Purpose:** This function is the main entry point of the script, orchestrating the generation of documentation for staged files in a Git repository.

### Detailed Explanation

The `main` function serves as the primary driver for the documentation generation process. It begins by loading configuration settings, including allowed file extensions and directories to ignore.  It then attempts to initialize an LLMClient to interface with a large language model API. If the API key is missing or invalid, it exits with an error. Next, it retrieves a list of staged files using Git commands. For each staged file within the 'backend' directory, it checks if the file extension is allowed and if the file is not located within an ignored directory. If both conditions are true, the `process_file` function is called to generate documentation (JSON and Markdown) for the file.  The paths to the generated JSON and Markdown files are stored. Finally, if any documentation files were generated, their paths are printed to the console; otherwise, a message indicating that no documentation was updated is logged.

### Visual Representation

```mermaid
graph TD
    A[Load Config];
    B{Initialize LLMClient};
    C[Get Staged Files];
    D{Files Staged?};
    E[Process Each File];
    F{File Allowed & Not Ignored?};
    G[Generate Documentation (JSON & Markdown)];
    H[Print Generated File Paths];
    I[Log 'No documentation updated'];
    A --> B;
    B -- Success --> C;
    B -- Error --> J[Exit with Error];
    C --> D;
    D -- Yes --> E;
    D -- No --> K[Log 'No staged files' & Exit];
    E --> F;
    F -- Yes --> G;
    F -- No --> E;
    G --> H;
    H --> L[Exit];
    I --> L;
    J --> L;
    K --> L
```

### Outputs

- **Returns:** `null` - This function does not return a value. It prints the paths of generated documentation files to standard output or logs a message if no files were processed.

### Dependencies

- **load_config** (internal)
- **LLMClient** (internal)
- **get_staged_files** (internal)
- **process_file** (internal)
- **log_info** (internal)
- **log_error** (internal)
- **Git** (external)
- **os** (external)
- **sys** (external)

---

