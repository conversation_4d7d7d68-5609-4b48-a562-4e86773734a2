{"path": "backend/src/app.ts", "contentHash": "161a5b12573b367fd75d8c42ed86d7adb875366e2b982796253f5cfed6536808", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "file_overview", "unitType": "file", "purpose": "This code snippet sets up an Express.js server with various middleware for security, routing, and error handling, and integrates with Next.js for web application serving.", "humanReadableExplanation": "This Express.js server utilizes numerous middleware functions to enhance security, manage requests, and handle errors.  It starts by initializing a Next.js application for serving the frontend.  Then, it configures Nunjucks for templating.  A custom middleware function `rawBodyMiddleware` is implemented to handle raw JSON bodies for webhook verification.  Several security middleware functions from `security.middleware` are applied, including `configureHelmet`, `configureCors`, and `securityHeaders`.  Compression middleware is used, but it excludes SSE streams and respects a `skipCompression` flag.  JSON body parsing is handled, with a larger limit for specific multimodal endpoints.  `express.urlencoded` is used to handle URL-encoded data.  `cookieParser` parses cookies.  Input sanitization is performed using `sanitizeInput`.  Rate limiting is applied to API routes, excluding authentication routes.  Global session middleware is added using `express-session`, with logging for debugging.  Static files are served from the `public` directory.  Health check endpoints are defined at `/health` and `/api/health`.  Swagger UI is integrated for API documentation at `/api-docs`.  API routes are mounted using the `routes` module.  Documentation routes are mounted at `/api/documentation`.  Admin UI routes are mounted at `/admin` asynchronously, with a timeout to prevent indefinite blocking.  Finally, default, 404, and error handlers are registered.", "dependencies": [{"type": "external", "name": "express"}, {"type": "external", "name": "compression"}, {"type": "external", "name": "path"}, {"type": "external", "name": "nunjucks"}, {"type": "external", "name": "swagger-ui-express"}, {"type": "external", "name": "express-session"}, {"type": "external", "name": "cookie-parser"}, {"type": "internal", "name": "specs"}, {"type": "internal", "name": "initNextApp"}, {"type": "internal", "name": "nextMiddleware"}, {"type": "internal", "name": "configureCors"}, {"type": "internal", "name": "configureHelmet"}, {"type": "internal", "name": "apiRateLimiter"}, {"type": "internal", "name": "sanitizeInput"}, {"type": "internal", "name": "securityHeaders"}, {"type": "internal", "name": "secureErrorHandler"}, {"type": "internal", "name": "notFoundHandler"}, {"type": "internal", "name": "routes"}, {"type": "internal", "name": "logger"}, {"type": "internal", "name": "documentationRoutes"}], "inputs": [], "outputs": {"type": "object", "description": "An Express.js application instance.", "throws": []}, "visualDiagram": "classDiagram\n    class ExpressApp {\n        +use(middleware: Function): void\n        +get(path: string, handler: Function): void\n        +set(setting: string, value: any): void\n    }\n    \n    class NextMiddleware {\n        +handle(req: Request, res: Response, next: NextFunction): void\n    }\n    \n    class SecurityMiddleware {\n        +configureCors(): Function\n        +configureHelmet(): Function\n        +apiRateLimiter(req: Request, res: Response, next: NextFunction): void\n        +sanitizeInput(req: Request, res: Response, next: NextFunction): void\n        +securityHeaders(req: Request, res: Response, next: NextFunction): void\n    }\n    \n    class ErrorMiddleware {\n        +secureErrorHandler(err: Error, req: Request, res: Response, next: NextFunction): void\n        +notFoundHandler(req: Request, res: Response, next: NextFunction): void\n    }\n    \n    class Routes {\n        +uiRouter: Router\n    }\n    \n    ExpressApp \"app\" --|> NextMiddleware\n    ExpressApp \"app\" --|> SecurityMiddleware\n    ExpressApp \"app\" --|> ErrorMiddleware\n    ExpressApp \"app\" --|> Routes\n    ExpressApp \"app\" --|> SwaggerUI\n    ExpressApp \"app\" --|> SessionMiddleware\n    ExpressApp \"app\" --|> RawBodyMiddleware\n    ExpressApp \"app\" --|> CompressionMiddleware\n    ExpressApp \"app\" --|> JsonMiddleware\n    ExpressApp \"app\" --|> UrlEncodedMiddleware\n    ExpressApp \"app\" --|> CookieParserMiddleware\n    ExpressApp \"app\" --|> SanitizeInputMiddleware\n    ExpressApp \"app\" --|> RateLimiterMiddleware\n    ExpressApp \"app\" --|> LoggerMiddleware\n    ExpressApp \"app\" --|> StaticMiddleware\n    ExpressApp \"app\" --|> HealthCheckMiddleware\n    ExpressApp \"app\" --|> DocumentationRoutesMiddleware\n    ExpressApp \"app\" --|> AdminUIMiddleware\n    "}]}