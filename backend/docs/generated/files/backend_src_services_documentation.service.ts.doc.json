{"path": "backend/src/services/documentation.service.ts", "contentHash": "22fbe406dd2fa2c74c83c4080d382f2c0fe7031fe19e34d8f1f6db5101d58c45", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "file_overview", "unitType": "file", "purpose": "This code snippet automatically generates documentation for changed files within a given project by leveraging Git's diff functionality and adding a standardized header to each file.", "humanReadableExplanation": "The code consists of three main functions.  `getChangedFiles` uses the `simple-git` library to identify files modified since the last commit. It returns an array of file paths. `generateDocForFile` reads a file, adds a JSDoc-style comment block containing a description, last modified timestamp, and then returns the updated file content.  `generateDocumentation` orchestrates the process: it gets the changed files, iterates through them, calls `generateDocForFile` to add documentation, and then overwrites the original files with the updated content.  The placeholder documentation is rudimentary; a real-world application would likely use a more sophisticated documentation generation technique.", "dependencies": [{"type": "external", "name": "simple-git"}, {"type": "internal", "name": "fs/promises"}, {"type": "internal", "name": "path"}], "inputs": [{"name": "projectPath", "type": "string", "description": "The file path to the project root directory containing the .git repository."}], "outputs": {"type": "void", "description": "This function does not return a value; it modifies files in place.", "throws": ["Error from simpleGit.diff", "Error from fs.readFile", "Error from fs.writeFile"]}, "visualDiagram": "classDiagram\n    class generateDocumentation {\n        +generateDocumentation(projectPath: string): Promise<void>\n    }\n    class getChangedFiles {\n        +getChangedFiles(projectPath: string): Promise<string[]>\n    }\n    class generateDocForFile {\n        +generateDocForFile(filePath: string): Promise<string>\n    }\n    generateDocumentation --|> getChangedFiles\n    generateDocumentation --|> generateDocForFile\n    getChangedFiles --> simpleGit\n    generateDocForFile --> fs.readFile\n    generateDocForFile --> fs.writeFile\n    generateDocForFile --> path.basename\n    generateDocForFile --> new Date().toISOString"}]}