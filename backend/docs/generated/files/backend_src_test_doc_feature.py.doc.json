{"path": "backend/src/test_doc_feature.py", "contentHash": "5eed6d041170726080c49ff102e24b75ed5da68cd9a1d8e9dcdf9cd913bbcd46", "commit": "52c73789c52a307b5ea20a22736f0b03959c85e2", "timestamp": "2025-07-05T14:14:09+05:30", "units": [{"unitName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "unitType": "function", "purpose": "This function calculates the nth <PERSON><PERSON><PERSON><PERSON> number using dynamic programming.", "humanReadableExplanation": "The `calculate_fibonacci` function computes the nth Fibonacci number efficiently using dynamic programming.  It first handles base cases where n is 0 or 1. For n greater than 1, it initializes a list `fib` with the first two Fibonacci numbers (0 and 1). It then iteratively calculates subsequent Fibonacci numbers by adding the two preceding numbers in the `fib` list and appending the result. Finally, it returns the nth Fibonacci number from the `fib` list.  The function raises a ValueError if the input `n` is negative.", "dependencies": [], "inputs": [{"name": "n", "type": "int", "description": "A positive integer representing which <PERSON><PERSON><PERSON><PERSON> number to calculate"}], "outputs": {"type": "int", "description": "The nth <PERSON><PERSON><PERSON><PERSON> number", "throws": ["ValueError"]}, "visualDiagram": "graph TD\n    A[Start];\n    B{n < 0?};\n    C[Raise ValueError];\n    D{n <= 1?};\n    E[Return n];\n    F[Initialize fib = [0, 1]];\n    G[i = 2 to n];\n    H[fib.append(fib[i-1] + fib[i-2])];\n    I[Return fib[n]];\n    A --> B;\n    B -- Yes --> C;\n    B -- No --> D;\n    D -- Yes --> E;\n    D -- No --> F;\n    F --> G;\n    G --> H;\n    H --> G;\n    G -- End --> I;\n    C --> A;\n    E --> A;\n    I --> A;"}, {"unitName": "DocumentationTester", "unitType": "class", "purpose": "The `DocumentationTester` class simulates running tests and provides a summary of the tests executed.", "humanReadableExplanation": "This Python class `DocumentationTester` is designed to mimic a testing framework.  It's primarily used for demonstrating how to generate documentation for classes and methods.  The class initializes with a name and keeps track of the number of tests run. The `run_test` method simulates running a single test (it always passes in this example) and increments the test counter.  The `get_test_summary` method returns a dictionary containing the tester's name and the total number of tests run.  This class is useful for showcasing documentation generation tools, not for actual testing.", "dependencies": [], "inputs": [{"name": "name", "type": "str", "description": "A string identifier for this tester instance."}, {"name": "test_name", "type": "str", "description": "Name of the test being run."}], "outputs": {"type": "dict", "description": "A dictionary containing test statistics.", "throws": []}, "visualDiagram": "classDiagram\n    class DocumentationTester {\n        - name: str\n        - test_count: int\n        + __init__(name: str)\n        + run_test(test_name: str): bool\n        + get_test_summary(): dict\n    }"}, {"unitName": "is_prime", "unitType": "function", "purpose": "This function checks if a given integer is a prime number.", "humanReadableExplanation": "The `is_prime` function determines if a given number is prime. It first handles base cases: numbers less than or equal to 1 are not prime, while 2 and 3 are.  Then, it efficiently checks for divisibility by 2 and 3.  The core logic uses a `while` loop and increments `i` by 6 in each iteration. This optimization is based on the fact that all primes greater than 3 can be expressed in the form 6k ± 1. The loop continues until `i * i` exceeds the input number. If the number is divisible by `i` or `i + 2`, it's not prime and `False` is returned. Otherwise, after the loop completes, the number is prime, and `True` is returned.", "dependencies": [], "inputs": [{"name": "number", "type": "int", "description": "The integer to check for primality."}], "outputs": {"type": "bool", "description": "Returns `True` if the number is prime, `False` otherwise.", "throws": []}, "visualDiagram": "graph TD\n    A[Start] --> B{number <= 1?};\n    B -- Yes --> C[Return False];\n    B -- No --> D{number <= 3?};\n    D -- Yes --> E[Return True];\n    D -- No --> F{number % 2 == 0 or number % 3 == 0?};\n    F -- Yes --> G[Return False];\n    F -- No --> H[i = 5];\n    H --> I{i * i <= number?};\n    I -- Yes --> J{number % i == 0 or number % (i + 2) == 0?};\n    J -- Yes --> G;\n    J -- No --> K[i += 6];\n    K --> I;\n    I -- No --> L[Return True];\n    C --> M[End];\n    E --> M;\n    G --> M;\n    L --> M;"}]}