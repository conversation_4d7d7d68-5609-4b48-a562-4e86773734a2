{"path": "backend/scripts/generate_docs.py", "contentHash": "1d6308764f8049d1af7bfddb0b962a5b46f30c0a19b206c3235e509ded6f1690", "commit": "52c73789c52a307b5ea20a22736f0b03959c85e2", "timestamp": "2025-07-05T14:14:09+05:30", "units": [{"unitName": "log_error", "unitType": "function", "purpose": "The `log_error` function prints an error message to the standard error stream.", "humanReadableExplanation": "This function takes a single argument, `message`, which is expected to be a string representing an error message.  It then uses the built-in `print` function to output this message. The `file=sys.stderr` argument redirects the output to the standard error stream (stderr).  Stderr is typically used for error messages to distinguish them from standard output (stdout), which is usually reserved for program results. This separation helps in debugging and monitoring applications, as error messages are clearly identifiable.", "dependencies": [], "inputs": [{"name": "message", "type": "str", "description": "The error message to be printed."}], "outputs": {"type": "null", "description": "This function does not return any value.", "throws": []}, "visualDiagram": "graph TD\n    A[Start] --> B{log_error(message)};\n    B --> C[print(message, file=sys.stderr)];\n    C --> D[End];"}, {"unitName": "log_info", "unitType": "function", "purpose": "The `log_info` function prints informational messages to the standard error stream.", "humanReadableExplanation": "This function takes a message as input and prints it to the standard error stream (sys.stderr).  Standard error is typically used for logging and displaying non-critical messages that should be visible to the user or system administrator. This helps separate informational messages from the standard output (stdout) which is usually reserved for the main program output. Using sys.stderr ensures that even if the standard output is redirected, these informational messages will still be visible.", "dependencies": [{"type": "internal", "name": "sys"}], "inputs": [{"name": "message", "type": "str", "description": "The message to be printed."}], "outputs": {"type": "None", "description": "This function does not return any value.", "throws": []}, "visualDiagram": "graph TD\n    A[Start];\n    B[log_info(message)];\n    C[print(message, file=sys.stderr)];\n    D[End];\n    A --> B;\n    B --> C;\n    C --> D;"}, {"unitName": "LLMClient", "unitType": "class", "purpose": "The LLMClient class facilitates interaction with a large language model (LLM) API, such as Gemini, to generate documentation and diagrams.", "humanReadableExplanation": "This class acts as an interface to an LLM API.  It initializes with an API key and model name from environment variables.  The `generate_documentation` method sends a prompt to the LLM to generate documentation in JSON format for a given code snippet.  The `generate_diagram` method takes the generated JSON documentation and a code snippet to create a Mermaid.js diagram.  Both methods include retry logic and handle potential errors during API calls or JSON parsing.  The prompts are carefully constructed to guide the LLM in generating accurate and detailed documentation and diagrams.", "dependencies": [{"type": "external", "name": "requests"}, {"type": "external", "name": "os"}, {"type": "external", "name": "json"}, {"type": "external", "name": "google.generativelanguage"}], "inputs": [{"name": "file_path", "type": "string", "description": "The path to the file containing the code snippet."}, {"name": "file_content", "type": "string", "description": "The full content of the file."}, {"name": "code_snippet", "type": "string", "description": "The code snippet to analyze."}, {"name": "unit_type", "type": "string", "description": "The type of code unit (e.g., 'function', 'class')."}, {"name": "max_retries", "type": "integer", "description": "The maximum number of retries for API requests."}, {"name": "timeout", "type": "integer", "description": "The timeout for API requests in seconds."}], "outputs": {"type": "object|None", "description": "For `generate_documentation`, returns a JSON object containing the generated documentation. For `generate_diagram`, returns a string containing the Mermaid.js diagram syntax. Returns None if there is an error.", "throws": ["ValueError", "requests.exceptions.RequestException", "json.JSONDecodeError", "KeyError", "IndexError"]}, "visualDiagram": "```mermaid\nclassDiagram\n    class LLMClient {\n        -api_key : string\n        -model_name : string\n        -api_endpoint : string\n        +__init__()\n        +_build_prompt()\n        +_build_diagram_prompt()\n        +generate_diagram()\n        +generate_documentation()\n    }\n\n    LLMClient : __init__()\n    LLMClient : _build_prompt()\n    LLMClient : _build_diagram_prompt()\n    LLMClient : generate_diagram()\n    LLMClient : generate_documentation()\n\n    LLMClient --> requests\n    LLMClient --> os\n    LLMClient --> json\n    LLMClient --> google.generativelanguage\n\n    class requests {\n    }\n\n    class os {\n    }\n\n    class json {\n    }\n\n    class google.generativelanguage {\n    }\n```"}, {"unitName": "get_staged_files", "unitType": "function", "purpose": "This function retrieves a list of files that have been staged in the Git repository.", "humanReadableExplanation": "The `get_staged_files` function uses the `subprocess` module to interact with the Git command-line interface. It first determines the root directory of the Git repository using `git rev-parse --show-toplevel`. Then, it changes the current working directory to the Git root using `os.chdir`.  After that, it executes the command `git diff --cached --name-only --diff-filter=AM` to get a list of staged files that have been added or modified. The output of this command is a newline-separated list of file paths. The function then splits this list into individual file paths, removes any empty strings, and returns the resulting list.  If any error occurs during the Git commands execution (e.g., `git` is not found or a Git command fails), it catches the exception, logs an error message using the `log_error` function, and returns an empty list.", "dependencies": [{"type": "external", "name": "subprocess"}, {"type": "external", "name": "os"}, {"type": "external", "name": "git"}], "inputs": [], "outputs": {"type": "list", "description": "A list of strings, where each string is the path to a staged file in the Git repository. Returns an empty list if there is an error or no staged files.", "throws": ["subprocess.CalledProcessError", "FileNotFoundError"]}, "visualDiagram": "graph TD\n    A[Start] --> B{Get Git Root (git rev-parse --show-toplevel)};\n    B -- Success --> C[Change Directory to Git Root (os.chdir)];\n    B -- Failure --> G[Error <PERSON>ling (log_error, return [])];\n    C --> D{Run git diff (git diff --cached --name-only --diff-filter=AM)};\n    D -- Success --> E{Split & Clean Output (split('\\n'), remove empty)};\n    D -- Failure --> G;\n    E --> F[Return File List];\n    G --> A;"}, {"unitName": "calculate_hash", "unitType": "function", "purpose": "The `calculate_hash` function computes a SHA-256 hash of the input content.", "humanReadableExplanation": "This function takes a string as input and uses the `hashlib` library to generate a SHA-256 hash.  SHA-256 is a cryptographic hash function that produces a 256-bit (64-character hexadecimal) hash. This hash is used to uniquely identify the input string; even a small change in the input will result in a drastically different hash. The function first encodes the input string into UTF-8 bytes before hashing it, ensuring consistent results across different systems. The resulting hexadecimal representation of the hash is then returned.", "dependencies": [{"type": "internal", "name": "<PERSON><PERSON><PERSON>"}], "inputs": [{"name": "content", "type": "str", "description": "The string content to be hashed."}], "outputs": {"type": "str", "description": "A 64-character hexadecimal string representing the SHA-256 hash of the input content.", "throws": []}, "visualDiagram": "graph TD\n    A[Start] --> B{Input: content (str)};\n    B --> C[Encode content to UTF-8];\n    C --> D[Compute SHA-256 hash];\n    D --> E[Return hexadecimal hash (str)];"}, {"unitName": "parse_js_ts_with_helper", "unitType": "function", "purpose": "This function parses JavaScript or TypeScript files using a Node.js helper script or falls back to a regular expression-based parser.", "humanReadableExplanation": "The `parse_js_ts_with_helper` function aims to parse JavaScript and TypeScript files efficiently. It prioritizes using a Node.js helper script (`parse_js_ts.ts`) for robust parsing.  The helper script's path is constructed relative to the current script's location. The function then uses `subprocess.run` to execute the helper script, passing the file path as an argument. The script's standard output, which is expected to be a JSON string, is parsed using `json.loads`. If any error occurs during this process (e.g., the helper script fails, the output is not valid JSON, or the helper script file is not found), the function gracefully falls back to a regular expression-based parser (`parse_with_regex`). This fallback mechanism ensures that parsing attempts to continue even if the preferred method encounters issues. The fallback parser reads the file content and attempts to extract relevant information using regular expressions. The function then returns the parsed data, either from the helper script or the fallback parser.", "dependencies": [{"type": "internal", "name": "parse_with_regex"}, {"type": "external", "name": "ts-node"}, {"type": "external", "name": "parse_js_ts.ts"}, {"type": "external", "name": "subprocess"}, {"type": "external", "name": "json"}, {"type": "external", "name": "os"}, {"type": "external", "name": "FileNotFoundError"}, {"type": "external", "name": "json.JSONDecodeError"}, {"type": "external", "name": "subprocess.CalledProcessError"}], "inputs": [{"name": "file_path", "type": "string", "description": "The path to the JavaScript or TypeScript file to be parsed."}], "outputs": {"type": "object", "description": "A JSON object representing the parsed content of the file. The structure of this object depends on the parsing method used (helper script or regex).  If parsing fails completely, it may return None.", "throws": ["subprocess.CalledProcessError", "json.JSONDecodeError", "FileNotFoundError"]}, "visualDiagram": "graph TD;A[parse_js_ts_with_helper(file_path)];B{Try ts-node helper};C[Execute ts-node parse_js_ts.ts file_path];D[json.loads(result.stdout)];E[Return parsed JSON];F{Catch Error};G[parse_with_regex(file_content)];H[Return parsed data];A-->B;B-->C;C-->D;D-->E;B-->F;F-->G;G-->H;E-->A;H-->A"}, {"unitName": "parse_file_for_units", "unitType": "function", "purpose": "The `parse_file_for_units` function determines the appropriate parsing method for a given file based on its extension and parses the file content to extract its constituent units (functions or classes).", "humanReadableExplanation": "This function acts as a router for parsing different file types.  It takes a file path and its content as input. If the file ends with '.py', it uses the `parse_python_with_ast` function to parse the file using Abstract Syntax Trees (ASTs). This is a more robust method for Python files as it leverages Python's internal understanding of the code structure.  If the file ends with '.js', '.ts', or '.tsx', it uses the `parse_js_ts_with_helper` function, which likely employs a Node.js helper script for more accurate parsing of JavaScript and TypeScript files. For all other file types, it falls back to a regular expression-based parser (`parse_with_regex`), which is less precise but provides broader support. The function then returns a list of parsed units, each represented as a dictionary containing the unit's name, type ('function' or 'class'), and its source code segment.", "dependencies": [{"type": "internal", "name": "parse_python_with_ast"}, {"type": "internal", "name": "parse_js_ts_with_helper"}, {"type": "internal", "name": "parse_with_regex"}], "inputs": [{"name": "file_path", "type": "string", "description": "The path to the file to be parsed."}, {"name": "content", "type": "string", "description": "The content of the file to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries, where each dictionary represents a parsed unit (function or class) from the file. Each unit dictionary contains the keys: 'name', 'type', and 'content'.  Returns an empty list if no units are found or parsing fails.", "throws": []}, "visualDiagram": "graph TD\n    A[parse_file_for_units(file_path, content)] --> B{file_path.endswith('.py')?};\n    B -- Yes --> C[parse_python_with_ast(content)];\n    B -- No --> D{file_path.endswith('.js','.ts','.tsx')?};\n    D -- Yes --> E[parse_js_ts_with_helper(file_path)];\n    D -- No --> F[parse_with_regex(content)];\n    C --> G[return units];\n    E --> G;\n    F --> G;"}, {"unitName": "parse_python_with_ast", "unitType": "function", "purpose": "This function parses Python code using the Abstract Syntax Tree (AST) to extract top-level functions and classes, returning them as a list of dictionaries.", "humanReadableExplanation": "The `parse_python_with_ast` function takes Python code as input and uses the `ast` module to analyze its structure. It iterates through the top-level nodes of the AST, identifying functions (`ast.FunctionDef`, `ast.AsyncFunctionDef`) and classes (`ast.ClassDef`). For each identified function or class, it extracts the source code segment using `ast.get_source_segment` and stores it in a dictionary along with the unit's name and type.  If no functions or classes are found, it returns a dictionary representing the file overview. If a `SyntaxError` occurs during AST parsing, it falls back to a regular expression-based parsing method (`parse_with_regex`). The function returns a list of dictionaries, where each dictionary represents a function or class found in the code, or a file overview if no functions or classes are found.", "dependencies": [{"type": "internal", "name": "ast"}, {"type": "internal", "name": "log_error"}, {"type": "internal", "name": "parse_with_regex"}], "inputs": [{"name": "content", "type": "str", "description": "The Python code to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries. Each dictionary contains the name, type ('function' or 'class'), and source code of a function or class found in the input. If no functions or classes are found, it contains a single dictionary with information about the file overview.", "throws": ["SyntaxError"]}, "visualDiagram": "graph TD\n    A[parse_python_with_ast(content)] --> B{SyntaxError?};\n    B -- Yes --> C[parse_with_regex(content)];\n    B -- No --> D[Iterate through AST nodes];\n    D --> E{Is node a function or class?};\n    E -- Yes --> F[Extract source, add to units];\n    E -- No --> G[Continue iteration];\n    F --> H[Return units];\n    G --> H;\n    C --> H;\n    H --> I[List of dictionaries (functions, classes, or file overview)];"}, {"unitName": "parse_with_regex", "unitType": "function", "purpose": "This function parses code content to identify functions and classes, returning them as a list of units.", "humanReadableExplanation": "The `parse_with_regex` function uses regular expressions to locate and extract function and class definitions from a given code string.  It first defines two regular expression patterns: `class_pattern` for classes and `func_pattern` for functions (handling both regular and async functions). These patterns are designed to match even if decorators are present. The function then iterates through the code using these patterns, finding all matches. Each match provides the type ('class' or 'function'), name, and start and end indices within the code string.  These matches are stored as tuples in the `found_spans` list. The list is sorted by starting index to ensure correct order. Finally, it iterates through the sorted matches, extracting the code segment between the start and end indices and creating a dictionary for each unit with its name, type, and content. If no functions or classes are found, it returns a single unit representing the entire file content. The function returns a list of dictionaries, where each dictionary represents a function or class found in the input code, containing its name, type, and code content.", "dependencies": [{"type": "internal", "name": "re"}], "inputs": [{"name": "content", "type": "str", "description": "The code content to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries, where each dictionary represents a function or class found in the input code. Each dictionary contains the keys \"name\", \"type\", and \"content\". If no functions or classes are found, it returns a list containing a single dictionary representing the entire file content.", "throws": []}, "visualDiagram": "graph TD\n    A[Input: Code Content] --> B{Regex Matching (class_pattern)};\n    A --> C{Regex Matching (func_pattern)};\n    B --> D[found_spans (class)];\n    C --> E[found_spans (function)];\n    D --> F{Merge & Sort by Start Index};\n    E --> F;\n    F --> G{Extract Code Segments};\n    G --> H[Output: List of Units];\n    H -- No functions/classes found --> I[Output: Single \"file_overview\" Unit];"}, {"unitName": "generate_markdown_from_doc", "unitType": "function", "purpose": "This function generates a Markdown documentation file from structured JSON documentation data.", "humanReadableExplanation": "The function takes a JSON file path and its corresponding data as input. It then creates a Markdown file with the same name but a '.md' extension. The Markdown file contains a structured representation of the documentation, including the file path, commit hash, timestamp, and individual units (functions or classes). For each unit, it includes its purpose, detailed explanation, visual diagram (if available), inputs, outputs, and dependencies.  The function handles potential errors during file writing and returns the path to the generated Markdown file or None if an error occurs.", "dependencies": [{"type": "internal", "name": "log_info"}, {"type": "internal", "name": "log_error"}], "inputs": [{"name": "doc_json_path", "type": "str", "description": "The path to the input JSON documentation file."}, {"name": "doc_data", "type": "dict", "description": "A dictionary containing the structured documentation data."}], "outputs": {"type": "str or None", "description": "The path to the generated Markdown file if successful, otherwise None.", "throws": ["IOError", "Exception"]}, "visualDiagram": "graph TD\n    A[generate_markdown_from_doc(doc_json_path, doc_data)] --> B{Open & Check doc_json_path};\n    B -- Success --> C[Write Markdown Header];\n    C --> D{Iterate through doc_data['units']};\n    D -- More Units --> E[Write Unit Section];\n    E --> D;\n    D -- No More Units --> F[Write Footer];\n    F --> G{Close File};\n    G -- Success --> H[Return md_path];\n    B -- Error --> I[Return None];\n    G -- Error --> I;\n    E --> J{Write Purpose, Explanation, Diagram, Inputs, Outputs, Dependencies};\n    I[Error Handling] --> K[log_error];\n    K --> Return None"}, {"unitName": "process_file", "unitType": "function", "purpose": "The `process_file` function processes a given file, generating and saving its documentation as JSON and Markdown files, utilizing an LLM client for content generation.", "humanReadableExplanation": "This function is the core of the documentation generation process. It takes a file path and an LLM client as input. First, it reads the file's content and calculates its SHA256 hash to detect changes. If the file has not changed since the last documentation generation, it skips processing. Otherwise, it parses the file to identify code units (functions, classes, etc.) using the `parse_file_for_units` function. For each unit, it uses the LLM client to generate documentation in JSON format and then generates a Mermaid.js diagram using the `generate_diagram` function.  The generated documentation, along with the diagram and metadata (file path, commit hash, timestamp), is saved as a JSON file. Finally, it generates a Markdown file from this JSON data for easier readability.  The function returns the paths to the generated JSON and Markdown files, or None if any errors occur during the process.", "dependencies": [{"type": "internal", "name": "calculate_hash"}, {"type": "internal", "name": "parse_file_for_units"}, {"type": "internal", "name": "get_git_info"}, {"type": "internal", "name": "generate_markdown_from_doc"}, {"type": "internal", "name": "LLMClient"}], "inputs": [{"name": "file_path", "type": "str", "description": "The path to the file to be processed."}, {"name": "llm_client", "type": "LLMClient", "description": "An instance of the LLMClient class, used to generate documentation and diagrams via an LLM API."}], "outputs": {"type": "[str, str] or [None, None]", "description": "Returns a tuple containing the paths to the generated JSON and Markdown documentation files. Returns (None, None) if any error occurs or if the file is unchanged.", "throws": ["IOError", "json.JSONDecodeError", "Exception"]}, "visualDiagram": "graph TD\n    A[Start];\n    B{Read File};\n    C{Calculate Hash};\n    D{Check for Existing Doc};\n    E{File Changed?};\n    F[Parse File for Units];\n    G{Units Found?};\n    H[Generate Documentation (LLM)];\n    I[Generate Diagram (LLM)];\n    J[Save JSON Doc];\n    K[Generate Markdown];\n    L[Save Markdown Doc];\n    M[Return Paths];\n    N[Return (None, None)];\n    A --> B;\n    B -- Error --> N;\n    B --> C;\n    C --> D;\n    D -- Exists & Unchanged --> N;\n    D --> E;\n    E -- Yes --> F;\n    E -- No --> N;\n    F -- Error --> N;\n    F --> G;\n    G -- Yes --> H;\n    G -- No --> N;\n    H -- Error --> N;\n    H --> I;\n    I --> J;\n    J -- Error --> N;\n    J --> K;\n    K -- Error --> N;\n    K --> L;\n    L --> M;"}, {"unitName": "get_git_info", "unitType": "function", "purpose": "The `get_git_info` function retrieves the latest commit hash and timestamp from the Git repository.", "humanReadableExplanation": "This function attempts to fetch the most recent commit's hash and timestamp from the local Git repository. It uses the `subprocess` module to execute Git commands.  If the `git` commands are successful, it returns the commit hash (a string) and the timestamp (a string in ISO 8601 format). If any error occurs during the Git command execution (e.g., not being in a Git repository), it returns default values: \"unknown_commit\" for the hash and the current timestamp in ISO 8601 format with Z timezone indicator for the timestamp.", "dependencies": [{"type": "internal", "name": "subprocess"}, {"type": "internal", "name": "datetime"}], "inputs": [], "outputs": {"type": "tuple", "description": "Returns a tuple containing the commit hash (string) and timestamp (string). If Git commands fail, it returns (\"unknown_commit\", current timestamp).", "throws": ["subprocess.CalledProcessError"]}, "visualDiagram": "graph TD\n    A[Start];\n    B{Execute 'git rev-parse HEAD'};\n    C[Success];\n    D{Execute 'git log -1 --format=%cd --date=iso-strict'};\n    E[Success];\n    F[Return commit_hash, timestamp];\n    G[Error];\n    H[Return \"unknown_commit\", current timestamp];\n    A --> B;\n    B --> C;\n    B --> G;\n    C --> D;\n    D --> E;\n    D --> G;\n    E --> F;\n    G --> H;"}, {"unitName": "load_config", "unitType": "function", "purpose": "This function loads the documentation generation configuration from a JSON file, providing default values if the file is missing or unreadable.", "humanReadableExplanation": "The `load_config` function attempts to read a JSON configuration file located at a relative path from the script's location.  The path is constructed using `os.path.join` to ensure platform independence. It uses a `try-except` block to handle potential errors: `FileNotFoundError` if the file doesn't exist and `json.JSONDecodeError` if the file's contents are not valid JSON. If an error occurs, it logs a warning message using the `log_error` function and returns a default configuration dictionary containing lists of allowed file extensions and directories to ignore. If the file is successfully read, the function uses `json.load` to parse the JSON data and returns the resulting Python dictionary.", "dependencies": [{"type": "internal", "name": "os"}, {"type": "internal", "name": "json"}, {"type": "internal", "name": "log_error"}], "inputs": [], "outputs": {"type": "dict", "description": "A dictionary containing the configuration settings. Returns a default configuration if the file is not found or is invalid.", "throws": ["FileNotFoundError", "json.JSONDecodeError"]}, "visualDiagram": "graph TD\n    A[Start];\n    B{File Exists?};\n    C[Read config file];\n    D{Valid JSON?};\n    E[Return config];\n    F[Log Error & Return Default Config];\n    A --> B;\n    B -- Yes --> C;\n    C --> D;\n    D -- Yes --> E;\n    D -- No --> F;\n    B -- No --> F;\n    E --> G[End];\n    F --> G;"}, {"unitName": "main", "unitType": "function", "purpose": "This function is the main entry point of the script, orchestrating the generation of documentation for staged files in a Git repository.", "humanReadableExplanation": "The `main` function serves as the primary driver for the documentation generation process. It begins by loading configuration settings, including allowed file extensions and directories to ignore.  It then attempts to initialize an LLMClient to interface with a large language model API. If the API key is missing or invalid, it exits with an error. Next, it retrieves a list of staged files using Git commands. For each staged file within the 'backend' directory, it checks if the file extension is allowed and if the file is not located within an ignored directory. If both conditions are true, the `process_file` function is called to generate documentation (JSON and Markdown) for the file.  The paths to the generated JSON and Markdown files are stored. Finally, if any documentation files were generated, their paths are printed to the console; otherwise, a message indicating that no documentation was updated is logged.", "dependencies": [{"type": "internal", "name": "load_config"}, {"type": "internal", "name": "LLMClient"}, {"type": "internal", "name": "get_staged_files"}, {"type": "internal", "name": "process_file"}, {"type": "internal", "name": "log_info"}, {"type": "internal", "name": "log_error"}, {"type": "external", "name": "Git"}, {"type": "external", "name": "os"}, {"type": "external", "name": "sys"}], "inputs": [], "outputs": {"type": "null", "description": "This function does not return a value. It prints the paths of generated documentation files to standard output or logs a message if no files were processed.", "throws": []}, "visualDiagram": "graph TD\n    A[Load Config];\n    B{Initialize LLMClient};\n    C[Get Staged Files];\n    D{Files Staged?};\n    E[Process Each File];\n    F{File Allowed & Not Ignored?};\n    G[Generate Documentation (JSON & Markdown)];\n    H[Print Generated File Paths];\n    I[Log 'No documentation updated'];\n    A --> B;\n    B -- Success --> C;\n    B -- Error --> J[Exit with Error];\n    C --> D;\n    D -- Yes --> E;\n    D -- No --> K[Log 'No staged files' & Exit];\n    E --> F;\n    F -- Yes --> G;\n    F -- No --> E;\n    G --> H;\n    H --> L[Exit];\n    I --> L;\n    J --> L;\n    K --> L"}]}