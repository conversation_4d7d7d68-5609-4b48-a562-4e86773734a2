{"path": "backend/src/test_doc_advanced.py", "contentHash": "a5a974d52475426758a4194fdbff5f63c643130adba385566f7dfa413734de33", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "simple_decorator", "unitType": "function", "purpose": "This decorator enhances a function by printing messages indicating the start and end of its execution.", "humanReadableExplanation": "The `simple_decorator` function is a Python decorator. It takes another function (`func`) as input and returns a modified version of that function. The modification involves adding print statements before and after the execution of the original function.  The `@wraps` decorator from `functools` is used to preserve metadata of the original function, such as its name and docstring, which is important for introspection and debugging. The inner `wrapper` function takes the same arguments as the original function, executes it, and then prints a completion message. This allows developers to track the execution flow of decorated functions.", "dependencies": [{"type": "internal", "name": "functools.wraps"}], "inputs": [{"name": "func", "type": "function", "description": "The function to be decorated."}], "outputs": {"type": "function", "description": "A decorated version of the input function that prints messages before and after execution.", "throws": []}, "visualDiagram": "graph TD\n    A[Decorated Function Call] --> B{Function Execution};\n    B --> C[Print 'Finished...'];\n    C --> D[Return Result];\n    A --> E[Print 'Executing...'];\n    E --> B;"}, {"unitName": "add_numbers", "unitType": "function", "purpose": "This function adds two integer numbers together.", "humanReadableExplanation": "The `add_numbers` function takes two integer arguments, `a` and `b`, and returns their sum.  It's a straightforward addition operation. The function is decorated with `simple_decorator`, which prints messages indicating the start and end of the function's execution.", "dependencies": [{"type": "internal", "name": "simple_decorator"}], "inputs": [{"name": "a", "type": "int", "description": "The first integer to be added."}, {"name": "b", "type": "int", "description": "The second integer to be added."}], "outputs": {"type": "int", "description": "The sum of the two input integers.", "throws": []}, "visualDiagram": "graph TD\n    A[Start];\n    B{a, b are integers?};\n    C[Calculate a + b];\n    D[Return sum];\n    E[End];\n    A --> B;\n    B -- Yes --> C;\n    B -- No --> E;\n    C --> D;\n    D --> E;"}, {"unitName": "process_data", "unitType": "function", "purpose": "The process_data function sorts a list of dictionaries based on a specified key.", "humanReadableExplanation": "This function takes a list of dictionaries as input and sorts them based on the values associated with a given key.  If the key is not found in a dictionary, it defaults to 0.  The sorting order can be reversed using the `reverse` parameter. The function includes input validation to ensure the input data is a list.  It uses the `sorted()` function with a `lambda` function as the `key` to perform the custom sorting.", "dependencies": [], "inputs": [{"name": "data", "type": "list", "description": "A list of dictionaries to be sorted."}, {"name": "key", "type": "str", "description": "The key to sort the dictionaries by. Defaults to \"default\"."}, {"name": "reverse", "type": "bool", "description": "If True, sorts in descending order. Defaults to False."}], "outputs": {"type": "list", "description": "A new list containing the sorted dictionaries.", "throws": ["TypeError"]}, "visualDiagram": "graph TD\n    A[Start] --> B{Is data a list?};\n    B -- Yes --> C[Sort data using key and reverse];\n    B -- No --> D[Raise TypeError];\n    C --> E[Return sorted list];\n    D --> E;\n    E[End]"}, {"unitName": "fetch_remote_data", "unitType": "function", "purpose": "This function simulates fetching data from a remote URL asynchronously.", "humanReadableExplanation": "The `fetch_remote_data` function mimics an asynchronous operation to retrieve data from a URL.  It uses `asyncio.sleep(1)` to simulate a one-second network delay.  After the simulated delay, it returns a dictionary indicating success and including the original URL.  The function is designed for testing or demonstration purposes, not for actual network requests.", "dependencies": [{"type": "internal", "name": "asyncio"}], "inputs": [{"name": "url", "type": "str", "description": "The URL from which to fetch data."}], "outputs": {"type": "dict", "description": "A dictionary containing the status of the operation and the URL.  Returns {'status': 'success', 'url': url} upon successful simulation.", "throws": []}, "visualDiagram": "graph TD\n    A[Start]\n    B{Simulate Network Delay (1 second)}\n    C[Return {'status': 'success', 'url': url}]\n    A --> B\n    B --> C"}, {"unitName": "AdvancedProcessor", "unitType": "class", "purpose": "The AdvancedProcessor class manages actions, tracking creation time and utilizing internal counters.", "humanReadableExplanation": "This class, AdvancedProcessor, is designed to perform actions with retry mechanisms. It keeps track of its creation time and uses an internal counter for bookkeeping.  The constructor (`__init__`) initializes the processor's name and records the creation timestamp using `asyncio.get_event_loop().time()`. The `created_at` property provides read-only access to this timestamp. The `perform_action` method attempts an action multiple times (defaulting to 3 retries) and returns `True` if successful.  The `_internal_action` method is a static method that increments an internal counter and always returns `True`.  The `__repr__` method provides a string representation of the object.", "dependencies": [{"type": "internal", "name": "asyncio"}], "inputs": [{"name": "name", "type": "str", "description": "The name of the processor instance."}, {"name": "action_type", "type": "str", "description": "The type of action to perform."}, {"name": "retries", "type": "int", "description": "The number of times to retry the action (defaults to 3)."}], "outputs": {"type": "bool", "description": "Returns `True` if the action was successful within the retry limit, `False` otherwise.", "throws": []}, "visualDiagram": "classDiagram\n    class AdvancedProcessor {\n        - _internal_counter : int\n        - name : str\n        - _created_at : float\n        + created_at : float\n        + perform_action(action_type : str, retries : int = 3) : bool\n        + __repr__() : str\n        + __init__(name : str)\n        + _internal_action() : bool {static}\n    }\n"}, {"unitName": "outer_function", "unitType": "function", "purpose": "This code demonstrates a function containing a nested function, performing a simple calculation.", "humanReadableExplanation": "The outer_function takes an integer 'x' as input. Inside, it defines a nested inner_function that takes an integer 'y' and returns the sum of 'x' and 'y'. The outer_function then calls the inner_function with 'y' set to 5 and returns the result of this inner calculation.", "dependencies": [], "inputs": [{"name": "x", "type": "int", "description": "An integer used in the inner function's calculation."}], "outputs": {"type": "int", "description": "The sum of the input 'x' and the hardcoded value 5.", "throws": []}, "visualDiagram": "graph TD\n    A[outer_function(x)] --> B{inner_function(5)};\n    B --> C[return x + 5];\n    A --> D[return inner_function(5)];"}, {"unitName": "final_function_test", "unitType": "function", "purpose": "This function serves as a test case to check the documentation generator's ability to handle files without a newline character at the end.", "humanReadableExplanation": "The `final_function_test` function is a simple function that returns the string \"final\".  Its primary purpose within the larger test suite is to verify that the documentation generation process correctly handles files that may not end with a newline character.  This is important for ensuring robustness and preventing potential parsing errors.", "dependencies": [], "inputs": [], "outputs": {"type": "str", "description": "Returns the string \"final\".", "throws": []}, "visualDiagram": "graph TD\n    A[Start] --> B{final_function_test()};\n    B --> C[return \"final\"];\n    C --> D[End];"}]}