{"path": "backend/.env.example", "contentHash": "dad5f2959b2466443524c595406f894ae344efc25fff42e7b998a2d395724d43", "commit": "10c5e15f86a81b2b3a881663ba8a41e47556c24c", "timestamp": "2025-06-27T16:33:48+05:30", "units": [{"unitName": "file_overview", "unitType": "file", "purpose": "This code snippet configures environment variables for the KAPI Automated Documentation System, specifying settings for either Gemini or OpenAI as the underlying large language model.", "humanReadableExplanation": "This `.env.example` file sets up environment variables to choose between using Google's Gemini or OpenAI's models for automated documentation.  The Gemini configuration section defines `GEMINI_API_KEY` (your API key for accessing Gemini) and `GEMINI_MODEL_NAME` (specifying which Gemini model to use, in this case, `gemini-1.5-pro-latest`).  The OpenAI configuration is commented out by default; to use OpenAI, you would uncomment these lines and comment out the Gemini lines.  The OpenAI settings include `OPENAI_API_KEY`, `OPENAI_MODEL_NAME` (e.g., `gpt-4o`), and `OPENAI_API_ENDPOINT` (the OpenAI API endpoint URL).  This file serves as a template; you'll need to replace the placeholder values with your actual API keys and desired model names.", "dependencies": [], "inputs": [], "outputs": {"type": "null", "description": "This file does not return any value; it sets environment variables.", "throws": []}, "visualDiagram": ""}]}