{"path": "backend/scripts/parse_js_ts.ts", "contentHash": "efa37c4f116a034a389f76e2de1c7c22babcb1b3cf900effea3709685e3a0b55", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "file_overview", "unitType": "file", "purpose": "This script parses JavaScript or TypeScript files to extract top-level functions and classes, outputting their names and content as JSON.", "humanReadableExplanation": "The script uses the `ts-morph` library to parse a given file. It iterates through top-level functions and classes, extracting their names and complete source code.  If a function or class is anonymous (lacks a name), it assigns '[anonymous]' as its name. The extracted information is structured as an array of objects, each representing a code unit (function or class), and then printed to the console as a JSON string.  The script includes error handling for missing file paths and files that do not exist.  It takes the file path as a command line argument.", "dependencies": [{"type": "external", "name": "ts-morph"}, {"type": "external", "name": "fs"}], "inputs": [{"name": "filePath", "type": "string", "description": "The path to the JavaScript or TypeScript file to be parsed."}], "outputs": {"type": "JSON string", "description": "A JSON array containing objects, each representing a function or class with its name and full source code. Each object has a 'name' (string), 'type' ('function' or 'class'), and 'content' (string) field.", "throws": ["Error: File not found", "Error: Invalid file path"]}, "visualDiagram": "classDiagram\n    class Project {\n        +addSourceFileAtPath(filePath: string): SourceFile\n    }\n    class SourceFile {\n        +getFunctions(): FunctionDeclaration[]\n        +getClasses(): ClassDeclaration[]\n    }\n    class FunctionDeclaration {\n        +getName(): string\n        +getFullText(): string\n    }\n    class ClassDeclaration {\n        +getName(): string\n        +getFullText(): string\n    }\n    Project \"1\" -- \"many\" SourceFile : addSourceFileAtPath\n    SourceFile \"1\" -- \"many\" FunctionDeclaration : getFunctions\n    SourceFile \"1\" -- \"many\" ClassDeclaration : getClasses\n    FunctionDeclaration -- CodeUnit : parseFile\n    ClassDeclaration -- CodeUnit : parseFile\n    class CodeUnit {\n        -name: string\n        -type: 'function' | 'class'\n        -content: string\n    }"}]}