# FastAPI to Node.js/Prisma Migration

This migration brings models from the FastAPI backend to the Node.js/Prisma backend. The migration includes several key model groups:

## Migration Overview

### 1. AI Integration Models
- **AIAgent**: AI agents configured for user workflows
- **AIAgentAction**: Records of actions taken by AI agents
- **AIPromptTemplate**: Reusable AI prompt templates

### 2. Workshop Models
- **Workshop**: Workshop model for Modern AI Pro
- **WorkshopParticipant**: Junction table for workshop participants
- **WorkshopMaterial**: Workshop materials and resources

### 3. Module and Lesson Models
- **Module**: Module model for workshop content organization
- **Lesson**: Lesson model for workshop content
- **LessonCompletion**: Tracking of lesson completions by users
- **Resource**: Learning resources for workshops

### 4. Gamification Models
- **EloHistory**: Track user ELO rating changes
- **KarmaTransaction**: Karma transaction log
- **Tag**, **Question**, **Answer**, etc.: Q&A functionality models

## How to Run the Migration

Follow these steps to run the migration:

1. **Make sure your database is backed up first!**

2. Run the migration script:
   ```bash
   chmod +x scripts/migrate_ai_models.sh
   ./scripts/migrate_ai_models.sh
   ```

3. The script will:
   - Apply schema changes
   - Run Prisma migration
   - Generate updated Prisma client

4. Verify the migration by checking your database

## Repository Files

The migration includes repository files for the new models:

- **AI Repositories**:
  - `ai/ai-agent.repository.ts`
  - `ai/ai-prompt-template.repository.ts`

- **Workshop Repositories**:
  - `workshop/workshop.repository.ts`
  - `workshop/module.repository.ts`

- **Gamification Repositories**:
  - `gamification/elo-history.repository.ts`
  - `gamification/karma.repository.ts`

## Next Steps

After running the migration:

1. Create services for the new repositories
2. Implement API controllers for the new endpoints
3. Set up authorization for the new endpoints
4. Test the new functionalities

## Important Notes

- This migration adds new fields to the `Conversation` model: `agentId`, `filePaths`, `totalTokens`, `totalCost`
- New fields are also added to the `Message` model: `codeLanguage`, `codeSnippet`, `filePath`, `lineStart`, `lineEnd`
- Ensure that any existing controllers using these models are updated to handle the new fields
