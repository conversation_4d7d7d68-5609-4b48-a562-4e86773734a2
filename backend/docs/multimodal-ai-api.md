# Multimodal AI API Documentation

This document describes the multimodal AI capabilities added to the Kapi backend, specifically designed for processing UI/UX mockups and general image analysis.

## Overview

The multimodal AI API provides two main functionalities:

1. **Mockup Processing** (`/api/ai/mockups/process`) - Specialized endpoint for analyzing and enhancing UI/UX mockups
2. **General Image Analysis** (`/api/ai/image/analyze`) - General-purpose image analysis with custom prompts

## Features

### 🎨 Mockup Processing
- Analyze UI/UX mockup images
- Get detailed design recommendations
- Receive structured component breakdowns
- Generate implementation code (HTML, CSS, React, etc.)
- Support for multiple design styles and platforms

### 🔍 Image Analysis
- Analyze any image with custom prompts
- Support for multiple AI models
- Configurable detail levels
- General-purpose computer vision tasks

## Authentication

All endpoints require authentication using the unified auth middleware. Include your API token in the Authorization header:

```
Authorization: Bearer YOUR_API_TOKEN
```

For development, you can use: `Bearer dev-token`

## Endpoints

### 1. Process Mockups

**POST** `/api/ai/mockups/process`

Analyze and enhance UI/UX mockup images with AI-powered design recommendations.

#### Request Body

```json
{
  "images": [
    {
      "url": "https://example.com/mockup1.png",
      "description": "Login page mockup"
    },
    {
      "url": "https://example.com/mockup2.png", 
      "description": "Dashboard mockup"
    }
  ],
  "instructions": "Make the design more modern and improve accessibility",
  "modelType": "gpt-4.1",
  "maxTokens": 8000,
  "temperature": 0.7,
  "outputFormat": "both",
  "designStyle": "modern",
  "targetPlatform": "web"
}
```

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `images` | Array | ✅ | - | Array of image objects with URL and optional description |
| `instructions` | String | ✅ | - | Specific instructions for improving the mockups |
| `modelType` | String | ❌ | "gpt-4.1" | AI model: `gpt-4.1`, `gpt-4.1-mini`, `o3`, `o4-mini` |
| `maxTokens` | Number | ❌ | 8000 | Maximum response tokens (100-16000) |
| `temperature` | Number | ❌ | 0.7 | Creativity level (0-2) |
| `outputFormat` | String | ❌ | "both" | Output format: `description`, `code`, `both` |
| `designStyle` | String | ❌ | "modern" | Design style preference |
| `targetPlatform` | String | ❌ | "web" | Target platform: `web`, `mobile`, `desktop` |

#### Response

```json
{
  "success": true,
  "analysis": "Detailed analysis of the current mockup design...",
  "recommendations": [
    "Improve color contrast for better accessibility",
    "Add proper navigation hierarchy",
    "Optimize button sizes for touch interactions"
  ],
  "updatedMockup": {
    "description": "Improved mockup with modern design patterns...",
    "components": [
      {
        "type": "header",
        "properties": {
          "text": "Welcome",
          "style": "primary",
          "variant": "large"
        },
        "position": {
          "x": 0,
          "y": 0,
          "width": 800,
          "height": 60
        }
      }
    ],
    "codeSnippet": "<header class=\"primary-header\">...</header>"
  },
  "usage": {
    "promptTokens": 1500,
    "completionTokens": 2000,
    "totalTokens": 3500,
    "cost": 0.035,
    "durationMs": 5000
  },
  "model": "gpt-4.1"
}
```

### 2. Analyze Image

**POST** `/api/ai/image/analyze`

Analyze a single image with a custom prompt for general-purpose tasks.

#### Request Body

```json
{
  "imageUrl": "https://example.com/image.png",
  "prompt": "Describe this image in detail and identify any issues",
  "modelType": "gpt-4.1",
  "maxTokens": 4000,
  "temperature": 0.7,
  "systemPrompt": "You are an expert image analyst...",
  "detail": "high"
}
```

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `imageUrl` | String | ✅ | - | URL of the image to analyze |
| `prompt` | String | ✅ | - | Analysis prompt for the image |
| `modelType` | String | ❌ | "gpt-4.1" | AI model to use |
| `maxTokens` | Number | ❌ | 4000 | Maximum response tokens |
| `temperature` | Number | ❌ | 0.7 | Creativity level |
| `systemPrompt` | String | ❌ | Default | Custom system prompt |
| `detail` | String | ❌ | "high" | Analysis detail: `low`, `high`, `auto` |

#### Response

```json
{
  "success": true,
  "content": "This image shows a modern web interface with...",
  "usage": {
    "promptTokens": 1200,
    "completionTokens": 800,
    "totalTokens": 2000,
    "cost": 0.02,
    "durationMs": 3000
  },
  "model": "gpt-4.1"
}
```

### 3. Health Check

**GET** `/api/ai/multimodal/health`

Check if the multimodal AI service is healthy and available.

#### Response

```json
{
  "success": true,
  "message": "Multimodal AI service is healthy",
  "capabilities": [
    "image_analysis",
    "mockup_processing", 
    "multimodal_chat",
    "text_generation",
    "embeddings"
  ],
  "models": ["gpt-4.1", "gpt-4.1-mini", "o3", "o4-mini"],
  "testTokens": 5,
  "testCost": 0.001
}
```

## Models

### Available Models

| Model | Description | Best For | Speed | Cost |
|-------|-------------|----------|-------|------|
| `gpt-4.1` | Latest GPT-4 model | Complex analysis, high quality | Slow | High |
| `gpt-4.1-mini` | Faster, cost-effective | General tasks | Fast | Low |
| `o3` | Advanced reasoning | Complex problem solving | Medium | High |
| `o4-mini` | Lightweight option | Simple tasks | Fast | Low |

### Model Selection Guidelines

- **Mockup Processing**: Use `gpt-4.1` for detailed analysis, `gpt-4.1-mini` for quick feedback
- **Image Analysis**: Use `gpt-4.1-mini` for general tasks, `gpt-4.1` for detailed analysis
- **Development/Testing**: Use `gpt-4.1-mini` to minimize costs

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error description",
  "details": "Detailed error message"
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid token)
- `500` - Internal Server Error (AI service issues)

## Usage Examples

### Example 1: Analyze a Login Page Mockup

```javascript
const response = await fetch('/api/ai/mockups/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    images: [{
      url: 'https://example.com/login-mockup.png',
      description: 'Current login page design'
    }],
    instructions: 'Improve the user experience and make it more accessible',
    designStyle: 'minimalist',
    targetPlatform: 'web',
    outputFormat: 'both'
  })
});

const result = await response.json();
console.log('Analysis:', result.analysis);
console.log('Recommendations:', result.recommendations);
console.log('Code:', result.updatedMockup.codeSnippet);
```

### Example 2: General Image Analysis

```javascript
const response = await fetch('/api/ai/image/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    imageUrl: 'https://example.com/screenshot.png',
    prompt: 'What are the usability issues in this interface?',
    modelType: 'gpt-4.1-mini'
  })
});

const result = await response.json();
console.log('Analysis:', result.content);
```

## Cost Management

### Token Usage

- **Mockup Processing**: Typically uses 2000-8000 tokens
- **Image Analysis**: Typically uses 1000-4000 tokens
- **Images**: High-detail images consume more tokens

### Cost Optimization Tips

1. Use `gpt-4.1-mini` for development and testing
2. Set appropriate `maxTokens` limits
3. Use `detail: "low"` for simple analysis tasks
4. Batch multiple images in single mockup processing requests

## Security Considerations

### Image URLs

- Only use HTTPS URLs for images
- Ensure images are publicly accessible
- Avoid including sensitive information in images
- Consider image size limits (recommended: < 10MB)

### Data Privacy

- Images are processed by Azure OpenAI
- No images are stored permanently
- Follow your organization's data handling policies

## Testing

Use the provided test script to verify the endpoints:

```bash
node test-multimodal.js
```

This will test all three endpoints with sample data and provide detailed feedback on functionality and performance.

## Integration with Sketch Canvas

The multimodal API is designed to integrate with the IDE's sketch canvas feature:

1. **Export Sketches**: Use the sketch canvas to create mockups
2. **Process with AI**: Send exported images to `/api/ai/mockups/process`
3. **Apply Recommendations**: Use the structured response to update designs
4. **Iterate**: Refine designs based on AI feedback

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check your API token
2. **400 Bad Request**: Verify request body format
3. **500 Internal Error**: Check Azure OpenAI configuration
4. **Image Load Errors**: Ensure image URLs are accessible

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
DEBUG=azure:* npm start
```

This will provide detailed logging of Azure OpenAI interactions.

## Support

For issues and questions:

1. Check the health endpoint: `/api/ai/multimodal/health`
2. Review server logs for error details
3. Verify Azure OpenAI configuration
4. Test with the provided test script

---

*Last updated: March 2025*