# Project API Documentation

This document outlines the API endpoints for the Project service in the KAPI platform.

## Overview

The Project API provides functionality for managing projects based on KAPI's Adaptive Project Identity System. Projects can be categorized into different motivation types (learner, contributor, builder) which affect which features are enabled.

## Authentication

All endpoints require authentication via Clerk. Include the authentication token in the request header.

## Base URL

All endpoints are prefixed with `/projects`.

## Endpoints

### List Projects

```
GET /projects
```

Lists all projects for the authenticated user.

**Query Parameters:**
- `activeOnly` (boolean, default: true): Filter to active projects only
- `sortByUpdated` (boolean, default: true): Sort by last updated time

**Response:**
```json
{
  "projects": [
    {
      "id": 1,
      "name": "Example Project",
      "description": "A sample project",
      "projectType": "WEB_APPLICATION",
      "projectMotivation": "learner",
      "createdAt": "2025-05-15T12:00:00Z",
      "updatedAt": "2025-05-15T12:00:00Z",
      "isActive": true
    }
  ]
}
```

### Get Project

```
GET /projects/:projectId
```

Retrieve details of a specific project.

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "Example Project",
    "description": "A sample project",
    "projectType": "WEB_APPLICATION",
    "projectMotivation": "learner",
    "createdAt": "2025-05-15T12:00:00Z",
    "updatedAt": "2025-05-15T12:00:00Z",
    "isActive": true
  }
}
```

### Create Project

```
POST /projects
```

Create a new project.

**Request Body:**
```json
{
  "name": "New Project",
  "description": "A new project description",
  "language": "TypeScript",
  "framework": "Express",
  "projectType": "WEB_APPLICATION",
  "projectMotivation": "learner"
}
```

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "New Project",
    "description": "A new project description",
    "language": "TypeScript",
    "framework": "Express",
    "projectType": "WEB_APPLICATION",
    "projectMotivation": "learner",
    "createdAt": "2025-05-15T12:00:00Z",
    "updatedAt": "2025-05-15T12:00:00Z",
    "isActive": true
  }
}
```

### Update Project

```
PUT /projects/:projectId
```

Update an existing project.

**Request Body:**
```json
{
  "name": "Updated Project Name",
  "description": "Updated description",
  "language": "JavaScript"
}
```

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "Updated Project Name",
    "description": "Updated description",
    "language": "JavaScript",
    "framework": "Express",
    "projectType": "WEB_APPLICATION",
    "projectMotivation": "learner",
    "createdAt": "2025-05-15T12:00:00Z",
    "updatedAt": "2025-05-15T13:00:00Z",
    "isActive": true
  }
}
```

### Delete Project

```
DELETE /projects/:projectId
```

Delete a project.

**Response:** HTTP 204 No Content

### Archive Project

```
POST /projects/:projectId/archive
```

Archive a project (mark as inactive).

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "Example Project",
    "isActive": false,
    "updatedAt": "2025-05-15T13:00:00Z"
  }
}
```

### Unarchive Project

```
POST /projects/:projectId/unarchive
```

Unarchive a project (mark as active).

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "Example Project",
    "isActive": true,
    "updatedAt": "2025-05-15T13:00:00Z"
  }
}
```

### Set Project Motivation

```
POST /projects/:projectId/motivation
```

Set the motivation type for a project.

**Request Body:**
```json
{
  "motivation": "builder"
}
```

**Response:**
```json
{
  "project": {
    "id": 1,
    "name": "Example Project",
    "projectMotivation": "builder",
    "updatedAt": "2025-05-15T13:00:00Z"
  }
}
```

### Check Feature Enablement

```
GET /projects/:projectId/feature/:featureName
```

Check if a specific feature is enabled for a project.

**Response:**
```json
{
  "projectId": 1,
  "feature": "explain_code",
  "enabled": true
}
```

### Add Project Objective

```
POST /projects/:projectId/objectives
```

Add an objective to a project.

**Request Body:**
```json
{
  "title": "New Objective",
  "description": "Description of the objective",
  "priority": 1,
  "status": "pending"
}
```

**Response:**
```json
{
  "objective": {
    "id": 1,
    "projectId": 1,
    "title": "New Objective",
    "description": "Description of the objective",
    "priority": 1,
    "status": "pending"
  }
}
```

### Get Project Objectives

```
GET /projects/:projectId/objectives
```

Get all objectives for a project.

**Response:**
```json
{
  "objectives": [
    {
      "id": 1,
      "projectId": 1,
      "title": "New Objective",
      "description": "Description of the objective",
      "priority": 1,
      "status": "pending"
    }
  ]
}
```

### Get Project Summary

```
GET /projects/:projectId/summary
```

Get a summary of a project with component counts.

**Response:**
```json
{
  "id": 1,
  "name": "Example Project",
  "description": "A sample project",
  "projectType": "WEB_APPLICATION",
  "projectMotivation": "learner",
  "language": "TypeScript",
  "framework": "Express",
  "createdAt": "2025-05-15T12:00:00Z",
  "updatedAt": "2025-05-15T12:00:00Z",
  "isActive": true,
  "components": {
    "objectives": 3,
    "techStack": 2,
    "slides": 5,
    "tests": 10
  },
  "featureState": {
    "explain_code": true,
    "refactor_suggestion": false,
    "generate_docs": true,
    "generate_tests": true,
    "sync_check": false,
    "progress_quizzes": true,
    "git_changelog": false,
    "timeline_view": false
  }
}
```

## Feature State Matrix

Different project motivation types enable different features:

| Feature | Learner | Contributor | Builder |
|---------|---------|-------------|---------|
| explain_code | ✅ | ✅ | ✅ |
| refactor_suggestion | ❌ | ✅ | ✅ |
| generate_docs | ✅ | ❌ | ✅ |
| generate_tests | ✅ | ❌ | ✅ |
| sync_check | ❌ | ❌ | ✅ |
| progress_quizzes | ✅ | ❌ | ❌ |
| git_changelog | ❌ | ✅ | ✅ |
| timeline_view | ❌ | ❌ | ✅ |