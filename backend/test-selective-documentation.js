/**
 * Test script for selective documentation generation
 * 
 * This script tests the new selective documentation features:
 * 1. Get file information for a directory
 * 2. Generate documentation for selected files
 * 3. Generate documentation for entire directories with priority
 * 4. Test file importance classification
 */

const axios = require('axios');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  projectPath: '/home/<USER>/Desktop/kapi-main/kapi',
  testDirectory: '/home/<USER>/Desktop/kapi-main/kapi/backend/src/services',
  selectedFiles: [
    '/home/<USER>/Desktop/kapi-main/kapi/backend/src/services/documentation.service.ts',
    '/home/<USER>/Desktop/kapi-main/kapi/backend/src/services/chroma-db.service.ts'
  ]
};

async function testGetDirectoryFiles() {
  console.log('📁 Testing Directory File Scanning...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/files`, {
      directoryPath: TEST_CONFIG.testDirectory,
      includeSubdirectories: true
    });
    
    const data = response.data;
    console.log('✅ Directory scan successful:');
    console.log(`   📊 Total files: ${data.totalFiles}`);
    console.log(`   📈 Summary:`, data.summary);
    
    console.log('\n🔍 File breakdown by importance:');
    ['critical', 'high', 'normal', 'low'].forEach(importance => {
      const files = data.files.filter(f => f.importance === importance);
      if (files.length > 0) {
        console.log(`\n   ${importance.toUpperCase()} (${files.length} files):`);
        files.slice(0, 3).forEach(file => {
          console.log(`     • ${file.name} (${(file.size / 1024).toFixed(1)}KB)`);
        });
        if (files.length > 3) {
          console.log(`     ... and ${files.length - 3} more`);
        }
      }
    });
    
    return data;
  } catch (error) {
    console.error('❌ Directory scan failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSelectedFilesDocumentation() {
  console.log('\n📝 Testing Selected Files Documentation...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath,
      selectedFiles: TEST_CONFIG.selectedFiles,
      priority: 'high'
    });
    
    console.log('✅ Selected files documentation started:', response.data);
    
    // Wait for processing
    console.log('⏳ Waiting for documentation generation...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
  } catch (error) {
    console.error('❌ Selected files documentation failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testDirectoryDocumentation() {
  console.log('\n📚 Testing Directory Documentation...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath,
      directoryPath: TEST_CONFIG.testDirectory,
      priority: 'normal',
      includeSubdirectories: false
    });
    
    console.log('✅ Directory documentation started:', response.data);
    
    // Wait for processing
    console.log('⏳ Waiting for documentation generation...');
    await new Promise(resolve => setTimeout(resolve, 20000));
    
  } catch (error) {
    console.error('❌ Directory documentation failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testPriorityLevels() {
  console.log('\n🎯 Testing Priority Levels...');
  
  const priorities = ['low', 'normal', 'high', 'critical'];
  
  for (const priority of priorities) {
    console.log(`\n   Testing priority: ${priority.toUpperCase()}`);
    
    try {
      const response = await axios.post(`${BASE_URL}/documentation/generate`, {
        projectPath: TEST_CONFIG.projectPath,
        selectedFiles: [TEST_CONFIG.selectedFiles[0]], // Just one file for testing
        priority: priority
      });
      
      console.log(`   ✅ ${priority} priority accepted:`, response.data.targets);
      
    } catch (error) {
      console.error(`   ❌ ${priority} priority failed:`, error.response?.data || error.message);
    }
  }
}

async function testErrorHandling() {
  console.log('\n🚨 Testing Error Handling...');
  
  // Test missing projectPath
  try {
    await axios.post(`${BASE_URL}/documentation/generate`, {
      selectedFiles: TEST_CONFIG.selectedFiles
    });
    console.log('❌ Should have failed for missing projectPath');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Correctly rejected missing projectPath');
    } else {
      console.log('❌ Unexpected error:', error.response?.data);
    }
  }
  
  // Test missing targets
  try {
    await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath
    });
    console.log('❌ Should have failed for missing targets');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Correctly rejected missing targets');
    } else {
      console.log('❌ Unexpected error:', error.response?.data);
    }
  }
  
  // Test invalid directory
  try {
    await axios.post(`${BASE_URL}/documentation/files`, {
      directoryPath: '/nonexistent/directory'
    });
    console.log('❌ Should have failed for invalid directory');
  } catch (error) {
    if (error.response?.status === 500) {
      console.log('✅ Correctly rejected invalid directory');
    } else {
      console.log('❌ Unexpected error:', error.response?.data);
    }
  }
}

async function demonstrateUserStory() {
  console.log('\n🎭 Demonstrating User Story...');
  console.log('User Story: As a developer, I want to create documentation for the files that I want');
  
  // Step 1: Scan directory to see available files
  console.log('\n1️⃣ Developer scans project directory to see available files...');
  const directoryInfo = await testGetDirectoryFiles();
  
  // Step 2: Select important files based on the scan
  console.log('\n2️⃣ Developer selects important files for documentation...');
  const importantFiles = directoryInfo.files
    .filter(f => f.importance === 'critical' || f.importance === 'high')
    .slice(0, 2)
    .map(f => f.path);
  
  console.log('   Selected files:', importantFiles.map(f => path.basename(f)));
  
  // Step 3: Generate documentation for selected files with high priority
  console.log('\n3️⃣ Developer generates documentation with high priority...');
  try {
    const response = await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath,
      selectedFiles: importantFiles,
      priority: 'high'
    });
    console.log('   ✅ Documentation generation started for selected files');
    console.log('   📊 Targets:', response.data.targets);
  } catch (error) {
    console.error('   ❌ Failed:', error.response?.data || error.message);
  }
  
  // Step 4: Use "Doc All" feature for a specific directory
  console.log('\n4️⃣ Developer uses "Doc All" button for services directory...');
  try {
    const response = await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath,
      directoryPath: TEST_CONFIG.testDirectory,
      priority: 'normal',
      includeSubdirectories: false
    });
    console.log('   ✅ Batch documentation started for directory');
    console.log('   📊 Targets:', response.data.targets);
  } catch (error) {
    console.error('   ❌ Failed:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting Selective Documentation Tests\n');
  console.log('Configuration:', TEST_CONFIG);
  console.log('=' .repeat(60));
  
  try {
    // Test individual features
    await testGetDirectoryFiles();
    await testSelectedFilesDocumentation();
    await testDirectoryDocumentation();
    await testPriorityLevels();
    await testErrorHandling();
    
    // Demonstrate the complete user story
    await demonstrateUserStory();
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 All selective documentation tests completed successfully!');
    console.log('\n📋 Summary of new features:');
    console.log('   ✅ File selection with importance classification');
    console.log('   ✅ Directory batch documentation ("Doc All")');
    console.log('   ✅ Priority-based processing');
    console.log('   ✅ Subdirectory inclusion control');
    console.log('   ✅ Comprehensive error handling');
    
  } catch (error) {
    console.log('\n' + '=' .repeat(60));
    console.error('💥 Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testGetDirectoryFiles,
  testSelectedFilesDocumentation,
  testDirectoryDocumentation,
  testPriorityLevels,
  testErrorHandling,
  demonstrateUserStory
};
