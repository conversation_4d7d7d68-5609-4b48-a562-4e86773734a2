#!/bin/bash

# ChromaDB Startup Script for KAPI Backend
# This script starts ChromaDB server and ensures it's ready for the backend

set -e

# Configuration
CHROMA_HOST=${CHROMA_HOST:-localhost}
CHROMA_PORT=${CHROMA_PORT:-8000}
CHROMA_DATA_DIR=${CHROMA_DATA_DIR:-./chroma_data}
MAX_WAIT_TIME=30

echo "🚀 Starting ChromaDB Server..."
echo "📍 Host: $CHROMA_HOST"
echo "🔌 Port: $CHROMA_PORT"
echo "📁 Data Directory: $CHROMA_DATA_DIR"

# Function to check if ChromaDB is running
check_chromadb() {
    curl -s -f "http://$CHROMA_HOST:$CHROMA_PORT/api/v2/version" > /dev/null 2>&1
}

# Function to wait for ChromaDB to be ready
wait_for_chromadb() {
    echo "⏳ Waiting for ChromaDB to be ready..."
    local count=0
    while [ $count -lt $MAX_WAIT_TIME ]; do
        if check_chromadb; then
            echo "✅ ChromaDB is ready!"
            return 0
        fi
        echo "   Waiting... ($((count + 1))/$MAX_WAIT_TIME)"
        sleep 1
        count=$((count + 1))
    done
    echo "❌ ChromaDB failed to start within $MAX_WAIT_TIME seconds"
    return 1
}

# Check if ChromaDB is already running
if check_chromadb; then
    echo "✅ ChromaDB is already running"
    VERSION=$(curl -s "http://$CHROMA_HOST:$CHROMA_PORT/api/v2/version" | tr -d '"')
    echo "📊 Version: $VERSION"
    exit 0
fi

# Check if chroma command is available
if ! command -v chroma &> /dev/null; then
    echo "❌ ChromaDB CLI not found. Installing..."
    
    # Try to install with pipx first
    if command -v pipx &> /dev/null; then
        echo "📦 Installing ChromaDB with pipx..."
        pipx install chromadb
    else
        echo "📦 Installing ChromaDB with pip..."
        pip install chromadb --break-system-packages
    fi
    
    # Verify installation
    if ! command -v chroma &> /dev/null; then
        echo "❌ Failed to install ChromaDB CLI"
        exit 1
    fi
    echo "✅ ChromaDB CLI installed successfully"
fi

# Create data directory if it doesn't exist
mkdir -p "$CHROMA_DATA_DIR"

# Start ChromaDB server in background
echo "🔄 Starting ChromaDB server..."
nohup chroma run --host "$CHROMA_HOST" --port "$CHROMA_PORT" --path "$CHROMA_DATA_DIR" > chromadb.log 2>&1 &
CHROMA_PID=$!

# Save PID for later cleanup
echo $CHROMA_PID > chromadb.pid
echo "📝 ChromaDB PID: $CHROMA_PID (saved to chromadb.pid)"

# Wait for ChromaDB to be ready
if wait_for_chromadb; then
    VERSION=$(curl -s "http://$CHROMA_HOST:$CHROMA_PORT/api/v2/version" | tr -d '"')
    echo "🎉 ChromaDB started successfully!"
    echo "📊 Version: $VERSION"
    echo "🌐 Access: http://$CHROMA_HOST:$CHROMA_PORT"
    echo "📋 Logs: $(pwd)/chromadb.log"
    echo ""
    echo "💡 To stop ChromaDB: ./stop-chromadb.sh"
    echo "💡 To check status: curl http://$CHROMA_HOST:$CHROMA_PORT/api/v2/version"
else
    echo "❌ Failed to start ChromaDB"
    if [ -f chromadb.pid ]; then
        kill $(cat chromadb.pid) 2>/dev/null || true
        rm -f chromadb.pid
    fi
    exit 1
fi
