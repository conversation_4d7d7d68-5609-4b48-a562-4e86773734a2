#!/usr/bin/env node

/**
 * Test ChromaDB Connection
 * 
 * This script tests the ChromaDB connection and basic functionality
 */

const { ChromaClient } = require('chromadb');

async function testChromaDBConnection() {
  console.log('🔍 Testing ChromaDB Connection...\n');

  try {
    // Initialize ChromaDB client
    const client = new ChromaClient({
      path: 'http://localhost:8000'
    });

    console.log('✅ ChromaDB client initialized');

    // Test connection by getting version
    try {
      const version = await client.version();
      console.log(`✅ ChromaDB server version: ${version}`);
    } catch (error) {
      console.error('❌ Failed to get ChromaDB version:', error.message);
      return false;
    }

    // Test collection operations
    try {
      // Create a test collection
      const collectionName = 'test_collection_' + Date.now();
      console.log(`\n📁 Creating test collection: ${collectionName}`);
      
      const collection = await client.createCollection({
        name: collectionName
      });
      
      console.log('✅ Test collection created successfully');

      // Add a test document
      console.log('\n📄 Adding test document...');
      await collection.add({
        ids: ['test_doc_1'],
        documents: ['This is a test document for ChromaDB connection verification.'],
        metadatas: [{ type: 'test', timestamp: new Date().toISOString() }]
      });
      
      console.log('✅ Test document added successfully');

      // Query the collection
      console.log('\n🔍 Querying test collection...');
      const results = await collection.query({
        queryTexts: ['test document'],
        nResults: 1
      });
      
      console.log('✅ Query executed successfully');
      console.log('📊 Query results:', {
        ids: results.ids[0],
        documents: results.documents[0],
        metadatas: results.metadatas[0]
      });

      // Clean up - delete the test collection
      console.log('\n🧹 Cleaning up test collection...');
      await client.deleteCollection({ name: collectionName });
      console.log('✅ Test collection deleted successfully');

      console.log('\n🎉 All ChromaDB tests passed! The semantic search functionality should work correctly.');
      return true;

    } catch (error) {
      console.error('❌ Collection operations failed:', error.message);
      return false;
    }

  } catch (error) {
    console.error('❌ ChromaDB connection test failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure ChromaDB server is running: chroma run --host localhost --port 8000');
    console.log('   2. Check if port 8000 is available: lsof -i :8000');
    console.log('   3. Verify ChromaDB installation: pip install chromadb');
    return false;
  }
}

// Run the test
if (require.main === module) {
  testChromaDBConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testChromaDBConnection };
