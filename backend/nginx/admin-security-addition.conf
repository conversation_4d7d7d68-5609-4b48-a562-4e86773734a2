# Add this to your kapihq.com.conf for admin security
# Place this BEFORE the main location / block

# Admin interface - restrict access
location /admin {
    # Optional: Restrict by IP (uncomment and add your IPs)
    # allow *******;    # Your office IP
    # allow *******;    # Your home IP
    # deny all;
    
    # Rate limiting for admin access
    limit_req zone=login burst=5 nodelay;
    
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}