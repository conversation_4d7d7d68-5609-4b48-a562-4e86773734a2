/**
 * Test script for semantic search functionality
 * 
 * This script tests the documentation generation and semantic search pipeline:
 * 1. Generate documentation for a sample file
 * 2. Index the documentation
 * 3. Perform semantic search queries
 * 4. Display results
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000'; // Adjust if your server runs on a different port

// Test configuration
const TEST_CONFIG = {
  projectPath: '/home/<USER>/Desktop/kapi-main/kapi',
  testQueries: [
    'How to generate documentation?',
    'What is the purpose of the documentation service?',
    'How does semantic search work?',
    'What are the API endpoints?',
    'How to index documentation files?'
  ]
};

async function testDocumentationGeneration() {
  console.log('🚀 Testing Documentation Generation...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/generate`, {
      projectPath: TEST_CONFIG.projectPath
    });
    
    console.log('✅ Documentation generation started:', response.data);
    
    // Wait a bit for generation to complete
    console.log('⏳ Waiting for documentation generation to complete...');
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
    
  } catch (error) {
    console.error('❌ Documentation generation failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testDocumentationIndexing() {
  console.log('📚 Testing Documentation Indexing...');
  
  try {
    const response = await axios.post(`${BASE_URL}/documentation/index`, {
      projectPath: TEST_CONFIG.projectPath,
      force: true
    });
    
    console.log('✅ Documentation indexing started:', response.data);
    
    // Wait a bit for indexing to complete
    console.log('⏳ Waiting for documentation indexing to complete...');
    await new Promise(resolve => setTimeout(resolve, 15000)); // Wait 15 seconds
    
  } catch (error) {
    console.error('❌ Documentation indexing failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSemanticSearch() {
  console.log('🔍 Testing Semantic Search...');
  
  for (const query of TEST_CONFIG.testQueries) {
    console.log(`\n🔎 Searching for: "${query}"`);
    
    try {
      const response = await axios.post(`${BASE_URL}/documentation/search`, {
        query: query,
        projectPath: TEST_CONFIG.projectPath,
        limit: 5,
        threshold: 0.5
      });
      
      const results = response.data;
      console.log(`📊 Found ${results.total} results:`);
      
      if (results.results.length === 0) {
        console.log('   No results found');
      } else {
        results.results.forEach((result, index) => {
          console.log(`   ${index + 1}. [${(result.similarity * 100).toFixed(1)}%] ${result.metadata.unit || 'Unknown'}`);
          console.log(`      File: ${result.metadata.sourceFile || result.metadata.filePath}`);
          console.log(`      Content: ${result.content.substring(0, 100)}...`);
        });
      }
      
    } catch (error) {
      console.error(`❌ Search failed for "${query}":`, error.response?.data || error.message);
    }
  }
}

async function testHealthCheck() {
  console.log('🏥 Testing Health Check...');
  
  try {
    const response = await axios.get(`${BASE_URL}/documentation/health`);
    console.log('✅ Health check passed:', response.data);
  } catch (error) {
    console.error('❌ Health check failed:', error.response?.data || error.message);
    throw error;
  }
}

async function runTests() {
  console.log('🧪 Starting Semantic Search Tests\n');
  console.log('Configuration:', TEST_CONFIG);
  console.log('=' .repeat(50));
  
  try {
    // Test health check first
    await testHealthCheck();
    
    // Test documentation generation
    await testDocumentationGeneration();
    
    // Test documentation indexing
    await testDocumentationIndexing();
    
    // Test semantic search
    await testSemanticSearch();
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 All tests completed successfully!');
    
  } catch (error) {
    console.log('\n' + '=' .repeat(50));
    console.error('💥 Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testDocumentationGeneration,
  testDocumentationIndexing,
  testSemanticSearch,
  testHealthCheck
};
