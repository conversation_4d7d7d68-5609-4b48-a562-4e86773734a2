import React, { useState } from 'react';
import ActivityBar from '../../components/ActivityBar';
import FileExplorer from './FileExplorer';
import { useProject } from '../../contexts/ProjectContext';
import DocumentationSelector from '../../components/DocumentationSelector';


const Sidebar = (props: React.HTMLAttributes<HTMLDivElement>) => {
  const [active, setActive] = useState('files');
  const { projectState, isProjectOpen } = useProject();

  return (
    <div
      {...props}
      className={`sidebar ${props.className ?? ''}`}
      style={{ height: '100%', width: '100%', ...props.style }}
    >
      <ActivityBar active={active} setActive={setActive} />
      {/* Sidebar content can be swapped based on `active` if needed */}
      <div style={{ height: 'calc(100% - 36px)', overflow: 'hidden' }}>
        {active === 'files' && <FileExplorer />}
        {active === 'search' && <p>Search coming soon</p>}
        {active === 'docs' && (
          isProjectOpen && projectState.path ? (
            <DocumentationSelector
              projectPath={projectState.path}
              initialDirectory={projectState.path}
            />
          ) : (
            <div style={{ padding: '20px', color: '#888' }}>
              <p>📄 Documentation Generator</p>
              <p>Open a project to start generating documentation</p>
            </div>
          )
        )}
        {active === 'bug' && <p>Let&apos;s hunt for bugs</p>}
        {active === 'source-control' && <p>Source Control</p>}
      </div>
    </div>
  );
};

export default Sidebar;
