/**
 * DocumentationSelector Component
 *
 * This component provides a UI for selective documentation generation.
 * It allows developers to:
 * 1. <PERSON><PERSON><PERSON> and select files for documentation
 * 2. Use "Doc All" functionality for directories
 * 3. Set priority levels for documentation generation
 * 4. View file importance classifications
 */

import React, { useState, useEffect } from 'react';
import { documentationApi, FileInfo, DirectoryFilesResponse } from '../services/DocumentationApi';

interface DocumentationSelectorProps {
  projectPath: string;
  initialDirectory?: string;
}

const DocumentationSelector: React.FC<DocumentationSelectorProps> = ({
  projectPath,
  initialDirectory
}) => {
  const [currentDirectory, setCurrentDirectory] = useState(initialDirectory || projectPath);
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [includeSubdirectories, setIncludeSubdirectories] = useState(true);
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'critical'>('normal');
  const [summary, setSummary] = useState({ critical: 0, high: 0, normal: 0, low: 0 });

  // Load files when directory changes
  useEffect(() => {
    loadDirectoryFiles();
  }, [currentDirectory, includeSubdirectories]);

  const loadDirectoryFiles = async () => {
    setLoading(true);
    try {
      const response: DirectoryFilesResponse = await documentationApi.getDirectoryFiles(
        currentDirectory,
        includeSubdirectories
      );
      setFiles(response.files || []);
      setSummary(response.summary || { critical: 0, high: 0, normal: 0, low: 0 });
    } catch (error) {
      console.error('Failed to load directory files:', error);
      setFiles([]); // Set empty array on error
      setSummary({ critical: 0, high: 0, normal: 0, low: 0 });
      // Handle error (show toast, etc.)
    } finally {
      setLoading(false);
    }
  };

  const handleFileToggle = (filePath: string) => {
    setSelectedFiles(prev =>
      prev.includes(filePath)
        ? prev.filter(f => f !== filePath)
        : [...prev, filePath]
    );
  };

  const handleSelectAll = () => {
    setSelectedFiles(files.map(f => f.path));
  };

  const handleSelectByImportance = (importance: 'critical' | 'high' | 'normal' | 'low') => {
    const importantFiles = files
      .filter(f => f.importance === importance)
      .map(f => f.path);
    setSelectedFiles(prev => [...new Set([...prev, ...importantFiles])]);
  };

  const handleClearSelection = () => {
    setSelectedFiles([]);
  };

  const handleGenerateDocumentation = async () => {
    if (selectedFiles.length === 0) {
      alert('Please select at least one file');
      return;
    }

    setLoading(true);
    try {
      const response = await documentationApi.generateDocumentationSelective({
        projectPath,
        selectedFiles,
        priority
      });

      alert(`Documentation generation started for ${selectedFiles.length} files with ${priority} priority`);
      console.log('Documentation generation response:', response);
    } catch (error) {
      console.error('Failed to generate documentation:', error);
      alert('Failed to start documentation generation');
    } finally {
      setLoading(false);
    }
  };

  const handleDocAllDirectory = async () => {
    if (!currentDirectory || !projectPath) {
      alert('Project path or directory not available');
      return;
    }

    setLoading(true);
    try {
      console.log('Starting documentation generation for:', {
        projectPath,
        directoryPath: currentDirectory,
        priority,
        includeSubdirectories
      });

      const response = await documentationApi.generateDocumentationSelective({
        projectPath,
        directoryPath: currentDirectory,
        priority,
        includeSubdirectories
      });

      alert(`Documentation generation started for entire directory with ${priority} priority`);
      console.log('Directory documentation response:', response);
    } catch (error) {
      console.error('Failed to generate directory documentation:', error);
      const errorMessage = error?.response?.data?.error || error?.message || 'Unknown error';
      alert(`Failed to start directory documentation: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'critical': return '#dc3545'; // Red
      case 'high': return '#fd7e14';     // Orange
      case 'normal': return '#28a745';   // Green
      case 'low': return '#6c757d';      // Gray
      default: return '#6c757d';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  return (
    <div className="documentation-selector" style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>📚 Documentation Generator</h2>

      {/* Directory Controls */}
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
        <h3>📁 Directory: {currentDirectory}</h3>

        <div style={{ display: 'flex', gap: '10px', alignItems: 'center', marginBottom: '10px' }}>
          <label>
            <input
              type="checkbox"
              checked={includeSubdirectories}
              onChange={(e) => setIncludeSubdirectories(e.target.checked)}
            />
            Include subdirectories
          </label>

          <select value={priority} onChange={(e) => setPriority(e.target.value as any)}>
            <option value="low">Low Priority</option>
            <option value="normal">Normal Priority</option>
            <option value="high">High Priority</option>
            <option value="critical">Critical Priority</option>
          </select>
        </div>

        <button
          onClick={handleDocAllDirectory}
          disabled={loading}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          🚀 Doc All ({files.length} files)
        </button>
      </div>

      {/* File Summary */}
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h4>📊 File Summary</h4>
        <div style={{ display: 'flex', gap: '20px' }}>
          <span style={{ color: getImportanceColor('critical') }}>
            🔴 Critical: {summary.critical}
          </span>
          <span style={{ color: getImportanceColor('high') }}>
            🟠 High: {summary.high}
          </span>
          <span style={{ color: getImportanceColor('normal') }}>
            🟢 Normal: {summary.normal}
          </span>
          <span style={{ color: getImportanceColor('low') }}>
            ⚪ Low: {summary.low}
          </span>
        </div>
      </div>

      {/* Selection Controls */}
      <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <button onClick={handleSelectAll}>Select All</button>
        <button onClick={() => handleSelectByImportance('critical')}>Select Critical</button>
        <button onClick={() => handleSelectByImportance('high')}>Select High</button>
        <button onClick={handleClearSelection}>Clear Selection</button>

        <span style={{ marginLeft: 'auto', fontWeight: 'bold' }}>
          Selected: {selectedFiles.length} files
        </span>
      </div>

      {/* File List */}
      <div style={{ maxHeight: '400px', overflowY: 'auto', border: '1px solid #ddd', borderRadius: '5px' }}>
        {loading ? (
          <div style={{ padding: '20px', textAlign: 'center' }}>Loading files...</div>
        ) : (
          files.map((file) => (
            <div
              key={file.path}
              style={{
                padding: '10px',
                borderBottom: '1px solid #eee',
                display: 'flex',
                alignItems: 'center',
                backgroundColor: selectedFiles.includes(file.path) ? '#e3f2fd' : 'white'
              }}
            >
              <input
                type="checkbox"
                checked={selectedFiles.includes(file.path)}
                onChange={() => handleFileToggle(file.path)}
                style={{ marginRight: '10px' }}
              />

              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: 'bold' }}>{file.name}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {file.relativePath} • {formatFileSize(file.size)}
                </div>
              </div>

              <span
                style={{
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: 'white',
                  backgroundColor: getImportanceColor(file.importance)
                }}
              >
                {file.importance.toUpperCase()}
              </span>
            </div>
          ))
        )}
      </div>

      {/* Generate Button */}
      <div style={{ marginTop: '20px', textAlign: 'center' }}>
        <button
          onClick={handleGenerateDocumentation}
          disabled={loading || selectedFiles.length === 0}
          style={{
            backgroundColor: selectedFiles.length > 0 ? '#28a745' : '#6c757d',
            color: 'white',
            padding: '15px 30px',
            border: 'none',
            borderRadius: '5px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: loading || selectedFiles.length === 0 ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? '⏳ Generating...' : `📝 Generate Documentation (${selectedFiles.length} files)`}
        </button>
      </div>
    </div>
  );
};

export default DocumentationSelector;
