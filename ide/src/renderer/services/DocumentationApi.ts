/**
 * DocumentationApi.ts
 *
 * Service for managing documentation-related API requests.
 * Provides methods for generating, updating, and retrieving documentation for projects.
 *
 * Maps to the /documentation/* endpoints in the backend.
 */

import apiClient from './ApiClient';

// Type definitions
export interface DocumentationRequest {
  project_id: number;
  file_path?: string;
  code_content?: string;
  doc_type?: 'readme' | 'api' | 'jsdoc' | 'user_guide' | 'architecture';
  format?: 'markdown' | 'html' | 'plain';
}

export interface DocumentationResponse {
  id: number;
  project_id: number;
  doc_type: string;
  content: string;
  format: string;
  created_at: string;
  updated_at: string;
}

// Additional interfaces for semantic search
export interface SemanticSearchRequest {
  query: string;
  projectPath?: string;
  limit?: number;
  threshold?: number;
}

export interface SemanticSearchResult {
  id: string;
  content: string;
  metadata: {
    filePath: string;
    fileName: string;
    sourceFile?: string;
    commit?: string;
    timestamp?: string;
    unit?: string;
    type?: string;
  };
  similarity: number;
}

export interface SemanticSearchResponse {
  query: string;
  results: SemanticSearchResult[];
  total: number;
  limit: number;
  threshold: number;
}

// Interfaces for selective documentation generation
export interface SelectiveDocumentationRequest {
  projectPath: string;
  selectedFiles?: string[];
  directoryPath?: string;
  priority?: 'low' | 'normal' | 'high' | 'critical';
  includeSubdirectories?: boolean;
}

export interface FileInfo {
  path: string;
  name: string;
  relativePath: string;
  importance: 'low' | 'normal' | 'high' | 'critical';
  size: number;
  lastModified: string;
}

export interface DirectoryFilesResponse {
  directory: string;
  includeSubdirectories: boolean;
  totalFiles: number;
  files: FileInfo[];
  summary: {
    critical: number;
    high: number;
    normal: number;
    low: number;
  };
}

// Interfaces for commit-aware documentation
export interface DocumentationChange {
  filePath: string;
  action: 'added' | 'modified' | 'deleted';
  metadata?: {
    filePath: string;
    fileName: string;
    sourceFile?: string;
    commit?: string;
    timestamp?: string;
    unit?: string;
    type?: string;
  };
}

export interface DocumentationChangesResponse {
  fromCommit: string;
  toCommit: string;
  projectPath: string;
  changes: DocumentationChange[];
  total: number;
}

class DocumentationApi {
  /**
   * Generates documentation for a project or specific file (legacy method)
   */
  async generateDocumentation(request: DocumentationRequest): Promise<DocumentationResponse> {
    const response = await apiClient.post('/documentation/generate', {
      projectPath: request.file_path, // Map to the expected backend format
      filePath: request.file_path
    });
    return response.data;
  }

  /**
   * Generates documentation for selected files with priority-based processing
   */
  async generateDocumentationSelective(request: SelectiveDocumentationRequest): Promise<{
    message: string;
    targets: {
      selectedFiles: number;
      directoryPath: string | null;
      priority: string;
      includeSubdirectories: boolean;
    }
  }> {
    const response = await apiClient.post('/documentation/generate', request);
    return response.data;
  }

  /**
   * Gets file information for documentation selection
   */
  async getDirectoryFiles(directoryPath: string, includeSubdirectories: boolean = true): Promise<DirectoryFilesResponse> {
    const response = await apiClient.post('/documentation/files', {
      directoryPath,
      includeSubdirectories
    });
    return response.data;
  }

  /**
   * Performs semantic search on documentation
   */
  async searchDocumentation(request: SemanticSearchRequest): Promise<SemanticSearchResponse> {
    const response = await apiClient.post('/documentation/search', request);
    return response.data;
  }

  /**
   * Indexes documentation for semantic search
   */
  async indexDocumentation(projectPath: string, force: boolean = false): Promise<{ message: string }> {
    const response = await apiClient.post('/documentation/index', {
      projectPath,
      force
    });
    return response.data;
  }

  /**
   * Checks health of documentation search service
   */
  async checkHealth(): Promise<{ status: string; service: string; timestamp: string }> {
    const response = await apiClient.get('/documentation/health');
    return response.data;
  }

  /**
   * Index documentation for a specific commit
   */
  async indexCommitDocumentation(commitHash: string, projectPath: string): Promise<{
    message: string;
    commitHash: string;
    projectPath: string;
  }> {
    const response = await apiClient.post(`/documentation/commit/${commitHash}`, {
      projectPath
    });
    return response.data;
  }

  /**
   * Get documentation changes between commits
   */
  async getDocumentationChanges(
    fromCommit: string,
    toCommit: string = 'HEAD',
    projectPath: string
  ): Promise<DocumentationChangesResponse> {
    const response = await apiClient.post('/documentation/changes', {
      fromCommit,
      toCommit,
      projectPath
    });
    return response.data;
  }

  /**
   * Gets documentation for a project
   */
  async getProjectDocumentation(projectId: number, docType?: string): Promise<DocumentationResponse[]> {
    // This would need to be implemented based on your specific requirements
    // For now, return empty array
    console.log('getProjectDocumentation not yet implemented', { projectId, docType });
    return [];
  }

  /**
   * Gets documentation for a specific file
   */
  async getFileDocumentation(projectId: number, filePath: string): Promise<DocumentationResponse> {
    // This would need to be implemented based on your specific requirements
    console.log('getFileDocumentation not yet implemented', { projectId, filePath });
    return {} as DocumentationResponse;
  }

  /**
   * Updates existing documentation
   */
  async updateDocumentation(docId: number, content: string): Promise<DocumentationResponse> {
    // This would need to be implemented based on your specific requirements
    console.log('updateDocumentation not yet implemented', { docId, content });
    return {} as DocumentationResponse;
  }

  /**
   * Syncs documentation with current code
   */
  async syncDocumentation(projectId: number, docType?: string): Promise<DocumentationResponse[]> {
    // This would need to be implemented based on your specific requirements
    console.log('syncDocumentation not yet implemented', { projectId, docType });
    return [];
  }
}

// Export a singleton instance
export const documentationApi = new DocumentationApi();
export default documentationApi;
